import{u as y,j as e,B as i,L as M,aM as G,d as h,b as c,e as x,A as D,w as A,g as a,m as j,n as g,ay as P,I as v,ad as H,ap as L,ao as z,k,a2 as S,J,K as b,s as C,b0 as T,G as o,y as _,aA as F,az as K,Y as Z}from"./mui-Cjipzt4F.js";import{r as u}from"./vendor-BfWiUekA.js";import{e as U,f as Y,g as V,C as q,a as Q,L as X,P as ee,b as ae,R as se,B as re,p as ne,c as ie,d as te,i as oe}from"./charts-Dx2u7Eir.js";import{u as le,b as ce,m as w}from"./index-_PFqhbKW.js";q.register(Q,X,ee,ae,se,re,ne,ie,te,oe);const de={id:1,firstName:"Sanju",middleName:"<PERSON>",lastName:"<PERSON>",admissionNumber:"VMS2024001",grade:10,section:"A",rollNumber:15,board:"CBSE",dateOfBirth:"2008-05-15",gender:"Male",bloodGroup:"B+",profilePhoto:null,address:"H.No 12-34, Jubilee Hills, Hyderabad",city:"Hyderabad",state:"Telangana",pincode:"500033",phone:"+91 9876543210",email:"<EMAIL>",fatherName:"Rajesh Kumar Reddy",fatherOccupation:"Software Engineer",fatherPhone:"+91 9876543211",motherName:"Priya Reddy",motherOccupation:"Teacher",motherPhone:"+91 9876543212",currentGPA:8.7,attendance:92,subjects:[{name:"Mathematics",grade:"A1",marks:95,teacher:"Mrs. Sharma"},{name:"Science",grade:"A1",marks:92,teacher:"Mr. Patel"},{name:"English",grade:"A2",marks:88,teacher:"Ms. Johnson"},{name:"Hindi",grade:"A1",marks:94,teacher:"Mrs. Gupta"},{name:"Social Studies",grade:"A2",marks:86,teacher:"Mr. Singh"},{name:"Telugu",grade:"A1",marks:96,teacher:"Mrs. Rao"}],performanceTrends:[{month:"Apr",gpa:8.2},{month:"May",gpa:8.4},{month:"Jun",gpa:8.6},{month:"Jul",gpa:8.5},{month:"Aug",gpa:8.7},{month:"Sep",gpa:8.8}],swotAnalysis:{strengths:["Strong in Mathematics","Good leadership skills","Excellent attendance"],weaknesses:["Needs improvement in English writing","Shy in group discussions"],opportunities:["Science Olympiad participation","Student council elections"],threats:["Increased competition","Time management challenges"]},achievements:[{title:"Mathematics Olympiad - District Level",date:"2024-03-15",type:"Academic"},{title:"Best Student of the Month",date:"2024-02-28",type:"Behavioral"},{title:"Science Fair - First Prize",date:"2024-01-20",type:"Academic"}],behavioralScores:{discipline:9,teamwork:8,leadership:9,creativity:7,communication:6,responsibility:9}},ye=()=>{const s=y(),l=le(),{studentId:m}=ce(),[t,r]=u.useState(0),[n,f]=u.useState(null),[I,W]=u.useState(!0);u.useEffect(()=>{(async()=>{W(!0);try{await new Promise(p=>setTimeout(p,1e3)),f(de)}catch(p){console.error("Error fetching student data:",p)}finally{W(!1)}})()},[m]);const B=(d,p)=>{r(p)},N=()=>{l(`/dashboard/students/${m}/edit`)},O=()=>{l(`/dashboard/students/${m}/swot`)};if(I)return e.jsx(i,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:400},children:e.jsx(M,{sx:{width:300}})});if(!n)return e.jsx(G,{severity:"error",children:"Student not found. Please check the student ID and try again."});const $={labels:n.performanceTrends.map(d=>d.month),datasets:[{label:"GPA Trend",data:n.performanceTrends.map(d=>d.gpa),borderColor:s.palette.primary.main,backgroundColor:h(s.palette.primary.main,.1),fill:!0,tension:.4}]},R={labels:Object.keys(n.behavioralScores).map(d=>d.charAt(0).toUpperCase()+d.slice(1)),datasets:[{label:"Behavioral Assessment",data:Object.values(n.behavioralScores),borderColor:s.palette.secondary.main,backgroundColor:h(s.palette.secondary.main,.2),pointBackgroundColor:s.palette.secondary.main,pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:s.palette.secondary.main}]},E={labels:n.subjects.map(d=>d.name),datasets:[{label:"Marks",data:n.subjects.map(d=>d.marks),backgroundColor:n.subjects.map((d,p)=>`hsl(${p*60%360}, 70%, 60%)`),borderColor:n.subjects.map((d,p)=>`hsl(${p*60%360}, 70%, 50%)`),borderWidth:2}]};return e.jsxs(i,{sx:{maxWidth:1400,mx:"auto",p:3},children:[e.jsx(w.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:e.jsx(c,{sx:{mb:3,overflow:"visible"},children:e.jsx(x,{sx:{p:4},children:e.jsxs(i,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:3},children:[e.jsxs(i,{sx:{display:"flex",alignItems:"center",gap:3},children:[e.jsx(D,{src:n.profilePhoto,sx:{width:120,height:120,border:`4px solid ${s.palette.primary.main}`,background:`linear-gradient(135deg, ${s.palette.primary.main} 0%, ${s.palette.secondary.main} 100%)`},children:e.jsx(A,{sx:{fontSize:60}})}),e.jsxs(i,{children:[e.jsxs(a,{variant:"h4",sx:{fontWeight:600,mb:1},children:[n.firstName," ",n.middleName," ",n.lastName]}),e.jsxs(a,{variant:"h6",color:"text.secondary",sx:{mb:1},children:["Class ",n.grade," - Section ",n.section]}),e.jsxs(a,{variant:"body1",color:"text.secondary",sx:{mb:2},children:["Admission No: ",n.admissionNumber," | Roll No: ",n.rollNumber]}),e.jsxs(j,{direction:"row",spacing:1,sx:{mb:2},children:[e.jsx(g,{label:n.board,color:"primary",variant:"filled",sx:{fontWeight:500}}),e.jsx(g,{label:`GPA: ${n.currentGPA}`,color:"success",variant:"outlined",icon:e.jsx(P,{})}),e.jsx(g,{label:`${n.attendance}% Attendance`,color:n.attendance>=90?"success":n.attendance>=75?"warning":"error",variant:"outlined"})]})]})]}),e.jsxs(j,{direction:"row",spacing:1,children:[e.jsx(v,{onClick:N,color:"primary",children:e.jsx(H,{})}),e.jsx(v,{color:"primary",children:e.jsx(L,{})}),e.jsx(v,{color:"primary",children:e.jsx(z,{})}),e.jsx(k,{variant:"contained",startIcon:e.jsx(S,{}),onClick:O,sx:{background:`linear-gradient(135deg, ${s.palette.primary.main} 0%, ${s.palette.secondary.main} 100%)`},children:"SWOT Analysis"})]})]})})})}),e.jsx(c,{sx:{mb:3},children:e.jsxs(J,{value:t,onChange:B,variant:"scrollable",scrollButtons:"auto",sx:{"& .MuiTab-root":{minHeight:64,fontWeight:500}},children:[e.jsx(b,{icon:e.jsx(A,{}),label:"Personal Info"}),e.jsx(b,{icon:e.jsx(C,{}),label:"Academic Performance"}),e.jsx(b,{icon:e.jsx(S,{}),label:"SWOT Analysis"}),e.jsx(b,{icon:e.jsx(T,{}),label:"Achievements"})]})}),e.jsxs(w.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},children:[t===0&&e.jsx(he,{student:n}),t===1&&e.jsx(xe,{student:n,performanceChartData:$,subjectPerformanceData:E,behavioralRadarData:R}),t===2&&e.jsx(me,{student:n}),t===3&&e.jsx(pe,{student:n})]},t)]})},he=({student:s})=>(y(),e.jsxs(o,{container:!0,spacing:3,children:[e.jsx(o,{item:!0,xs:12,md:6,children:e.jsx(c,{children:e.jsxs(x,{children:[e.jsx(a,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Basic Information"}),e.jsxs(j,{spacing:2,children:[e.jsxs(i,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(_,{color:"action"}),e.jsx(a,{variant:"body2",color:"text.secondary",children:"Date of Birth:"}),e.jsx(a,{variant:"body2",children:s.dateOfBirth})]}),e.jsxs(i,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(A,{color:"action"}),e.jsx(a,{variant:"body2",color:"text.secondary",children:"Gender:"}),e.jsx(a,{variant:"body2",children:s.gender})]}),e.jsxs(i,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(a,{variant:"body2",color:"text.secondary",children:"Blood Group:"}),e.jsx(a,{variant:"body2",children:s.bloodGroup})]})]})]})})}),e.jsx(o,{item:!0,xs:12,md:6,children:e.jsx(c,{children:e.jsxs(x,{children:[e.jsx(a,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Contact Information"}),e.jsxs(j,{spacing:2,children:[e.jsxs(i,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(F,{color:"action"}),e.jsx(a,{variant:"body2",color:"text.secondary",children:"Address:"}),e.jsx(a,{variant:"body2",children:s.address})]}),e.jsxs(i,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(K,{color:"action"}),e.jsx(a,{variant:"body2",color:"text.secondary",children:"Phone:"}),e.jsx(a,{variant:"body2",children:s.phone})]}),e.jsxs(i,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Z,{color:"action"}),e.jsx(a,{variant:"body2",color:"text.secondary",children:"Email:"}),e.jsx(a,{variant:"body2",children:s.email})]})]})]})})}),e.jsx(o,{item:!0,xs:12,children:e.jsx(c,{children:e.jsxs(x,{children:[e.jsx(a,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Parent/Guardian Information"}),e.jsxs(o,{container:!0,spacing:3,children:[e.jsxs(o,{item:!0,xs:12,md:6,children:[e.jsx(a,{variant:"subtitle2",sx:{mb:1,fontWeight:500},children:"Father's Details"}),e.jsxs(j,{spacing:1,children:[e.jsxs(a,{variant:"body2",children:["Name: ",s.fatherName]}),e.jsxs(a,{variant:"body2",children:["Occupation: ",s.fatherOccupation]}),e.jsxs(a,{variant:"body2",children:["Phone: ",s.fatherPhone]})]})]}),e.jsxs(o,{item:!0,xs:12,md:6,children:[e.jsx(a,{variant:"subtitle2",sx:{mb:1,fontWeight:500},children:"Mother's Details"}),e.jsxs(j,{spacing:1,children:[e.jsxs(a,{variant:"body2",children:["Name: ",s.motherName]}),e.jsxs(a,{variant:"body2",children:["Occupation: ",s.motherOccupation]}),e.jsxs(a,{variant:"body2",children:["Phone: ",s.motherPhone]})]})]})]})]})})})]})),xe=({student:s,performanceChartData:l,subjectPerformanceData:m,behavioralRadarData:t})=>e.jsxs(o,{container:!0,spacing:3,children:[e.jsx(o,{item:!0,xs:12,md:4,children:e.jsx(c,{children:e.jsxs(x,{children:[e.jsx(a,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Performance Overview"}),e.jsxs(j,{spacing:2,children:[e.jsxs(i,{children:[e.jsx(a,{variant:"body2",color:"text.secondary",children:"Current GPA"}),e.jsx(a,{variant:"h4",color:"primary.main",sx:{fontWeight:600},children:s.currentGPA})]}),e.jsxs(i,{children:[e.jsx(a,{variant:"body2",color:"text.secondary",children:"Attendance"}),e.jsxs(a,{variant:"h4",color:"success.main",sx:{fontWeight:600},children:[s.attendance,"%"]})]}),e.jsxs(i,{children:[e.jsx(a,{variant:"body2",color:"text.secondary",children:"Class Rank"}),e.jsx(a,{variant:"h4",color:"secondary.main",sx:{fontWeight:600},children:"3rd"})]})]})]})})}),e.jsx(o,{item:!0,xs:12,md:8,children:e.jsx(c,{children:e.jsxs(x,{children:[e.jsx(a,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"GPA Trend"}),e.jsx(i,{sx:{height:300},children:e.jsx(U,{data:l,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!1,min:7,max:10}}}})})]})})}),e.jsx(o,{item:!0,xs:12,md:8,children:e.jsx(c,{children:e.jsxs(x,{children:[e.jsx(a,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Subject Performance"}),e.jsx(i,{sx:{height:300},children:e.jsx(Y,{data:m,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,max:100}}}})})]})})}),e.jsx(o,{item:!0,xs:12,md:4,children:e.jsx(c,{children:e.jsxs(x,{children:[e.jsx(a,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Behavioral Assessment"}),e.jsx(i,{sx:{height:300},children:e.jsx(V,{data:t,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{r:{beginAtZero:!0,max:10}}}})})]})})}),e.jsx(o,{item:!0,xs:12,children:e.jsx(c,{children:e.jsxs(x,{children:[e.jsx(a,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Subject Details"}),e.jsx(o,{container:!0,spacing:2,children:s.subjects.map((r,n)=>e.jsx(o,{item:!0,xs:12,sm:6,md:4,children:e.jsx(c,{variant:"outlined",children:e.jsxs(x,{sx:{p:2},children:[e.jsx(a,{variant:"subtitle1",sx:{fontWeight:600},children:r.name}),e.jsxs(a,{variant:"body2",color:"text.secondary",sx:{mb:1},children:["Teacher: ",r.teacher]}),e.jsxs(i,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(g,{label:r.grade,color:r.grade.startsWith("A")?"success":"warning",size:"small"}),e.jsxs(a,{variant:"h6",sx:{fontWeight:600},children:[r.marks,"%"]})]})]})})},n))})]})})})]}),me=({student:s})=>{const l=y(),m=[{title:"Strengths",items:s.swotAnalysis.strengths,color:l.palette.success.main,icon:"💪"},{title:"Weaknesses",items:s.swotAnalysis.weaknesses,color:l.palette.error.main,icon:"⚠️"},{title:"Opportunities",items:s.swotAnalysis.opportunities,color:l.palette.info.main,icon:"🚀"},{title:"Threats",items:s.swotAnalysis.threats,color:l.palette.warning.main,icon:"⚡"}];return e.jsxs(i,{children:[e.jsxs(i,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[e.jsx(a,{variant:"h5",sx:{fontWeight:600},children:"SWOT Analysis Overview"}),e.jsx(k,{variant:"contained",startIcon:e.jsx(S,{}),sx:{background:`linear-gradient(135deg, ${l.palette.primary.main} 0%, ${l.palette.secondary.main} 100%)`},children:"Update SWOT"})]}),e.jsx(o,{container:!0,spacing:3,children:m.map((t,r)=>e.jsx(o,{item:!0,xs:12,md:6,children:e.jsx(c,{sx:{height:"100%",border:`2px solid ${h(t.color,.2)}`,background:`linear-gradient(135deg, ${h(t.color,.05)} 0%, ${h(t.color,.02)} 100%)`},children:e.jsxs(x,{children:[e.jsxs(i,{sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[e.jsx(a,{variant:"h2",sx:{fontSize:24},children:t.icon}),e.jsx(a,{variant:"h6",sx:{fontWeight:600,color:t.color},children:t.title})]}),e.jsx(j,{spacing:1,children:t.items.map((n,f)=>e.jsx(i,{sx:{p:2,borderRadius:1,background:h(t.color,.1),border:`1px solid ${h(t.color,.2)}`},children:e.jsx(a,{variant:"body2",children:n})},f))})]})})},r))}),e.jsx(c,{sx:{mt:3},children:e.jsxs(x,{children:[e.jsx(a,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"SWOT Matrix"}),e.jsxs(o,{container:!0,spacing:2,children:[e.jsx(o,{item:!0,xs:6,children:e.jsxs(i,{sx:{p:2,border:`2px solid ${l.palette.success.main}`,borderRadius:1,background:h(l.palette.success.main,.05),minHeight:150},children:[e.jsx(a,{variant:"subtitle1",sx:{fontWeight:600,color:"success.main",mb:1},children:"Strengths (Internal Positive)"}),s.swotAnalysis.strengths.map((t,r)=>e.jsxs(a,{variant:"body2",sx:{mb:.5},children:["• ",t]},r))]})}),e.jsx(o,{item:!0,xs:6,children:e.jsxs(i,{sx:{p:2,border:`2px solid ${l.palette.error.main}`,borderRadius:1,background:h(l.palette.error.main,.05),minHeight:150},children:[e.jsx(a,{variant:"subtitle1",sx:{fontWeight:600,color:"error.main",mb:1},children:"Weaknesses (Internal Negative)"}),s.swotAnalysis.weaknesses.map((t,r)=>e.jsxs(a,{variant:"body2",sx:{mb:.5},children:["• ",t]},r))]})}),e.jsx(o,{item:!0,xs:6,children:e.jsxs(i,{sx:{p:2,border:`2px solid ${l.palette.info.main}`,borderRadius:1,background:h(l.palette.info.main,.05),minHeight:150},children:[e.jsx(a,{variant:"subtitle1",sx:{fontWeight:600,color:"info.main",mb:1},children:"Opportunities (External Positive)"}),s.swotAnalysis.opportunities.map((t,r)=>e.jsxs(a,{variant:"body2",sx:{mb:.5},children:["• ",t]},r))]})}),e.jsx(o,{item:!0,xs:6,children:e.jsxs(i,{sx:{p:2,border:`2px solid ${l.palette.warning.main}`,borderRadius:1,background:h(l.palette.warning.main,.05),minHeight:150},children:[e.jsx(a,{variant:"subtitle1",sx:{fontWeight:600,color:"warning.main",mb:1},children:"Threats (External Negative)"}),s.swotAnalysis.threats.map((t,r)=>e.jsxs(a,{variant:"body2",sx:{mb:.5},children:["• ",t]},r))]})})]})]})})]})},pe=({student:s})=>{const l=y(),m=r=>{switch(r){case"Academic":return e.jsx(C,{sx:{color:l.palette.primary.main}});case"Behavioral":return e.jsx(T,{sx:{color:l.palette.secondary.main}});default:return e.jsx(P,{sx:{color:l.palette.warning.main}})}},t=r=>{switch(r){case"Academic":return l.palette.primary.main;case"Behavioral":return l.palette.secondary.main;default:return l.palette.warning.main}};return e.jsxs(i,{children:[e.jsx(a,{variant:"h5",sx:{fontWeight:600,mb:3},children:"Achievements & Recognition"}),e.jsx(o,{container:!0,spacing:3,children:s.achievements.map((r,n)=>e.jsx(o,{item:!0,xs:12,md:6,children:e.jsx(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:n*.1},children:e.jsx(c,{sx:{border:`2px solid ${h(t(r.type),.2)}`,background:`linear-gradient(135deg, ${h(t(r.type),.05)} 0%, ${h(t(r.type),.02)} 100%)`,transition:"transform 0.2s ease","&:hover":{transform:"translateY(-4px)"}},children:e.jsxs(x,{children:[e.jsxs(i,{sx:{display:"flex",alignItems:"center",gap:2,mb:2},children:[m(r.type),e.jsxs(i,{children:[e.jsx(a,{variant:"h6",sx:{fontWeight:600},children:r.title}),e.jsx(a,{variant:"body2",color:"text.secondary",children:new Date(r.date).toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric"})})]})]}),e.jsx(g,{label:r.type,size:"small",sx:{backgroundColor:t(r.type),color:"white",fontWeight:500}})]})})})},n))}),e.jsx(c,{sx:{mt:3},children:e.jsxs(x,{children:[e.jsx(a,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Achievement Statistics"}),e.jsxs(o,{container:!0,spacing:3,children:[e.jsx(o,{item:!0,xs:12,md:4,children:e.jsxs(i,{sx:{textAlign:"center"},children:[e.jsx(a,{variant:"h3",color:"primary.main",sx:{fontWeight:600},children:s.achievements.filter(r=>r.type==="Academic").length}),e.jsx(a,{variant:"body1",color:"text.secondary",children:"Academic Awards"})]})}),e.jsx(o,{item:!0,xs:12,md:4,children:e.jsxs(i,{sx:{textAlign:"center"},children:[e.jsx(a,{variant:"h3",color:"secondary.main",sx:{fontWeight:600},children:s.achievements.filter(r=>r.type==="Behavioral").length}),e.jsx(a,{variant:"body1",color:"text.secondary",children:"Behavioral Recognition"})]})}),e.jsx(o,{item:!0,xs:12,md:4,children:e.jsxs(i,{sx:{textAlign:"center"},children:[e.jsx(a,{variant:"h3",color:"warning.main",sx:{fontWeight:600},children:s.achievements.length}),e.jsx(a,{variant:"body1",color:"text.secondary",children:"Total Achievements"})]})})]})]})})]})};export{ye as default};
//# sourceMappingURL=StudentProfile-BDX8WOY5.js.map
