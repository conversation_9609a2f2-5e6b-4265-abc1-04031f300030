/**
 * VidyaMitra Platform - Student Registration Component
 * 
 * Comprehensive student registration form with Indian educational board support
 * Features multi-step form, validation, and cultural context
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Avatar,
  IconButton,
  Alert,
  Chip,
  Divider,
  Stack,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Person,
  School,
  ContactPhone,
  PhotoCamera,
  Save,
  ArrowBack,
  ArrowForward,
  CheckCircle,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

// Indian Educational Boards Configuration
const EDUCATIONAL_BOARDS = [
  { value: 'CBSE', label: 'Central Board of Secondary Education (CBSE)', color: '#2E5BA8' },
  { value: 'ICSE', label: 'Indian Certificate of Secondary Education (ICSE)', color: '#FF9933' },
  { value: 'STATE_AP', label: 'Andhra Pradesh State Board', color: '#00C853' },
  { value: 'STATE_TN', label: 'Tamil Nadu State Board', color: '#9C27B0' },
  { value: 'STATE_KA', label: 'Karnataka State Board', color: '#FF5722' },
  { value: 'STATE_TG', label: 'Telangana State Board', color: '#607D8B' },
  { value: 'IB', label: 'International Baccalaureate (IB)', color: '#795548' },
];

const GRADES = [
  { value: 1, label: 'Class 1' },
  { value: 2, label: 'Class 2' },
  { value: 3, label: 'Class 3' },
  { value: 4, label: 'Class 4' },
  { value: 5, label: 'Class 5' },
  { value: 6, label: 'Class 6' },
  { value: 7, label: 'Class 7' },
  { value: 8, label: 'Class 8' },
  { value: 9, label: 'Class 9' },
  { value: 10, label: 'Class 10' },
  { value: 11, label: 'Class 11' },
  { value: 12, label: 'Class 12' },
];

const SECTIONS = ['A', 'B', 'C', 'D', 'E', 'F'];

const LANGUAGES = [
  { value: 'en', label: 'English' },
  { value: 'hi', label: 'हिन्दी (Hindi)' },
  { value: 'te', label: 'తెలుగు (Telugu)' },
  { value: 'ta', label: 'தமிழ் (Tamil)' },
  { value: 'kn', label: 'ಕನ್ನಡ (Kannada)' },
  { value: 'ml', label: 'മലയാളം (Malayalam)' },
];

const StudentRegistration = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { t } = useTranslation(['common', 'students']);
  
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [profilePhoto, setProfilePhoto] = useState(null);
  
  const [formData, setFormData] = useState({
    // Basic Information
    firstName: '',
    middleName: '',
    lastName: '',
    dateOfBirth: '',
    gender: '',
    bloodGroup: '',
    
    // Academic Information
    admissionNumber: '',
    grade: '',
    section: '',
    board: '',
    academicYear: '2024-2025',
    rollNumber: '',
    
    // Contact Information
    address: '',
    city: '',
    state: '',
    pincode: '',
    phone: '',
    email: '',
    
    // Parent/Guardian Information
    fatherName: '',
    fatherOccupation: '',
    fatherPhone: '',
    motherName: '',
    motherOccupation: '',
    motherPhone: '',
    guardianName: '',
    guardianRelation: '',
    guardianPhone: '',
    
    // Emergency Contact
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelation: '',
    
    // Preferences
    preferredLanguage: 'en',
    specialNeeds: '',
    medicalConditions: '',
    previousSchool: '',
    
    // Consent
    dataConsent: false,
    communicationConsent: false,
  });

  const steps = [
    {
      label: 'Basic Information',
      icon: Person,
      description: 'Student personal details',
    },
    {
      label: 'Academic Details',
      icon: School,
      description: 'Educational information',
    },
    {
      label: 'Contact Information',
      icon: ContactPhone,
      description: 'Address and contacts',
    },
    {
      label: 'Review & Submit',
      icon: CheckCircle,
      description: 'Confirm details',
    },
  ];

  const handleInputChange = (field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const handlePhotoUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfilePhoto(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const validateStep = (step) => {
    const newErrors = {};
    
    switch (step) {
      case 0: // Basic Information
        if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
        if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
        if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date of birth is required';
        if (!formData.gender) newErrors.gender = 'Gender is required';
        break;
        
      case 1: // Academic Details
        if (!formData.admissionNumber.trim()) newErrors.admissionNumber = 'Admission number is required';
        if (!formData.grade) newErrors.grade = 'Grade is required';
        if (!formData.section) newErrors.section = 'Section is required';
        if (!formData.board) newErrors.board = 'Educational board is required';
        break;
        
      case 2: // Contact Information
        if (!formData.address.trim()) newErrors.address = 'Address is required';
        if (!formData.city.trim()) newErrors.city = 'City is required';
        if (!formData.state.trim()) newErrors.state = 'State is required';
        if (!formData.pincode.trim()) newErrors.pincode = 'Pincode is required';
        if (!formData.fatherName.trim()) newErrors.fatherName = 'Father name is required';
        if (!formData.motherName.trim()) newErrors.motherName = 'Mother name is required';
        if (!formData.emergencyContactName.trim()) newErrors.emergencyContactName = 'Emergency contact is required';
        if (!formData.emergencyContactPhone.trim()) newErrors.emergencyContactPhone = 'Emergency contact phone is required';
        break;
        
      case 3: // Review & Submit
        if (!formData.dataConsent) newErrors.dataConsent = 'Data consent is required';
        break;
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    if (!validateStep(activeStep)) return;
    
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Navigate to student profile or success page
      navigate('/dashboard/students');
    } catch (error) {
      console.error('Registration failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 600,
              mb: 1,
              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
            }}
          >
            Student Registration
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Register a new student with comprehensive information for SWOT analysis
          </Typography>
        </Box>
      </motion.div>

      {/* Stepper */}
      <Card sx={{ mb: 4, overflow: 'visible' }}>
        <CardContent>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel
                  StepIconComponent={({ active, completed }) => (
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        background: completed
                          ? `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`
                          : active
                          ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
                          : alpha(theme.palette.action.disabled, 0.12),
                        color: completed || active ? 'white' : theme.palette.action.disabled,
                        transition: 'all 0.3s ease',
                      }}
                    >
                      <step.icon sx={{ fontSize: 24 }} />
                    </Box>
                  )}
                >
                  <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>
                    {step.label}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {step.description}
                  </Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>
        </CardContent>
      </Card>

      {/* Form Content */}
      <Card>
        <CardContent sx={{ p: 4 }}>
          <AnimatePresence mode="wait">
            <motion.div
              key={activeStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {/* Step content will be rendered here */}
              {activeStep === 0 && (
                <BasicInformationStep
                  formData={formData}
                  errors={errors}
                  handleInputChange={handleInputChange}
                  profilePhoto={profilePhoto}
                  handlePhotoUpload={handlePhotoUpload}
                />
              )}
              {activeStep === 1 && (
                <AcademicDetailsStep
                  formData={formData}
                  errors={errors}
                  handleInputChange={handleInputChange}
                />
              )}
              {activeStep === 2 && (
                <ContactInformationStep
                  formData={formData}
                  errors={errors}
                  handleInputChange={handleInputChange}
                />
              )}
              {activeStep === 3 && (
                <ReviewSubmitStep
                  formData={formData}
                  errors={errors}
                  handleInputChange={handleInputChange}
                  profilePhoto={profilePhoto}
                />
              )}
            </motion.div>
          </AnimatePresence>

          {/* Navigation Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
            <Button
              onClick={handleBack}
              disabled={activeStep === 0}
              startIcon={<ArrowBack />}
              variant="outlined"
            >
              Back
            </Button>
            
            <Button
              onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}
              endIcon={activeStep === steps.length - 1 ? <Save /> : <ArrowForward />}
              variant="contained"
              loading={loading}
            >
              {activeStep === steps.length - 1 ? 'Register Student' : 'Next'}
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

// Step Components
const BasicInformationStep = ({ formData, errors, handleInputChange, profilePhoto, handlePhotoUpload }) => {
  const theme = useTheme();

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
        Basic Information
      </Typography>

      {/* Profile Photo Upload */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
        <Box sx={{ position: 'relative' }}>
          <Avatar
            src={profilePhoto}
            sx={{
              width: 120,
              height: 120,
              border: `4px solid ${alpha(theme.palette.primary.main, 0.2)}`,
              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
            }}
          >
            <Person sx={{ fontSize: 60 }} />
          </Avatar>
          <IconButton
            component="label"
            sx={{
              position: 'absolute',
              bottom: 0,
              right: 0,
              background: theme.palette.primary.main,
              color: 'white',
              '&:hover': {
                background: theme.palette.primary.dark,
              },
            }}
          >
            <PhotoCamera />
            <input
              type="file"
              hidden
              accept="image/*"
              onChange={handlePhotoUpload}
            />
          </IconButton>
        </Box>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            label="First Name *"
            value={formData.firstName}
            onChange={handleInputChange('firstName')}
            error={!!errors.firstName}
            helperText={errors.firstName}
            placeholder="e.g., Sanju"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            label="Middle Name"
            value={formData.middleName}
            onChange={handleInputChange('middleName')}
            placeholder="e.g., Kumar"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            label="Last Name *"
            value={formData.lastName}
            onChange={handleInputChange('lastName')}
            error={!!errors.lastName}
            helperText={errors.lastName}
            placeholder="e.g., Reddy"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Date of Birth *"
            type="date"
            value={formData.dateOfBirth}
            onChange={handleInputChange('dateOfBirth')}
            error={!!errors.dateOfBirth}
            helperText={errors.dateOfBirth}
            InputLabelProps={{ shrink: true }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth error={!!errors.gender}>
            <InputLabel>Gender *</InputLabel>
            <Select
              value={formData.gender}
              onChange={handleInputChange('gender')}
              label="Gender *"
            >
              <MenuItem value="Male">Male</MenuItem>
              <MenuItem value="Female">Female</MenuItem>
              <MenuItem value="Other">Other</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Blood Group</InputLabel>
            <Select
              value={formData.bloodGroup}
              onChange={handleInputChange('bloodGroup')}
              label="Blood Group"
            >
              <MenuItem value="A+">A+</MenuItem>
              <MenuItem value="A-">A-</MenuItem>
              <MenuItem value="B+">B+</MenuItem>
              <MenuItem value="B-">B-</MenuItem>
              <MenuItem value="AB+">AB+</MenuItem>
              <MenuItem value="AB-">AB-</MenuItem>
              <MenuItem value="O+">O+</MenuItem>
              <MenuItem value="O-">O-</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Preferred Language</InputLabel>
            <Select
              value={formData.preferredLanguage}
              onChange={handleInputChange('preferredLanguage')}
              label="Preferred Language"
            >
              {LANGUAGES.map((lang) => (
                <MenuItem key={lang.value} value={lang.value}>
                  {lang.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>
    </Box>
  );
};

const AcademicDetailsStep = ({ formData, errors, handleInputChange }) => {
  const theme = useTheme();

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
        Academic Information
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Admission Number *"
            value={formData.admissionNumber}
            onChange={handleInputChange('admissionNumber')}
            error={!!errors.admissionNumber}
            helperText={errors.admissionNumber}
            placeholder="e.g., VMS2024001"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Roll Number"
            value={formData.rollNumber}
            onChange={handleInputChange('rollNumber')}
            placeholder="e.g., 15"
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <FormControl fullWidth error={!!errors.grade}>
            <InputLabel>Grade/Class *</InputLabel>
            <Select
              value={formData.grade}
              onChange={handleInputChange('grade')}
              label="Grade/Class *"
            >
              {GRADES.map((grade) => (
                <MenuItem key={grade.value} value={grade.value}>
                  {grade.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={4}>
          <FormControl fullWidth error={!!errors.section}>
            <InputLabel>Section *</InputLabel>
            <Select
              value={formData.section}
              onChange={handleInputChange('section')}
              label="Section *"
            >
              {SECTIONS.map((section) => (
                <MenuItem key={section} value={section}>
                  Section {section}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            label="Academic Year"
            value={formData.academicYear}
            onChange={handleInputChange('academicYear')}
            placeholder="2024-2025"
          />
        </Grid>

        <Grid item xs={12}>
          <FormControl fullWidth error={!!errors.board}>
            <InputLabel>Educational Board *</InputLabel>
            <Select
              value={formData.board}
              onChange={handleInputChange('board')}
              label="Educational Board *"
            >
              {EDUCATIONAL_BOARDS.map((board) => (
                <MenuItem key={board.value} value={board.value}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip
                      size="small"
                      sx={{
                        backgroundColor: board.color,
                        color: 'white',
                        minWidth: 60,
                      }}
                      label={board.value}
                    />
                    {board.label}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Previous School"
            value={formData.previousSchool}
            onChange={handleInputChange('previousSchool')}
            placeholder="Name of previous school (if applicable)"
            multiline
            rows={2}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

const ContactInformationStep = ({ formData, errors, handleInputChange }) => {
  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
        Contact Information
      </Typography>

      {/* Address Information */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500, color: 'primary.main' }}>
        Address Details
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Address *"
            value={formData.address}
            onChange={handleInputChange('address')}
            error={!!errors.address}
            helperText={errors.address}
            multiline
            rows={3}
            placeholder="Complete address with house number, street, area"
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            label="City *"
            value={formData.city}
            onChange={handleInputChange('city')}
            error={!!errors.city}
            helperText={errors.city}
            placeholder="e.g., Hyderabad"
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            label="State *"
            value={formData.state}
            onChange={handleInputChange('state')}
            error={!!errors.state}
            helperText={errors.state}
            placeholder="e.g., Telangana"
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            label="Pincode *"
            value={formData.pincode}
            onChange={handleInputChange('pincode')}
            error={!!errors.pincode}
            helperText={errors.pincode}
            placeholder="e.g., 500001"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Phone Number"
            value={formData.phone}
            onChange={handleInputChange('phone')}
            placeholder="+91 9876543210"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={handleInputChange('email')}
            placeholder="<EMAIL>"
          />
        </Grid>
      </Grid>

      <Divider sx={{ my: 3 }} />

      {/* Parent/Guardian Information */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500, color: 'primary.main' }}>
        Parent/Guardian Information
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Father's Name *"
            value={formData.fatherName}
            onChange={handleInputChange('fatherName')}
            error={!!errors.fatherName}
            helperText={errors.fatherName}
            placeholder="Father's full name"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Father's Occupation"
            value={formData.fatherOccupation}
            onChange={handleInputChange('fatherOccupation')}
            placeholder="e.g., Software Engineer"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Father's Phone"
            value={formData.fatherPhone}
            onChange={handleInputChange('fatherPhone')}
            placeholder="+91 9876543210"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Mother's Name *"
            value={formData.motherName}
            onChange={handleInputChange('motherName')}
            error={!!errors.motherName}
            helperText={errors.motherName}
            placeholder="Mother's full name"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Mother's Occupation"
            value={formData.motherOccupation}
            onChange={handleInputChange('motherOccupation')}
            placeholder="e.g., Teacher"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Mother's Phone"
            value={formData.motherPhone}
            onChange={handleInputChange('motherPhone')}
            placeholder="+91 9876543210"
          />
        </Grid>
      </Grid>

      <Divider sx={{ my: 3 }} />

      {/* Emergency Contact */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500, color: 'primary.main' }}>
        Emergency Contact
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            label="Emergency Contact Name *"
            value={formData.emergencyContactName}
            onChange={handleInputChange('emergencyContactName')}
            error={!!errors.emergencyContactName}
            helperText={errors.emergencyContactName}
            placeholder="Contact person name"
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            label="Emergency Contact Phone *"
            value={formData.emergencyContactPhone}
            onChange={handleInputChange('emergencyContactPhone')}
            error={!!errors.emergencyContactPhone}
            helperText={errors.emergencyContactPhone}
            placeholder="+91 9876543210"
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            label="Relation"
            value={formData.emergencyContactRelation}
            onChange={handleInputChange('emergencyContactRelation')}
            placeholder="e.g., Uncle, Aunt"
          />
        </Grid>
      </Grid>
    </Box>
  );
};

const ReviewSubmitStep = ({ formData, errors, handleInputChange, profilePhoto }) => {
  const theme = useTheme();

  const selectedBoard = EDUCATIONAL_BOARDS.find(board => board.value === formData.board);
  const selectedGrade = GRADES.find(grade => grade.value === formData.grade);

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
        Review & Submit
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        Please review all information carefully before submitting. You can go back to make changes if needed.
      </Alert>

      {/* Student Summary Card */}
      <Card sx={{ mb: 3, background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)` }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, mb: 3 }}>
            <Avatar
              src={profilePhoto}
              sx={{
                width: 80,
                height: 80,
                border: `3px solid ${theme.palette.primary.main}`,
              }}
            >
              <Person sx={{ fontSize: 40 }} />
            </Avatar>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 600 }}>
                {formData.firstName} {formData.middleName} {formData.lastName}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {selectedGrade?.label} - Section {formData.section}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Admission No: {formData.admissionNumber}
              </Typography>
            </Box>
          </Box>

          {selectedBoard && (
            <Chip
              label={selectedBoard.label}
              sx={{
                backgroundColor: selectedBoard.color,
                color: 'white',
                fontWeight: 500,
              }}
            />
          )}
        </CardContent>
      </Card>

      {/* Information Summary */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                Personal Information
              </Typography>
              <Stack spacing={1}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="text.secondary">Date of Birth:</Typography>
                  <Typography variant="body2">{formData.dateOfBirth}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="text.secondary">Gender:</Typography>
                  <Typography variant="body2">{formData.gender}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="text.secondary">Blood Group:</Typography>
                  <Typography variant="body2">{formData.bloodGroup || 'Not specified'}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="text.secondary">Preferred Language:</Typography>
                  <Typography variant="body2">
                    {LANGUAGES.find(lang => lang.value === formData.preferredLanguage)?.label}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                Contact Information
              </Typography>
              <Stack spacing={1}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="text.secondary">City:</Typography>
                  <Typography variant="body2">{formData.city}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="text.secondary">State:</Typography>
                  <Typography variant="body2">{formData.state}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="text.secondary">Father's Name:</Typography>
                  <Typography variant="body2">{formData.fatherName}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="text.secondary">Mother's Name:</Typography>
                  <Typography variant="body2">{formData.motherName}</Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Consent Checkboxes */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
            Consent & Permissions
          </Typography>

          <Stack spacing={2}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.dataConsent}
                  onChange={handleInputChange('dataConsent')}
                  color="primary"
                />
              }
              label={
                <Typography variant="body2">
                  I consent to the collection and processing of student data for educational purposes and SWOT analysis. *
                </Typography>
              }
            />
            {errors.dataConsent && (
              <Typography variant="caption" color="error">
                {errors.dataConsent}
              </Typography>
            )}

            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.communicationConsent}
                  onChange={handleInputChange('communicationConsent')}
                  color="primary"
                />
              }
              label={
                <Typography variant="body2">
                  I consent to receive communications about student progress, events, and important updates.
                </Typography>
              }
            />

            <Alert severity="warning" sx={{ mt: 2 }}>
              <Typography variant="body2">
                By submitting this form, you confirm that all information provided is accurate and complete.
                The student data will be used for educational assessment, SWOT analysis, and academic progress tracking.
              </Typography>
            </Alert>
          </Stack>
        </CardContent>
      </Card>
    </Box>
  );
};

export default StudentRegistration;
