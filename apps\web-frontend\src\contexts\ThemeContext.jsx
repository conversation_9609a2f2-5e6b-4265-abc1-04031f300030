/**
 * VidyaMitra Platform - Theme Context Provider
 * 
 * Provides theme management with dark/light mode switching,
 * modern design system, and cultural adaptations
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import createModernEducationalTheme from '../theme/modernEducationalTheme';

// Create Theme Context
const ThemeContext = createContext();

// Custom hook to use theme context
export const useThemeMode = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useThemeMode must be used within a ThemeContextProvider');
  }
  return context;
};

// Theme Context Provider Component
export const ThemeContextProvider = ({ children }) => {
  // Get initial theme mode from localStorage or default to light
  const [mode, setMode] = useState(() => {
    const savedMode = localStorage.getItem('vidyamitra-theme-mode');
    return savedMode || 'light';
  });

  // Create theme based on current mode
  const theme = createModernEducationalTheme(mode);

  // Toggle between light and dark mode
  const toggleMode = () => {
    const newMode = mode === 'light' ? 'dark' : 'light';
    setMode(newMode);
    localStorage.setItem('vidyamitra-theme-mode', newMode);
  };

  // Set specific mode
  const setThemeMode = (newMode) => {
    if (newMode === 'light' || newMode === 'dark') {
      setMode(newMode);
      localStorage.setItem('vidyamitra-theme-mode', newMode);
    }
  };

  // Auto-detect system preference
  const setSystemMode = () => {
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const systemMode = systemPrefersDark ? 'dark' : 'light';
    setThemeMode(systemMode);
  };

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e) => {
      // Only auto-switch if user hasn't manually set a preference
      const savedMode = localStorage.getItem('vidyamitra-theme-mode');
      if (!savedMode) {
        setMode(e.matches ? 'dark' : 'light');
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  // Context value
  const contextValue = {
    mode,
    theme,
    toggleMode,
    setThemeMode,
    setSystemMode,
    isDark: mode === 'dark',
    isLight: mode === 'light',
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </ThemeContext.Provider>
  );
};

// Enhanced Theme Provider with additional features
export const EnhancedThemeProvider = ({ children }) => {
  const [mode, setMode] = useState(() => {
    const savedMode = localStorage.getItem('vidyamitra-theme-mode');
    return savedMode || 'light';
  });

  const [customizations, setCustomizations] = useState(() => {
    const savedCustomizations = localStorage.getItem('vidyamitra-theme-customizations');
    return savedCustomizations ? JSON.parse(savedCustomizations) : {};
  });

  // Create theme with customizations
  const theme = createTheme(createModernEducationalTheme(mode), customizations);

  const toggleMode = () => {
    const newMode = mode === 'light' ? 'dark' : 'light';
    setMode(newMode);
    localStorage.setItem('vidyamitra-theme-mode', newMode);
  };

  const updateCustomizations = (newCustomizations) => {
    setCustomizations(prev => {
      const updated = { ...prev, ...newCustomizations };
      localStorage.setItem('vidyamitra-theme-customizations', JSON.stringify(updated));
      return updated;
    });
  };

  const resetCustomizations = () => {
    setCustomizations({});
    localStorage.removeItem('vidyamitra-theme-customizations');
  };

  // Board-specific theme switching
  const setBoardTheme = (board) => {
    const boardColors = {
      CBSE: { primary: { main: '#2E5BA8' } },
      ICSE: { primary: { main: '#FF9933' } },
      STATE: { primary: { main: '#00C853' } },
      IB: { primary: { main: '#9C27B0' } },
    };

    if (boardColors[board]) {
      updateCustomizations({
        palette: boardColors[board],
      });
    }
  };

  // Cultural theme variants
  const setCulturalTheme = (variant) => {
    const culturalThemes = {
      traditional: {
        palette: {
          primary: { main: '#8B4513' }, // Henna brown
          secondary: { main: '#FF69B4' }, // Lotus pink
        },
      },
      modern: {
        palette: {
          primary: { main: '#2E5BA8' },
          secondary: { main: '#FF9933' },
        },
      },
      festive: {
        palette: {
          primary: { main: '#FF6B35' }, // Marigold
          secondary: { main: '#F7931E' }, // Saffron
        },
      },
    };

    if (culturalThemes[variant]) {
      updateCustomizations(culturalThemes[variant]);
    }
  };

  // Accessibility enhancements
  const setAccessibilityMode = (enabled) => {
    if (enabled) {
      updateCustomizations({
        palette: {
          mode: mode,
          contrastThreshold: 4.5, // WCAG AA compliance
        },
        typography: {
          fontSize: 16, // Larger base font size
        },
        components: {
          MuiButton: {
            styleOverrides: {
              root: {
                minHeight: 44, // Larger touch targets
              },
            },
          },
        },
      });
    } else {
      resetCustomizations();
    }
  };

  const contextValue = {
    mode,
    theme,
    customizations,
    toggleMode,
    setThemeMode: setMode,
    updateCustomizations,
    resetCustomizations,
    setBoardTheme,
    setCulturalTheme,
    setAccessibilityMode,
    isDark: mode === 'dark',
    isLight: mode === 'light',
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </ThemeContext.Provider>
  );
};

// Theme customization hook
export const useThemeCustomization = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useThemeCustomization must be used within a ThemeContextProvider');
  }
  
  return {
    setBoardTheme: context.setBoardTheme,
    setCulturalTheme: context.setCulturalTheme,
    setAccessibilityMode: context.setAccessibilityMode,
    updateCustomizations: context.updateCustomizations,
    resetCustomizations: context.resetCustomizations,
  };
};

// Performance monitoring hook
export const useThemePerformance = () => {
  const [renderTime, setRenderTime] = useState(0);
  
  useEffect(() => {
    const startTime = performance.now();
    
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const paintEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (paintEntry) {
        setRenderTime(paintEntry.startTime - startTime);
      }
    });
    
    observer.observe({ entryTypes: ['paint'] });
    
    return () => observer.disconnect();
  }, []);
  
  return { renderTime };
};

export default ThemeContextProvider;
