/**
 * VidyaMitra Platform - TeacherDashboard Component Tests
 * 
 * Comprehensive unit tests for teacher dashboard with Indian educational context
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import TeacherDashboard from '../TeacherDashboard';

// Mock Chart.js
jest.mock('react-chartjs-2', () => ({
  Line: () => <div data-testid="line-chart">Attendance Trends</div>,
  Doughnut: () => <div data-testid="doughnut-chart">Grade Distribution</div>,
}));

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
  },
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key,
  }),
}));

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

describe('TeacherDashboard Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders teacher dashboard with welcome message', () => {
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    expect(screen.getByText('Teacher Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Welcome back, Mrs. Priya Sharma! Manage your classes and track student progress.')).toBeInTheDocument();
  });

  test('displays quick stats cards', () => {
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    expect(screen.getByText('105')).toBeInTheDocument(); // Total Students
    expect(screen.getByText('Total Students')).toBeInTheDocument();
    expect(screen.getByText('12')).toBeInTheDocument(); // Pending Grades
    expect(screen.getByText('Pending Grades')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument(); // Upcoming Tests
    expect(screen.getByText('Upcoming Tests')).toBeInTheDocument();
    expect(screen.getByText('92%')).toBeInTheDocument(); // Avg Attendance
    expect(screen.getByText('Avg Attendance')).toBeInTheDocument();
  });

  test('displays quick action buttons', () => {
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    expect(screen.getByText('Enter Grades')).toBeInTheDocument();
    expect(screen.getByText('Mark Attendance')).toBeInTheDocument();
    expect(screen.getByText('SWOT Analysis')).toBeInTheDocument();
    expect(screen.getByText('Generate Reports')).toBeInTheDocument();
    expect(screen.getByText('Add Student')).toBeInTheDocument();
  });

  test('handles navigation to grade entry', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    const gradeButton = screen.getByText('Enter Grades');
    await user.click(gradeButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard/grades');
  });

  test('handles navigation to attendance management', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    const attendanceButton = screen.getByText('Mark Attendance');
    await user.click(attendanceButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard/attendance');
  });

  test('handles navigation to SWOT wizard', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    const swotButton = screen.getByText('SWOT Analysis');
    await user.click(swotButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard/swot/wizard');
  });

  test('displays charts for analytics', () => {
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    expect(screen.getByText('Weekly Attendance Trends')).toBeInTheDocument();
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    expect(screen.getByText('Grade Distribution')).toBeInTheDocument();
    expect(screen.getByTestId('doughnut-chart')).toBeInTheDocument();
  });

  test('displays student performance table with Indian names', () => {
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    expect(screen.getByText('Recent Student Performance')).toBeInTheDocument();
    expect(screen.getByText('Sanju Kumar Reddy')).toBeInTheDocument();
    expect(screen.getByText('Niraimathi Selvam')).toBeInTheDocument();
    expect(screen.getByText('Mahesh Reddy')).toBeInTheDocument();
    expect(screen.getByText('Ravi Teja Sharma')).toBeInTheDocument();
    expect(screen.getByText('Ankitha Patel')).toBeInTheDocument();
    expect(screen.getByText('Sirisha Nair')).toBeInTheDocument();
    expect(screen.getByText('Priya Agarwal')).toBeInTheDocument();
  });

  test('displays Indian grading system in performance table', () => {
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    // Should show Indian grades
    expect(screen.getByText('A1')).toBeInTheDocument();
    expect(screen.getByText('A2')).toBeInTheDocument();
    expect(screen.getByText('B1')).toBeInTheDocument();
    expect(screen.getByText('B2')).toBeInTheDocument();
  });

  test('handles student profile navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    // Find and click the first view button (visibility icon)
    const viewButtons = screen.getAllByLabelText('View Profile');
    await user.click(viewButtons[0]);
    
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard/students/1');
  });

  test('handles SWOT analysis navigation for students', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    // Find and click the first SWOT button (assessment icon)
    const swotButtons = screen.getAllByLabelText('SWOT Analysis');
    await user.click(swotButtons[0]);
    
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard/students/1/swot');
  });

  test('displays class and subject information', () => {
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    expect(screen.getByText('10-A')).toBeInTheDocument();
    expect(screen.getByText('10-B')).toBeInTheDocument();
    expect(screen.getByText('9-A')).toBeInTheDocument();
    expect(screen.getByText('Mathematics')).toBeInTheDocument();
    expect(screen.getByText('Physics')).toBeInTheDocument();
  });

  test('shows attendance percentages', () => {
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    expect(screen.getByText('95%')).toBeInTheDocument();
    expect(screen.getByText('92%')).toBeInTheDocument();
    expect(screen.getByText('88%')).toBeInTheDocument();
    expect(screen.getByText('94%')).toBeInTheDocument();
    expect(screen.getByText('90%')).toBeInTheDocument();
    expect(screen.getByText('96%')).toBeInTheDocument();
    expect(screen.getByText('85%')).toBeInTheDocument();
  });

  test('displays status chips with appropriate colors', () => {
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    expect(screen.getByText('Excellent')).toBeInTheDocument();
    expect(screen.getByText('Good')).toBeInTheDocument();
    expect(screen.getByText('Average')).toBeInTheDocument();
  });

  test('handles floating action button for student registration', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    const fab = screen.getByLabelText('Add Student');
    await user.click(fab);
    
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard/students/register');
  });

  test('supports accessibility features', () => {
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    // Check for proper heading structure
    expect(screen.getByRole('heading', { name: /teacher dashboard/i })).toBeInTheDocument();

    // Check for proper table structure
    expect(screen.getByRole('table')).toBeInTheDocument();
    expect(screen.getByRole('columnheader', { name: /student name/i })).toBeInTheDocument();
    expect(screen.getByRole('columnheader', { name: /class/i })).toBeInTheDocument();
    expect(screen.getByRole('columnheader', { name: /subject/i })).toBeInTheDocument();

    // Check for proper button labels
    expect(screen.getByRole('button', { name: /enter grades/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /mark attendance/i })).toBeInTheDocument();
  });

  test('displays responsive design elements', () => {
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    // Check for grid containers
    const dashboardContainer = screen.getByTestId('teacher-dashboard-container');
    expect(dashboardContainer).toBeInTheDocument();
  });

  test('handles view all students navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    const viewAllButton = screen.getByText('View All Students');
    await user.click(viewAllButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard/students');
  });
});

// Integration tests
describe('TeacherDashboard Integration', () => {
  test('complete dashboard interaction workflow', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    // Test quick actions workflow
    await user.click(screen.getByText('Enter Grades'));
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard/grades');

    await user.click(screen.getByText('Mark Attendance'));
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard/attendance');

    await user.click(screen.getByText('SWOT Analysis'));
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard/swot/wizard');

    // Test student interactions
    const viewButtons = screen.getAllByLabelText('View Profile');
    await user.click(viewButtons[0]);
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard/students/1');
  });

  test('dashboard data consistency', () => {
    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    // Verify that total students count matches table entries
    expect(screen.getByText('105')).toBeInTheDocument(); // Total students
    
    // Verify that student names are consistent
    const studentNames = [
      'Sanju Kumar Reddy',
      'Niraimathi Selvam', 
      'Mahesh Reddy',
      'Ravi Teja Sharma',
      'Ankitha Patel',
      'Sirisha Nair',
      'Priya Agarwal'
    ];

    studentNames.forEach(name => {
      expect(screen.getByText(name)).toBeInTheDocument();
    });
  });

  test('responsive behavior on different screen sizes', () => {
    // Test mobile view
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });

    render(
      <TestWrapper>
        <TeacherDashboard />
      </TestWrapper>
    );

    // Dashboard should still render all essential elements
    expect(screen.getByText('Teacher Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Total Students')).toBeInTheDocument();
    expect(screen.getByText('Enter Grades')).toBeInTheDocument();
  });
});
