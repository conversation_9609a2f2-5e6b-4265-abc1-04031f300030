/**
 * VidyaMitra Platform - Student Routes
 * 
 * This file defines all API routes related to student management
 * including CRUD operations, SWOT analysis, and performance tracking.
 */

const express = require('express');
const router = express.Router();
const {
  Student, Guardian, AcademicPerformance, Subject, Attendance,
  QuarterlyAttendance, BehavioralIncident, BehavioralSummary,
  ExtracurricularActivity, SWOTAnalysis, AuditLog
} = require('../models/index');

/**
 * @route   GET /api/v1/students
 * @desc    Get all students with filtering and pagination
 * @access  Private (Teacher, Admin)
 */
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      grade_level,
      board,
      stream,
      status = 'Active',
      search,
      sort_by = 'name',
      sort_order = 'asc'
    } = req.query;

    // Build filter object
    const filter = { status };
    
    if (grade_level) filter.grade_level = grade_level;
    if (board) filter.board = board;
    if (stream && stream !== 'N/A') filter.stream = stream;
    
    // Add search functionality
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { student_id: { $regex: search, $options: 'i' } },
        { admission_number: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort object
    const sortOrder = sort_order === 'desc' ? -1 : 1;
    const sort = { [sort_by]: sortOrder };

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const students = await Student.find(filter)
      .populate('school_id', 'name board_affiliation')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const total = await Student.countDocuments(filter);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.json({
      success: true,
      data: students,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_records: total,
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage,
        records_per_page: parseInt(limit)
      }
    });

  } catch (error) {
    console.error('Error fetching students:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch students'
    });
  }
});

/**
 * @route   GET /api/v1/students/:id
 * @desc    Get student by ID with complete profile
 * @access  Private (Teacher, Admin, Parent)
 */
router.get('/:id', async (req, res) => {
  try {
    const student = await Student.findById(req.params.id)
      .populate('school_id', 'name board_affiliation address')
      .lean();

    if (!student) {
      return res.status(404).json({
        success: false,
        error: 'Student not found'
      });
    }

    // Get guardians
    const guardians = await Guardian.find({ student_id: req.params.id }).lean();

    // Get latest academic performance
    const latestPerformance = await AcademicPerformance.findOne({
      student_id: req.params.id
    })
    .sort({ academic_year: -1, quarter: -1 })
    .populate({
      path: 'subjects',
      model: 'Subject'
    })
    .lean();

    // Get latest attendance summary
    const latestAttendance = await QuarterlyAttendance.findOne({
      student_id: req.params.id
    })
    .sort({ academic_year: -1, quarter: -1 })
    .lean();

    // Get latest behavioral summary
    const latestBehavior = await BehavioralSummary.findOne({
      student_id: req.params.id
    })
    .sort({ academic_year: -1, quarter: -1 })
    .lean();

    // Get extracurricular activities
    const activities = await ExtracurricularActivity.find({
      student_id: req.params.id,
      academic_year: student.academic_year
    }).lean();

    // Get latest SWOT analysis
    const latestSWOT = await SWOTAnalysis.findOne({
      student_id: req.params.id
    })
    .sort({ academic_year: -1, quarter: -1 })
    .lean();

    // Compile complete profile
    const completeProfile = {
      ...student,
      guardians,
      academic_performance: latestPerformance,
      attendance_summary: latestAttendance,
      behavioral_summary: latestBehavior,
      extracurricular_activities: activities,
      swot_analysis: latestSWOT
    };

    res.json({
      success: true,
      data: completeProfile
    });

  } catch (error) {
    console.error('Error fetching student profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch student profile'
    });
  }
});

/**
 * @route   POST /api/v1/students
 * @desc    Create new student
 * @access  Private (Admin, Teacher)
 */
router.post('/', async (req, res) => {
  try {
    const studentData = req.body;

    // Generate student ID if not provided
    if (!studentData.student_id) {
      const lastStudent = await Student.findOne()
        .sort({ student_id: -1 })
        .select('student_id')
        .lean();
      
      let nextNumber = 1;
      if (lastStudent && lastStudent.student_id) {
        const lastNumber = parseInt(lastStudent.student_id.substring(3));
        nextNumber = lastNumber + 1;
      }
      
      studentData.student_id = `STU${nextNumber.toString().padStart(5, '0')}`;
    }

    // Create student
    const student = new Student(studentData);
    await student.save();

    // Log the action
    await AuditLog.create({
      log_id: `LOG${Date.now()}${Math.random().toString(36).substr(2, 4)}`,
      user_id: req.user.id,
      action: 'CREATE',
      resource_type: 'Student',
      resource_id: student._id.toString(),
      details: { student_id: student.student_id, name: student.name }
    });

    res.status(201).json({
      success: true,
      data: student,
      message: 'Student created successfully'
    });

  } catch (error) {
    console.error('Error creating student:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        error: 'Student ID or admission number already exists'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to create student'
    });
  }
});

/**
 * @route   PUT /api/v1/students/:id
 * @desc    Update student information
 * @access  Private (Admin, Teacher)
 */
router.put('/:id', async (req, res) => {
  try {
    const student = await Student.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );

    if (!student) {
      return res.status(404).json({
        success: false,
        error: 'Student not found'
      });
    }

    // Log the action
    await AuditLog.create({
      log_id: `LOG${Date.now()}${Math.random().toString(36).substr(2, 4)}`,
      user_id: req.user.id,
      action: 'UPDATE',
      resource_type: 'Student',
      resource_id: student._id.toString(),
      details: { updated_fields: Object.keys(req.body) }
    });

    res.json({
      success: true,
      data: student,
      message: 'Student updated successfully'
    });

  } catch (error) {
    console.error('Error updating student:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update student'
    });
  }
});

/**
 * @route   DELETE /api/v1/students/:id
 * @desc    Soft delete student (set status to inactive)
 * @access  Private (Admin only)
 */
router.delete('/:id', async (req, res) => {
  try {
    const student = await Student.findByIdAndUpdate(
      req.params.id,
      { status: 'Inactive' },
      { new: true }
    );

    if (!student) {
      return res.status(404).json({
        success: false,
        error: 'Student not found'
      });
    }

    // Log the action
    await AuditLog.create({
      log_id: `LOG${Date.now()}${Math.random().toString(36).substr(2, 4)}`,
      user_id: req.user.id,
      action: 'DELETE',
      resource_type: 'Student',
      resource_id: student._id.toString(),
      details: { action: 'soft_delete' }
    });

    res.json({
      success: true,
      message: 'Student deactivated successfully'
    });

  } catch (error) {
    console.error('Error deactivating student:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to deactivate student'
    });
  }
});

module.exports = router;
