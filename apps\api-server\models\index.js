/**
 * VidyaMitra Platform - Database Models Index
 * 
 * This file exports all database models for the VidyaMitra Platform
 * and provides a centralized access point for all schemas.
 */

const baseModels = require('./schema');
const academicModels = require('./academicSchemas');
const behavioralModels = require('./behavioralSchemas');

/**
 * User Student Access Schema
 * Controls which users can access which student records
 */
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const UserStudentAccessSchema = new Schema({
  access_id: {
    type: String,
    required: true,
    unique: true,
    match: /^ACC\d{8}$/
  },
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  student_id: {
    type: Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  access_level: {
    type: String,
    required: true,
    enum: ['read', 'write', 'admin']
  },
  granted_by: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  granted_date: {
    type: Date,
    default: Date.now
  },
  expires_date: Date,
  is_active: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Compound index for uniqueness
UserStudentAccessSchema.index({ user_id: 1, student_id: 1 }, { unique: true });

const UserStudentAccess = mongoose.model('UserStudentAccess', UserStudentAccessSchema);

/**
 * System Configuration Schema
 * Stores system-wide configuration settings
 */
const SystemConfigSchema = new Schema({
  config_key: {
    type: String,
    required: true,
    unique: true
  },
  config_value: {
    type: Schema.Types.Mixed,
    required: true
  },
  description: String,
  category: {
    type: String,
    enum: ['Academic', 'Behavioral', 'System', 'UI', 'Integration', 'Security'],
    default: 'System'
  },
  is_editable: {
    type: Boolean,
    default: true
  },
  last_modified_by: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

const SystemConfig = mongoose.model('SystemConfig', SystemConfigSchema);

/**
 * Audit Log Schema
 * Tracks all system activities for security and compliance
 */
const AuditLogSchema = new Schema({
  log_id: {
    type: String,
    required: true,
    unique: true,
    match: /^LOG\d{10}$/
  },
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  action: {
    type: String,
    required: true,
    enum: [
      'CREATE', 'READ', 'UPDATE', 'DELETE',
      'LOGIN', 'LOGOUT', 'LOGIN_FAILED',
      'EXPORT', 'IMPORT', 'GENERATE_REPORT',
      'SWOT_GENERATED', 'DATA_SYNC'
    ]
  },
  resource_type: {
    type: String,
    required: true,
    enum: [
      'Student', 'Guardian', 'User', 'AcademicPerformance',
      'Attendance', 'BehavioralIncident', 'ExtracurricularActivity',
      'SWOTAnalysis', 'School', 'Class', 'SystemConfig'
    ]
  },
  resource_id: String,
  details: {
    type: Schema.Types.Mixed
  },
  ip_address: String,
  user_agent: String,
  session_id: String,
  status: {
    type: String,
    enum: ['SUCCESS', 'FAILURE', 'PARTIAL'],
    default: 'SUCCESS'
  },
  error_message: String
}, {
  timestamps: true
});

AuditLogSchema.index({ user_id: 1, createdAt: -1 });
AuditLogSchema.index({ action: 1, createdAt: -1 });
AuditLogSchema.index({ resource_type: 1, resource_id: 1 });

const AuditLog = mongoose.model('AuditLog', AuditLogSchema);

/**
 * Notification Schema
 * Stores system notifications for users
 */
const NotificationSchema = new Schema({
  notification_id: {
    type: String,
    required: true,
    unique: true,
    match: /^NOT\d{8}$/
  },
  recipient_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  sender_id: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  type: {
    type: String,
    required: true,
    enum: [
      'SWOT_GENERATED', 'PERFORMANCE_ALERT', 'ATTENDANCE_ALERT',
      'BEHAVIORAL_INCIDENT', 'PARENT_CONFERENCE', 'SYSTEM_UPDATE',
      'ASSIGNMENT_DUE', 'EXAM_REMINDER', 'ACHIEVEMENT'
    ]
  },
  title: {
    type: String,
    required: true,
    maxlength: 200
  },
  message: {
    type: String,
    required: true
  },
  priority: {
    type: String,
    enum: ['Low', 'Medium', 'High', 'Urgent'],
    default: 'Medium'
  },
  is_read: {
    type: Boolean,
    default: false
  },
  read_at: Date,
  related_entity: {
    type: String,
    enum: ['Student', 'Class', 'School', 'Assignment', 'Exam']
  },
  related_entity_id: String,
  action_required: {
    type: Boolean,
    default: false
  },
  action_url: String,
  expires_at: Date
}, {
  timestamps: true
});

NotificationSchema.index({ recipient_id: 1, is_read: 1, createdAt: -1 });

const Notification = mongoose.model('Notification', NotificationSchema);

// Combine all models
const allModels = {
  // Base models
  ...baseModels,
  
  // Academic models
  ...academicModels,
  
  // Behavioral models
  ...behavioralModels,
  
  // Additional models
  UserStudentAccess,
  SystemConfig,
  AuditLog,
  Notification
};

// Export individual model categories for organized access
module.exports = {
  // All models combined
  ...allModels,
  
  // Categorized exports
  baseModels,
  academicModels,
  behavioralModels,
  
  // Utility functions
  getAllModels: () => allModels,
  getModelNames: () => Object.keys(allModels),
  
  // Database connection helper
  connectDB: async (connectionString) => {
    try {
      await mongoose.connect(connectionString, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });
      console.log('MongoDB connected successfully');
      return true;
    } catch (error) {
      console.error('MongoDB connection error:', error);
      return false;
    }
  },
  
  // Database initialization helper
  initializeDB: async () => {
    try {
      // Create indexes for all models
      const modelNames = Object.keys(allModels);
      for (const modelName of modelNames) {
        await allModels[modelName].createIndexes();
        console.log(`Indexes created for ${modelName}`);
      }
      console.log('Database initialization completed');
      return true;
    } catch (error) {
      console.error('Database initialization error:', error);
      return false;
    }
  }
};
