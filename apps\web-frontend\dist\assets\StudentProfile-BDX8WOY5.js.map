{"version": 3, "file": "StudentProfile-BDX8WOY5.js", "sources": ["../../src/components/Students/StudentProfile.jsx"], "sourcesContent": ["/**\n * VidyaMitra Platform - Student Profile Component\n * \n * Comprehensive student profile display with academic performance,\n * SWOT analysis, and Indian educational context\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Avatar,\n  Grid,\n  Chip,\n  Button,\n  Tab,\n  Tabs,\n  LinearProgress,\n  IconButton,\n  Divider,\n  Stack,\n  Alert,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Person,\n  School,\n  Assessment,\n  TrendingUp,\n  Edit,\n  Print,\n  Share,\n  Phone,\n  Email,\n  LocationOn,\n  CalendarToday,\n  Star,\n  EmojiEvents,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Line, Radar, Bar } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  RadialLinearScale,\n  BarElement,\n  Title,\n  Tooltip as ChartTooltip,\n  Legend,\n  Filler,\n} from 'chart.js';\n\n// Register Chart.js components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  RadialLinearScale,\n  BarElement,\n  Title,\n  ChartTooltip,\n  Legend,\n  Filler\n);\n\n// Sample student data (in real app, this would come from API)\nconst sampleStudentData = {\n  id: 1,\n  firstName: 'Sanju',\n  middleName: 'Kumar',\n  lastName: 'Reddy',\n  admissionNumber: 'VMS2024001',\n  grade: 10,\n  section: 'A',\n  rollNumber: 15,\n  board: 'CBSE',\n  dateOfBirth: '2008-05-15',\n  gender: 'Male',\n  bloodGroup: 'B+',\n  profilePhoto: null,\n  \n  // Contact Information\n  address: 'H.No 12-34, Jubilee Hills, Hyderabad',\n  city: 'Hyderabad',\n  state: 'Telangana',\n  pincode: '500033',\n  phone: '+91 9876543210',\n  email: '<EMAIL>',\n  \n  // Parent Information\n  fatherName: 'Rajesh Kumar Reddy',\n  fatherOccupation: 'Software Engineer',\n  fatherPhone: '+91 9876543211',\n  motherName: 'Priya Reddy',\n  motherOccupation: 'Teacher',\n  motherPhone: '+91 9876543212',\n  \n  // Academic Performance\n  currentGPA: 8.7,\n  attendance: 92,\n  subjects: [\n    { name: 'Mathematics', grade: 'A1', marks: 95, teacher: 'Mrs. Sharma' },\n    { name: 'Science', grade: 'A1', marks: 92, teacher: 'Mr. Patel' },\n    { name: 'English', grade: 'A2', marks: 88, teacher: 'Ms. Johnson' },\n    { name: 'Hindi', grade: 'A1', marks: 94, teacher: 'Mrs. Gupta' },\n    { name: 'Social Studies', grade: 'A2', marks: 86, teacher: 'Mr. Singh' },\n    { name: 'Telugu', grade: 'A1', marks: 96, teacher: 'Mrs. Rao' },\n  ],\n  \n  // Performance Trends\n  performanceTrends: [\n    { month: 'Apr', gpa: 8.2 },\n    { month: 'May', gpa: 8.4 },\n    { month: 'Jun', gpa: 8.6 },\n    { month: 'Jul', gpa: 8.5 },\n    { month: 'Aug', gpa: 8.7 },\n    { month: 'Sep', gpa: 8.8 },\n  ],\n  \n  // SWOT Analysis\n  swotAnalysis: {\n    strengths: ['Strong in Mathematics', 'Good leadership skills', 'Excellent attendance'],\n    weaknesses: ['Needs improvement in English writing', 'Shy in group discussions'],\n    opportunities: ['Science Olympiad participation', 'Student council elections'],\n    threats: ['Increased competition', 'Time management challenges'],\n  },\n  \n  // Achievements\n  achievements: [\n    { title: 'Mathematics Olympiad - District Level', date: '2024-03-15', type: 'Academic' },\n    { title: 'Best Student of the Month', date: '2024-02-28', type: 'Behavioral' },\n    { title: 'Science Fair - First Prize', date: '2024-01-20', type: 'Academic' },\n  ],\n  \n  // Behavioral Assessment\n  behavioralScores: {\n    discipline: 9,\n    teamwork: 8,\n    leadership: 9,\n    creativity: 7,\n    communication: 6,\n    responsibility: 9,\n  },\n};\n\nconst StudentProfile = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const { studentId } = useParams();\n  const [activeTab, setActiveTab] = useState(0);\n  const [student, setStudent] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Simulate API call to fetch student data\n    const fetchStudentData = async () => {\n      setLoading(true);\n      try {\n        // In real app, fetch data based on studentId\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        setStudent(sampleStudentData);\n      } catch (error) {\n        console.error('Error fetching student data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStudentData();\n  }, [studentId]);\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n\n  const handleEditProfile = () => {\n    navigate(`/dashboard/students/${studentId}/edit`);\n  };\n\n  const handleSWOTAnalysis = () => {\n    navigate(`/dashboard/students/${studentId}/swot`);\n  };\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>\n        <LinearProgress sx={{ width: 300 }} />\n      </Box>\n    );\n  }\n\n  if (!student) {\n    return (\n      <Alert severity=\"error\">\n        Student not found. Please check the student ID and try again.\n      </Alert>\n    );\n  }\n\n  // Chart configurations\n  const performanceChartData = {\n    labels: student.performanceTrends.map(trend => trend.month),\n    datasets: [\n      {\n        label: 'GPA Trend',\n        data: student.performanceTrends.map(trend => trend.gpa),\n        borderColor: theme.palette.primary.main,\n        backgroundColor: alpha(theme.palette.primary.main, 0.1),\n        fill: true,\n        tension: 0.4,\n      },\n    ],\n  };\n\n  const behavioralRadarData = {\n    labels: Object.keys(student.behavioralScores).map(key => \n      key.charAt(0).toUpperCase() + key.slice(1)\n    ),\n    datasets: [\n      {\n        label: 'Behavioral Assessment',\n        data: Object.values(student.behavioralScores),\n        borderColor: theme.palette.secondary.main,\n        backgroundColor: alpha(theme.palette.secondary.main, 0.2),\n        pointBackgroundColor: theme.palette.secondary.main,\n        pointBorderColor: '#fff',\n        pointHoverBackgroundColor: '#fff',\n        pointHoverBorderColor: theme.palette.secondary.main,\n      },\n    ],\n  };\n\n  const subjectPerformanceData = {\n    labels: student.subjects.map(subject => subject.name),\n    datasets: [\n      {\n        label: 'Marks',\n        data: student.subjects.map(subject => subject.marks),\n        backgroundColor: student.subjects.map((_, index) => \n          `hsl(${(index * 60) % 360}, 70%, 60%)`\n        ),\n        borderColor: student.subjects.map((_, index) => \n          `hsl(${(index * 60) % 360}, 70%, 50%)`\n        ),\n        borderWidth: 2,\n      },\n    ],\n  };\n\n  return (\n    <Box sx={{ maxWidth: 1400, mx: 'auto', p: 3 }}>\n      {/* Header Section */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Card sx={{ mb: 3, overflow: 'visible' }}>\n          <CardContent sx={{ p: 4 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>\n                <Avatar\n                  src={student.profilePhoto}\n                  sx={{\n                    width: 120,\n                    height: 120,\n                    border: `4px solid ${theme.palette.primary.main}`,\n                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n                  }}\n                >\n                  <Person sx={{ fontSize: 60 }} />\n                </Avatar>\n                \n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600, mb: 1 }}>\n                    {student.firstName} {student.middleName} {student.lastName}\n                  </Typography>\n                  <Typography variant=\"h6\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                    Class {student.grade} - Section {student.section}\n                  </Typography>\n                  <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    Admission No: {student.admissionNumber} | Roll No: {student.rollNumber}\n                  </Typography>\n                  \n                  <Stack direction=\"row\" spacing={1} sx={{ mb: 2 }}>\n                    <Chip\n                      label={student.board}\n                      color=\"primary\"\n                      variant=\"filled\"\n                      sx={{ fontWeight: 500 }}\n                    />\n                    <Chip\n                      label={`GPA: ${student.currentGPA}`}\n                      color=\"success\"\n                      variant=\"outlined\"\n                      icon={<Star />}\n                    />\n                    <Chip\n                      label={`${student.attendance}% Attendance`}\n                      color={student.attendance >= 90 ? 'success' : student.attendance >= 75 ? 'warning' : 'error'}\n                      variant=\"outlined\"\n                    />\n                  </Stack>\n                </Box>\n              </Box>\n              \n              <Stack direction=\"row\" spacing={1}>\n                <IconButton onClick={handleEditProfile} color=\"primary\">\n                  <Edit />\n                </IconButton>\n                <IconButton color=\"primary\">\n                  <Print />\n                </IconButton>\n                <IconButton color=\"primary\">\n                  <Share />\n                </IconButton>\n                <Button\n                  variant=\"contained\"\n                  startIcon={<Assessment />}\n                  onClick={handleSWOTAnalysis}\n                  sx={{\n                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n                  }}\n                >\n                  SWOT Analysis\n                </Button>\n              </Stack>\n            </Box>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* Tabs Navigation */}\n      <Card sx={{ mb: 3 }}>\n        <Tabs\n          value={activeTab}\n          onChange={handleTabChange}\n          variant=\"scrollable\"\n          scrollButtons=\"auto\"\n          sx={{\n            '& .MuiTab-root': {\n              minHeight: 64,\n              fontWeight: 500,\n            },\n          }}\n        >\n          <Tab icon={<Person />} label=\"Personal Info\" />\n          <Tab icon={<School />} label=\"Academic Performance\" />\n          <Tab icon={<Assessment />} label=\"SWOT Analysis\" />\n          <Tab icon={<EmojiEvents />} label=\"Achievements\" />\n        </Tabs>\n      </Card>\n\n      {/* Tab Content */}\n      <motion.div\n        key={activeTab}\n        initial={{ opacity: 0, x: 20 }}\n        animate={{ opacity: 1, x: 0 }}\n        transition={{ duration: 0.3 }}\n      >\n        {activeTab === 0 && <PersonalInfoTab student={student} />}\n        {activeTab === 1 && (\n          <AcademicPerformanceTab \n            student={student} \n            performanceChartData={performanceChartData}\n            subjectPerformanceData={subjectPerformanceData}\n            behavioralRadarData={behavioralRadarData}\n          />\n        )}\n        {activeTab === 2 && <SWOTAnalysisTab student={student} />}\n        {activeTab === 3 && <AchievementsTab student={student} />}\n      </motion.div>\n    </Box>\n  );\n};\n\n// Tab Components\nconst PersonalInfoTab = ({ student }) => {\n  const theme = useTheme();\n\n  return (\n    <Grid container spacing={3}>\n      <Grid item xs={12} md={6}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              Basic Information\n            </Typography>\n            <Stack spacing={2}>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <CalendarToday color=\"action\" />\n                <Typography variant=\"body2\" color=\"text.secondary\">Date of Birth:</Typography>\n                <Typography variant=\"body2\">{student.dateOfBirth}</Typography>\n              </Box>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <Person color=\"action\" />\n                <Typography variant=\"body2\" color=\"text.secondary\">Gender:</Typography>\n                <Typography variant=\"body2\">{student.gender}</Typography>\n              </Box>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <Typography variant=\"body2\" color=\"text.secondary\">Blood Group:</Typography>\n                <Typography variant=\"body2\">{student.bloodGroup}</Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={6}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              Contact Information\n            </Typography>\n            <Stack spacing={2}>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <LocationOn color=\"action\" />\n                <Typography variant=\"body2\" color=\"text.secondary\">Address:</Typography>\n                <Typography variant=\"body2\">{student.address}</Typography>\n              </Box>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <Phone color=\"action\" />\n                <Typography variant=\"body2\" color=\"text.secondary\">Phone:</Typography>\n                <Typography variant=\"body2\">{student.phone}</Typography>\n              </Box>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <Email color=\"action\" />\n                <Typography variant=\"body2\" color=\"text.secondary\">Email:</Typography>\n                <Typography variant=\"body2\">{student.email}</Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              Parent/Guardian Information\n            </Typography>\n            <Grid container spacing={3}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 500 }}>Father's Details</Typography>\n                <Stack spacing={1}>\n                  <Typography variant=\"body2\">Name: {student.fatherName}</Typography>\n                  <Typography variant=\"body2\">Occupation: {student.fatherOccupation}</Typography>\n                  <Typography variant=\"body2\">Phone: {student.fatherPhone}</Typography>\n                </Stack>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 500 }}>Mother's Details</Typography>\n                <Stack spacing={1}>\n                  <Typography variant=\"body2\">Name: {student.motherName}</Typography>\n                  <Typography variant=\"body2\">Occupation: {student.motherOccupation}</Typography>\n                  <Typography variant=\"body2\">Phone: {student.motherPhone}</Typography>\n                </Stack>\n              </Grid>\n            </Grid>\n          </CardContent>\n        </Card>\n      </Grid>\n    </Grid>\n  );\n};\n\nconst AcademicPerformanceTab = ({ student, performanceChartData, subjectPerformanceData, behavioralRadarData }) => {\n  return (\n    <Grid container spacing={3}>\n      {/* Performance Overview */}\n      <Grid item xs={12} md={4}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              Performance Overview\n            </Typography>\n            <Stack spacing={2}>\n              <Box>\n                <Typography variant=\"body2\" color=\"text.secondary\">Current GPA</Typography>\n                <Typography variant=\"h4\" color=\"primary.main\" sx={{ fontWeight: 600 }}>\n                  {student.currentGPA}\n                </Typography>\n              </Box>\n              <Box>\n                <Typography variant=\"body2\" color=\"text.secondary\">Attendance</Typography>\n                <Typography variant=\"h4\" color=\"success.main\" sx={{ fontWeight: 600 }}>\n                  {student.attendance}%\n                </Typography>\n              </Box>\n              <Box>\n                <Typography variant=\"body2\" color=\"text.secondary\">Class Rank</Typography>\n                <Typography variant=\"h4\" color=\"secondary.main\" sx={{ fontWeight: 600 }}>\n                  3rd\n                </Typography>\n              </Box>\n            </Stack>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      {/* GPA Trend Chart */}\n      <Grid item xs={12} md={8}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              GPA Trend\n            </Typography>\n            <Box sx={{ height: 300 }}>\n              <Line\n                data={performanceChartData}\n                options={{\n                  responsive: true,\n                  maintainAspectRatio: false,\n                  plugins: {\n                    legend: {\n                      display: false,\n                    },\n                  },\n                  scales: {\n                    y: {\n                      beginAtZero: false,\n                      min: 7,\n                      max: 10,\n                    },\n                  },\n                }}\n              />\n            </Box>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      {/* Subject Performance */}\n      <Grid item xs={12} md={8}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              Subject Performance\n            </Typography>\n            <Box sx={{ height: 300 }}>\n              <Bar\n                data={subjectPerformanceData}\n                options={{\n                  responsive: true,\n                  maintainAspectRatio: false,\n                  plugins: {\n                    legend: {\n                      display: false,\n                    },\n                  },\n                  scales: {\n                    y: {\n                      beginAtZero: true,\n                      max: 100,\n                    },\n                  },\n                }}\n              />\n            </Box>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      {/* Behavioral Assessment */}\n      <Grid item xs={12} md={4}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              Behavioral Assessment\n            </Typography>\n            <Box sx={{ height: 300 }}>\n              <Radar\n                data={behavioralRadarData}\n                options={{\n                  responsive: true,\n                  maintainAspectRatio: false,\n                  plugins: {\n                    legend: {\n                      display: false,\n                    },\n                  },\n                  scales: {\n                    r: {\n                      beginAtZero: true,\n                      max: 10,\n                    },\n                  },\n                }}\n              />\n            </Box>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      {/* Subject Details */}\n      <Grid item xs={12}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n              Subject Details\n            </Typography>\n            <Grid container spacing={2}>\n              {student.subjects.map((subject, index) => (\n                <Grid item xs={12} sm={6} md={4} key={index}>\n                  <Card variant=\"outlined\">\n                    <CardContent sx={{ p: 2 }}>\n                      <Typography variant=\"subtitle1\" sx={{ fontWeight: 600 }}>\n                        {subject.name}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                        Teacher: {subject.teacher}\n                      </Typography>\n                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                        <Chip\n                          label={subject.grade}\n                          color={subject.grade.startsWith('A') ? 'success' : 'warning'}\n                          size=\"small\"\n                        />\n                        <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n                          {subject.marks}%\n                        </Typography>\n                      </Box>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </CardContent>\n        </Card>\n      </Grid>\n    </Grid>\n  );\n};\n\nconst SWOTAnalysisTab = ({ student }) => {\n  const theme = useTheme();\n\n  const swotCategories = [\n    {\n      title: 'Strengths',\n      items: student.swotAnalysis.strengths,\n      color: theme.palette.success.main,\n      icon: '💪',\n    },\n    {\n      title: 'Weaknesses',\n      items: student.swotAnalysis.weaknesses,\n      color: theme.palette.error.main,\n      icon: '⚠️',\n    },\n    {\n      title: 'Opportunities',\n      items: student.swotAnalysis.opportunities,\n      color: theme.palette.info.main,\n      icon: '🚀',\n    },\n    {\n      title: 'Threats',\n      items: student.swotAnalysis.threats,\n      color: theme.palette.warning.main,\n      icon: '⚡',\n    },\n  ];\n\n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600 }}>\n          SWOT Analysis Overview\n        </Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<Assessment />}\n          sx={{\n            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n          }}\n        >\n          Update SWOT\n        </Button>\n      </Box>\n\n      <Grid container spacing={3}>\n        {swotCategories.map((category, index) => (\n          <Grid item xs={12} md={6} key={index}>\n            <Card\n              sx={{\n                height: '100%',\n                border: `2px solid ${alpha(category.color, 0.2)}`,\n                background: `linear-gradient(135deg, ${alpha(category.color, 0.05)} 0%, ${alpha(category.color, 0.02)} 100%)`,\n              }}\n            >\n              <CardContent>\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>\n                  <Typography variant=\"h2\" sx={{ fontSize: 24 }}>\n                    {category.icon}\n                  </Typography>\n                  <Typography\n                    variant=\"h6\"\n                    sx={{\n                      fontWeight: 600,\n                      color: category.color,\n                    }}\n                  >\n                    {category.title}\n                  </Typography>\n                </Box>\n\n                <Stack spacing={1}>\n                  {category.items.map((item, itemIndex) => (\n                    <Box\n                      key={itemIndex}\n                      sx={{\n                        p: 2,\n                        borderRadius: 1,\n                        background: alpha(category.color, 0.1),\n                        border: `1px solid ${alpha(category.color, 0.2)}`,\n                      }}\n                    >\n                      <Typography variant=\"body2\">\n                        {item}\n                      </Typography>\n                    </Box>\n                  ))}\n                </Stack>\n              </CardContent>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n\n      {/* SWOT Matrix Visualization */}\n      <Card sx={{ mt: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n            SWOT Matrix\n          </Typography>\n          <Grid container spacing={2}>\n            <Grid item xs={6}>\n              <Box\n                sx={{\n                  p: 2,\n                  border: `2px solid ${theme.palette.success.main}`,\n                  borderRadius: 1,\n                  background: alpha(theme.palette.success.main, 0.05),\n                  minHeight: 150,\n                }}\n              >\n                <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, color: 'success.main', mb: 1 }}>\n                  Strengths (Internal Positive)\n                </Typography>\n                {student.swotAnalysis.strengths.map((strength, index) => (\n                  <Typography key={index} variant=\"body2\" sx={{ mb: 0.5 }}>\n                    • {strength}\n                  </Typography>\n                ))}\n              </Box>\n            </Grid>\n            <Grid item xs={6}>\n              <Box\n                sx={{\n                  p: 2,\n                  border: `2px solid ${theme.palette.error.main}`,\n                  borderRadius: 1,\n                  background: alpha(theme.palette.error.main, 0.05),\n                  minHeight: 150,\n                }}\n              >\n                <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, color: 'error.main', mb: 1 }}>\n                  Weaknesses (Internal Negative)\n                </Typography>\n                {student.swotAnalysis.weaknesses.map((weakness, index) => (\n                  <Typography key={index} variant=\"body2\" sx={{ mb: 0.5 }}>\n                    • {weakness}\n                  </Typography>\n                ))}\n              </Box>\n            </Grid>\n            <Grid item xs={6}>\n              <Box\n                sx={{\n                  p: 2,\n                  border: `2px solid ${theme.palette.info.main}`,\n                  borderRadius: 1,\n                  background: alpha(theme.palette.info.main, 0.05),\n                  minHeight: 150,\n                }}\n              >\n                <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, color: 'info.main', mb: 1 }}>\n                  Opportunities (External Positive)\n                </Typography>\n                {student.swotAnalysis.opportunities.map((opportunity, index) => (\n                  <Typography key={index} variant=\"body2\" sx={{ mb: 0.5 }}>\n                    • {opportunity}\n                  </Typography>\n                ))}\n              </Box>\n            </Grid>\n            <Grid item xs={6}>\n              <Box\n                sx={{\n                  p: 2,\n                  border: `2px solid ${theme.palette.warning.main}`,\n                  borderRadius: 1,\n                  background: alpha(theme.palette.warning.main, 0.05),\n                  minHeight: 150,\n                }}\n              >\n                <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, color: 'warning.main', mb: 1 }}>\n                  Threats (External Negative)\n                </Typography>\n                {student.swotAnalysis.threats.map((threat, index) => (\n                  <Typography key={index} variant=\"body2\" sx={{ mb: 0.5 }}>\n                    • {threat}\n                  </Typography>\n                ))}\n              </Box>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nconst AchievementsTab = ({ student }) => {\n  const theme = useTheme();\n\n  const getAchievementIcon = (type) => {\n    switch (type) {\n      case 'Academic':\n        return <School sx={{ color: theme.palette.primary.main }} />;\n      case 'Behavioral':\n        return <EmojiEvents sx={{ color: theme.palette.secondary.main }} />;\n      default:\n        return <Star sx={{ color: theme.palette.warning.main }} />;\n    }\n  };\n\n  const getAchievementColor = (type) => {\n    switch (type) {\n      case 'Academic':\n        return theme.palette.primary.main;\n      case 'Behavioral':\n        return theme.palette.secondary.main;\n      default:\n        return theme.palette.warning.main;\n    }\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h5\" sx={{ fontWeight: 600, mb: 3 }}>\n        Achievements & Recognition\n      </Typography>\n\n      <Grid container spacing={3}>\n        {student.achievements.map((achievement, index) => (\n          <Grid item xs={12} md={6} key={index}>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: index * 0.1 }}\n            >\n              <Card\n                sx={{\n                  border: `2px solid ${alpha(getAchievementColor(achievement.type), 0.2)}`,\n                  background: `linear-gradient(135deg, ${alpha(getAchievementColor(achievement.type), 0.05)} 0%, ${alpha(getAchievementColor(achievement.type), 0.02)} 100%)`,\n                  transition: 'transform 0.2s ease',\n                  '&:hover': {\n                    transform: 'translateY(-4px)',\n                  },\n                }}\n              >\n                <CardContent>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>\n                    {getAchievementIcon(achievement.type)}\n                    <Box>\n                      <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n                        {achievement.title}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        {new Date(achievement.date).toLocaleDateString('en-IN', {\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric',\n                        })}\n                      </Typography>\n                    </Box>\n                  </Box>\n\n                  <Chip\n                    label={achievement.type}\n                    size=\"small\"\n                    sx={{\n                      backgroundColor: getAchievementColor(achievement.type),\n                      color: 'white',\n                      fontWeight: 500,\n                    }}\n                  />\n                </CardContent>\n              </Card>\n            </motion.div>\n          </Grid>\n        ))}\n      </Grid>\n\n      {/* Achievement Statistics */}\n      <Card sx={{ mt: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n            Achievement Statistics\n          </Typography>\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={4}>\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h3\" color=\"primary.main\" sx={{ fontWeight: 600 }}>\n                  {student.achievements.filter(a => a.type === 'Academic').length}\n                </Typography>\n                <Typography variant=\"body1\" color=\"text.secondary\">\n                  Academic Awards\n                </Typography>\n              </Box>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h3\" color=\"secondary.main\" sx={{ fontWeight: 600 }}>\n                  {student.achievements.filter(a => a.type === 'Behavioral').length}\n                </Typography>\n                <Typography variant=\"body1\" color=\"text.secondary\">\n                  Behavioral Recognition\n                </Typography>\n              </Box>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h3\" color=\"warning.main\" sx={{ fontWeight: 600 }}>\n                  {student.achievements.length}\n                </Typography>\n                <Typography variant=\"body1\" color=\"text.secondary\">\n                  Total Achievements\n                </Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default StudentProfile;\n"], "names": ["ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "RadialLinearScale", "BarElement", "Title", "ChartTooltip", "Legend", "Filler", "sampleStudentData", "StudentProfile", "theme", "useTheme", "navigate", "useNavigate", "studentId", "useParams", "activeTab", "setActiveTab", "useState", "student", "setStudent", "loading", "setLoading", "useEffect", "resolve", "error", "handleTabChange", "event", "newValue", "handleEditProfile", "handleSWOTAnalysis", "jsx", "Box", "LinearProgress", "<PERSON><PERSON>", "performanceChartData", "trend", "alpha", "behavioralRadarData", "key", "subjectPerformanceData", "subject", "_", "index", "jsxs", "motion", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Avatar", "Person", "Typography", "<PERSON><PERSON>", "Chip", "Star", "IconButton", "Edit", "Print", "Share", "<PERSON><PERSON>", "Assessment", "Tabs", "Tab", "School", "EmojiEvents", "PersonalInfoTab", "AcademicPerformanceTab", "SWOTAnalysisTab", "AchievementsTab", "Grid", "CalendarToday", "LocationOn", "Phone", "Email", "Line", "Bar", "Radar", "swotCategories", "category", "item", "itemIndex", "strength", "weakness", "opportunity", "threat", "getAchievementIcon", "type", "getAchievementColor", "achievement", "a"], "mappings": "sdA4DAA,EAAQ,SACNC,EACAC,EACAC,GACAC,GACAC,GACAC,GACAC,GACA<PERSON>,GACAC,GACAC,EACF,EAGA,MAAMC,GAAoB,CACxB,GAAI,EACJ,UAAW,QACX,WAAY,QACZ,SAAU,QACV,gBAAiB,aACjB,MAAO,GACP,QAAS,IACT,WAAY,GACZ,MAAO,OACP,YAAa,aACb,OAAQ,OACR,WAAY,KACZ,aAAc,KAGd,QAAS,uCACT,KAAM,YACN,MAAO,YACP,QAAS,SACT,MAAO,iBACP,MAAO,0BAGP,WAAY,qBACZ,iBAAkB,oBAClB,YAAa,iBACb,WAAY,cACZ,iBAAkB,UAClB,YAAa,iBAGb,WAAY,IACZ,WAAY,GACZ,SAAU,CACR,CAAE,KAAM,cAAe,MAAO,KAAM,MAAO,GAAI,QAAS,aAAc,EACtE,CAAE,KAAM,UAAW,MAAO,KAAM,MAAO,GAAI,QAAS,WAAY,EAChE,CAAE,KAAM,UAAW,MAAO,KAAM,MAAO,GAAI,QAAS,aAAc,EAClE,CAAE,KAAM,QAAS,MAAO,KAAM,MAAO,GAAI,QAAS,YAAa,EAC/D,CAAE,KAAM,iBAAkB,MAAO,KAAM,MAAO,GAAI,QAAS,WAAY,EACvE,CAAE,KAAM,SAAU,MAAO,KAAM,MAAO,GAAI,QAAS,UAAW,CAChE,EAGA,kBAAmB,CACjB,CAAE,MAAO,MAAO,IAAK,GAAI,EACzB,CAAE,MAAO,MAAO,IAAK,GAAI,EACzB,CAAE,MAAO,MAAO,IAAK,GAAI,EACzB,CAAE,MAAO,MAAO,IAAK,GAAI,EACzB,CAAE,MAAO,MAAO,IAAK,GAAI,EACzB,CAAE,MAAO,MAAO,IAAK,GAAI,CAC3B,EAGA,aAAc,CACZ,UAAW,CAAC,wBAAyB,yBAA0B,sBAAsB,EACrF,WAAY,CAAC,uCAAwC,0BAA0B,EAC/E,cAAe,CAAC,iCAAkC,2BAA2B,EAC7E,QAAS,CAAC,wBAAyB,4BAA4B,CACjE,EAGA,aAAc,CACZ,CAAE,MAAO,wCAAyC,KAAM,aAAc,KAAM,UAAW,EACvF,CAAE,MAAO,4BAA6B,KAAM,aAAc,KAAM,YAAa,EAC7E,CAAE,MAAO,6BAA8B,KAAM,aAAc,KAAM,UAAW,CAC9E,EAGA,iBAAkB,CAChB,WAAY,EACZ,SAAU,EACV,WAAY,EACZ,WAAY,EACZ,cAAe,EACf,eAAgB,CAAA,CAEpB,EAEMC,GAAiB,IAAM,CAC3B,MAAMC,EAAQC,EAAS,EACjBC,EAAWC,GAAY,EACvB,CAAE,UAAAC,CAAU,EAAIC,GAAU,EAC1B,CAACC,EAAWC,CAAY,EAAIC,EAAAA,SAAS,CAAC,EACtC,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,IAAI,EACrC,CAACG,EAASC,CAAU,EAAIJ,EAAAA,SAAS,EAAI,EAE3CK,EAAAA,UAAU,IAAM,EAEW,SAAY,CACnCD,EAAW,EAAI,EACX,GAAA,CAEF,MAAM,IAAI,QAAQE,GAAW,WAAWA,EAAS,GAAI,CAAC,EACtDJ,EAAWZ,EAAiB,QACrBiB,EAAO,CACN,QAAA,MAAM,+BAAgCA,CAAK,CAAA,QACnD,CACAH,EAAW,EAAK,CAAA,CAEpB,GAEiB,CAAA,EAChB,CAACR,CAAS,CAAC,EAER,MAAAY,EAAkB,CAACC,EAAOC,IAAa,CAC3CX,EAAaW,CAAQ,CACvB,EAEMC,EAAoB,IAAM,CACrBjB,EAAA,uBAAuBE,CAAS,OAAO,CAClD,EAEMgB,EAAqB,IAAM,CACtBlB,EAAA,uBAAuBE,CAAS,OAAO,CAClD,EAEA,GAAIO,EAEA,OAAAU,EAAA,IAACC,GAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,SAAU,WAAY,SAAU,OAAQ,KAClF,eAACC,EAAe,CAAA,GAAI,CAAE,MAAO,IAAO,CAAA,EACtC,EAIJ,GAAI,CAACd,EACH,OACGY,EAAAA,IAAAG,EAAA,CAAM,SAAS,QAAQ,SAExB,gEAAA,EAKJ,MAAMC,EAAuB,CAC3B,OAAQhB,EAAQ,kBAAkB,IAAIiB,GAASA,EAAM,KAAK,EAC1D,SAAU,CACR,CACE,MAAO,YACP,KAAMjB,EAAQ,kBAAkB,IAAIiB,GAASA,EAAM,GAAG,EACtD,YAAa1B,EAAM,QAAQ,QAAQ,KACnC,gBAAiB2B,EAAM3B,EAAM,QAAQ,QAAQ,KAAM,EAAG,EACtD,KAAM,GACN,QAAS,EAAA,CACX,CAEJ,EAEM4B,EAAsB,CAC1B,OAAQ,OAAO,KAAKnB,EAAQ,gBAAgB,EAAE,IAAIoB,GAChDA,EAAI,OAAO,CAAC,EAAE,YAAY,EAAIA,EAAI,MAAM,CAAC,CAC3C,EACA,SAAU,CACR,CACE,MAAO,wBACP,KAAM,OAAO,OAAOpB,EAAQ,gBAAgB,EAC5C,YAAaT,EAAM,QAAQ,UAAU,KACrC,gBAAiB2B,EAAM3B,EAAM,QAAQ,UAAU,KAAM,EAAG,EACxD,qBAAsBA,EAAM,QAAQ,UAAU,KAC9C,iBAAkB,OAClB,0BAA2B,OAC3B,sBAAuBA,EAAM,QAAQ,UAAU,IAAA,CACjD,CAEJ,EAEM8B,EAAyB,CAC7B,OAAQrB,EAAQ,SAAS,IAAIsB,GAAWA,EAAQ,IAAI,EACpD,SAAU,CACR,CACE,MAAO,QACP,KAAMtB,EAAQ,SAAS,IAAIsB,GAAWA,EAAQ,KAAK,EACnD,gBAAiBtB,EAAQ,SAAS,IAAI,CAACuB,EAAGC,IACxC,OAAQA,EAAQ,GAAM,GAAG,aAC3B,EACA,YAAaxB,EAAQ,SAAS,IAAI,CAACuB,EAAGC,IACpC,OAAQA,EAAQ,GAAM,GAAG,aAC3B,EACA,YAAa,CAAA,CACf,CAEJ,EAGE,OAAAC,OAACZ,EAAI,CAAA,GAAI,CAAE,SAAU,KAAM,GAAI,OAAQ,EAAG,CAAA,EAExC,SAAA,CAAAD,EAAA,IAACc,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,GAAI,EAC9B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,EAAI,EAE5B,SAACd,EAAA,IAAAe,EAAA,CAAK,GAAI,CAAE,GAAI,EAAG,SAAU,SAAA,EAC3B,SAAAf,EAAAA,IAACgB,EAAY,CAAA,GAAI,CAAE,EAAG,CAAE,EACtB,SAACH,EAAAA,KAAAZ,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,gBAAiB,GAAI,CACrF,EAAA,SAAA,CAACY,EAAAA,KAAAZ,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,CAAA,EACrD,SAAA,CAAAD,EAAA,IAACiB,EAAA,CACC,IAAK7B,EAAQ,aACb,GAAI,CACF,MAAO,IACP,OAAQ,IACR,OAAQ,aAAaT,EAAM,QAAQ,QAAQ,IAAI,GAC/C,WAAY,2BAA2BA,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,QACvG,EAEA,eAACuC,EAAO,CAAA,GAAI,CAAE,SAAU,GAAM,CAAA,CAAA,CAChC,SAECjB,EACC,CAAA,SAAA,CAACY,EAAAA,KAAAM,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,IAAK,GAAI,CAAA,EACjD,SAAA,CAAQ/B,EAAA,UAAU,IAAEA,EAAQ,WAAW,IAAEA,EAAQ,QAAA,EACpD,EACAyB,EAAAA,KAACM,EAAW,CAAA,QAAQ,KAAK,MAAM,iBAAiB,GAAI,CAAE,GAAI,CAAA,EAAK,SAAA,CAAA,SACtD/B,EAAQ,MAAM,cAAYA,EAAQ,OAAA,EAC3C,EACAyB,EAAAA,KAACM,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,GAAI,CAAE,GAAI,CAAA,EAAK,SAAA,CAAA,iBACjD/B,EAAQ,gBAAgB,eAAaA,EAAQ,UAAA,EAC9D,EAEAyB,EAAAA,KAACO,EAAM,CAAA,UAAU,MAAM,QAAS,EAAG,GAAI,CAAE,GAAI,CAAA,EAC3C,SAAA,CAAApB,EAAA,IAACqB,EAAA,CACC,MAAOjC,EAAQ,MACf,MAAM,UACN,QAAQ,SACR,GAAI,CAAE,WAAY,GAAI,CAAA,CACxB,EACAY,EAAA,IAACqB,EAAA,CACC,MAAO,QAAQjC,EAAQ,UAAU,GACjC,MAAM,UACN,QAAQ,WACR,WAAOkC,EAAK,CAAA,CAAA,CAAA,CACd,EACAtB,EAAA,IAACqB,EAAA,CACC,MAAO,GAAGjC,EAAQ,UAAU,eAC5B,MAAOA,EAAQ,YAAc,GAAK,UAAYA,EAAQ,YAAc,GAAK,UAAY,QACrF,QAAQ,UAAA,CAAA,CACV,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAECyB,EAAA,KAAAO,EAAA,CAAM,UAAU,MAAM,QAAS,EAC9B,SAAA,CAAApB,EAAAA,IAACuB,GAAW,QAASzB,EAAmB,MAAM,UAC5C,SAAAE,EAAA,IAACwB,IAAK,CACR,CAAA,QACCD,EAAW,CAAA,MAAM,UAChB,SAAAvB,MAACyB,GAAM,CAAA,EACT,QACCF,EAAW,CAAA,MAAM,UAChB,SAAAvB,MAAC0B,GAAM,CAAA,EACT,EACA1B,EAAA,IAAC2B,EAAA,CACC,QAAQ,YACR,gBAAYC,EAAW,EAAA,EACvB,QAAS7B,EACT,GAAI,CACF,WAAY,2BAA2BpB,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,QACvG,EACD,SAAA,eAAA,CAAA,CAED,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,QAGCoC,EAAK,CAAA,GAAI,CAAE,GAAI,GACd,SAAAF,EAAA,KAACgB,EAAA,CACC,MAAO5C,EACP,SAAUU,EACV,QAAQ,aACR,cAAc,OACd,GAAI,CACF,iBAAkB,CAChB,UAAW,GACX,WAAY,GAAA,CAEhB,EAEA,SAAA,CAAAK,MAAC8B,GAAI,KAAM9B,EAAA,IAACkB,EAAO,EAAA,EAAI,MAAM,gBAAgB,QAC5CY,EAAI,CAAA,WAAOC,EAAO,EAAA,EAAI,MAAM,uBAAuB,QACnDD,EAAI,CAAA,WAAOF,EAAW,EAAA,EAAI,MAAM,gBAAgB,QAChDE,EAAI,CAAA,WAAOE,EAAY,CAAA,CAAA,EAAI,MAAM,cAAe,CAAA,CAAA,CAAA,CAAA,EAErD,EAGAnB,EAAA,KAACC,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,EAAI,EAE3B,SAAA,CAAc7B,IAAA,GAAMe,EAAAA,IAAAiC,GAAA,CAAgB,QAAA7C,CAAkB,CAAA,EACtDH,IAAc,GACbe,EAAA,IAACkC,GAAA,CACC,QAAA9C,EACA,qBAAAgB,EACA,uBAAAK,EACA,oBAAAF,CAAA,CACF,EAEDtB,IAAc,GAAMe,EAAAA,IAAAmC,GAAA,CAAgB,QAAA/C,CAAkB,CAAA,EACtDH,IAAc,GAAMe,EAAAA,IAAAoC,GAAA,CAAgB,QAAAhD,CAAkB,CAAA,CAAA,CAAA,EAflDH,CAAA,CAgBP,EACF,CAEJ,EAGMgD,GAAkB,CAAC,CAAE,QAAA7C,MACXR,EAAS,EAGpBiC,EAAAA,KAAAwB,EAAA,CAAK,UAAS,GAAC,QAAS,EACvB,SAAA,CAACrC,EAAA,IAAAqC,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAArC,EAAAA,IAACe,EACC,CAAA,SAAAF,EAAAA,KAACG,EACC,CAAA,SAAA,CAAAhB,EAAA,IAACmB,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,oBAAA,EACAN,EAAAA,KAACO,EAAM,CAAA,QAAS,EACd,SAAA,CAACP,EAAAA,KAAAZ,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,CAAA,EACrD,SAAA,CAACD,EAAAA,IAAAsC,EAAA,CAAc,MAAM,QAAS,CAAA,QAC7BnB,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAc,iBAAA,EAChEnB,EAAA,IAAAmB,EAAA,CAAW,QAAQ,QAAS,WAAQ,WAAY,CAAA,CAAA,EACnD,EACAN,EAAAA,KAACZ,EAAI,CAAA,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,CAAA,EACrD,SAAA,CAACD,EAAAA,IAAAkB,EAAA,CAAO,MAAM,QAAS,CAAA,QACtBC,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAO,UAAA,EACzDnB,EAAA,IAAAmB,EAAA,CAAW,QAAQ,QAAS,WAAQ,MAAO,CAAA,CAAA,EAC9C,EACAN,EAAAA,KAACZ,EAAI,CAAA,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,CAAA,EACrD,SAAA,CAAAD,MAACmB,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAY,eAAA,EAC9DnB,EAAA,IAAAmB,EAAA,CAAW,QAAQ,QAAS,WAAQ,UAAW,CAAA,CAAA,CAClD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAEAnB,EAAA,IAACqC,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAArC,EAAAA,IAACe,EACC,CAAA,SAAAF,EAAAA,KAACG,EACC,CAAA,SAAA,CAAAhB,EAAA,IAACmB,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,sBAAA,EACAN,EAAAA,KAACO,EAAM,CAAA,QAAS,EACd,SAAA,CAACP,EAAAA,KAAAZ,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,CAAA,EACrD,SAAA,CAACD,EAAAA,IAAAuC,EAAA,CAAW,MAAM,QAAS,CAAA,QAC1BpB,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAQ,WAAA,EAC1DnB,EAAA,IAAAmB,EAAA,CAAW,QAAQ,QAAS,WAAQ,OAAQ,CAAA,CAAA,EAC/C,EACAN,EAAAA,KAACZ,EAAI,CAAA,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,CAAA,EACrD,SAAA,CAACD,EAAAA,IAAAwC,EAAA,CAAM,MAAM,QAAS,CAAA,QACrBrB,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAM,SAAA,EACxDnB,EAAA,IAAAmB,EAAA,CAAW,QAAQ,QAAS,WAAQ,KAAM,CAAA,CAAA,EAC7C,EACAN,EAAAA,KAACZ,EAAI,CAAA,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,CAAA,EACrD,SAAA,CAACD,EAAAA,IAAAyC,EAAA,CAAM,MAAM,QAAS,CAAA,QACrBtB,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAM,SAAA,EACxDnB,EAAA,IAAAmB,EAAA,CAAW,QAAQ,QAAS,WAAQ,KAAM,CAAA,CAAA,CAC7C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAEAnB,EAAA,IAACqC,GAAK,KAAI,GAAC,GAAI,GACb,SAAArC,EAAAA,IAACe,EACC,CAAA,SAAAF,EAAA,KAACG,EACC,CAAA,SAAA,CAAAhB,EAAA,IAACmB,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,8BAAA,EACCN,EAAA,KAAAwB,EAAA,CAAK,UAAS,GAAC,QAAS,EACvB,SAAA,CAAAxB,OAACwB,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAA,CAACrC,EAAAA,IAAAmB,EAAA,CAAW,QAAQ,YAAY,GAAI,CAAE,GAAI,EAAG,WAAY,GAAI,EAAG,SAAgB,kBAAA,CAAA,EAChFN,EAAAA,KAACO,EAAM,CAAA,QAAS,EACd,SAAA,CAACP,EAAAA,KAAAM,EAAA,CAAW,QAAQ,QAAQ,SAAA,CAAA,SAAO/B,EAAQ,UAAA,EAAW,EACtDyB,EAAAA,KAACM,EAAW,CAAA,QAAQ,QAAQ,SAAA,CAAA,eAAa/B,EAAQ,gBAAA,EAAiB,EAClEyB,EAAAA,KAACM,EAAW,CAAA,QAAQ,QAAQ,SAAA,CAAA,UAAQ/B,EAAQ,WAAA,CAAY,CAAA,CAAA,CAC1D,CAAA,CAAA,EACF,SACCiD,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAA,CAACrC,EAAAA,IAAAmB,EAAA,CAAW,QAAQ,YAAY,GAAI,CAAE,GAAI,EAAG,WAAY,GAAI,EAAG,SAAgB,kBAAA,CAAA,EAChFN,EAAAA,KAACO,EAAM,CAAA,QAAS,EACd,SAAA,CAACP,EAAAA,KAAAM,EAAA,CAAW,QAAQ,QAAQ,SAAA,CAAA,SAAO/B,EAAQ,UAAA,EAAW,EACtDyB,EAAAA,KAACM,EAAW,CAAA,QAAQ,QAAQ,SAAA,CAAA,eAAa/B,EAAQ,gBAAA,EAAiB,EAClEyB,EAAAA,KAACM,EAAW,CAAA,QAAQ,QAAQ,SAAA,CAAA,UAAQ/B,EAAQ,WAAA,CAAY,CAAA,CAAA,CAC1D,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,EACF,GAIE8C,GAAyB,CAAC,CAAE,QAAA9C,EAAS,qBAAAgB,EAAsB,uBAAAK,EAAwB,oBAAAF,KAEpFM,EAAAA,KAAAwB,EAAA,CAAK,UAAS,GAAC,QAAS,EAEvB,SAAA,CAACrC,EAAA,IAAAqC,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAArC,EAAAA,IAACe,EACC,CAAA,SAAAF,EAAAA,KAACG,EACC,CAAA,SAAA,CAAAhB,EAAA,IAACmB,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,uBAAA,EACAN,EAAAA,KAACO,EAAM,CAAA,QAAS,EACd,SAAA,CAAAP,OAACZ,EACC,CAAA,SAAA,CAAAD,MAACmB,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAW,cAAA,EAC7DnB,EAAAA,IAAAmB,EAAA,CAAW,QAAQ,KAAK,MAAM,eAAe,GAAI,CAAE,WAAY,GAAA,EAC7D,SAAA/B,EAAQ,UACX,CAAA,CAAA,EACF,SACCa,EACC,CAAA,SAAA,CAAAD,MAACmB,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAU,aAAA,EAC7DN,EAAAA,KAACM,EAAW,CAAA,QAAQ,KAAK,MAAM,eAAe,GAAI,CAAE,WAAY,GAAA,EAC7D,SAAA,CAAQ/B,EAAA,WAAW,GAAA,CACtB,CAAA,CAAA,EACF,SACCa,EACC,CAAA,SAAA,CAAAD,MAACmB,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAU,aAAA,EAC7DnB,EAAAA,IAACmB,EAAW,CAAA,QAAQ,KAAK,MAAM,iBAAiB,GAAI,CAAE,WAAY,KAAO,SAEzE,KAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGAnB,EAAA,IAACqC,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAArC,EAAAA,IAACe,EACC,CAAA,SAAAF,EAAAA,KAACG,EACC,CAAA,SAAA,CAAAhB,EAAA,IAACmB,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,YAAA,QACClB,EAAI,CAAA,GAAI,CAAE,OAAQ,KACjB,SAAAD,EAAA,IAAC0C,EAAA,CACC,KAAMtC,EACN,QAAS,CACP,WAAY,GACZ,oBAAqB,GACrB,QAAS,CACP,OAAQ,CACN,QAAS,EAAA,CAEb,EACA,OAAQ,CACN,EAAG,CACD,YAAa,GACb,IAAK,EACL,IAAK,EAAA,CACP,CACF,CACF,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGAJ,EAAA,IAACqC,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAArC,EAAAA,IAACe,EACC,CAAA,SAAAF,EAAAA,KAACG,EACC,CAAA,SAAA,CAAAhB,EAAA,IAACmB,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,sBAAA,QACClB,EAAI,CAAA,GAAI,CAAE,OAAQ,KACjB,SAAAD,EAAA,IAAC2C,EAAA,CACC,KAAMlC,EACN,QAAS,CACP,WAAY,GACZ,oBAAqB,GACrB,QAAS,CACP,OAAQ,CACN,QAAS,EAAA,CAEb,EACA,OAAQ,CACN,EAAG,CACD,YAAa,GACb,IAAK,GAAA,CACP,CACF,CACF,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGAT,EAAA,IAACqC,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAArC,EAAAA,IAACe,EACC,CAAA,SAAAF,EAAAA,KAACG,EACC,CAAA,SAAA,CAAAhB,EAAA,IAACmB,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,wBAAA,QACClB,EAAI,CAAA,GAAI,CAAE,OAAQ,KACjB,SAAAD,EAAA,IAAC4C,EAAA,CACC,KAAMrC,EACN,QAAS,CACP,WAAY,GACZ,oBAAqB,GACrB,QAAS,CACP,OAAQ,CACN,QAAS,EAAA,CAEb,EACA,OAAQ,CACN,EAAG,CACD,YAAa,GACb,IAAK,EAAA,CACP,CACF,CACF,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGAP,EAAA,IAACqC,GAAK,KAAI,GAAC,GAAI,GACb,SAAArC,EAAAA,IAACe,EACC,CAAA,SAAAF,EAAA,KAACG,EACC,CAAA,SAAA,CAAAhB,EAAA,IAACmB,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,kBAAA,EACCnB,EAAA,IAAAqC,EAAA,CAAK,UAAS,GAAC,QAAS,EACtB,SAAAjD,EAAQ,SAAS,IAAI,CAACsB,EAASE,IAC9BZ,EAAAA,IAACqC,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EAAG,GAAI,EAC5B,eAACtB,EAAK,CAAA,QAAQ,WACZ,SAAAF,EAAAA,KAACG,EAAY,CAAA,GAAI,CAAE,EAAG,CACpB,EAAA,SAAA,CAAChB,EAAAA,IAAAmB,EAAA,CAAW,QAAQ,YAAY,GAAI,CAAE,WAAY,GAAA,EAC/C,SAAAT,EAAQ,IACX,CAAA,EACAG,EAAAA,KAACM,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,GAAI,CAAE,GAAI,CAAA,EAAK,SAAA,CAAA,YACtDT,EAAQ,OAAA,EACpB,EACAG,EAAAA,KAACZ,EAAI,CAAA,GAAI,CAAE,QAAS,OAAQ,eAAgB,gBAAiB,WAAY,QAAA,EACvE,SAAA,CAAAD,EAAA,IAACqB,EAAA,CACC,MAAOX,EAAQ,MACf,MAAOA,EAAQ,MAAM,WAAW,GAAG,EAAI,UAAY,UACnD,KAAK,OAAA,CACP,EACAG,OAACM,GAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GACxC,EAAA,SAAA,CAAQT,EAAA,MAAM,GAAA,CACjB,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,GApBoCE,CAqBtC,CACD,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,EACF,EAIEuB,GAAkB,CAAC,CAAE,QAAA/C,KAAc,CACvC,MAAMT,EAAQC,EAAS,EAEjBiE,EAAiB,CACrB,CACE,MAAO,YACP,MAAOzD,EAAQ,aAAa,UAC5B,MAAOT,EAAM,QAAQ,QAAQ,KAC7B,KAAM,IACR,EACA,CACE,MAAO,aACP,MAAOS,EAAQ,aAAa,WAC5B,MAAOT,EAAM,QAAQ,MAAM,KAC3B,KAAM,IACR,EACA,CACE,MAAO,gBACP,MAAOS,EAAQ,aAAa,cAC5B,MAAOT,EAAM,QAAQ,KAAK,KAC1B,KAAM,IACR,EACA,CACE,MAAO,UACP,MAAOS,EAAQ,aAAa,QAC5B,MAAOT,EAAM,QAAQ,QAAQ,KAC7B,KAAM,GAAA,CAEV,EAEA,cACGsB,EACC,CAAA,SAAA,CAACY,EAAAA,KAAAZ,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,gBAAiB,WAAY,SAAU,GAAI,CAAA,EACrF,SAAA,CAACD,EAAAA,IAAAmB,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GAAA,EAAO,SAElD,wBAAA,CAAA,EACAnB,EAAA,IAAC2B,EAAA,CACC,QAAQ,YACR,gBAAYC,EAAW,EAAA,EACvB,GAAI,CACF,WAAY,2BAA2BjD,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,QACvG,EACD,SAAA,aAAA,CAAA,CAED,EACF,QAEC0D,EAAK,CAAA,UAAS,GAAC,QAAS,EACtB,WAAe,IAAI,CAACS,EAAUlC,UAC5ByB,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAArC,EAAA,IAACe,EAAA,CACC,GAAI,CACF,OAAQ,OACR,OAAQ,aAAaT,EAAMwC,EAAS,MAAO,EAAG,CAAC,GAC/C,WAAY,2BAA2BxC,EAAMwC,EAAS,MAAO,GAAI,CAAC,QAAQxC,EAAMwC,EAAS,MAAO,GAAI,CAAC,QACvG,EAEA,gBAAC9B,EACC,CAAA,SAAA,CAACH,EAAAA,KAAAZ,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,EAAG,GAAI,CAAA,EAC5D,SAAA,CAACD,EAAAA,IAAAmB,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,SAAU,EAAA,EACtC,SAAA2B,EAAS,IACZ,CAAA,EACA9C,EAAA,IAACmB,EAAA,CACC,QAAQ,KACR,GAAI,CACF,WAAY,IACZ,MAAO2B,EAAS,KAClB,EAEC,SAASA,EAAA,KAAA,CAAA,CACZ,EACF,EAEA9C,EAAAA,IAACoB,GAAM,QAAS,EACb,WAAS,MAAM,IAAI,CAAC2B,EAAMC,IACzBhD,EAAA,IAACC,EAAA,CAEC,GAAI,CACF,EAAG,EACH,aAAc,EACd,WAAYK,EAAMwC,EAAS,MAAO,EAAG,EACrC,OAAQ,aAAaxC,EAAMwC,EAAS,MAAO,EAAG,CAAC,EACjD,EAEA,SAAC9C,EAAA,IAAAmB,EAAA,CAAW,QAAQ,QACjB,SACH4B,CAAA,CAAA,CAAA,EAVKC,CAAA,CAYR,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,GAzC2BpC,CA2C/B,CACD,EACH,EAGAZ,EAAAA,IAACe,GAAK,GAAI,CAAE,GAAI,CAAE,EAChB,gBAACC,EACC,CAAA,SAAA,CAAAhB,EAAA,IAACmB,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,cAAA,EACCN,EAAA,KAAAwB,EAAA,CAAK,UAAS,GAAC,QAAS,EACvB,SAAA,CAAArC,EAAA,IAACqC,EAAK,CAAA,KAAI,GAAC,GAAI,EACb,SAAAxB,EAAA,KAACZ,EAAA,CACC,GAAI,CACF,EAAG,EACH,OAAQ,aAAatB,EAAM,QAAQ,QAAQ,IAAI,GAC/C,aAAc,EACd,WAAY2B,EAAM3B,EAAM,QAAQ,QAAQ,KAAM,GAAI,EAClD,UAAW,GACb,EAEA,SAAA,CAAAqB,EAAA,IAACmB,EAAW,CAAA,QAAQ,YAAY,GAAI,CAAE,WAAY,IAAK,MAAO,eAAgB,GAAI,CAAE,EAAG,SAEvF,gCAAA,EACC/B,EAAQ,aAAa,UAAU,IAAI,CAAC6D,EAAUrC,IAC5CC,EAAA,KAAAM,EAAA,CAAuB,QAAQ,QAAQ,GAAI,CAAE,GAAI,EAAO,EAAA,SAAA,CAAA,KACpD8B,CAAA,CAAA,EADYrC,CAEjB,CACD,CAAA,CAAA,CAAA,EAEL,EACCZ,EAAA,IAAAqC,EAAA,CAAK,KAAI,GAAC,GAAI,EACb,SAAAxB,EAAA,KAACZ,EAAA,CACC,GAAI,CACF,EAAG,EACH,OAAQ,aAAatB,EAAM,QAAQ,MAAM,IAAI,GAC7C,aAAc,EACd,WAAY2B,EAAM3B,EAAM,QAAQ,MAAM,KAAM,GAAI,EAChD,UAAW,GACb,EAEA,SAAA,CAAAqB,EAAA,IAACmB,EAAW,CAAA,QAAQ,YAAY,GAAI,CAAE,WAAY,IAAK,MAAO,aAAc,GAAI,CAAE,EAAG,SAErF,iCAAA,EACC/B,EAAQ,aAAa,WAAW,IAAI,CAAC8D,EAAUtC,IAC7CC,EAAA,KAAAM,EAAA,CAAuB,QAAQ,QAAQ,GAAI,CAAE,GAAI,EAAO,EAAA,SAAA,CAAA,KACpD+B,CAAA,CAAA,EADYtC,CAEjB,CACD,CAAA,CAAA,CAAA,EAEL,EACCZ,EAAA,IAAAqC,EAAA,CAAK,KAAI,GAAC,GAAI,EACb,SAAAxB,EAAA,KAACZ,EAAA,CACC,GAAI,CACF,EAAG,EACH,OAAQ,aAAatB,EAAM,QAAQ,KAAK,IAAI,GAC5C,aAAc,EACd,WAAY2B,EAAM3B,EAAM,QAAQ,KAAK,KAAM,GAAI,EAC/C,UAAW,GACb,EAEA,SAAA,CAAAqB,EAAA,IAACmB,EAAW,CAAA,QAAQ,YAAY,GAAI,CAAE,WAAY,IAAK,MAAO,YAAa,GAAI,CAAE,EAAG,SAEpF,oCAAA,EACC/B,EAAQ,aAAa,cAAc,IAAI,CAAC+D,EAAavC,IACnDC,EAAA,KAAAM,EAAA,CAAuB,QAAQ,QAAQ,GAAI,CAAE,GAAI,EAAO,EAAA,SAAA,CAAA,KACpDgC,CAAA,CAAA,EADYvC,CAEjB,CACD,CAAA,CAAA,CAAA,EAEL,EACCZ,EAAA,IAAAqC,EAAA,CAAK,KAAI,GAAC,GAAI,EACb,SAAAxB,EAAA,KAACZ,EAAA,CACC,GAAI,CACF,EAAG,EACH,OAAQ,aAAatB,EAAM,QAAQ,QAAQ,IAAI,GAC/C,aAAc,EACd,WAAY2B,EAAM3B,EAAM,QAAQ,QAAQ,KAAM,GAAI,EAClD,UAAW,GACb,EAEA,SAAA,CAAAqB,EAAA,IAACmB,EAAW,CAAA,QAAQ,YAAY,GAAI,CAAE,WAAY,IAAK,MAAO,eAAgB,GAAI,CAAE,EAAG,SAEvF,8BAAA,EACC/B,EAAQ,aAAa,QAAQ,IAAI,CAACgE,EAAQxC,IACxCC,EAAA,KAAAM,EAAA,CAAuB,QAAQ,QAAQ,GAAI,CAAE,GAAI,EAAO,EAAA,SAAA,CAAA,KACpDiC,CAAA,CAAA,EADYxC,CAEjB,CACD,CAAA,CAAA,CAAA,CAEL,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ,EAEMwB,GAAkB,CAAC,CAAE,QAAAhD,KAAc,CACvC,MAAMT,EAAQC,EAAS,EAEjByE,EAAsBC,GAAS,CACnC,OAAQA,EAAM,CACZ,IAAK,WACI,OAAAtD,MAAC+B,GAAO,GAAI,CAAE,MAAOpD,EAAM,QAAQ,QAAQ,IAAA,EAAQ,EAC5D,IAAK,aACI,OAAAqB,MAACgC,GAAY,GAAI,CAAE,MAAOrD,EAAM,QAAQ,UAAU,IAAA,EAAQ,EACnE,QACS,OAAAqB,MAACsB,GAAK,GAAI,CAAE,MAAO3C,EAAM,QAAQ,QAAQ,IAAA,EAAQ,CAAA,CAE9D,EAEM4E,EAAuBD,GAAS,CACpC,OAAQA,EAAM,CACZ,IAAK,WACI,OAAA3E,EAAM,QAAQ,QAAQ,KAC/B,IAAK,aACI,OAAAA,EAAM,QAAQ,UAAU,KACjC,QACS,OAAAA,EAAM,QAAQ,QAAQ,IAAA,CAEnC,EAEA,cACGsB,EACC,CAAA,SAAA,CAACD,EAAAA,IAAAmB,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,IAAK,GAAI,CAAE,EAAG,SAEzD,4BAAA,CAAA,QAECkB,EAAK,CAAA,UAAS,GAAC,QAAS,EACtB,WAAQ,aAAa,IAAI,CAACmB,EAAa5C,UACrCyB,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAArC,EAAA,IAACc,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,GAAK,MAAOF,EAAQ,EAAI,EAEhD,SAAAZ,EAAA,IAACe,EAAA,CACC,GAAI,CACF,OAAQ,aAAaT,EAAMiD,EAAoBC,EAAY,IAAI,EAAG,EAAG,CAAC,GACtE,WAAY,2BAA2BlD,EAAMiD,EAAoBC,EAAY,IAAI,EAAG,GAAI,CAAC,QAAQlD,EAAMiD,EAAoBC,EAAY,IAAI,EAAG,GAAI,CAAC,SACnJ,WAAY,sBACZ,UAAW,CACT,UAAW,kBAAA,CAEf,EAEA,gBAACxC,EACC,CAAA,SAAA,CAACH,EAAAA,KAAAZ,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,EAAG,GAAI,CAAA,EAC3D,SAAA,CAAAoD,EAAmBG,EAAY,IAAI,SACnCvD,EACC,CAAA,SAAA,CAACD,EAAAA,IAAAmB,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GAAA,EACxC,SAAAqC,EAAY,KACf,CAAA,EACCxD,EAAAA,IAAAmB,EAAA,CAAW,QAAQ,QAAQ,MAAM,iBAC/B,SAAI,IAAA,KAAKqC,EAAY,IAAI,EAAE,mBAAmB,QAAS,CACtD,KAAM,UACN,MAAO,OACP,IAAK,SAAA,CACN,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAxD,EAAA,IAACqB,EAAA,CACC,MAAOmC,EAAY,KACnB,KAAK,QACL,GAAI,CACF,gBAAiBD,EAAoBC,EAAY,IAAI,EACrD,MAAO,QACP,WAAY,GAAA,CACd,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CAAA,GA3C2B5C,CA6C/B,CACD,EACH,EAGAZ,EAAAA,IAACe,GAAK,GAAI,CAAE,GAAI,CAAE,EAChB,gBAACC,EACC,CAAA,SAAA,CAAAhB,EAAA,IAACmB,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,yBAAA,EACCN,EAAA,KAAAwB,EAAA,CAAK,UAAS,GAAC,QAAS,EACvB,SAAA,CAAArC,EAAA,IAACqC,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAAA,KAACZ,EAAI,CAAA,GAAI,CAAE,UAAW,QACpB,EAAA,SAAA,CAAAD,MAACmB,GAAW,QAAQ,KAAK,MAAM,eAAe,GAAI,CAAE,WAAY,GAAA,EAC7D,SAAA/B,EAAQ,aAAa,OAAOqE,GAAKA,EAAE,OAAS,UAAU,EAAE,OAC3D,QACCtC,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAEnD,iBAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACCnB,EAAA,IAAAqC,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAAA,KAACZ,EAAI,CAAA,GAAI,CAAE,UAAW,QACpB,EAAA,SAAA,CAAAD,MAACmB,GAAW,QAAQ,KAAK,MAAM,iBAAiB,GAAI,CAAE,WAAY,GAAA,EAC/D,SAAA/B,EAAQ,aAAa,OAAOqE,GAAKA,EAAE,OAAS,YAAY,EAAE,OAC7D,QACCtC,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAEnD,wBAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACCnB,EAAA,IAAAqC,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAAA,KAACZ,EAAI,CAAA,GAAI,CAAE,UAAW,QACpB,EAAA,SAAA,CAAAD,EAAA,IAACmB,EAAW,CAAA,QAAQ,KAAK,MAAM,eAAe,GAAI,CAAE,WAAY,GAAI,EACjE,SAAQ/B,EAAA,aAAa,OACxB,QACC+B,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAEnD,oBAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ"}