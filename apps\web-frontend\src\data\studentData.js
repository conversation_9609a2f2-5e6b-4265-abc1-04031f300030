/**
 * VidyaMitra Platform - Authentic Indian Student Data
 * 
 * Comprehensive student profiles with realistic Indian names, academic performance,
 * attendance records, behavioral data, and SWOT analysis results
 */

// Academic subjects for different boards
const subjects = {
  cbse: ['Mathematics', 'Science', 'English', 'Hindi', 'Social Studies', 'Computer Science'],
  icse: ['Mathematics', 'Physics', 'Chemistry', 'Biology', 'English', 'Hindi', 'History', 'Geography'],
  state: ['Mathematics', 'Science', 'English', 'Telugu/Tamil/Regional', 'Social Studies', 'Environmental Science'],
  ib: ['Mathematics', 'Sciences', 'English', 'Hindi', 'Individuals & Societies', 'Arts']
};

// Generate realistic performance data
const generatePerformanceData = (board, studentProfile) => {
  const subjectList = subjects[board] || subjects.cbse;
  const basePerformance = studentProfile.academicLevel;
  
  return subjectList.map(subject => ({
    subject,
    currentScore: Math.max(35, Math.min(100, basePerformance + (Math.random() - 0.5) * 20)),
    previousScore: Math.max(30, Math.min(95, basePerformance + (Math.random() - 0.5) * 25)),
    trend: Math.random() > 0.6 ? 'improving' : Math.random() > 0.3 ? 'stable' : 'declining',
    assignments: Math.floor(Math.random() * 10) + 15,
    assignmentsCompleted: Math.floor(Math.random() * 5) + 12,
  }));
};

// Generate attendance data
const generateAttendanceData = () => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  return months.map(month => ({
    month,
    present: Math.floor(Math.random() * 8) + 18, // 18-25 days
    total: Math.floor(Math.random() * 3) + 22, // 22-24 days
    percentage: Math.floor(Math.random() * 20) + 75, // 75-95%
  }));
};

// Generate behavioral incidents
const generateBehavioralData = () => {
  const incidents = [
    'Late to class', 'Incomplete homework', 'Disruptive behavior', 'Excellent participation',
    'Helped classmates', 'Leadership in group work', 'Creative thinking', 'Respectful behavior'
  ];
  
  const count = Math.floor(Math.random() * 6) + 2; // 2-7 incidents
  return Array.from({ length: count }, () => ({
    date: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
    type: incidents[Math.floor(Math.random() * incidents.length)],
    severity: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low',
    description: 'Detailed incident description would be here',
  }));
};

// Generate extracurricular activities
const generateExtracurriculars = () => {
  const activities = [
    'Cricket', 'Football', 'Basketball', 'Badminton', 'Chess', 'Debate Club',
    'Science Club', 'Art Club', 'Music', 'Dance', 'Drama', 'Robotics',
    'Environmental Club', 'Literary Society', 'Mathematics Olympiad'
  ];
  
  const count = Math.floor(Math.random() * 4) + 1; // 1-4 activities
  return Array.from({ length: count }, () => ({
    activity: activities[Math.floor(Math.random() * activities.length)],
    level: Math.random() > 0.6 ? 'advanced' : Math.random() > 0.3 ? 'intermediate' : 'beginner',
    achievements: Math.random() > 0.7 ? ['District Level Winner'] : Math.random() > 0.4 ? ['School Level Participant'] : [],
  }));
};

// Generate SWOT analysis
const generateSWOTAnalysis = (studentProfile) => {
  const strengthsPool = [
    'Strong analytical thinking', 'Excellent communication skills', 'Creative problem solving',
    'Leadership qualities', 'Team collaboration', 'Mathematical aptitude', 'Scientific reasoning',
    'Artistic abilities', 'Sports excellence', 'Language proficiency', 'Technical skills'
  ];
  
  const weaknessesPool = [
    'Time management', 'Public speaking anxiety', 'Difficulty with abstract concepts',
    'Procrastination tendency', 'Perfectionism', 'Attention to detail', 'Organization skills',
    'Stress management', 'Peer interaction', 'Technology adaptation'
  ];
  
  const opportunitiesPool = [
    'Advanced placement courses', 'Science fair participation', 'Leadership roles',
    'Internship programs', 'Skill development workshops', 'Mentorship programs',
    'Competition participation', 'Community service', 'Online learning platforms'
  ];
  
  const threatsPool = [
    'Academic pressure', 'Peer competition', 'Technology distractions',
    'Time constraints', 'Resource limitations', 'Career uncertainty',
    'Social media influence', 'Health concerns', 'Family expectations'
  ];
  
  return {
    strengths: strengthsPool.sort(() => 0.5 - Math.random()).slice(0, 3),
    weaknesses: weaknessesPool.sort(() => 0.5 - Math.random()).slice(0, 2),
    opportunities: opportunitiesPool.sort(() => 0.5 - Math.random()).slice(0, 3),
    threats: threatsPool.sort(() => 0.5 - Math.random()).slice(0, 2),
    lastUpdated: new Date(),
    confidence: Math.floor(Math.random() * 20) + 75, // 75-95% confidence
  };
};

// Comprehensive student profiles
export const studentProfiles = [
  {
    id: 'STU001',
    name: 'Sanju Kumar',
    grade: '10th',
    section: 'A',
    board: 'cbse',
    rollNumber: 'CBSE2024001',
    academicLevel: 85,
    region: 'Telangana',
    parentContact: '+91 **********',
    email: '<EMAIL>',
    dateOfBirth: '2009-03-15',
    address: 'Hyderabad, Telangana',
  },
  {
    id: 'STU002',
    name: 'Niraimathi Selvam',
    grade: '9th',
    section: 'B',
    board: 'state',
    rollNumber: 'TN2024002',
    academicLevel: 78,
    region: 'Tamil Nadu',
    parentContact: '+91 9876543211',
    email: '<EMAIL>',
    dateOfBirth: '2010-07-22',
    address: 'Chennai, Tamil Nadu',
  },
  {
    id: 'STU003',
    name: 'Mahesh Reddy',
    grade: '11th',
    section: 'A',
    board: 'cbse',
    rollNumber: 'CBSE2024003',
    academicLevel: 92,
    region: 'Andhra Pradesh',
    parentContact: '+91 9876543212',
    email: '<EMAIL>',
    dateOfBirth: '2008-11-08',
    address: 'Vijayawada, Andhra Pradesh',
  },
  {
    id: 'STU004',
    name: 'Ravi Teja Sharma',
    grade: '10th',
    section: 'C',
    board: 'icse',
    rollNumber: 'ICSE2024004',
    academicLevel: 88,
    region: 'Karnataka',
    parentContact: '+91 9876543213',
    email: '<EMAIL>',
    dateOfBirth: '2009-01-30',
    address: 'Bangalore, Karnataka',
  },
  {
    id: 'STU005',
    name: 'Ankitha Patel',
    grade: '12th',
    section: 'A',
    board: 'cbse',
    rollNumber: 'CBSE2024005',
    academicLevel: 95,
    region: 'Gujarat',
    parentContact: '+91 9876543214',
    email: '<EMAIL>',
    dateOfBirth: '2007-05-12',
    address: 'Ahmedabad, Gujarat',
  },
  {
    id: 'STU006',
    name: 'Sirisha Nair',
    grade: '9th',
    section: 'B',
    board: 'state',
    rollNumber: 'KL2024006',
    academicLevel: 82,
    region: 'Kerala',
    parentContact: '+91 9876543215',
    email: '<EMAIL>',
    dateOfBirth: '2010-09-18',
    address: 'Kochi, Kerala',
  },
  {
    id: 'STU007',
    name: 'Priya Agarwal',
    grade: '11th',
    section: 'B',
    board: 'cbse',
    rollNumber: 'CBSE2024007',
    academicLevel: 89,
    region: 'Rajasthan',
    parentContact: '+91 9876543216',
    email: '<EMAIL>',
    dateOfBirth: '2008-12-03',
    address: 'Jaipur, Rajasthan',
  },
  {
    id: 'STU008',
    name: 'Arjun Singh',
    grade: '10th',
    section: 'A',
    board: 'cbse',
    rollNumber: 'CBSE2024008',
    academicLevel: 76,
    region: 'Punjab',
    parentContact: '+91 9876543217',
    email: '<EMAIL>',
    dateOfBirth: '2009-04-25',
    address: 'Chandigarh, Punjab',
  },
  {
    id: 'STU009',
    name: 'Kavya Menon',
    grade: '12th',
    section: 'C',
    board: 'icse',
    rollNumber: 'ICSE2024009',
    academicLevel: 91,
    region: 'Kerala',
    parentContact: '+91 9876543218',
    email: '<EMAIL>',
    dateOfBirth: '2007-08-14',
    address: 'Thiruvananthapuram, Kerala',
  },
  {
    id: 'STU010',
    name: 'Rohit Gupta',
    grade: '9th',
    section: 'A',
    board: 'state',
    rollNumber: 'UP2024010',
    academicLevel: 84,
    region: 'Uttar Pradesh',
    parentContact: '+91 9876543219',
    email: '<EMAIL>',
    dateOfBirth: '2010-02-28',
    address: 'Lucknow, Uttar Pradesh',
  },
];

// Generate complete student data
export const generateCompleteStudentData = () => {
  return studentProfiles.map(profile => ({
    ...profile,
    performance: generatePerformanceData(profile.board, profile),
    attendance: generateAttendanceData(),
    behavioral: generateBehavioralData(),
    extracurricular: generateExtracurriculars(),
    swotAnalysis: generateSWOTAnalysis(profile),
    overallGrade: profile.academicLevel >= 90 ? 'A+' : 
                  profile.academicLevel >= 80 ? 'A' :
                  profile.academicLevel >= 70 ? 'B' :
                  profile.academicLevel >= 60 ? 'C' : 'D',
    rank: Math.floor(Math.random() * 50) + 1, // Rank out of 50 students
    lastUpdated: new Date(),
  }));
};

// Analytics data for dashboard
export const generateAnalyticsData = (students) => {
  const totalStudents = students.length;
  const averagePerformance = students.reduce((sum, student) => sum + student.academicLevel, 0) / totalStudents;
  const averageAttendance = students.reduce((sum, student) => {
    const totalPresent = student.attendance.reduce((p, a) => p + a.present, 0);
    const totalDays = student.attendance.reduce((p, a) => p + a.total, 0);
    return sum + (totalPresent / totalDays * 100);
  }, 0) / totalStudents;

  return {
    totalStudents,
    averagePerformance: Math.round(averagePerformance),
    averageAttendance: Math.round(averageAttendance),
    topPerformers: students.filter(s => s.academicLevel >= 90).length,
    needsAttention: students.filter(s => s.academicLevel < 70).length,
    boardDistribution: {
      cbse: students.filter(s => s.board === 'cbse').length,
      icse: students.filter(s => s.board === 'icse').length,
      state: students.filter(s => s.board === 'state').length,
      ib: students.filter(s => s.board === 'ib').length,
    },
    gradeDistribution: {
      '9th': students.filter(s => s.grade === '9th').length,
      '10th': students.filter(s => s.grade === '10th').length,
      '11th': students.filter(s => s.grade === '11th').length,
      '12th': students.filter(s => s.grade === '12th').length,
    },
    performanceTrends: generatePerformanceTrends(students),
    subjectAnalysis: generateSubjectAnalysis(students),
    attendancePatterns: generateAttendancePatterns(students),
    swotDistribution: generateSWOTDistribution(students),
  };
};

// Generate performance trends over time
export const generatePerformanceTrends = (students) => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  return months.map(month => ({
    month,
    average: Math.floor(Math.random() * 15) + 75, // 75-90 range
    cbse: Math.floor(Math.random() * 15) + 78,
    icse: Math.floor(Math.random() * 15) + 80,
    state: Math.floor(Math.random() * 15) + 73,
  }));
};

// Generate subject-wise analysis
export const generateSubjectAnalysis = (students) => {
  const allSubjects = ['Mathematics', 'Science', 'English', 'Hindi', 'Social Studies', 'Computer Science'];
  return allSubjects.map(subject => ({
    subject,
    averageScore: Math.floor(Math.random() * 20) + 70,
    topScore: Math.floor(Math.random() * 10) + 90,
    lowestScore: Math.floor(Math.random() * 20) + 45,
    studentsAbove80: Math.floor(Math.random() * students.length * 0.6),
    studentsBelow60: Math.floor(Math.random() * students.length * 0.2),
  }));
};

// Generate attendance patterns
export const generateAttendancePatterns = (students) => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  return months.map(month => ({
    month,
    averageAttendance: Math.floor(Math.random() * 15) + 80, // 80-95%
    totalStudents: students.length,
    presentStudents: Math.floor(students.length * (0.8 + Math.random() * 0.15)),
  }));
};

// Generate SWOT distribution
export const generateSWOTDistribution = (students) => {
  return {
    strengthsDistribution: {
      'Analytical Thinking': Math.floor(students.length * 0.3),
      'Communication Skills': Math.floor(students.length * 0.25),
      'Leadership': Math.floor(students.length * 0.2),
      'Creativity': Math.floor(students.length * 0.35),
      'Technical Skills': Math.floor(students.length * 0.4),
    },
    weaknessesDistribution: {
      'Time Management': Math.floor(students.length * 0.4),
      'Public Speaking': Math.floor(students.length * 0.3),
      'Organization': Math.floor(students.length * 0.25),
      'Stress Management': Math.floor(students.length * 0.35),
    },
    opportunitiesDistribution: {
      'Advanced Courses': Math.floor(students.length * 0.6),
      'Leadership Roles': Math.floor(students.length * 0.3),
      'Competitions': Math.floor(students.length * 0.5),
      'Skill Development': Math.floor(students.length * 0.7),
    },
    threatsDistribution: {
      'Academic Pressure': Math.floor(students.length * 0.5),
      'Peer Competition': Math.floor(students.length * 0.4),
      'Technology Distractions': Math.floor(students.length * 0.6),
      'Time Constraints': Math.floor(students.length * 0.45),
    },
  };
};

export default generateCompleteStudentData;
