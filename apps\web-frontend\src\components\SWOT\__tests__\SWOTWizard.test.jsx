/**
 * VidyaMitra Platform - SWOTWizard Component Tests
 * 
 * Comprehensive unit tests for SWOT analysis wizard with Indian educational context
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import SWOTWizard from '../SWOTWizard';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }) => children,
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

describe('SWOTWizard Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders SWOT wizard with initial step', () => {
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    expect(screen.getByText('SWOT Analysis Wizard')).toBeInTheDocument();
    expect(screen.getByText('Create comprehensive SWOT analysis for students')).toBeInTheDocument();
    expect(screen.getByText('Student Selection')).toBeInTheDocument();
  });

  test('displays student selection step', () => {
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    expect(screen.getByText('Select Student')).toBeInTheDocument();
    expect(screen.getByText('Choose a student for SWOT analysis')).toBeInTheDocument();
    
    // Should show Indian student names
    expect(screen.getByText('Sanju Kumar Reddy')).toBeInTheDocument();
    expect(screen.getByText('Niraimathi Selvam')).toBeInTheDocument();
    expect(screen.getByText('Mahesh Reddy')).toBeInTheDocument();
  });

  test('allows student selection and navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    // Select a student
    const studentCard = screen.getByText('Sanju Kumar Reddy').closest('div');
    await user.click(studentCard);

    // Should highlight selected student
    expect(studentCard).toHaveClass('selected');

    // Navigate to next step
    const nextButton = screen.getByRole('button', { name: /next/i });
    await user.click(nextButton);

    // Should be on strengths step
    await waitFor(() => {
      expect(screen.getByText('Identify Strengths')).toBeInTheDocument();
    });
  });

  test('handles strengths identification step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    // Navigate to strengths step
    await user.click(screen.getByText('Sanju Kumar Reddy').closest('div'));
    await user.click(screen.getByRole('button', { name: /next/i }));

    await waitFor(() => {
      expect(screen.getByText('Academic Strengths')).toBeInTheDocument();
      expect(screen.getByText('Personal Strengths')).toBeInTheDocument();
      expect(screen.getByText('Cultural Strengths')).toBeInTheDocument();
    });

    // Should show Indian context strengths
    expect(screen.getByText('Strong mathematical reasoning')).toBeInTheDocument();
    expect(screen.getByText('Excellent memory retention')).toBeInTheDocument();
    expect(screen.getByText('Multilingual abilities')).toBeInTheDocument();
    expect(screen.getByText('Cultural awareness')).toBeInTheDocument();
  });

  test('allows custom strength addition', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    // Navigate to strengths step
    await user.click(screen.getByText('Sanju Kumar Reddy').closest('div'));
    await user.click(screen.getByRole('button', { name: /next/i }));

    await waitFor(() => {
      const customInput = screen.getByPlaceholderText('Add custom strength...');
      expect(customInput).toBeInTheDocument();
    });

    // Add custom strength
    const customInput = screen.getByPlaceholderText('Add custom strength...');
    await user.type(customInput, 'Excellent in classical music');
    await user.click(screen.getByRole('button', { name: /add/i }));

    // Should display the custom strength
    expect(screen.getByText('Excellent in classical music')).toBeInTheDocument();
  });

  test('handles weaknesses identification step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    // Navigate through steps
    await user.click(screen.getByText('Sanju Kumar Reddy').closest('div'));
    await user.click(screen.getByRole('button', { name: /next/i }));
    
    // Skip strengths step
    await waitFor(() => {
      const nextButton = screen.getByRole('button', { name: /next/i });
      user.click(nextButton);
    });

    await waitFor(() => {
      expect(screen.getByText('Identify Weaknesses')).toBeInTheDocument();
      expect(screen.getByText('Academic Challenges')).toBeInTheDocument();
      expect(screen.getByText('Behavioral Areas')).toBeInTheDocument();
    });

    // Should show contextual weaknesses
    expect(screen.getByText('Difficulty with English communication')).toBeInTheDocument();
    expect(screen.getByText('Needs improvement in time management')).toBeInTheDocument();
  });

  test('handles opportunities identification step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    // Navigate through steps quickly
    await user.click(screen.getByText('Sanju Kumar Reddy').closest('div'));
    
    // Navigate through multiple steps
    for (let i = 0; i < 3; i++) {
      await user.click(screen.getByRole('button', { name: /next/i }));
      await waitFor(() => {
        // Wait for step transition
      });
    }

    await waitFor(() => {
      expect(screen.getByText('Identify Opportunities')).toBeInTheDocument();
      expect(screen.getByText('Academic Opportunities')).toBeInTheDocument();
      expect(screen.getByText('Career Opportunities')).toBeInTheDocument();
    });

    // Should show Indian context opportunities
    expect(screen.getByText('Science Olympiad participation')).toBeInTheDocument();
    expect(screen.getByText('Engineering entrance exam preparation')).toBeInTheDocument();
    expect(screen.getByText('Cultural competition participation')).toBeInTheDocument();
  });

  test('handles threats identification step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    // Navigate through all steps to threats
    await user.click(screen.getByText('Sanju Kumar Reddy').closest('div'));
    
    for (let i = 0; i < 4; i++) {
      await user.click(screen.getByRole('button', { name: /next/i }));
      await waitFor(() => {
        // Wait for step transition
      });
    }

    await waitFor(() => {
      expect(screen.getByText('Identify Threats')).toBeInTheDocument();
      expect(screen.getByText('Academic Threats')).toBeInTheDocument();
      expect(screen.getByText('External Factors')).toBeInTheDocument();
    });

    // Should show contextual threats
    expect(screen.getByText('High competition in entrance exams')).toBeInTheDocument();
    expect(screen.getByText('Peer pressure for career choices')).toBeInTheDocument();
  });

  test('displays review and submit step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    // Navigate through all steps to review
    await user.click(screen.getByText('Sanju Kumar Reddy').closest('div'));
    
    for (let i = 0; i < 5; i++) {
      await user.click(screen.getByRole('button', { name: /next/i }));
      await waitFor(() => {
        // Wait for step transition
      });
    }

    await waitFor(() => {
      expect(screen.getByText('Review & Submit')).toBeInTheDocument();
      expect(screen.getByText('SWOT Analysis Summary')).toBeInTheDocument();
      expect(screen.getByText('Strengths')).toBeInTheDocument();
      expect(screen.getByText('Weaknesses')).toBeInTheDocument();
      expect(screen.getByText('Opportunities')).toBeInTheDocument();
      expect(screen.getByText('Threats')).toBeInTheDocument();
    });
  });

  test('handles SWOT analysis submission', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    // Navigate through all steps
    await user.click(screen.getByText('Sanju Kumar Reddy').closest('div'));
    
    for (let i = 0; i < 5; i++) {
      await user.click(screen.getByRole('button', { name: /next/i }));
      await waitFor(() => {
        // Wait for step transition
      });
    }

    // Submit SWOT analysis
    await waitFor(() => {
      const submitButton = screen.getByRole('button', { name: /submit analysis/i });
      expect(submitButton).toBeInTheDocument();
      user.click(submitButton);
    });

    // Should navigate after submission
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard/students/1/swot');
    });
  });

  test('allows navigation back through steps', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    // Navigate forward
    await user.click(screen.getByText('Sanju Kumar Reddy').closest('div'));
    await user.click(screen.getByRole('button', { name: /next/i }));

    await waitFor(() => {
      expect(screen.getByText('Identify Strengths')).toBeInTheDocument();
    });

    // Navigate back
    const backButton = screen.getByRole('button', { name: /back/i });
    await user.click(backButton);

    await waitFor(() => {
      expect(screen.getByText('Student Selection')).toBeInTheDocument();
    });
  });

  test('validates required selections', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    // Try to proceed without selecting student
    const nextButton = screen.getByRole('button', { name: /next/i });
    await user.click(nextButton);

    // Should show validation error
    await waitFor(() => {
      expect(screen.getByText('Please select a student')).toBeInTheDocument();
    });
  });

  test('supports accessibility features', () => {
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    // Check for proper heading structure
    expect(screen.getByRole('heading', { name: /swot analysis wizard/i })).toBeInTheDocument();

    // Check for proper stepper accessibility
    expect(screen.getByRole('button', { name: /next/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /back/i })).toBeInTheDocument();

    // Check for proper form labels
    expect(screen.getByText('Select Student')).toBeInTheDocument();
  });

  test('displays progress indicator', () => {
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    // Should show step progress
    expect(screen.getByText('Step 1 of 6')).toBeInTheDocument();
    
    // Should show progress bar
    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toBeInTheDocument();
  });

  test('handles Indian cultural context in SWOT items', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    // Navigate to strengths step
    await user.click(screen.getByText('Sanju Kumar Reddy').closest('div'));
    await user.click(screen.getByRole('button', { name: /next/i }));

    await waitFor(() => {
      // Should show culturally relevant items
      expect(screen.getByText('Respect for elders and teachers')).toBeInTheDocument();
      expect(screen.getByText('Strong family support system')).toBeInTheDocument();
      expect(screen.getByText('Participation in cultural festivals')).toBeInTheDocument();
    });
  });
});

// Integration tests
describe('SWOTWizard Integration', () => {
  test('complete SWOT analysis workflow', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SWOTWizard />
      </TestWrapper>
    );

    // Complete workflow
    // Step 1: Select student
    await user.click(screen.getByText('Sanju Kumar Reddy').closest('div'));
    await user.click(screen.getByRole('button', { name: /next/i }));

    // Step 2: Select strengths
    await waitFor(() => {
      const strengthItem = screen.getByText('Strong mathematical reasoning');
      user.click(strengthItem);
    });
    await user.click(screen.getByRole('button', { name: /next/i }));

    // Step 3: Select weaknesses
    await waitFor(() => {
      const weaknessItem = screen.getByText('Difficulty with English communication');
      user.click(weaknessItem);
    });
    await user.click(screen.getByRole('button', { name: /next/i }));

    // Step 4: Select opportunities
    await waitFor(() => {
      const opportunityItem = screen.getByText('Science Olympiad participation');
      user.click(opportunityItem);
    });
    await user.click(screen.getByRole('button', { name: /next/i }));

    // Step 5: Select threats
    await waitFor(() => {
      const threatItem = screen.getByText('High competition in entrance exams');
      user.click(threatItem);
    });
    await user.click(screen.getByRole('button', { name: /next/i }));

    // Step 6: Review and submit
    await waitFor(() => {
      expect(screen.getByText('SWOT Analysis Summary')).toBeInTheDocument();
      expect(screen.getByText('Strong mathematical reasoning')).toBeInTheDocument();
      expect(screen.getByText('Difficulty with English communication')).toBeInTheDocument();
    });

    // Submit
    const submitButton = screen.getByRole('button', { name: /submit analysis/i });
    await user.click(submitButton);

    // Verify navigation
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard/students/1/swot');
    });
  });
});
