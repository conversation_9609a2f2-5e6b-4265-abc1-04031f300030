/**
 * VidyaMitra Platform - Comprehensive Analytics Dashboard
 * 
 * Advanced data visualizations showing student performance analytics,
 * attendance patterns, SWOT analysis distribution, and board-specific insights
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Tab,
  Tabs,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  alpha,
  useTheme,
} from '@mui/material';
import {
  TrendingUp,
  School,
  Assessment,
  People,
  Timeline,
  BarChart,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import generateCompleteStudentData, { generateAnalyticsData } from '../../data/studentData';
import ModernMetricCard from '../Dashboard/ModernMetricCard';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const AnalyticsDashboard = () => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [timeRange, setTimeRange] = useState('year');
  const [selectedBoard, setSelectedBoard] = useState('all');
  const [studentsData, setStudentsData] = useState([]);
  const [analyticsData, setAnalyticsData] = useState(null);

  useEffect(() => {
    const data = generateCompleteStudentData();
    setStudentsData(data);
    setAnalyticsData(generateAnalyticsData(data));
  }, []);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  if (!analyticsData) {
    return <Box>Loading...</Box>;
  }

  // Chart configurations
  const performanceTrendConfig = {
    data: {
      labels: analyticsData.performanceTrends.map(d => d.month),
      datasets: [
        {
          label: 'Overall Average',
          data: analyticsData.performanceTrends.map(d => d.average),
          borderColor: theme.palette.primary.main,
          backgroundColor: alpha(theme.palette.primary.main, 0.1),
          tension: 0.4,
        },
        {
          label: 'CBSE',
          data: analyticsData.performanceTrends.map(d => d.cbse),
          borderColor: theme.palette.secondary.main,
          backgroundColor: alpha(theme.palette.secondary.main, 0.1),
          tension: 0.4,
        },
        {
          label: 'ICSE',
          data: analyticsData.performanceTrends.map(d => d.icse),
          borderColor: theme.palette.success.main,
          backgroundColor: alpha(theme.palette.success.main, 0.1),
          tension: 0.4,
        },
        {
          label: 'State Board',
          data: analyticsData.performanceTrends.map(d => d.state),
          borderColor: theme.palette.warning.main,
          backgroundColor: alpha(theme.palette.warning.main, 0.1),
          tension: 0.4,
        },
      ],
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: 'Academic Performance Trends',
        },
      },
      scales: {
        y: {
          beginAtZero: false,
          min: 60,
          max: 100,
        },
      },
    },
  };

  const subjectAnalysisConfig = {
    data: {
      labels: analyticsData.subjectAnalysis.map(s => s.subject),
      datasets: [
        {
          label: 'Average Score',
          data: analyticsData.subjectAnalysis.map(s => s.averageScore),
          backgroundColor: [
            alpha(theme.palette.primary.main, 0.8),
            alpha(theme.palette.secondary.main, 0.8),
            alpha(theme.palette.success.main, 0.8),
            alpha(theme.palette.warning.main, 0.8),
            alpha(theme.palette.error.main, 0.8),
            alpha(theme.palette.info.main, 0.8),
          ],
          borderColor: [
            theme.palette.primary.main,
            theme.palette.secondary.main,
            theme.palette.success.main,
            theme.palette.warning.main,
            theme.palette.error.main,
            theme.palette.info.main,
          ],
          borderWidth: 2,
        },
      ],
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          display: false,
        },
        title: {
          display: true,
          text: 'Subject-wise Performance Analysis',
        },
      },
      scales: {
        y: {
          beginAtZero: false,
          min: 50,
          max: 100,
        },
      },
    },
  };

  const boardDistributionConfig = {
    data: {
      labels: ['CBSE', 'ICSE', 'State Board', 'IB'],
      datasets: [
        {
          data: [
            analyticsData.boardDistribution.cbse,
            analyticsData.boardDistribution.icse,
            analyticsData.boardDistribution.state,
            analyticsData.boardDistribution.ib,
          ],
          backgroundColor: [
            alpha(theme.palette.primary.main, 0.8),
            alpha(theme.palette.secondary.main, 0.8),
            alpha(theme.palette.success.main, 0.8),
            alpha(theme.palette.warning.main, 0.8),
          ],
          borderColor: [
            theme.palette.primary.main,
            theme.palette.secondary.main,
            theme.palette.success.main,
            theme.palette.warning.main,
          ],
          borderWidth: 2,
        },
      ],
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'bottom',
        },
        title: {
          display: true,
          text: 'Student Distribution by Board',
        },
      },
    },
  };

  const attendancePatternConfig = {
    data: {
      labels: analyticsData.attendancePatterns.map(d => d.month),
      datasets: [
        {
          label: 'Attendance Percentage',
          data: analyticsData.attendancePatterns.map(d => d.averageAttendance),
          backgroundColor: alpha(theme.palette.success.main, 0.6),
          borderColor: theme.palette.success.main,
          borderWidth: 2,
        },
      ],
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          display: false,
        },
        title: {
          display: true,
          text: 'Monthly Attendance Patterns',
        },
      },
      scales: {
        y: {
          beginAtZero: false,
          min: 70,
          max: 100,
        },
      },
    },
  };

  const metricCards = [
    {
      title: 'Total Students',
      value: analyticsData.totalStudents,
      icon: People,
      color: 'primary',
      trend: '+5.2%',
      description: 'Active students enrolled',
    },
    {
      title: 'Average Performance',
      value: `${analyticsData.averagePerformance}%`,
      icon: TrendingUp,
      color: 'success',
      trend: '+2.1%',
      description: 'Overall academic performance',
    },
    {
      title: 'Average Attendance',
      value: `${analyticsData.averageAttendance}%`,
      icon: School,
      color: 'info',
      trend: '+1.8%',
      description: 'Monthly attendance rate',
    },
    {
      title: 'Top Performers',
      value: analyticsData.topPerformers,
      icon: Assessment,
      color: 'warning',
      trend: '+3.5%',
      description: 'Students scoring 90%+',
    },
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 600,
              mb: 1,
              color: theme.palette.text.primary,
            }}
          >
            Analytics Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Comprehensive insights into student performance and institutional metrics
          </Typography>
        </Box>

        {/* Controls */}
        <Box sx={{ mb: 4, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value="month">This Month</MenuItem>
              <MenuItem value="quarter">This Quarter</MenuItem>
              <MenuItem value="year">This Year</MenuItem>
              <MenuItem value="all">All Time</MenuItem>
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Board</InputLabel>
            <Select
              value={selectedBoard}
              label="Board"
              onChange={(e) => setSelectedBoard(e.target.value)}
            >
              <MenuItem value="all">All Boards</MenuItem>
              <MenuItem value="cbse">CBSE</MenuItem>
              <MenuItem value="icse">ICSE</MenuItem>
              <MenuItem value="state">State Board</MenuItem>
              <MenuItem value="ib">IB</MenuItem>
            </Select>
          </FormControl>
        </Box>

        {/* Metric Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {metricCards.map((card, index) => (
            <Grid item xs={12} sm={6} md={3} key={card.title}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <ModernMetricCard {...card} />
              </motion.div>
            </Grid>
          ))}
        </Grid>

        {/* Charts Tabs */}
        <Card
          sx={{
            background: alpha(theme.palette.background.paper, 0.9),
            backdropFilter: 'blur(20px)',
            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          }}
        >
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{
              borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
              px: 2,
            }}
          >
            <Tab label="Performance Trends" icon={<Timeline />} />
            <Tab label="Subject Analysis" icon={<BarChart />} />
            <Tab label="Board Distribution" icon={<School />} />
            <Tab label="Attendance Patterns" icon={<TrendingUp />} />
          </Tabs>

          <CardContent sx={{ p: 3 }}>
            {activeTab === 0 && (
              <Box sx={{ height: 400 }}>
                <Line {...performanceTrendConfig} />
              </Box>
            )}
            {activeTab === 1 && (
              <Box sx={{ height: 400 }}>
                <Bar {...subjectAnalysisConfig} />
              </Box>
            )}
            {activeTab === 2 && (
              <Box sx={{ height: 400, display: 'flex', justifyContent: 'center' }}>
                <Box sx={{ width: 400 }}>
                  <Doughnut {...boardDistributionConfig} />
                </Box>
              </Box>
            )}
            {activeTab === 3 && (
              <Box sx={{ height: 400 }}>
                <Bar {...attendancePatternConfig} />
              </Box>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </Container>
  );
};

export default AnalyticsDashboard;
