import{u as M,a2 as R,s as z,y as H,j as e,B as o,g as r,G as a,b as h,e as m,a4 as P,a5 as N,a6 as G,a7 as i,m as A,k as u,bd as Y,a_ as g,a$ as b,aq as q,ar as K,d as U,be as J,A as V,as as X,H as Z,aM as ee,L as se,bf as re,an as ae,Y as ne}from"./mui-Cjipzt4F.js";import{r as c}from"./vendor-BfWiUekA.js";import{u as te,m as ie}from"./index-_PFqhbKW.js";import"./charts-Dx2u7Eir.js";const I=[{id:"progress_report",name:"Student Progress Report",description:"Comprehensive academic and behavioral progress report",icon:R,color:"primary"},{id:"report_card",name:"Report Card",description:"Traditional report card with grades and attendance",icon:z,color:"success"},{id:"swot_report",name:"SWOT Analysis Report",description:"Detailed SWOT analysis with recommendations",icon:R,color:"info"},{id:"attendance_report",name:"Attendance Report",description:"Monthly attendance summary and patterns",icon:H,color:"warning"}],k=[{id:1,name:"Sanju <PERSON>",class:"10-A",rollNumber:1},{id:2,name:"Niraimathi Selvam",class:"10-A",rollNumber:2},{id:3,name:"Mahesh Reddy",class:"10-B",rollNumber:3},{id:4,name:"Ravi Teja Sharma",class:"9-A",rollNumber:4},{id:5,name:"Ankitha Patel",class:"10-A",rollNumber:5},{id:6,name:"Sirisha Nair",class:"10-B",rollNumber:6},{id:7,name:"Priya Agarwal",class:"9-A",rollNumber:7}],xe=()=>{const l=M();te();const[d,B]=c.useState(""),[x,_]=c.useState(""),[n,v]=c.useState([]),[W,$]=c.useState(""),[j,L]=c.useState({grades:!0,attendance:!0,behavior:!0,swot:!1,parentComments:!1}),[y,w]=c.useState(!1),[T,C]=c.useState(0),Q=s=>{v(t=>t.includes(s)?t.filter(f=>f!==s):[...t,s])},O=()=>{const s=k.filter(t=>x?t.class===x:!0);v(s.map(t=>t.id))},D=()=>{v([])},p=s=>t=>{L(f=>({...f,[s]:t.target.checked}))},E=async()=>{if(!d||n.length===0){alert("Please select a template and at least one student");return}w(!0),C(0);try{for(let s=0;s<=100;s+=10)await new Promise(t=>setTimeout(t,200)),C(s);alert(`Successfully generated ${n.length} reports!`)}catch(s){console.error("Error generating reports:",s)}finally{w(!1),C(0)}},F=x?k.filter(s=>s.class===x):k,S=I.find(s=>s.id===d);return e.jsxs(o,{sx:{maxWidth:1200,mx:"auto",p:3},children:[e.jsx(ie.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:e.jsxs(o,{sx:{mb:4},children:[e.jsx(r,{variant:"h4",sx:{fontWeight:600,mb:1,background:`linear-gradient(135deg, ${l.palette.primary.main} 0%, ${l.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"Report Generation"}),e.jsx(r,{variant:"body1",color:"text.secondary",children:"Generate comprehensive student reports with Indian educational context"})]})}),e.jsxs(a,{container:!0,spacing:3,children:[e.jsxs(a,{item:!0,xs:12,md:8,children:[e.jsx(h,{sx:{mb:3},children:e.jsxs(m,{children:[e.jsx(r,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Select Report Template"}),e.jsx(a,{container:!0,spacing:2,children:I.map(s=>e.jsx(a,{item:!0,xs:12,sm:6,children:e.jsx(h,{sx:{cursor:"pointer",border:d===s.id?`2px solid ${l.palette[s.color].main}`:"1px solid",borderColor:d===s.id?`${s.color}.main`:"divider",transition:"all 0.2s ease","&:hover":{transform:"translateY(-2px)",boxShadow:l.shadows[4]}},onClick:()=>B(s.id),children:e.jsxs(m,{children:[e.jsxs(o,{sx:{display:"flex",alignItems:"center",gap:2,mb:1},children:[e.jsx(s.icon,{color:s.color}),e.jsx(r,{variant:"subtitle1",sx:{fontWeight:600},children:s.name})]}),e.jsx(r,{variant:"body2",color:"text.secondary",children:s.description})]})})},s.id))})]})}),e.jsx(h,{sx:{mb:3},children:e.jsxs(m,{children:[e.jsx(r,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Report Configuration"}),e.jsxs(a,{container:!0,spacing:3,children:[e.jsx(a,{item:!0,xs:12,md:4,children:e.jsxs(P,{fullWidth:!0,children:[e.jsx(N,{children:"Class"}),e.jsxs(G,{value:x,onChange:s=>_(s.target.value),label:"Class",children:[e.jsx(i,{value:"",children:"All Classes"}),e.jsx(i,{value:"9-A",children:"Class 9-A"}),e.jsx(i,{value:"9-B",children:"Class 9-B"}),e.jsx(i,{value:"10-A",children:"Class 10-A"}),e.jsx(i,{value:"10-B",children:"Class 10-B"})]})]})}),e.jsx(a,{item:!0,xs:12,md:4,children:e.jsxs(P,{fullWidth:!0,children:[e.jsx(N,{children:"Report Period"}),e.jsxs(G,{value:W,onChange:s=>$(s.target.value),label:"Report Period",children:[e.jsx(i,{value:"Q1_2024",children:"Q1 2024-2025"}),e.jsx(i,{value:"Q2_2024",children:"Q2 2024-2025"}),e.jsx(i,{value:"Q3_2024",children:"Q3 2024-2025"}),e.jsx(i,{value:"Q4_2024",children:"Q4 2024-2025"}),e.jsx(i,{value:"ANNUAL_2024",children:"Annual 2024-2025"})]})]})}),e.jsx(a,{item:!0,xs:12,md:4,children:e.jsxs(A,{direction:"row",spacing:1,children:[e.jsx(u,{variant:"outlined",size:"small",onClick:O,children:"Select All"}),e.jsx(u,{variant:"outlined",size:"small",onClick:D,children:"Deselect All"})]})})]})]})}),e.jsx(h,{sx:{mb:3},children:e.jsxs(m,{children:[e.jsx(r,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Include in Report"}),e.jsx(Y,{children:e.jsxs(a,{container:!0,spacing:2,children:[e.jsx(a,{item:!0,xs:12,sm:6,children:e.jsx(g,{control:e.jsx(b,{checked:j.grades,onChange:p("grades")}),label:"Academic Grades"})}),e.jsx(a,{item:!0,xs:12,sm:6,children:e.jsx(g,{control:e.jsx(b,{checked:j.attendance,onChange:p("attendance")}),label:"Attendance Record"})}),e.jsx(a,{item:!0,xs:12,sm:6,children:e.jsx(g,{control:e.jsx(b,{checked:j.behavior,onChange:p("behavior")}),label:"Behavioral Assessment"})}),e.jsx(a,{item:!0,xs:12,sm:6,children:e.jsx(g,{control:e.jsx(b,{checked:j.swot,onChange:p("swot")}),label:"SWOT Analysis"})}),e.jsx(a,{item:!0,xs:12,sm:6,children:e.jsx(g,{control:e.jsx(b,{checked:j.parentComments,onChange:p("parentComments")}),label:"Parent Comments Section"})})]})})]})}),e.jsx(h,{children:e.jsxs(m,{children:[e.jsxs(r,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:["Select Students (",n.length," selected)"]}),e.jsx(q,{sx:{maxHeight:300,overflow:"auto"},children:F.map(s=>e.jsxs(K,{sx:{cursor:"pointer",borderRadius:1,mb:1,border:n.includes(s.id)?`2px solid ${l.palette.primary.main}`:"1px solid",borderColor:n.includes(s.id)?"primary.main":"divider",background:n.includes(s.id)?U(l.palette.primary.main,.05):"transparent"},onClick:()=>Q(s.id),children:[e.jsx(J,{children:e.jsx(V,{sx:{bgcolor:n.includes(s.id)?"primary.main":"grey.400"},children:s.name.charAt(0)})}),e.jsx(X,{primary:s.name,secondary:`Class ${s.class} - Roll No. ${s.rollNumber}`}),n.includes(s.id)&&e.jsx(Z,{color:"primary"})]},s.id))})]})})]}),e.jsx(a,{item:!0,xs:12,md:4,children:e.jsx(h,{sx:{position:"sticky",top:24},children:e.jsxs(m,{children:[e.jsx(r,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Report Summary"}),S&&e.jsxs(ee,{severity:"info",sx:{mb:2},children:[e.jsx(r,{variant:"subtitle2",sx:{fontWeight:600},children:S.name}),e.jsx(r,{variant:"body2",children:S.description})]}),e.jsxs(A,{spacing:2,sx:{mb:3},children:[e.jsxs(o,{children:[e.jsx(r,{variant:"body2",color:"text.secondary",children:"Selected Students:"}),e.jsx(r,{variant:"h6",sx:{fontWeight:600},children:n.length})]}),e.jsxs(o,{children:[e.jsx(r,{variant:"body2",color:"text.secondary",children:"Class Filter:"}),e.jsx(r,{variant:"body1",children:x||"All Classes"})]}),e.jsxs(o,{children:[e.jsx(r,{variant:"body2",color:"text.secondary",children:"Report Period:"}),e.jsx(r,{variant:"body1",children:W||"Not selected"})]})]}),y&&e.jsxs(o,{sx:{mb:3},children:[e.jsxs(r,{variant:"body2",sx:{mb:1},children:["Generating Reports... ",T,"%"]}),e.jsx(se,{variant:"determinate",value:T})]}),e.jsxs(A,{spacing:2,children:[e.jsx(u,{variant:"contained",fullWidth:!0,startIcon:e.jsx(R,{}),onClick:E,disabled:!d||n.length===0||y,sx:{background:`linear-gradient(135deg, ${l.palette.primary.main} 0%, ${l.palette.secondary.main} 100%)`},children:y?"Generating...":"Generate Reports"}),e.jsx(u,{variant:"outlined",fullWidth:!0,startIcon:e.jsx(re,{}),disabled:!d,children:"Preview Template"}),e.jsx(u,{variant:"outlined",fullWidth:!0,startIcon:e.jsx(ae,{}),disabled:n.length===0,children:"Download All"}),e.jsx(u,{variant:"outlined",fullWidth:!0,startIcon:e.jsx(ne,{}),disabled:n.length===0,children:"Email to Parents"})]})]})})})]})]})};export{xe as default};
//# sourceMappingURL=ReportGeneration-CrX_JsK4.js.map
