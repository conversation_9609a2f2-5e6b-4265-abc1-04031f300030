/**
 * VidyaMitra Platform - Modern Login/Signup Page
 * 
 * Contemporary design with glassmorphism, smooth animations, and modern UI patterns
 * Maintains Indian educational context while providing a visually impressive experience
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  IconButton,
  InputAdornment,
  Tabs,
  Tab,
  Chip,
  Avatar,
  Stack,
  Divider,
  useTheme,
  alpha,
  Fade,
  Slide,
  Zoom,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  School,
  Person,
  Email,
  Lock,
  Google,
  Apple,
  Facebook,
  ArrowForward,
  CheckCircle,
  Star,
  TrendingUp,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';

// Animated Background Component
const AnimatedBackground = () => {
  const theme = useTheme();
  
  return (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `linear-gradient(135deg, ${theme.palette.primary.main}15 0%, ${theme.palette.secondary.main}10 100%)`,
        overflow: 'hidden',
        zIndex: -1,
      }}
    >
      {/* Floating Elements */}
      {[...Array(6)].map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, scale: 0 }}
          animate={{ 
            opacity: [0.1, 0.3, 0.1],
            scale: [1, 1.2, 1],
            x: [0, 100, 0],
            y: [0, -50, 0],
          }}
          transition={{
            duration: 8 + i * 2,
            repeat: Infinity,
            delay: i * 1.5,
          }}
          style={{
            position: 'absolute',
            top: `${20 + i * 15}%`,
            left: `${10 + i * 15}%`,
            width: 60 + i * 20,
            height: 60 + i * 20,
            borderRadius: '50%',
            background: i % 2 === 0 
              ? `linear-gradient(135deg, ${theme.palette.primary.main}20, ${theme.palette.primary.light}10)`
              : `linear-gradient(135deg, ${theme.palette.secondary.main}20, ${theme.palette.secondary.light}10)`,
            backdropFilter: 'blur(10px)',
          }}
        />
      ))}
    </Box>
  );
};

// Feature Highlight Component
const FeatureHighlight = ({ icon: Icon, title, description, delay = 0 }) => {
  const theme = useTheme();
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay, duration: 0.6 }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          p: 2,
          borderRadius: 2,
          background: alpha(theme.palette.background.paper, 0.1),
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateX(8px)',
            background: alpha(theme.palette.background.paper, 0.2),
          },
        }}
      >
        <Avatar
          sx={{
            bgcolor: theme.palette.primary.main,
            width: 48,
            height: 48,
          }}
        >
          <Icon />
        </Avatar>
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
            {title}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {description}
          </Typography>
        </Box>
      </Box>
    </motion.div>
  );
};

// Login Form Component
const LoginForm = ({ onSubmit, loading }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });

  const handleChange = (field) => (event) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSubmit = (event) => {
    event.preventDefault();
    onSubmit(formData);
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
      <Stack spacing={3}>
        <TextField
          fullWidth
          label="Email Address"
          type="email"
          value={formData.email}
          onChange={handleChange('email')}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Email color="primary" />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              backdropFilter: 'blur(10px)',
              background: alpha('#fff', 0.1),
            },
          }}
        />
        
        <TextField
          fullWidth
          label="Password"
          type={showPassword ? 'text' : 'password'}
          value={formData.password}
          onChange={handleChange('password')}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Lock color="primary" />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  onClick={() => setShowPassword(!showPassword)}
                  edge="end"
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              backdropFilter: 'blur(10px)',
              background: alpha('#fff', 0.1),
            },
          }}
        />

        <Button
          type="submit"
          fullWidth
          variant="contained"
          size="large"
          disabled={loading}
          endIcon={<ArrowForward />}
          sx={{
            py: 1.5,
            borderRadius: 2,
            background: 'linear-gradient(135deg, #2E5BA8 0%, #4A90E2 100%)',
            boxShadow: '0px 8px 24px rgba(46, 91, 168, 0.3)',
            '&:hover': {
              background: 'linear-gradient(135deg, #1E4A97 0%, #2E5BA8 100%)',
              boxShadow: '0px 12px 32px rgba(46, 91, 168, 0.4)',
              transform: 'translateY(-2px)',
            },
          }}
        >
          {loading ? 'Signing In...' : 'Sign In'}
        </Button>
      </Stack>
    </Box>
  );
};

// Signup Form Component
const SignupForm = ({ onSubmit, loading }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    role: 'teacher',
    board: 'CBSE',
  });

  const handleChange = (field) => (event) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSubmit = (event) => {
    event.preventDefault();
    onSubmit(formData);
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
      <Stack spacing={3}>
        <TextField
          fullWidth
          label="Full Name"
          value={formData.name}
          onChange={handleChange('name')}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Person color="primary" />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              backdropFilter: 'blur(10px)',
              background: alpha('#fff', 0.1),
            },
          }}
        />
        
        <TextField
          fullWidth
          label="Email Address"
          type="email"
          value={formData.email}
          onChange={handleChange('email')}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Email color="primary" />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              backdropFilter: 'blur(10px)',
              background: alpha('#fff', 0.1),
            },
          }}
        />
        
        <TextField
          fullWidth
          label="Password"
          type={showPassword ? 'text' : 'password'}
          value={formData.password}
          onChange={handleChange('password')}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Lock color="primary" />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  onClick={() => setShowPassword(!showPassword)}
                  edge="end"
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              backdropFilter: 'blur(10px)',
              background: alpha('#fff', 0.1),
            },
          }}
        />

        {/* Board Selection */}
        <Box>
          <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
            Education Board
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap">
            {['CBSE', 'ICSE', 'State Board', 'IB'].map((board) => (
              <Chip
                key={board}
                label={board}
                clickable
                color={formData.board === board ? 'primary' : 'default'}
                onClick={() => setFormData(prev => ({ ...prev, board }))}
                sx={{
                  borderRadius: 2,
                  fontWeight: 500,
                }}
              />
            ))}
          </Stack>
        </Box>

        <Button
          type="submit"
          fullWidth
          variant="contained"
          size="large"
          disabled={loading}
          endIcon={<ArrowForward />}
          sx={{
            py: 1.5,
            borderRadius: 2,
            background: 'linear-gradient(135deg, #FF9933 0%, #FFB366 100%)',
            boxShadow: '0px 8px 24px rgba(255, 153, 51, 0.3)',
            '&:hover': {
              background: 'linear-gradient(135deg, #FF8F00 0%, #FF9933 100%)',
              boxShadow: '0px 12px 32px rgba(255, 153, 51, 0.4)',
              transform: 'translateY(-2px)',
            },
          }}
        >
          {loading ? 'Creating Account...' : 'Create Account'}
        </Button>
      </Stack>
    </Box>
  );
};

// Main Modern Login Page Component
const ModernLoginPage = ({ onLogin, onSignup }) => {
  const { t } = useTranslation(['auth', 'common']);
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleLogin = async (formData) => {
    setLoading(true);
    try {
      if (onLogin) {
        await onLogin(formData);
      } else {
        // Fallback for demo
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('Login:', formData);
      }
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSignup = async (formData) => {
    setLoading(true);
    try {
      if (onSignup) {
        await onSignup(formData);
      } else {
        // Fallback for demo
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('Signup:', formData);
      }
    } catch (error) {
      console.error('Signup error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        position: 'relative',
        background: `linear-gradient(135deg, ${theme.palette.background.default} 0%, ${theme.palette.background.surface} 100%)`,
      }}
    >
      <AnimatedBackground />

      {/* Left Side - Features */}
      <Box
        sx={{
          flex: 1,
          display: { xs: 'none', md: 'flex' },
          flexDirection: 'column',
          justifyContent: 'center',
          p: 6,
          position: 'relative',
        }}
      >
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Box sx={{ mb: 6 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
              <Avatar
                sx={{
                  bgcolor: theme.palette.primary.main,
                  width: 64,
                  height: 64,
                }}
              >
                <School sx={{ fontSize: 32 }} />
              </Avatar>
              <Typography
                variant="h3"
                sx={{
                  fontWeight: 600, // Reduced from 800
                  color: theme.palette.mode === 'dark' ? '#FFFFFF' : '#1E293B', // Solid color for better visibility
                  textShadow: theme.palette.mode === 'dark'
                    ? '0 2px 8px rgba(0,0,0,0.5)'
                    : '0 2px 4px rgba(0,0,0,0.1)',
                }}
              >
                VidyaMitra
              </Typography>
            </Box>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: theme.palette.text.primary,
                mb: 2,
              }}
            >
              Empowering Indian Education with AI-Driven SWOT Analysis
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: theme.palette.text.secondary,
                fontSize: '1.1rem',
                lineHeight: 1.6,
              }}
            >
              Transform student assessment and development with our comprehensive
              platform designed specifically for Indian educational institutions.
            </Typography>
          </Box>

          <Stack spacing={3}>
            <FeatureHighlight
              icon={TrendingUp}
              title="AI-Powered Analysis"
              description="Advanced SWOT analysis using machine learning algorithms"
              delay={0.2}
            />
            <FeatureHighlight
              icon={School}
              title="Board-Specific Features"
              description="Tailored for CBSE, ICSE, and State Board curricula"
              delay={0.4}
            />
            <FeatureHighlight
              icon={CheckCircle}
              title="Comprehensive Tracking"
              description="Academic, behavioral, and extracurricular monitoring"
              delay={0.6}
            />
          </Stack>
        </motion.div>
      </Box>

      {/* Right Side - Auth Form */}
      <Box
        sx={{
          flex: { xs: 1, md: 0.6 },
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 3,
        }}
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          style={{ width: '100%', maxWidth: 480 }}
        >
          <Card
            sx={{
              p: 4,
              borderRadius: 4,
              background: alpha(theme.palette.background.paper, 0.9),
              backdropFilter: 'blur(20px)',
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              boxShadow: '0px 20px 40px rgba(46, 91, 168, 0.15)',
            }}
          >
            <CardContent sx={{ p: 0 }}>
              {/* Header */}
              <Box sx={{ textAlign: 'center', mb: 4 }}>
                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 500, // Reduced from 700
                    mb: 1,
                    color: theme.palette.text.primary, // Solid color for better visibility
                  }}
                >
                  Welcome Back
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Access your educational dashboard
                </Typography>
              </Box>

              {/* Tabs */}
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                centered
                sx={{
                  mb: 4,
                  '& .MuiTab-root': {
                    textTransform: 'none',
                    fontWeight: 600,
                    fontSize: '1rem',
                    minWidth: 120,
                  },
                  '& .MuiTabs-indicator': {
                    borderRadius: 2,
                    height: 3,
                  },
                }}
              >
                <Tab label="Sign In" />
                <Tab label="Sign Up" />
              </Tabs>

              {/* Forms */}
              <AnimatePresence mode="wait">
                {activeTab === 0 ? (
                  <motion.div
                    key="login"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <LoginForm onSubmit={handleLogin} loading={loading} />
                  </motion.div>
                ) : (
                  <motion.div
                    key="signup"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <SignupForm onSubmit={handleSignup} loading={loading} />
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Social Login */}
              <Box sx={{ mt: 4 }}>
                <Divider sx={{ mb: 3 }}>
                  <Typography variant="body2" color="text.secondary">
                    Or continue with
                  </Typography>
                </Divider>

                <Stack direction="row" spacing={2} justifyContent="center">
                  <IconButton
                    sx={{
                      border: `1px solid ${alpha(theme.palette.divider, 0.5)}`,
                      borderRadius: 2,
                      p: 1.5,
                      '&:hover': {
                        background: alpha(theme.palette.primary.main, 0.1),
                        transform: 'translateY(-2px)',
                      },
                    }}
                  >
                    <Google />
                  </IconButton>
                  <IconButton
                    sx={{
                      border: `1px solid ${alpha(theme.palette.divider, 0.5)}`,
                      borderRadius: 2,
                      p: 1.5,
                      '&:hover': {
                        background: alpha(theme.palette.primary.main, 0.1),
                        transform: 'translateY(-2px)',
                      },
                    }}
                  >
                    <Apple />
                  </IconButton>
                  <IconButton
                    sx={{
                      border: `1px solid ${alpha(theme.palette.divider, 0.5)}`,
                      borderRadius: 2,
                      p: 1.5,
                      '&:hover': {
                        background: alpha(theme.palette.primary.main, 0.1),
                        transform: 'translateY(-2px)',
                      },
                    }}
                  >
                    <Facebook />
                  </IconButton>
                </Stack>
              </Box>
            </CardContent>
          </Card>
        </motion.div>
      </Box>
    </Box>
  );
};

export default ModernLoginPage;
