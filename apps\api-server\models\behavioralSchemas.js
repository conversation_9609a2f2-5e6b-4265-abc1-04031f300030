/**
 * VidyaMitra Platform - Behavioral and Extracurricular Schemas
 * 
 * This file defines the behavioral incidents, extracurricular activities, and SWOT analysis schemas
 * for the VidyaMitra Platform based on the technical documentation.
 * 
 * Based on: docs/technical/03-data-model.md
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Behavioral Incidents Schema
 * Stores behavioral incident records
 */
const BehavioralIncidentSchema = new Schema({
  incident_id: {
    type: String,
    required: true,
    unique: true,
    match: /^INC\d{8}$/
  },
  student_id: {
    type: Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  date: {
    type: Date,
    required: true
  },
  type: {
    type: String,
    required: true,
    enum: ['positive', 'negative']
  },
  category: {
    type: String,
    enum: [
      'Academic Excellence',
      'Leadership',
      'Helping Others',
      'Creativity',
      'Sports Achievement',
      'Cultural Activity',
      'Classroom Disruption',
      'Academic Dishonesty',
      'Bullying',
      'Tardiness',
      'Uniform Violation',
      'Technology Misuse',
      'Other'
    ]
  },
  description: {
    type: String,
    required: true
  },
  severity: {
    type: String,
    enum: ['Low', 'Medium', 'High'],
    default: 'Medium'
  },
  action_taken: String,
  reported_by: {
    type: String,
    required: true,
    maxlength: 100
  },
  witnesses: [String],
  follow_up_required: {
    type: Boolean,
    default: false
  },
  follow_up_date: Date,
  parent_notified: {
    type: Boolean,
    default: false
  },
  parent_notification_date: Date,
  // Indian education specific fields
  value_education_topic: String,
  counseling_session: {
    required: {
      type: Boolean,
      default: false
    },
    completed: {
      type: Boolean,
      default: false
    },
    counselor_name: String,
    session_date: Date,
    notes: String
  }
}, {
  timestamps: true
});

BehavioralIncidentSchema.index({ student_id: 1, date: 1 });

/**
 * Behavioral Summary Schema
 * Stores quarterly behavioral summaries
 */
const BehavioralSummarySchema = new Schema({
  summary_id: {
    type: String,
    required: true,
    unique: true,
    match: /^BSUM\d{8}$/
  },
  student_id: {
    type: Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  quarter: {
    type: Number,
    required: true,
    min: 1,
    max: 4
  },
  academic_year: {
    type: String,
    required: true,
    match: /^\d{4}-\d{4}$/
  },
  positive_incidents: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  negative_incidents: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  behavior_trend: {
    type: String,
    required: true,
    enum: ['improving', 'stable', 'declining']
  },
  behavior_score: {
    type: Number,
    min: 0,
    max: 100
  },
  character_traits: [{
    trait: {
      type: String,
      enum: ['Honesty', 'Respect', 'Responsibility', 'Compassion', 'Perseverance', 'Cooperation', 'Initiative']
    },
    rating: {
      type: Number,
      min: 1,
      max: 5
    }
  }],
  disciplinary_actions: [{
    action: String,
    date: Date,
    reason: String
  }]
}, {
  timestamps: true
});

// Compound index for uniqueness
BehavioralSummarySchema.index({ student_id: 1, quarter: 1, academic_year: 1 }, { unique: true });

/**
 * Extracurricular Activities Schema
 * Stores extracurricular activity participation
 */
const ExtracurricularActivitySchema = new Schema({
  activity_id: {
    type: String,
    required: true,
    unique: true,
    match: /^ACT\d{8}$/
  },
  student_id: {
    type: Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  academic_year: {
    type: String,
    required: true,
    match: /^\d{4}-\d{4}$/
  },
  activity_name: {
    type: String,
    required: true,
    maxlength: 100
  },
  activity_type: {
    type: String,
    enum: ['Sports', 'Cultural', 'Academic', 'Community Service', 'Leadership', 'Technical', 'Arts', 'Other'],
    required: true
  },
  role: {
    type: String,
    required: true,
    maxlength: 50
  },
  hours_per_week: {
    type: Number,
    required: true,
    min: 0
  },
  advisor: {
    type: String,
    required: true,
    maxlength: 100
  },
  attendance_rate: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  start_date: Date,
  end_date: Date,
  achievements: [{
    title: String,
    description: String,
    date: Date,
    level: {
      type: String,
      enum: ['School', 'District', 'State', 'National', 'International']
    },
    position: String
  }],
  skills_developed: [String],
  leadership_roles: [{
    position: String,
    responsibilities: String,
    duration: String
  }],
  // Indian education specific fields
  nss_ncc_participation: {
    type: Boolean,
    default: false
  },
  community_service_hours: {
    type: Number,
    default: 0
  },
  cultural_program_participation: [{
    event_name: String,
    date: Date,
    role: String,
    achievement: String
  }]
}, {
  timestamps: true
});

ExtracurricularActivitySchema.index({ student_id: 1, academic_year: 1 });

/**
 * SWOT Analysis Schema
 * Stores SWOT analysis results
 */
const SWOTAnalysisSchema = new Schema({
  analysis_id: {
    type: String,
    required: true,
    unique: true,
    match: /^SWOT\d{8}$/
  },
  student_id: {
    type: Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  quarter: {
    type: Number,
    required: true,
    min: 1,
    max: 4
  },
  academic_year: {
    type: String,
    required: true,
    match: /^\d{4}-\d{4}$/
  },
  analysis_date: {
    type: Date,
    required: true,
    default: Date.now
  },
  generated_by: {
    type: String,
    enum: ['AI', 'Teacher', 'System'],
    default: 'AI'
  },
  analysis_data: {
    type: Schema.Types.Mixed,
    required: true
  },
  strengths: [{
    category: String,
    type: String,
    description: String,
    score: Number,
    evidence: [String]
  }],
  weaknesses: [{
    category: String,
    type: String,
    description: String,
    score: Number,
    evidence: [String]
  }],
  opportunities: [{
    category: String,
    type: String,
    description: String,
    priority: {
      type: String,
      enum: ['High', 'Medium', 'Low']
    }
  }],
  threats: [{
    category: String,
    type: String,
    description: String,
    severity: {
      type: String,
      enum: ['High', 'Medium', 'Low']
    }
  }],
  recommendations: [{
    target_audience: {
      type: String,
      enum: ['Student', 'Parent', 'Teacher', 'School']
    },
    action: String,
    priority: {
      type: String,
      enum: ['High', 'Medium', 'Low']
    },
    timeline: String
  }],
  overall_score: {
    type: Number,
    min: 0,
    max: 100
  },
  confidence_level: {
    type: Number,
    min: 0,
    max: 1
  }
}, {
  timestamps: true
});

// Compound index for uniqueness
SWOTAnalysisSchema.index({ student_id: 1, quarter: 1, academic_year: 1 }, { unique: true });

// Create and export models
const behavioralModels = {
  BehavioralIncident: mongoose.model('BehavioralIncident', BehavioralIncidentSchema),
  BehavioralSummary: mongoose.model('BehavioralSummary', BehavioralSummarySchema),
  ExtracurricularActivity: mongoose.model('ExtracurricularActivity', ExtracurricularActivitySchema),
  SWOTAnalysis: mongoose.model('SWOTAnalysis', SWOTAnalysisSchema)
};

module.exports = behavioralModels;
