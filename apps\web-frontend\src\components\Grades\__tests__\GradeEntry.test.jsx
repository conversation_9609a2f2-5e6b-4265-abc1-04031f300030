/**
 * VidyaMitra Platform - GradeEntry Component Tests
 * 
 * Comprehensive unit tests for grade entry with Indian grading system
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import GradeEntry from '../GradeEntry';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
  },
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

describe('GradeEntry Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders grade entry interface', () => {
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    expect(screen.getByText('Grade Entry')).toBeInTheDocument();
    expect(screen.getByText('Enter and manage student grades efficiently')).toBeInTheDocument();
  });

  test('displays class and subject selection', () => {
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    expect(screen.getByText('Select Class')).toBeInTheDocument();
    expect(screen.getByText('Select Subject')).toBeInTheDocument();
    expect(screen.getByText('Select Assessment')).toBeInTheDocument();
  });

  test('shows Indian grading system options', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Select class first
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    // Select subject
    const subjectSelect = screen.getByLabelText(/subject/i);
    await user.click(subjectSelect);
    await user.click(screen.getByText('Mathematics'));

    // Should show Indian grading options
    await waitFor(() => {
      expect(screen.getByText('CBSE Grading System')).toBeInTheDocument();
    });
  });

  test('displays student list for grade entry', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Select class and subject
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    const subjectSelect = screen.getByLabelText(/subject/i);
    await user.click(subjectSelect);
    await user.click(screen.getByText('Mathematics'));

    // Should display student list
    await waitFor(() => {
      expect(screen.getByText('Sanju Kumar Reddy')).toBeInTheDocument();
      expect(screen.getByText('Niraimathi Selvam')).toBeInTheDocument();
      expect(screen.getByText('Mahesh Reddy')).toBeInTheDocument();
      expect(screen.getByText('Ravi Teja Sharma')).toBeInTheDocument();
    });
  });

  test('allows grade entry with Indian grading system', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Setup class and subject
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    const subjectSelect = screen.getByLabelText(/subject/i);
    await user.click(subjectSelect);
    await user.click(screen.getByText('Mathematics'));

    await waitFor(() => {
      // Find grade input for first student
      const gradeInputs = screen.getAllByLabelText(/grade/i);
      expect(gradeInputs.length).toBeGreaterThan(0);
    });

    // Enter grade
    const gradeInputs = screen.getAllByLabelText(/grade/i);
    await user.click(gradeInputs[0]);

    // Should show Indian grade options
    await waitFor(() => {
      expect(screen.getByText('A1')).toBeInTheDocument();
      expect(screen.getByText('A2')).toBeInTheDocument();
      expect(screen.getByText('B1')).toBeInTheDocument();
      expect(screen.getByText('B2')).toBeInTheDocument();
      expect(screen.getByText('C1')).toBeInTheDocument();
      expect(screen.getByText('C2')).toBeInTheDocument();
    });

    await user.click(screen.getByText('A1'));

    // Should save grade
    await waitFor(() => {
      expect(screen.getByDisplayValue('A1')).toBeInTheDocument();
    });
  });

  test('supports marks entry with percentage conversion', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Setup and navigate to marks entry mode
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    // Switch to marks entry mode
    await user.click(screen.getByText('Marks Entry'));

    await waitFor(() => {
      const marksInputs = screen.getAllByLabelText(/marks/i);
      expect(marksInputs.length).toBeGreaterThan(0);
    });

    // Enter marks
    const marksInputs = screen.getAllByLabelText(/marks/i);
    await user.type(marksInputs[0], '95');

    // Should show grade conversion
    await waitFor(() => {
      expect(screen.getByText('A1')).toBeInTheDocument(); // Converted grade
      expect(screen.getByText('95%')).toBeInTheDocument(); // Percentage
    });
  });

  test('handles assessment type selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Select assessment type
    const assessmentSelect = screen.getByLabelText(/assessment/i);
    await user.click(assessmentSelect);

    await waitFor(() => {
      expect(screen.getByText('Unit Test 1')).toBeInTheDocument();
      expect(screen.getByText('Unit Test 2')).toBeInTheDocument();
      expect(screen.getByText('Mid-term Exam')).toBeInTheDocument();
      expect(screen.getByText('Final Exam')).toBeInTheDocument();
      expect(screen.getByText('Project Work')).toBeInTheDocument();
      expect(screen.getByText('Practical Exam')).toBeInTheDocument();
    });

    await user.click(screen.getByText('Mid-term Exam'));

    // Should update assessment context
    await waitFor(() => {
      expect(screen.getByText('Mid-term Exam - Mathematics')).toBeInTheDocument();
    });
  });

  test('supports bulk grade entry', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Setup class and subject
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    await waitFor(() => {
      expect(screen.getByText('Bulk Grade Entry')).toBeInTheDocument();
    });

    // Use bulk entry
    await user.click(screen.getByText('Bulk Grade Entry'));

    await waitFor(() => {
      expect(screen.getByText('Apply Grade to All')).toBeInTheDocument();
    });

    // Select grade for all
    const bulkGradeSelect = screen.getByLabelText(/bulk grade/i);
    await user.click(bulkGradeSelect);
    await user.click(screen.getByText('B1'));

    await user.click(screen.getByRole('button', { name: /apply to all/i }));

    // Should apply grade to all students
    await waitFor(() => {
      const b1Grades = screen.getAllByDisplayValue('B1');
      expect(b1Grades.length).toBeGreaterThan(1);
    });
  });

  test('validates grade entries', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Setup and try to enter invalid marks
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    await user.click(screen.getByText('Marks Entry'));

    await waitFor(() => {
      const marksInputs = screen.getAllByLabelText(/marks/i);
      expect(marksInputs.length).toBeGreaterThan(0);
    });

    // Enter invalid marks
    const marksInputs = screen.getAllByLabelText(/marks/i);
    await user.type(marksInputs[0], '150'); // Invalid - over 100

    // Should show validation error
    await waitFor(() => {
      expect(screen.getByText('Marks cannot exceed 100')).toBeInTheDocument();
    });
  });

  test('displays grade statistics', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Setup class and subject
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    await waitFor(() => {
      expect(screen.getByText('Grade Distribution')).toBeInTheDocument();
      expect(screen.getByText('Class Average')).toBeInTheDocument();
      expect(screen.getByText('Highest Score')).toBeInTheDocument();
      expect(screen.getByText('Lowest Score')).toBeInTheDocument();
    });
  });

  test('supports grade comments', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Setup and add comment
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    await waitFor(() => {
      const commentButtons = screen.getAllByLabelText(/add comment/i);
      expect(commentButtons.length).toBeGreaterThan(0);
    });

    // Add comment for student
    const commentButtons = screen.getAllByLabelText(/add comment/i);
    await user.click(commentButtons[0]);

    await waitFor(() => {
      expect(screen.getByText('Add Grade Comment')).toBeInTheDocument();
    });

    const commentInput = screen.getByLabelText(/comment/i);
    await user.type(commentInput, 'Excellent performance in algebra');
    await user.click(screen.getByRole('button', { name: /save comment/i }));

    // Should save comment
    await waitFor(() => {
      expect(screen.getByText('Comment saved successfully')).toBeInTheDocument();
    });
  });

  test('handles grade submission', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Setup and enter grades
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    const subjectSelect = screen.getByLabelText(/subject/i);
    await user.click(subjectSelect);
    await user.click(screen.getByText('Mathematics'));

    await waitFor(() => {
      const gradeInputs = screen.getAllByLabelText(/grade/i);
      user.click(gradeInputs[0]);
    });

    await user.click(screen.getByText('A1'));

    // Submit grades
    const submitButton = screen.getByRole('button', { name: /submit grades/i });
    await user.click(submitButton);

    // Should show success message
    await waitFor(() => {
      expect(screen.getByText('Grades submitted successfully')).toBeInTheDocument();
    });
  });

  test('supports grade history viewing', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Navigate to grade history
    await user.click(screen.getByText('Grade History'));

    await waitFor(() => {
      expect(screen.getByText('Previous Assessments')).toBeInTheDocument();
      expect(screen.getByText('Grade Trends')).toBeInTheDocument();
    });
  });

  test('supports accessibility features', () => {
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Check for proper heading structure
    expect(screen.getByRole('heading', { name: /grade entry/i })).toBeInTheDocument();

    // Check for proper form labels
    expect(screen.getByLabelText(/class/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/subject/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/assessment/i)).toBeInTheDocument();

    // Check for proper table structure
    expect(screen.getByRole('table')).toBeInTheDocument();
  });

  test('displays Indian subject options', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Open subject dropdown
    const subjectSelect = screen.getByLabelText(/subject/i);
    await user.click(subjectSelect);

    // Should show Indian curriculum subjects
    await waitFor(() => {
      expect(screen.getByText('Mathematics')).toBeInTheDocument();
      expect(screen.getByText('Physics')).toBeInTheDocument();
      expect(screen.getByText('Chemistry')).toBeInTheDocument();
      expect(screen.getByText('Biology')).toBeInTheDocument();
      expect(screen.getByText('English')).toBeInTheDocument();
      expect(screen.getByText('Hindi')).toBeInTheDocument();
      expect(screen.getByText('Social Science')).toBeInTheDocument();
    });
  });
});

// Integration tests
describe('GradeEntry Integration', () => {
  test('complete grade entry workflow', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Complete workflow
    // 1. Select class
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    // 2. Select subject
    const subjectSelect = screen.getByLabelText(/subject/i);
    await user.click(subjectSelect);
    await user.click(screen.getByText('Mathematics'));

    // 3. Select assessment
    const assessmentSelect = screen.getByLabelText(/assessment/i);
    await user.click(assessmentSelect);
    await user.click(screen.getByText('Mid-term Exam'));

    // 4. Enter grades for multiple students
    await waitFor(() => {
      const gradeInputs = screen.getAllByLabelText(/grade/i);
      expect(gradeInputs.length).toBeGreaterThan(0);
    });

    const gradeInputs = screen.getAllByLabelText(/grade/i);
    
    // Enter different grades
    await user.click(gradeInputs[0]);
    await user.click(screen.getByText('A1'));
    
    await user.click(gradeInputs[1]);
    await user.click(screen.getByText('A2'));

    // 5. Submit grades
    const submitButton = screen.getByRole('button', { name: /submit grades/i });
    await user.click(submitButton);

    // Should complete successfully
    await waitFor(() => {
      expect(screen.getByText('Grades submitted successfully')).toBeInTheDocument();
    });
  });

  test('grade calculation accuracy', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <GradeEntry />
      </TestWrapper>
    );

    // Setup and enter marks
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    await user.click(screen.getByText('Marks Entry'));

    await waitFor(() => {
      const marksInputs = screen.getAllByLabelText(/marks/i);
      expect(marksInputs.length).toBeGreaterThan(0);
    });

    // Enter marks and verify grade conversion
    const marksInputs = screen.getAllByLabelText(/marks/i);
    await user.type(marksInputs[0], '95');
    await user.type(marksInputs[1], '85');
    await user.type(marksInputs[2], '75');

    // Verify grade conversions
    await waitFor(() => {
      expect(screen.getByText('A1')).toBeInTheDocument(); // 95%
      expect(screen.getByText('A2')).toBeInTheDocument(); // 85%
      expect(screen.getByText('B1')).toBeInTheDocument(); // 75%
    });

    // Verify class statistics
    await waitFor(() => {
      expect(screen.getByText('85.0%')).toBeInTheDocument(); // Class average
    });
  });
});
