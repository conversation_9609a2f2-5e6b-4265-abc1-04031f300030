{"version": 3, "file": "StudentRegistration-B1REbrCi.js", "sources": ["../../src/components/Students/StudentRegistration.jsx"], "sourcesContent": ["/**\n * VidyaMitra Platform - Student Registration Component\n * \n * Comprehensive student registration form with Indian educational board support\n * Features multi-step form, validation, and cultural context\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Stepper,\n  Step,\n  StepLabel,\n  Button,\n  TextField,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormControlLabel,\n  Checkbox,\n  Avatar,\n  IconButton,\n  Alert,\n  Chip,\n  Divider,\n  Stack,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Person,\n  School,\n  ContactPhone,\n  PhotoCamera,\n  Save,\n  ArrowBack,\n  ArrowForward,\n  CheckCircle,\n} from '@mui/icons-material';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\n\n// Indian Educational Boards Configuration\nconst EDUCATIONAL_BOARDS = [\n  { value: 'CBSE', label: 'Central Board of Secondary Education (CBSE)', color: '#2E5BA8' },\n  { value: 'ICSE', label: 'Indian Certificate of Secondary Education (ICSE)', color: '#FF9933' },\n  { value: 'STATE_AP', label: 'Andhra Pradesh State Board', color: '#00C853' },\n  { value: 'STATE_TN', label: 'Tamil Nadu State Board', color: '#9C27B0' },\n  { value: 'STATE_KA', label: 'Karnataka State Board', color: '#FF5722' },\n  { value: 'STATE_TG', label: 'Telangana State Board', color: '#607D8B' },\n  { value: 'IB', label: 'International Baccalaureate (IB)', color: '#795548' },\n];\n\nconst GRADES = [\n  { value: 1, label: 'Class 1' },\n  { value: 2, label: 'Class 2' },\n  { value: 3, label: 'Class 3' },\n  { value: 4, label: 'Class 4' },\n  { value: 5, label: 'Class 5' },\n  { value: 6, label: 'Class 6' },\n  { value: 7, label: 'Class 7' },\n  { value: 8, label: 'Class 8' },\n  { value: 9, label: 'Class 9' },\n  { value: 10, label: 'Class 10' },\n  { value: 11, label: 'Class 11' },\n  { value: 12, label: 'Class 12' },\n];\n\nconst SECTIONS = ['A', 'B', 'C', 'D', 'E', 'F'];\n\nconst LANGUAGES = [\n  { value: 'en', label: 'English' },\n  { value: 'hi', label: 'हिन्दी (Hindi)' },\n  { value: 'te', label: 'తెలుగు (Telugu)' },\n  { value: 'ta', label: 'தமிழ் (Tamil)' },\n  { value: 'kn', label: 'ಕನ್ನಡ (Kannada)' },\n  { value: 'ml', label: 'മലയാളം (Malayalam)' },\n];\n\nconst StudentRegistration = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const { t } = useTranslation(['common', 'students']);\n  \n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [profilePhoto, setProfilePhoto] = useState(null);\n  \n  const [formData, setFormData] = useState({\n    // Basic Information\n    firstName: '',\n    middleName: '',\n    lastName: '',\n    dateOfBirth: '',\n    gender: '',\n    bloodGroup: '',\n    \n    // Academic Information\n    admissionNumber: '',\n    grade: '',\n    section: '',\n    board: '',\n    academicYear: '2024-2025',\n    rollNumber: '',\n    \n    // Contact Information\n    address: '',\n    city: '',\n    state: '',\n    pincode: '',\n    phone: '',\n    email: '',\n    \n    // Parent/Guardian Information\n    fatherName: '',\n    fatherOccupation: '',\n    fatherPhone: '',\n    motherName: '',\n    motherOccupation: '',\n    motherPhone: '',\n    guardianName: '',\n    guardianRelation: '',\n    guardianPhone: '',\n    \n    // Emergency Contact\n    emergencyContactName: '',\n    emergencyContactPhone: '',\n    emergencyContactRelation: '',\n    \n    // Preferences\n    preferredLanguage: 'en',\n    specialNeeds: '',\n    medicalConditions: '',\n    previousSchool: '',\n    \n    // Consent\n    dataConsent: false,\n    communicationConsent: false,\n  });\n\n  const steps = [\n    {\n      label: 'Basic Information',\n      icon: Person,\n      description: 'Student personal details',\n    },\n    {\n      label: 'Academic Details',\n      icon: School,\n      description: 'Educational information',\n    },\n    {\n      label: 'Contact Information',\n      icon: ContactPhone,\n      description: 'Address and contacts',\n    },\n    {\n      label: 'Review & Submit',\n      icon: CheckCircle,\n      description: 'Confirm details',\n    },\n  ];\n\n  const handleInputChange = (field) => (event) => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n\n  const handlePhotoUpload = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setProfilePhoto(e.target.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const validateStep = (step) => {\n    const newErrors = {};\n    \n    switch (step) {\n      case 0: // Basic Information\n        if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';\n        if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';\n        if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date of birth is required';\n        if (!formData.gender) newErrors.gender = 'Gender is required';\n        break;\n        \n      case 1: // Academic Details\n        if (!formData.admissionNumber.trim()) newErrors.admissionNumber = 'Admission number is required';\n        if (!formData.grade) newErrors.grade = 'Grade is required';\n        if (!formData.section) newErrors.section = 'Section is required';\n        if (!formData.board) newErrors.board = 'Educational board is required';\n        break;\n        \n      case 2: // Contact Information\n        if (!formData.address.trim()) newErrors.address = 'Address is required';\n        if (!formData.city.trim()) newErrors.city = 'City is required';\n        if (!formData.state.trim()) newErrors.state = 'State is required';\n        if (!formData.pincode.trim()) newErrors.pincode = 'Pincode is required';\n        if (!formData.fatherName.trim()) newErrors.fatherName = 'Father name is required';\n        if (!formData.motherName.trim()) newErrors.motherName = 'Mother name is required';\n        if (!formData.emergencyContactName.trim()) newErrors.emergencyContactName = 'Emergency contact is required';\n        if (!formData.emergencyContactPhone.trim()) newErrors.emergencyContactPhone = 'Emergency contact phone is required';\n        break;\n        \n      case 3: // Review & Submit\n        if (!formData.dataConsent) newErrors.dataConsent = 'Data consent is required';\n        break;\n    }\n    \n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleNext = () => {\n    if (validateStep(activeStep)) {\n      setActiveStep(prev => prev + 1);\n    }\n  };\n\n  const handleBack = () => {\n    setActiveStep(prev => prev - 1);\n  };\n\n  const handleSubmit = async () => {\n    if (!validateStep(activeStep)) return;\n    \n    setLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      // Navigate to student profile or success page\n      navigate('/dashboard/students');\n    } catch (error) {\n      console.error('Registration failed:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Box sx={{ mb: 4 }}>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              fontWeight: 600,\n              mb: 1,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n            }}\n          >\n            Student Registration\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Register a new student with comprehensive information for SWOT analysis\n          </Typography>\n        </Box>\n      </motion.div>\n\n      {/* Stepper */}\n      <Card sx={{ mb: 4, overflow: 'visible' }}>\n        <CardContent>\n          <Stepper activeStep={activeStep} alternativeLabel>\n            {steps.map((step, index) => (\n              <Step key={step.label}>\n                <StepLabel\n                  StepIconComponent={({ active, completed }) => (\n                    <Box\n                      sx={{\n                        width: 48,\n                        height: 48,\n                        borderRadius: '50%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        background: completed\n                          ? `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`\n                          : active\n                          ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`\n                          : alpha(theme.palette.action.disabled, 0.12),\n                        color: completed || active ? 'white' : theme.palette.action.disabled,\n                        transition: 'all 0.3s ease',\n                      }}\n                    >\n                      <step.icon sx={{ fontSize: 24 }} />\n                    </Box>\n                  )}\n                >\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 500 }}>\n                    {step.label}\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    {step.description}\n                  </Typography>\n                </StepLabel>\n              </Step>\n            ))}\n          </Stepper>\n        </CardContent>\n      </Card>\n\n      {/* Form Content */}\n      <Card>\n        <CardContent sx={{ p: 4 }}>\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={activeStep}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -20 }}\n              transition={{ duration: 0.3 }}\n            >\n              {/* Step content will be rendered here */}\n              {activeStep === 0 && (\n                <BasicInformationStep\n                  formData={formData}\n                  errors={errors}\n                  handleInputChange={handleInputChange}\n                  profilePhoto={profilePhoto}\n                  handlePhotoUpload={handlePhotoUpload}\n                />\n              )}\n              {activeStep === 1 && (\n                <AcademicDetailsStep\n                  formData={formData}\n                  errors={errors}\n                  handleInputChange={handleInputChange}\n                />\n              )}\n              {activeStep === 2 && (\n                <ContactInformationStep\n                  formData={formData}\n                  errors={errors}\n                  handleInputChange={handleInputChange}\n                />\n              )}\n              {activeStep === 3 && (\n                <ReviewSubmitStep\n                  formData={formData}\n                  errors={errors}\n                  handleInputChange={handleInputChange}\n                  profilePhoto={profilePhoto}\n                />\n              )}\n            </motion.div>\n          </AnimatePresence>\n\n          {/* Navigation Buttons */}\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\n            <Button\n              onClick={handleBack}\n              disabled={activeStep === 0}\n              startIcon={<ArrowBack />}\n              variant=\"outlined\"\n            >\n              Back\n            </Button>\n            \n            <Button\n              onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}\n              endIcon={activeStep === steps.length - 1 ? <Save /> : <ArrowForward />}\n              variant=\"contained\"\n              loading={loading}\n            >\n              {activeStep === steps.length - 1 ? 'Register Student' : 'Next'}\n            </Button>\n          </Box>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\n// Step Components\nconst BasicInformationStep = ({ formData, errors, handleInputChange, profilePhoto, handlePhotoUpload }) => {\n  const theme = useTheme();\n\n  return (\n    <Box>\n      <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\n        Basic Information\n      </Typography>\n\n      {/* Profile Photo Upload */}\n      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>\n        <Box sx={{ position: 'relative' }}>\n          <Avatar\n            src={profilePhoto}\n            sx={{\n              width: 120,\n              height: 120,\n              border: `4px solid ${alpha(theme.palette.primary.main, 0.2)}`,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n            }}\n          >\n            <Person sx={{ fontSize: 60 }} />\n          </Avatar>\n          <IconButton\n            component=\"label\"\n            sx={{\n              position: 'absolute',\n              bottom: 0,\n              right: 0,\n              background: theme.palette.primary.main,\n              color: 'white',\n              '&:hover': {\n                background: theme.palette.primary.dark,\n              },\n            }}\n          >\n            <PhotoCamera />\n            <input\n              type=\"file\"\n              hidden\n              accept=\"image/*\"\n              onChange={handlePhotoUpload}\n            />\n          </IconButton>\n        </Box>\n      </Box>\n\n      <Grid container spacing={3}>\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"First Name *\"\n            value={formData.firstName}\n            onChange={handleInputChange('firstName')}\n            error={!!errors.firstName}\n            helperText={errors.firstName}\n            placeholder=\"e.g., Sanju\"\n          />\n        </Grid>\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"Middle Name\"\n            value={formData.middleName}\n            onChange={handleInputChange('middleName')}\n            placeholder=\"e.g., Kumar\"\n          />\n        </Grid>\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"Last Name *\"\n            value={formData.lastName}\n            onChange={handleInputChange('lastName')}\n            error={!!errors.lastName}\n            helperText={errors.lastName}\n            placeholder=\"e.g., Reddy\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Date of Birth *\"\n            type=\"date\"\n            value={formData.dateOfBirth}\n            onChange={handleInputChange('dateOfBirth')}\n            error={!!errors.dateOfBirth}\n            helperText={errors.dateOfBirth}\n            InputLabelProps={{ shrink: true }}\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <FormControl fullWidth error={!!errors.gender}>\n            <InputLabel>Gender *</InputLabel>\n            <Select\n              value={formData.gender}\n              onChange={handleInputChange('gender')}\n              label=\"Gender *\"\n            >\n              <MenuItem value=\"Male\">Male</MenuItem>\n              <MenuItem value=\"Female\">Female</MenuItem>\n              <MenuItem value=\"Other\">Other</MenuItem>\n            </Select>\n          </FormControl>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <FormControl fullWidth>\n            <InputLabel>Blood Group</InputLabel>\n            <Select\n              value={formData.bloodGroup}\n              onChange={handleInputChange('bloodGroup')}\n              label=\"Blood Group\"\n            >\n              <MenuItem value=\"A+\">A+</MenuItem>\n              <MenuItem value=\"A-\">A-</MenuItem>\n              <MenuItem value=\"B+\">B+</MenuItem>\n              <MenuItem value=\"B-\">B-</MenuItem>\n              <MenuItem value=\"AB+\">AB+</MenuItem>\n              <MenuItem value=\"AB-\">AB-</MenuItem>\n              <MenuItem value=\"O+\">O+</MenuItem>\n              <MenuItem value=\"O-\">O-</MenuItem>\n            </Select>\n          </FormControl>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <FormControl fullWidth>\n            <InputLabel>Preferred Language</InputLabel>\n            <Select\n              value={formData.preferredLanguage}\n              onChange={handleInputChange('preferredLanguage')}\n              label=\"Preferred Language\"\n            >\n              {LANGUAGES.map((lang) => (\n                <MenuItem key={lang.value} value={lang.value}>\n                  {lang.label}\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nconst AcademicDetailsStep = ({ formData, errors, handleInputChange }) => {\n  const theme = useTheme();\n\n  return (\n    <Box>\n      <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\n        Academic Information\n      </Typography>\n\n      <Grid container spacing={3}>\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Admission Number *\"\n            value={formData.admissionNumber}\n            onChange={handleInputChange('admissionNumber')}\n            error={!!errors.admissionNumber}\n            helperText={errors.admissionNumber}\n            placeholder=\"e.g., VMS2024001\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Roll Number\"\n            value={formData.rollNumber}\n            onChange={handleInputChange('rollNumber')}\n            placeholder=\"e.g., 15\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <FormControl fullWidth error={!!errors.grade}>\n            <InputLabel>Grade/Class *</InputLabel>\n            <Select\n              value={formData.grade}\n              onChange={handleInputChange('grade')}\n              label=\"Grade/Class *\"\n            >\n              {GRADES.map((grade) => (\n                <MenuItem key={grade.value} value={grade.value}>\n                  {grade.label}\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <FormControl fullWidth error={!!errors.section}>\n            <InputLabel>Section *</InputLabel>\n            <Select\n              value={formData.section}\n              onChange={handleInputChange('section')}\n              label=\"Section *\"\n            >\n              {SECTIONS.map((section) => (\n                <MenuItem key={section} value={section}>\n                  Section {section}\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"Academic Year\"\n            value={formData.academicYear}\n            onChange={handleInputChange('academicYear')}\n            placeholder=\"2024-2025\"\n          />\n        </Grid>\n\n        <Grid item xs={12}>\n          <FormControl fullWidth error={!!errors.board}>\n            <InputLabel>Educational Board *</InputLabel>\n            <Select\n              value={formData.board}\n              onChange={handleInputChange('board')}\n              label=\"Educational Board *\"\n            >\n              {EDUCATIONAL_BOARDS.map((board) => (\n                <MenuItem key={board.value} value={board.value}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <Chip\n                      size=\"small\"\n                      sx={{\n                        backgroundColor: board.color,\n                        color: 'white',\n                        minWidth: 60,\n                      }}\n                      label={board.value}\n                    />\n                    {board.label}\n                  </Box>\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n        </Grid>\n\n        <Grid item xs={12}>\n          <TextField\n            fullWidth\n            label=\"Previous School\"\n            value={formData.previousSchool}\n            onChange={handleInputChange('previousSchool')}\n            placeholder=\"Name of previous school (if applicable)\"\n            multiline\n            rows={2}\n          />\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nconst ContactInformationStep = ({ formData, errors, handleInputChange }) => {\n  return (\n    <Box>\n      <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\n        Contact Information\n      </Typography>\n\n      {/* Address Information */}\n      <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 500, color: 'primary.main' }}>\n        Address Details\n      </Typography>\n\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12}>\n          <TextField\n            fullWidth\n            label=\"Address *\"\n            value={formData.address}\n            onChange={handleInputChange('address')}\n            error={!!errors.address}\n            helperText={errors.address}\n            multiline\n            rows={3}\n            placeholder=\"Complete address with house number, street, area\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"City *\"\n            value={formData.city}\n            onChange={handleInputChange('city')}\n            error={!!errors.city}\n            helperText={errors.city}\n            placeholder=\"e.g., Hyderabad\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"State *\"\n            value={formData.state}\n            onChange={handleInputChange('state')}\n            error={!!errors.state}\n            helperText={errors.state}\n            placeholder=\"e.g., Telangana\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"Pincode *\"\n            value={formData.pincode}\n            onChange={handleInputChange('pincode')}\n            error={!!errors.pincode}\n            helperText={errors.pincode}\n            placeholder=\"e.g., 500001\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Phone Number\"\n            value={formData.phone}\n            onChange={handleInputChange('phone')}\n            placeholder=\"+91 9876543210\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Email Address\"\n            type=\"email\"\n            value={formData.email}\n            onChange={handleInputChange('email')}\n            placeholder=\"<EMAIL>\"\n          />\n        </Grid>\n      </Grid>\n\n      <Divider sx={{ my: 3 }} />\n\n      {/* Parent/Guardian Information */}\n      <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 500, color: 'primary.main' }}>\n        Parent/Guardian Information\n      </Typography>\n\n      <Grid container spacing={3}>\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Father's Name *\"\n            value={formData.fatherName}\n            onChange={handleInputChange('fatherName')}\n            error={!!errors.fatherName}\n            helperText={errors.fatherName}\n            placeholder=\"Father's full name\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Father's Occupation\"\n            value={formData.fatherOccupation}\n            onChange={handleInputChange('fatherOccupation')}\n            placeholder=\"e.g., Software Engineer\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Father's Phone\"\n            value={formData.fatherPhone}\n            onChange={handleInputChange('fatherPhone')}\n            placeholder=\"+91 9876543210\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Mother's Name *\"\n            value={formData.motherName}\n            onChange={handleInputChange('motherName')}\n            error={!!errors.motherName}\n            helperText={errors.motherName}\n            placeholder=\"Mother's full name\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Mother's Occupation\"\n            value={formData.motherOccupation}\n            onChange={handleInputChange('motherOccupation')}\n            placeholder=\"e.g., Teacher\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"Mother's Phone\"\n            value={formData.motherPhone}\n            onChange={handleInputChange('motherPhone')}\n            placeholder=\"+91 9876543210\"\n          />\n        </Grid>\n      </Grid>\n\n      <Divider sx={{ my: 3 }} />\n\n      {/* Emergency Contact */}\n      <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 500, color: 'primary.main' }}>\n        Emergency Contact\n      </Typography>\n\n      <Grid container spacing={3}>\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"Emergency Contact Name *\"\n            value={formData.emergencyContactName}\n            onChange={handleInputChange('emergencyContactName')}\n            error={!!errors.emergencyContactName}\n            helperText={errors.emergencyContactName}\n            placeholder=\"Contact person name\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"Emergency Contact Phone *\"\n            value={formData.emergencyContactPhone}\n            onChange={handleInputChange('emergencyContactPhone')}\n            error={!!errors.emergencyContactPhone}\n            helperText={errors.emergencyContactPhone}\n            placeholder=\"+91 9876543210\"\n          />\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            label=\"Relation\"\n            value={formData.emergencyContactRelation}\n            onChange={handleInputChange('emergencyContactRelation')}\n            placeholder=\"e.g., Uncle, Aunt\"\n          />\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nconst ReviewSubmitStep = ({ formData, errors, handleInputChange, profilePhoto }) => {\n  const theme = useTheme();\n\n  const selectedBoard = EDUCATIONAL_BOARDS.find(board => board.value === formData.board);\n  const selectedGrade = GRADES.find(grade => grade.value === formData.grade);\n\n  return (\n    <Box>\n      <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\n        Review & Submit\n      </Typography>\n\n      <Alert severity=\"info\" sx={{ mb: 3 }}>\n        Please review all information carefully before submitting. You can go back to make changes if needed.\n      </Alert>\n\n      {/* Student Summary Card */}\n      <Card sx={{ mb: 3, background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)` }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, mb: 3 }}>\n            <Avatar\n              src={profilePhoto}\n              sx={{\n                width: 80,\n                height: 80,\n                border: `3px solid ${theme.palette.primary.main}`,\n              }}\n            >\n              <Person sx={{ fontSize: 40 }} />\n            </Avatar>\n            <Box>\n              <Typography variant=\"h5\" sx={{ fontWeight: 600 }}>\n                {formData.firstName} {formData.middleName} {formData.lastName}\n              </Typography>\n              <Typography variant=\"body1\" color=\"text.secondary\">\n                {selectedGrade?.label} - Section {formData.section}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Admission No: {formData.admissionNumber}\n              </Typography>\n            </Box>\n          </Box>\n\n          {selectedBoard && (\n            <Chip\n              label={selectedBoard.label}\n              sx={{\n                backgroundColor: selectedBoard.color,\n                color: 'white',\n                fontWeight: 500,\n              }}\n            />\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Information Summary */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Personal Information\n              </Typography>\n              <Stack spacing={1}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Date of Birth:</Typography>\n                  <Typography variant=\"body2\">{formData.dateOfBirth}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Gender:</Typography>\n                  <Typography variant=\"body2\">{formData.gender}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Blood Group:</Typography>\n                  <Typography variant=\"body2\">{formData.bloodGroup || 'Not specified'}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Preferred Language:</Typography>\n                  <Typography variant=\"body2\">\n                    {LANGUAGES.find(lang => lang.value === formData.preferredLanguage)?.label}\n                  </Typography>\n                </Box>\n              </Stack>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Contact Information\n              </Typography>\n              <Stack spacing={1}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">City:</Typography>\n                  <Typography variant=\"body2\">{formData.city}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">State:</Typography>\n                  <Typography variant=\"body2\">{formData.state}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Father's Name:</Typography>\n                  <Typography variant=\"body2\">{formData.fatherName}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Mother's Name:</Typography>\n                  <Typography variant=\"body2\">{formData.motherName}</Typography>\n                </Box>\n              </Stack>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Consent Checkboxes */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n            Consent & Permissions\n          </Typography>\n\n          <Stack spacing={2}>\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={formData.dataConsent}\n                  onChange={handleInputChange('dataConsent')}\n                  color=\"primary\"\n                />\n              }\n              label={\n                <Typography variant=\"body2\">\n                  I consent to the collection and processing of student data for educational purposes and SWOT analysis. *\n                </Typography>\n              }\n            />\n            {errors.dataConsent && (\n              <Typography variant=\"caption\" color=\"error\">\n                {errors.dataConsent}\n              </Typography>\n            )}\n\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={formData.communicationConsent}\n                  onChange={handleInputChange('communicationConsent')}\n                  color=\"primary\"\n                />\n              }\n              label={\n                <Typography variant=\"body2\">\n                  I consent to receive communications about student progress, events, and important updates.\n                </Typography>\n              }\n            />\n\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              <Typography variant=\"body2\">\n                By submitting this form, you confirm that all information provided is accurate and complete.\n                The student data will be used for educational assessment, SWOT analysis, and academic progress tracking.\n              </Typography>\n            </Alert>\n          </Stack>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default StudentRegistration;\n"], "names": ["EDUCATIONAL_BOARDS", "GRADES", "SECTIONS", "LANGUAGES", "StudentRegistration", "theme", "useTheme", "navigate", "useNavigate", "t", "useTranslation", "activeStep", "setActiveStep", "useState", "loading", "setLoading", "errors", "setErrors", "profilePhoto", "setProfilePhoto", "formData", "setFormData", "steps", "Person", "School", "ContactPhone", "CheckCircle", "handleInputChange", "field", "event", "value", "prev", "handlePhotoUpload", "file", "reader", "e", "validateStep", "step", "newErrors", "handleNext", "handleBack", "handleSubmit", "resolve", "error", "jsxs", "Box", "jsx", "motion", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Stepper", "index", "Step", "<PERSON><PERSON><PERSON><PERSON>", "active", "completed", "alpha", "AnimatePresence", "BasicInformationStep", "AcademicDetailsStep", "ContactInformationStep", "ReviewSubmitStep", "<PERSON><PERSON>", "ArrowBack", "Save", "ArrowForward", "Avatar", "IconButton", "PhotoCamera", "Grid", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "lang", "grade", "section", "board", "Chip", "Divider", "selectedBoard", "selected<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "_a", "FormControlLabel", "Checkbox"], "mappings": "maAiDA,MAAMA,EAAqB,CACzB,CAAE,MAAO,OAAQ,MAAO,8CAA+C,MAAO,SAAU,EACxF,CAAE,MAAO,OAAQ,MAAO,mDAAoD,MAAO,SAAU,EAC7F,CAAE,MAAO,WAAY,MAAO,6BAA8B,MAAO,SAAU,EAC3E,CAAE,MAAO,WAAY,MAAO,yBAA0B,MAAO,SAAU,EACvE,CAAE,MAAO,WAAY,MAAO,wBAAyB,MAAO,SAAU,EACtE,CAAE,MAAO,WAAY,MAAO,wBAAyB,MAAO,SAAU,EACtE,CAAE,MAAO,KAAM,MAAO,mCAAoC,MAAO,SAAU,CAC7E,EAEMC,EAAS,CACb,CAAE,MAAO,EAAG,MAAO,SAAU,EAC7B,CAAE,MAAO,EAAG,MAAO,SAAU,EAC7B,CAAE,MAAO,EAAG,MAAO,SAAU,EAC7B,CAAE,MAAO,EAAG,MAAO,SAAU,EAC7B,CAAE,MAAO,EAAG,MAAO,SAAU,EAC7B,CAAE,MAAO,EAAG,MAAO,SAAU,EAC7B,CAAE,MAAO,EAAG,MAAO,SAAU,EAC7B,CAAE,MAAO,EAAG,MAAO,SAAU,EAC7B,CAAE,MAAO,EAAG,MAAO,SAAU,EAC7B,CAAE,MAAO,GAAI,MAAO,UAAW,EAC/B,CAAE,MAAO,GAAI,MAAO,UAAW,EAC/B,CAAE,MAAO,GAAI,MAAO,UAAW,CACjC,EAEMC,GAAW,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,GAAG,EAExCC,EAAY,CAChB,CAAE,MAAO,KAAM,MAAO,SAAU,EAChC,CAAE,MAAO,KAAM,MAAO,gBAAiB,EACvC,CAAE,MAAO,KAAM,MAAO,iBAAkB,EACxC,CAAE,MAAO,KAAM,MAAO,eAAgB,EACtC,CAAE,MAAO,KAAM,MAAO,iBAAkB,EACxC,CAAE,MAAO,KAAM,MAAO,oBAAqB,CAC7C,EAEMC,GAAsB,IAAM,CAChC,MAAMC,EAAQC,EAAS,EACjBC,EAAWC,GAAY,EACvB,CAAE,EAAAC,CAAE,EAAIC,GAAe,CAAC,SAAU,UAAU,CAAC,EAE7C,CAACC,EAAYC,CAAa,EAAIC,EAAAA,SAAS,CAAC,EACxC,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAK,EACtC,CAACG,EAAQC,CAAS,EAAIJ,EAAAA,SAAS,CAAA,CAAE,EACjC,CAACK,EAAcC,CAAe,EAAIN,EAAAA,SAAS,IAAI,EAE/C,CAACO,EAAUC,CAAW,EAAIR,WAAS,CAEvC,UAAW,GACX,WAAY,GACZ,SAAU,GACV,YAAa,GACb,OAAQ,GACR,WAAY,GAGZ,gBAAiB,GACjB,MAAO,GACP,QAAS,GACT,MAAO,GACP,aAAc,YACd,WAAY,GAGZ,QAAS,GACT,KAAM,GACN,MAAO,GACP,QAAS,GACT,MAAO,GACP,MAAO,GAGP,WAAY,GACZ,iBAAkB,GAClB,YAAa,GACb,WAAY,GACZ,iBAAkB,GAClB,YAAa,GACb,aAAc,GACd,iBAAkB,GAClB,cAAe,GAGf,qBAAsB,GACtB,sBAAuB,GACvB,yBAA0B,GAG1B,kBAAmB,KACnB,aAAc,GACd,kBAAmB,GACnB,eAAgB,GAGhB,YAAa,GACb,qBAAsB,EAAA,CACvB,EAEKS,EAAQ,CACZ,CACE,MAAO,oBACP,KAAMC,EACN,YAAa,0BACf,EACA,CACE,MAAO,mBACP,KAAMC,EACN,YAAa,yBACf,EACA,CACE,MAAO,sBACP,KAAMC,EACN,YAAa,sBACf,EACA,CACE,MAAO,kBACP,KAAMC,GACN,YAAa,iBAAA,CAEjB,EAEMC,EAAqBC,GAAWC,GAAU,CACxC,MAAAC,EAAQD,EAAM,OAAO,OAAS,WAAaA,EAAM,OAAO,QAAUA,EAAM,OAAO,MACrFR,EAAqBU,IAAA,CACnB,GAAGA,EACH,CAACH,CAAK,EAAGE,CAAA,EACT,EAGEd,EAAOY,CAAK,GACdX,EAAmBc,IAAA,CACjB,GAAGA,EACH,CAACH,CAAK,EAAG,IAAA,EACT,CAEN,EAEMI,EAAqBH,GAAU,CACnC,MAAMI,EAAOJ,EAAM,OAAO,MAAM,CAAC,EACjC,GAAII,EAAM,CACF,MAAAC,EAAS,IAAI,WACZA,EAAA,OAAUC,GAAM,CACLhB,EAAAgB,EAAE,OAAO,MAAM,CACjC,EACAD,EAAO,cAAcD,CAAI,CAAA,CAE7B,EAEMG,EAAgBC,GAAS,CAC7B,MAAMC,EAAY,CAAC,EAEnB,OAAQD,EAAM,CACZ,IAAK,GACEjB,EAAS,UAAU,KAAK,MAAa,UAAY,0BACjDA,EAAS,SAAS,KAAK,MAAa,SAAW,yBAC/CA,EAAS,cAAakB,EAAU,YAAc,6BAC9ClB,EAAS,SAAQkB,EAAU,OAAS,sBACzC,MAEF,IAAK,GACElB,EAAS,gBAAgB,KAAK,MAAa,gBAAkB,gCAC7DA,EAAS,QAAOkB,EAAU,MAAQ,qBAClClB,EAAS,UAASkB,EAAU,QAAU,uBACtClB,EAAS,QAAOkB,EAAU,MAAQ,iCACvC,MAEF,IAAK,GACElB,EAAS,QAAQ,KAAK,MAAa,QAAU,uBAC7CA,EAAS,KAAK,KAAK,MAAa,KAAO,oBACvCA,EAAS,MAAM,KAAK,MAAa,MAAQ,qBACzCA,EAAS,QAAQ,KAAK,MAAa,QAAU,uBAC7CA,EAAS,WAAW,KAAK,MAAa,WAAa,2BACnDA,EAAS,WAAW,KAAK,MAAa,WAAa,2BACnDA,EAAS,qBAAqB,KAAK,MAAa,qBAAuB,iCACvEA,EAAS,sBAAsB,KAAK,MAAa,sBAAwB,uCAC9E,MAEF,IAAK,GACEA,EAAS,cAAakB,EAAU,YAAc,4BACnD,KAAA,CAGJ,OAAArB,EAAUqB,CAAS,EACZ,OAAO,KAAKA,CAAS,EAAE,SAAW,CAC3C,EAEMC,EAAa,IAAM,CACnBH,EAAazB,CAAU,GACXC,EAAAmB,GAAQA,EAAO,CAAC,CAElC,EAEMS,EAAa,IAAM,CACT5B,EAAAmB,GAAQA,EAAO,CAAC,CAChC,EAEMU,EAAe,SAAY,CAC3B,GAACL,EAAazB,CAAU,EAE5B,CAAAI,EAAW,EAAI,EACX,GAAA,CAEF,MAAM,IAAI,QAAQ2B,GAAW,WAAWA,EAAS,GAAI,CAAC,EAGtDnC,EAAS,qBAAqB,QACvBoC,EAAO,CACN,QAAA,MAAM,uBAAwBA,CAAK,CAAA,QAC3C,CACA5B,EAAW,EAAK,CAAA,EAEpB,EAGE,OAAA6B,OAACC,EAAI,CAAA,GAAI,CAAE,SAAU,KAAM,GAAI,OAAQ,EAAG,CAAA,EAExC,SAAA,CAAAC,EAAA,IAACC,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,GAAI,EAC9B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,EAAI,EAE5B,gBAACF,EAAI,CAAA,GAAI,CAAE,GAAI,CACb,EAAA,SAAA,CAAAC,EAAA,IAACE,EAAA,CACC,QAAQ,KACR,GAAI,CACF,WAAY,IACZ,GAAI,EACJ,WAAY,2BAA2B3C,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,SACrG,qBAAsB,OACtB,oBAAqB,cACrB,eAAgB,MAClB,EACD,SAAA,sBAAA,CAED,QACC2C,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAEnD,yEAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAGAF,EAAAA,IAACG,GAAK,GAAI,CAAE,GAAI,EAAG,SAAU,SAAU,EACrC,SAACH,EAAA,IAAAI,EAAA,CACC,eAACC,EAAQ,CAAA,WAAAxC,EAAwB,iBAAgB,GAC9C,SAAAW,EAAM,IAAI,CAACe,EAAMe,IAChBN,EAAAA,IAACO,GACC,CAAA,SAAAT,EAAA,KAACU,GAAA,CACC,kBAAmB,CAAC,CAAE,OAAAC,EAAQ,UAAAC,CAC5B,IAAAV,EAAA,IAACD,EAAA,CACC,GAAI,CACF,MAAO,GACP,OAAQ,GACR,aAAc,MACd,QAAS,OACT,WAAY,SACZ,eAAgB,SAChB,WAAYW,EACR,2BAA2BnD,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,QAAQ,IAAI,SACvFkD,EACA,2BAA2BlD,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,SACzFoD,EAAMpD,EAAM,QAAQ,OAAO,SAAU,GAAI,EAC7C,MAAOmD,GAAaD,EAAS,QAAUlD,EAAM,QAAQ,OAAO,SAC5D,WAAY,eACd,EAEA,SAAAyC,EAAAA,IAACT,EAAK,KAAL,CAAU,GAAI,CAAE,SAAU,GAAM,CAAA,CAAA,CACnC,EAGF,SAAA,CAACS,EAAAA,IAAAE,EAAA,CAAW,QAAQ,YAAY,GAAI,CAAE,WAAY,GAAA,EAC/C,SAAAX,EAAK,KACR,CAAA,QACCW,EAAW,CAAA,QAAQ,UAAU,MAAM,iBACjC,WAAK,WACR,CAAA,CAAA,CAAA,CAAA,GA7BOX,EAAK,KA+BhB,CACD,CAAA,CACH,EACF,CACF,CAAA,EAGAS,EAAAA,IAACG,GACC,SAACL,OAAAM,EAAA,CAAY,GAAI,CAAE,EAAG,GACpB,SAAA,CAACJ,EAAAA,IAAAY,GAAA,CAAgB,KAAK,OACpB,SAAAd,EAAA,KAACG,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,KAAM,CAAE,QAAS,EAAG,EAAG,GAAI,EAC3B,WAAY,CAAE,SAAU,EAAI,EAG3B,SAAA,CAAApC,IAAe,GACdmC,EAAA,IAACa,GAAA,CACC,SAAAvC,EACA,OAAAJ,EACA,kBAAAW,EACA,aAAAT,EACA,kBAAAc,CAAA,CACF,EAEDrB,IAAe,GACdmC,EAAA,IAACc,GAAA,CACC,SAAAxC,EACA,OAAAJ,EACA,kBAAAW,CAAA,CACF,EAEDhB,IAAe,GACdmC,EAAA,IAACe,GAAA,CACC,SAAAzC,EACA,OAAAJ,EACA,kBAAAW,CAAA,CACF,EAEDhB,IAAe,GACdmC,EAAA,IAACgB,GAAA,CACC,SAAA1C,EACA,OAAAJ,EACA,kBAAAW,EACA,aAAAT,CAAA,CAAA,CACF,CAAA,EApCGP,CAAA,EAuCT,EAGAiC,EAAAA,KAACC,EAAI,CAAA,GAAI,CAAE,QAAS,OAAQ,eAAgB,gBAAiB,GAAI,CAAA,EAC/D,SAAA,CAAAC,EAAA,IAACiB,EAAA,CACC,QAASvB,EACT,SAAU7B,IAAe,EACzB,gBAAYqD,GAAU,EAAA,EACtB,QAAQ,WACT,SAAA,MAAA,CAED,EAEAlB,EAAA,IAACiB,EAAA,CACC,QAASpD,IAAeW,EAAM,OAAS,EAAImB,EAAeF,EAC1D,QAAS5B,IAAeW,EAAM,OAAS,EAAKwB,EAAAA,IAAAmB,GAAA,EAAK,EAAKnB,MAACoB,GAAa,CAAA,CAAA,EACpE,QAAQ,YACR,QAAApD,EAEC,SAAeH,IAAAW,EAAM,OAAS,EAAI,mBAAqB,MAAA,CAAA,CAC1D,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ,EAGMqC,GAAuB,CAAC,CAAE,SAAAvC,EAAU,OAAAJ,EAAQ,kBAAAW,EAAmB,aAAAT,EAAc,kBAAAc,KAAwB,CACzG,MAAM3B,EAAQC,EAAS,EAEvB,cACGuC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,GAAI,EAAG,SAEzD,mBAAA,CAAA,QAGCH,EAAI,CAAA,GAAI,CAAE,QAAS,OAAQ,eAAgB,SAAU,GAAI,CAAA,EACxD,SAACD,EAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,SAAU,UACnB,EAAA,SAAA,CAAAC,EAAA,IAACqB,EAAA,CACC,IAAKjD,EACL,GAAI,CACF,MAAO,IACP,OAAQ,IACR,OAAQ,aAAauC,EAAMpD,EAAM,QAAQ,QAAQ,KAAM,EAAG,CAAC,GAC3D,WAAY,2BAA2BA,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,QACvG,EAEA,eAACkB,EAAO,CAAA,GAAI,CAAE,SAAU,GAAM,CAAA,CAAA,CAChC,EACAqB,EAAA,KAACwB,GAAA,CACC,UAAU,QACV,GAAI,CACF,SAAU,WACV,OAAQ,EACR,MAAO,EACP,WAAY/D,EAAM,QAAQ,QAAQ,KAClC,MAAO,QACP,UAAW,CACT,WAAYA,EAAM,QAAQ,QAAQ,IAAA,CAEtC,EAEA,SAAA,CAAAyC,EAAA,IAACuB,GAAY,EAAA,EACbvB,EAAA,IAAC,QAAA,CACC,KAAK,OACL,OAAM,GACN,OAAO,UACP,SAAUd,CAAA,CAAA,CACZ,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,EAECY,EAAA,KAAA0B,EAAA,CAAK,UAAS,GAAC,QAAS,EACvB,SAAA,CAAAxB,MAACwB,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,eACN,MAAOnD,EAAS,UAChB,SAAUO,EAAkB,WAAW,EACvC,MAAO,CAAC,CAACX,EAAO,UAChB,WAAYA,EAAO,UACnB,YAAY,aAAA,CAAA,EAEhB,QACCsD,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,cACN,MAAOnD,EAAS,WAChB,SAAUO,EAAkB,YAAY,EACxC,YAAY,aAAA,CAAA,EAEhB,QACC2C,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,cACN,MAAOnD,EAAS,SAChB,SAAUO,EAAkB,UAAU,EACtC,MAAO,CAAC,CAACX,EAAO,SAChB,WAAYA,EAAO,SACnB,YAAY,aAAA,CAAA,EAEhB,QAECsD,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,kBACN,KAAK,OACL,MAAOnD,EAAS,YAChB,SAAUO,EAAkB,aAAa,EACzC,MAAO,CAAC,CAACX,EAAO,YAChB,WAAYA,EAAO,YACnB,gBAAiB,CAAE,OAAQ,EAAK,CAAA,CAAA,EAEpC,QAECsD,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAC1B,EAAAA,KAAA4B,EAAA,CAAY,UAAS,GAAC,MAAO,CAAC,CAACxD,EAAO,OACrC,SAAA,CAAA8B,EAAAA,IAAC2B,GAAW,SAAQ,UAAA,CAAA,EACpB7B,EAAA,KAAC8B,EAAA,CACC,MAAOtD,EAAS,OAChB,SAAUO,EAAkB,QAAQ,EACpC,MAAM,WAEN,SAAA,CAACmB,EAAA,IAAA6B,EAAA,CAAS,MAAM,OAAO,SAAI,OAAA,EAC1B7B,EAAA,IAAA6B,EAAA,CAAS,MAAM,SAAS,SAAM,SAAA,EAC9B7B,EAAA,IAAA6B,EAAA,CAAS,MAAM,QAAQ,SAAK,OAAA,CAAA,CAAA,CAAA,CAAA,CAC/B,CAAA,CACF,CACF,CAAA,EAEA7B,EAAA,IAACwB,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAA1B,EAAAA,KAAC4B,EAAY,CAAA,UAAS,GACpB,SAAA,CAAA1B,EAAAA,IAAC2B,GAAW,SAAW,aAAA,CAAA,EACvB7B,EAAA,KAAC8B,EAAA,CACC,MAAOtD,EAAS,WAChB,SAAUO,EAAkB,YAAY,EACxC,MAAM,cAEN,SAAA,CAACmB,EAAA,IAAA6B,EAAA,CAAS,MAAM,KAAK,SAAE,KAAA,EACtB7B,EAAA,IAAA6B,EAAA,CAAS,MAAM,KAAK,SAAE,KAAA,EACtB7B,EAAA,IAAA6B,EAAA,CAAS,MAAM,KAAK,SAAE,KAAA,EACtB7B,EAAA,IAAA6B,EAAA,CAAS,MAAM,KAAK,SAAE,KAAA,EACtB7B,EAAA,IAAA6B,EAAA,CAAS,MAAM,MAAM,SAAG,MAAA,EACxB7B,EAAA,IAAA6B,EAAA,CAAS,MAAM,MAAM,SAAG,MAAA,EACxB7B,EAAA,IAAA6B,EAAA,CAAS,MAAM,KAAK,SAAE,KAAA,EACtB7B,EAAA,IAAA6B,EAAA,CAAS,MAAM,KAAK,SAAE,IAAA,CAAA,CAAA,CAAA,CAAA,CACzB,CAAA,CACF,CACF,CAAA,EAEA7B,EAAA,IAACwB,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAA1B,EAAAA,KAAC4B,EAAY,CAAA,UAAS,GACpB,SAAA,CAAA1B,EAAAA,IAAC2B,GAAW,SAAkB,oBAAA,CAAA,EAC9B3B,EAAA,IAAC4B,EAAA,CACC,MAAOtD,EAAS,kBAChB,SAAUO,EAAkB,mBAAmB,EAC/C,MAAM,qBAEL,SAAUxB,EAAA,IAAKyE,GACb9B,EAAAA,IAAA6B,EAAA,CAA0B,MAAOC,EAAK,MACpC,SAAAA,EAAK,KADO,EAAAA,EAAK,KAEpB,CACD,CAAA,CAAA,CACH,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,EAEMhB,GAAsB,CAAC,CAAE,SAAAxC,EAAU,OAAAJ,EAAQ,kBAAAW,MACjCrB,EAAS,SAGpBuC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,GAAI,EAAG,SAEzD,sBAAA,CAAA,EAECJ,EAAA,KAAA0B,EAAA,CAAK,UAAS,GAAC,QAAS,EACvB,SAAA,CAAAxB,MAACwB,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,qBACN,MAAOnD,EAAS,gBAChB,SAAUO,EAAkB,iBAAiB,EAC7C,MAAO,CAAC,CAACX,EAAO,gBAChB,WAAYA,EAAO,gBACnB,YAAY,kBAAA,CAAA,EAEhB,QAECsD,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,cACN,MAAOnD,EAAS,WAChB,SAAUO,EAAkB,YAAY,EACxC,YAAY,UAAA,CAAA,EAEhB,QAEC2C,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAC1B,EAAAA,KAAA4B,EAAA,CAAY,UAAS,GAAC,MAAO,CAAC,CAACxD,EAAO,MACrC,SAAA,CAAA8B,EAAAA,IAAC2B,GAAW,SAAa,eAAA,CAAA,EACzB3B,EAAA,IAAC4B,EAAA,CACC,MAAOtD,EAAS,MAChB,SAAUO,EAAkB,OAAO,EACnC,MAAM,gBAEL,SAAO1B,EAAA,IAAK4E,GACV/B,EAAAA,IAAA6B,EAAA,CAA2B,MAAOE,EAAM,MACtC,SAAAA,EAAM,KADM,EAAAA,EAAM,KAErB,CACD,CAAA,CAAA,CACH,CAAA,CACF,CACF,CAAA,QAECP,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAC1B,EAAAA,KAAA4B,EAAA,CAAY,UAAS,GAAC,MAAO,CAAC,CAACxD,EAAO,QACrC,SAAA,CAAA8B,EAAAA,IAAC2B,GAAW,SAAS,WAAA,CAAA,EACrB3B,EAAA,IAAC4B,EAAA,CACC,MAAOtD,EAAS,QAChB,SAAUO,EAAkB,SAAS,EACrC,MAAM,YAEL,YAAS,IAAKmD,GACZlC,OAAA+B,EAAA,CAAuB,MAAOG,EAAS,SAAA,CAAA,WAC7BA,CAAA,CAAA,EADIA,CAEf,CACD,CAAA,CAAA,CACH,CAAA,CACF,CACF,CAAA,QAECR,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,gBACN,MAAOnD,EAAS,aAChB,SAAUO,EAAkB,cAAc,EAC1C,YAAY,WAAA,CAAA,EAEhB,EAECmB,EAAA,IAAAwB,EAAA,CAAK,KAAI,GAAC,GAAI,GACb,SAAA1B,EAAAA,KAAC4B,EAAY,CAAA,UAAS,GAAC,MAAO,CAAC,CAACxD,EAAO,MACrC,SAAA,CAAA8B,EAAAA,IAAC2B,GAAW,SAAmB,qBAAA,CAAA,EAC/B3B,EAAA,IAAC4B,EAAA,CACC,MAAOtD,EAAS,MAChB,SAAUO,EAAkB,OAAO,EACnC,MAAM,sBAEL,WAAmB,IAAKoD,GACtBjC,EAAAA,IAAA6B,EAAA,CAA2B,MAAOI,EAAM,MACvC,gBAAClC,EAAI,CAAA,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,CACrD,EAAA,SAAA,CAAAC,EAAA,IAACkC,EAAA,CACC,KAAK,QACL,GAAI,CACF,gBAAiBD,EAAM,MACvB,MAAO,QACP,SAAU,EACZ,EACA,MAAOA,EAAM,KAAA,CACf,EACCA,EAAM,KAAA,EACT,CAAA,EAZaA,EAAM,KAarB,CACD,CAAA,CAAA,CACH,CAAA,CACF,CACF,CAAA,EAECjC,EAAA,IAAAwB,EAAA,CAAK,KAAI,GAAC,GAAI,GACb,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,kBACN,MAAOnD,EAAS,eAChB,SAAUO,EAAkB,gBAAgB,EAC5C,YAAY,0CACZ,UAAS,GACT,KAAM,CAAA,CAAA,CAEV,CAAA,CAAA,CACF,CAAA,CAAA,EACF,GAIEkC,GAAyB,CAAC,CAAE,SAAAzC,EAAU,OAAAJ,EAAQ,kBAAAW,YAE/CkB,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,GAAI,EAAG,SAEzD,qBAAA,CAAA,EAGCF,EAAA,IAAAE,EAAA,CAAW,QAAQ,YAAY,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAA,EAAkB,SAEvF,kBAAA,EAEAJ,EAAAA,KAAC0B,EAAK,CAAA,UAAS,GAAC,QAAS,EAAG,GAAI,CAAE,GAAI,CAAA,EACpC,SAAA,CAAAxB,EAAA,IAACwB,EAAK,CAAA,KAAI,GAAC,GAAI,GACb,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,YACN,MAAOnD,EAAS,QAChB,SAAUO,EAAkB,SAAS,EACrC,MAAO,CAAC,CAACX,EAAO,QAChB,WAAYA,EAAO,QACnB,UAAS,GACT,KAAM,EACN,YAAY,kDAAA,CAAA,EAEhB,QAECsD,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,SACN,MAAOnD,EAAS,KAChB,SAAUO,EAAkB,MAAM,EAClC,MAAO,CAAC,CAACX,EAAO,KAChB,WAAYA,EAAO,KACnB,YAAY,iBAAA,CAAA,EAEhB,QAECsD,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,UACN,MAAOnD,EAAS,MAChB,SAAUO,EAAkB,OAAO,EACnC,MAAO,CAAC,CAACX,EAAO,MAChB,WAAYA,EAAO,MACnB,YAAY,iBAAA,CAAA,EAEhB,QAECsD,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,YACN,MAAOnD,EAAS,QAChB,SAAUO,EAAkB,SAAS,EACrC,MAAO,CAAC,CAACX,EAAO,QAChB,WAAYA,EAAO,QACnB,YAAY,cAAA,CAAA,EAEhB,QAECsD,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,eACN,MAAOnD,EAAS,MAChB,SAAUO,EAAkB,OAAO,EACnC,YAAY,gBAAA,CAAA,EAEhB,QAEC2C,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,gBACN,KAAK,QACL,MAAOnD,EAAS,MAChB,SAAUO,EAAkB,OAAO,EACnC,YAAY,qBAAA,CAAA,CAEhB,CAAA,CAAA,EACF,QAECsD,EAAQ,CAAA,GAAI,CAAE,GAAI,GAAK,EAGvBnC,EAAA,IAAAE,EAAA,CAAW,QAAQ,YAAY,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAA,EAAkB,SAEvF,8BAAA,EAECJ,EAAA,KAAA0B,EAAA,CAAK,UAAS,GAAC,QAAS,EACvB,SAAA,CAAAxB,MAACwB,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,kBACN,MAAOnD,EAAS,WAChB,SAAUO,EAAkB,YAAY,EACxC,MAAO,CAAC,CAACX,EAAO,WAChB,WAAYA,EAAO,WACnB,YAAY,oBAAA,CAAA,EAEhB,QAECsD,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,sBACN,MAAOnD,EAAS,iBAChB,SAAUO,EAAkB,kBAAkB,EAC9C,YAAY,yBAAA,CAAA,EAEhB,QAEC2C,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,iBACN,MAAOnD,EAAS,YAChB,SAAUO,EAAkB,aAAa,EACzC,YAAY,gBAAA,CAAA,EAEhB,QAEC2C,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,kBACN,MAAOnD,EAAS,WAChB,SAAUO,EAAkB,YAAY,EACxC,MAAO,CAAC,CAACX,EAAO,WAChB,WAAYA,EAAO,WACnB,YAAY,oBAAA,CAAA,EAEhB,QAECsD,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,sBACN,MAAOnD,EAAS,iBAChB,SAAUO,EAAkB,kBAAkB,EAC9C,YAAY,eAAA,CAAA,EAEhB,QAEC2C,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,iBACN,MAAOnD,EAAS,YAChB,SAAUO,EAAkB,aAAa,EACzC,YAAY,gBAAA,CAAA,CAEhB,CAAA,CAAA,EACF,QAECsD,EAAQ,CAAA,GAAI,CAAE,GAAI,GAAK,EAGvBnC,EAAA,IAAAE,EAAA,CAAW,QAAQ,YAAY,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAA,EAAkB,SAEvF,oBAAA,EAECJ,EAAA,KAAA0B,EAAA,CAAK,UAAS,GAAC,QAAS,EACvB,SAAA,CAAAxB,MAACwB,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,2BACN,MAAOnD,EAAS,qBAChB,SAAUO,EAAkB,sBAAsB,EAClD,MAAO,CAAC,CAACX,EAAO,qBAChB,WAAYA,EAAO,qBACnB,YAAY,qBAAA,CAAA,EAEhB,QAECsD,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,4BACN,MAAOnD,EAAS,sBAChB,SAAUO,EAAkB,uBAAuB,EACnD,MAAO,CAAC,CAACX,EAAO,sBAChB,WAAYA,EAAO,sBACnB,YAAY,gBAAA,CAAA,EAEhB,QAECsD,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAA,IAACyB,EAAA,CACC,UAAS,GACT,MAAM,WACN,MAAOnD,EAAS,yBAChB,SAAUO,EAAkB,0BAA0B,EACtD,YAAY,mBAAA,CAAA,CAEhB,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAIEmC,GAAmB,CAAC,CAAE,SAAA1C,EAAU,OAAAJ,EAAQ,kBAAAW,EAAmB,aAAAT,KAAmB,OAClF,MAAMb,EAAQC,EAAS,EAEjB4E,EAAgBlF,EAAmB,QAAc+E,EAAM,QAAU3D,EAAS,KAAK,EAC/E+D,EAAgBlF,EAAO,QAAc4E,EAAM,QAAUzD,EAAS,KAAK,EAEzE,cACGyB,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,GAAI,EAAG,SAEzD,iBAAA,CAAA,EAEAF,EAAAA,IAACsC,GAAM,SAAS,OAAO,GAAI,CAAE,GAAI,CAAE,EAAG,SAEtC,uGAAA,CAAA,EAGAtC,EAAAA,IAACG,EAAK,CAAA,GAAI,CAAE,GAAI,EAAG,WAAY,2BAA2BQ,EAAMpD,EAAM,QAAQ,QAAQ,KAAM,GAAI,CAAC,QAAQoD,EAAMpD,EAAM,QAAQ,UAAU,KAAM,GAAI,CAAC,QAChJ,EAAA,SAAAuC,EAAA,KAACM,EACC,CAAA,SAAA,CAACN,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,EAAG,GAAI,CAAA,EAC5D,SAAA,CAAAC,EAAA,IAACqB,EAAA,CACC,IAAKjD,EACL,GAAI,CACF,MAAO,GACP,OAAQ,GACR,OAAQ,aAAab,EAAM,QAAQ,QAAQ,IAAI,EACjD,EAEA,eAACkB,EAAO,CAAA,GAAI,CAAE,SAAU,GAAM,CAAA,CAAA,CAChC,SACCsB,EACC,CAAA,SAAA,CAAAD,OAACI,GAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GACxC,EAAA,SAAA,CAAS5B,EAAA,UAAU,IAAEA,EAAS,WAAW,IAAEA,EAAS,QAAA,EACvD,EACCwB,EAAA,KAAAI,EAAA,CAAW,QAAQ,QAAQ,MAAM,iBAC/B,SAAA,CAAemC,GAAA,YAAAA,EAAA,MAAM,cAAY/D,EAAS,OAAA,EAC7C,EACCwB,EAAA,KAAAI,EAAA,CAAW,QAAQ,QAAQ,MAAM,iBAAiB,SAAA,CAAA,iBAClC5B,EAAS,eAAA,CAC1B,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEC8D,GACCpC,EAAA,IAACkC,EAAA,CACC,MAAOE,EAAc,MACrB,GAAI,CACF,gBAAiBA,EAAc,MAC/B,MAAO,QACP,WAAY,GAAA,CACd,CAAA,CACF,CAAA,CAEJ,CACF,CAAA,EAGAtC,EAAAA,KAAC0B,EAAK,CAAA,UAAS,GAAC,QAAS,EAAG,GAAI,CAAE,GAAI,CAAA,EACpC,SAAA,CAACxB,EAAA,IAAAwB,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAAA,IAACG,EACC,CAAA,SAAAL,EAAAA,KAACM,EACC,CAAA,SAAA,CAAAJ,EAAA,IAACE,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,uBAAA,EACAJ,EAAAA,KAACyC,EAAM,CAAA,QAAS,EACd,SAAA,CAAAzC,OAACC,GAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,eAC1C,EAAA,SAAA,CAAAC,MAACE,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAc,iBAAA,EAChEF,EAAA,IAAAE,EAAA,CAAW,QAAQ,QAAS,WAAS,WAAY,CAAA,CAAA,EACpD,EACAJ,OAACC,GAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,eAC1C,EAAA,SAAA,CAAAC,MAACE,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAO,UAAA,EACzDF,EAAA,IAAAE,EAAA,CAAW,QAAQ,QAAS,WAAS,MAAO,CAAA,CAAA,EAC/C,EACAJ,OAACC,GAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,eAC1C,EAAA,SAAA,CAAAC,MAACE,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAY,eAAA,QAC9DA,EAAW,CAAA,QAAQ,QAAS,SAAA5B,EAAS,YAAc,eAAgB,CAAA,CAAA,EACtE,EACAwB,OAACC,GAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,eAC1C,EAAA,SAAA,CAAAC,MAACE,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAmB,sBAAA,EACrEF,EAAA,IAAAE,EAAA,CAAW,QAAQ,QACjB,UAAUsC,EAAAnF,EAAA,KAAayE,GAAAA,EAAK,QAAUxD,EAAS,iBAAiB,IAAtD,YAAAkE,EAAyD,KACtE,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAEAxC,EAAA,IAACwB,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAxB,EAAAA,IAACG,EACC,CAAA,SAAAL,EAAAA,KAACM,EACC,CAAA,SAAA,CAAAJ,EAAA,IAACE,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,sBAAA,EACAJ,EAAAA,KAACyC,EAAM,CAAA,QAAS,EACd,SAAA,CAAAzC,OAACC,GAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,eAC1C,EAAA,SAAA,CAAAC,MAACE,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAK,QAAA,EACvDF,EAAA,IAAAE,EAAA,CAAW,QAAQ,QAAS,WAAS,IAAK,CAAA,CAAA,EAC7C,EACAJ,OAACC,GAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,eAC1C,EAAA,SAAA,CAAAC,MAACE,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAM,SAAA,EACxDF,EAAA,IAAAE,EAAA,CAAW,QAAQ,QAAS,WAAS,KAAM,CAAA,CAAA,EAC9C,EACAJ,OAACC,GAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,eAC1C,EAAA,SAAA,CAAAC,MAACE,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAc,iBAAA,EAChEF,EAAA,IAAAE,EAAA,CAAW,QAAQ,QAAS,WAAS,UAAW,CAAA,CAAA,EACnD,EACAJ,OAACC,GAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,eAC1C,EAAA,SAAA,CAAAC,MAACE,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAAc,iBAAA,EAChEF,EAAA,IAAAE,EAAA,CAAW,QAAQ,QAAS,WAAS,UAAW,CAAA,CAAA,CACnD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,EACF,EAGAF,EAAA,IAACG,EACC,CAAA,SAAAL,EAAAA,KAACM,EACC,CAAA,SAAA,CAAAJ,EAAA,IAACE,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,wBAAA,EAEAJ,EAAAA,KAACyC,EAAM,CAAA,QAAS,EACd,SAAA,CAAAvC,EAAA,IAACyC,EAAA,CACC,QACEzC,EAAA,IAAC0C,EAAA,CACC,QAASpE,EAAS,YAClB,SAAUO,EAAkB,aAAa,EACzC,MAAM,SAAA,CACR,EAEF,MACEmB,EAAA,IAACE,EAAW,CAAA,QAAQ,QAAQ,SAE5B,0GAAA,CAAA,CAAA,CAEJ,EACChC,EAAO,aACL8B,MAAAE,EAAA,CAAW,QAAQ,UAAU,MAAM,QACjC,SAAAhC,EAAO,WACV,CAAA,EAGF8B,EAAA,IAACyC,EAAA,CACC,QACEzC,EAAA,IAAC0C,EAAA,CACC,QAASpE,EAAS,qBAClB,SAAUO,EAAkB,sBAAsB,EAClD,MAAM,SAAA,CACR,EAEF,MACEmB,EAAA,IAACE,EAAW,CAAA,QAAQ,QAAQ,SAE5B,4FAAA,CAAA,CAAA,CAEJ,EAECF,EAAA,IAAAsC,EAAA,CAAM,SAAS,UAAU,GAAI,CAAE,GAAI,CAAE,EACpC,SAACtC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,QAAQ,iNAG5B,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ"}