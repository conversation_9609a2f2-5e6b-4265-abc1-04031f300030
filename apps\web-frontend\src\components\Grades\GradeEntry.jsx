/**
 * <PERSON>idyaMitra Platform - Grade Entry Component
 * 
 * Grade entry interface for teachers with Indian grading system support
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Avatar,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Alert,
  Chip,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Save,
  Print,
  Download,
  Grade,
  Assessment,
  TrendingUp,
  TrendingDown,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

// Sample grade data with Indian student names
const gradeData = [
  { id: 1, name: '<PERSON><PERSON>', rollNumber: 1, previousGrade: 'A1', currentMarks: 95, maxMarks: 100 },
  { id: 2, name: '<PERSON><PERSON><PERSON><PERSON>', rollNumber: 2, previousGrade: 'A2', currentMarks: 88, maxMarks: 100 },
  { id: 3, name: '<PERSON><PERSON><PERSON>', rollNumber: 3, previousGrade: 'B1', currentMarks: 82, maxMarks: 100 },
  { id: 4, name: '<PERSON>', rollNumber: 4, previousGrade: 'A1', currentMarks: 92, maxMarks: 100 },
  { id: 5, name: 'Ankitha Patel', rollNumber: 5, previousGrade: 'A2', currentMarks: 85, maxMarks: 100 },
  { id: 6, name: 'Sirisha Nair', rollNumber: 6, previousGrade: 'A1', currentMarks: 96, maxMarks: 100 },
  { id: 7, name: 'Priya Agarwal', rollNumber: 7, previousGrade: 'B2', currentMarks: 78, maxMarks: 100 },
];

// Indian grading system
const getGradeFromMarks = (marks, maxMarks) => {
  const percentage = (marks / maxMarks) * 100;
  if (percentage >= 91) return 'A1';
  if (percentage >= 81) return 'A2';
  if (percentage >= 71) return 'B1';
  if (percentage >= 61) return 'B2';
  if (percentage >= 51) return 'C1';
  if (percentage >= 41) return 'C2';
  if (percentage >= 33) return 'D';
  return 'E';
};

const getGradeColor = (grade) => {
  switch (grade) {
    case 'A1':
    case 'A2':
      return 'success';
    case 'B1':
    case 'B2':
      return 'info';
    case 'C1':
    case 'C2':
      return 'warning';
    case 'D':
    case 'E':
      return 'error';
    default:
      return 'default';
  }
};

const GradeEntry = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [selectedClass, setSelectedClass] = useState('10-A');
  const [selectedSubject, setSelectedSubject] = useState('Mathematics');
  const [selectedTest, setSelectedTest] = useState('Unit Test 1');
  const [grades, setGrades] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Initialize grades state
    const initialGrades = {};
    gradeData.forEach(student => {
      initialGrades[student.id] = {
        marks: student.currentMarks,
        maxMarks: student.maxMarks,
      };
    });
    setGrades(initialGrades);
  }, []);

  const handleMarksChange = (studentId, marks) => {
    setGrades(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        marks: parseInt(marks) || 0,
      }
    }));
  };

  const handleMaxMarksChange = (studentId, maxMarks) => {
    setGrades(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        maxMarks: parseInt(maxMarks) || 100,
      }
    }));
  };

  const handleSaveGrades = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('Grades saved successfully!');
    } catch (error) {
      console.error('Error saving grades:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateStatistics = () => {
    const allMarks = Object.values(grades).map(g => (g.marks / g.maxMarks) * 100);
    const average = allMarks.reduce((sum, mark) => sum + mark, 0) / allMarks.length;
    const highest = Math.max(...allMarks);
    const lowest = Math.min(...allMarks);
    
    return {
      average: average.toFixed(1),
      highest: highest.toFixed(1),
      lowest: lowest.toFixed(1),
      totalStudents: allMarks.length,
    };
  };

  const stats = calculateStatistics();

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 600,
              mb: 1,
              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
            }}
          >
            Grade Entry
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Enter and manage student grades with Indian grading system
          </Typography>
        </Box>
      </motion.div>

      {/* Controls */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Class</InputLabel>
                <Select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  label="Class"
                >
                  <MenuItem value="9-A">Class 9-A</MenuItem>
                  <MenuItem value="9-B">Class 9-B</MenuItem>
                  <MenuItem value="10-A">Class 10-A</MenuItem>
                  <MenuItem value="10-B">Class 10-B</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Subject</InputLabel>
                <Select
                  value={selectedSubject}
                  onChange={(e) => setSelectedSubject(e.target.value)}
                  label="Subject"
                >
                  <MenuItem value="Mathematics">Mathematics</MenuItem>
                  <MenuItem value="Science">Science</MenuItem>
                  <MenuItem value="English">English</MenuItem>
                  <MenuItem value="Hindi">Hindi</MenuItem>
                  <MenuItem value="Social Studies">Social Studies</MenuItem>
                  <MenuItem value="Telugu">Telugu</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Test/Assessment</InputLabel>
                <Select
                  value={selectedTest}
                  onChange={(e) => setSelectedTest(e.target.value)}
                  label="Test/Assessment"
                >
                  <MenuItem value="Unit Test 1">Unit Test 1</MenuItem>
                  <MenuItem value="Unit Test 2">Unit Test 2</MenuItem>
                  <MenuItem value="Mid Term">Mid Term Exam</MenuItem>
                  <MenuItem value="Final Term">Final Term Exam</MenuItem>
                  <MenuItem value="Assignment">Assignment</MenuItem>
                  <MenuItem value="Project">Project Work</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Stack direction="row" spacing={2}>
                <Button
                  variant="contained"
                  startIcon={<Save />}
                  onClick={handleSaveGrades}
                  loading={loading}
                  sx={{
                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                  }}
                >
                  Save Grades
                </Button>
              </Stack>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Statistics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card
            sx={{
              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
              color: 'white',
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 600 }}>
                    {stats.average}%
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Class Average
                  </Typography>
                </Box>
                <Assessment sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card
            sx={{
              background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
              color: 'white',
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 600 }}>
                    {stats.highest}%
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Highest Score
                  </Typography>
                </Box>
                <TrendingUp sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card
            sx={{
              background: `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,
              color: 'white',
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 600 }}>
                    {stats.lowest}%
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Lowest Score
                  </Typography>
                </Box>
                <TrendingDown sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card
            sx={{
              background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,
              color: 'white',
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 600 }}>
                    {stats.totalStudents}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Total Students
                  </Typography>
                </Box>
                <Grade sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Grade Entry Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
            Grade Entry - {selectedClass} | {selectedSubject} | {selectedTest}
          </Typography>

          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Roll No.</TableCell>
                  <TableCell>Student Name</TableCell>
                  <TableCell>Previous Grade</TableCell>
                  <TableCell>Marks Obtained</TableCell>
                  <TableCell>Max Marks</TableCell>
                  <TableCell>Percentage</TableCell>
                  <TableCell>Current Grade</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {gradeData.map((student) => {
                  const currentGrade = grades[student.id];
                  const percentage = currentGrade ? (currentGrade.marks / currentGrade.maxMarks) * 100 : 0;
                  const grade = currentGrade ? getGradeFromMarks(currentGrade.marks, currentGrade.maxMarks) : 'E';
                  
                  return (
                    <TableRow key={student.id} hover>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {student.rollNumber}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar sx={{ width: 32, height: 32 }}>
                            {student.name.charAt(0)}
                          </Avatar>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {student.name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={student.previousGrade}
                          color={getGradeColor(student.previousGrade)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          size="small"
                          type="number"
                          value={currentGrade?.marks || ''}
                          onChange={(e) => handleMarksChange(student.id, e.target.value)}
                          sx={{ width: 80 }}
                          inputProps={{ min: 0, max: currentGrade?.maxMarks || 100 }}
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          size="small"
                          type="number"
                          value={currentGrade?.maxMarks || 100}
                          onChange={(e) => handleMaxMarksChange(student.id, e.target.value)}
                          sx={{ width: 80 }}
                          inputProps={{ min: 1 }}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {percentage.toFixed(1)}%
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={grade}
                          color={getGradeColor(grade)}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default GradeEntry;
