import{u as z,j as e,B as l,g as n,b as h,e as m,G as c,a4 as b,a5 as k,a6 as y,a7 as t,m as R,k as U,aY as D,a2 as H,x as L,aj as O,b8 as K,b2 as Y,aw as q,b3 as J,b4 as Q,b5 as T,b6 as i,b7 as V,A as X,n as w,W as A}from"./mui-Cjipzt4F.js";import{r as u}from"./vendor-BfWiUekA.js";import{u as Z,m as _}from"./index-_PFqhbKW.js";import"./charts-Dx2u7Eir.js";const G=[{id:1,name:"<PERSON><PERSON>",rollNumber:1,previousGrade:"A1",currentMarks:95,maxMarks:100},{id:2,name:"<PERSON><PERSON><PERSON><PERSON>",rollNumber:2,previousGrade:"A2",currentMarks:88,maxMarks:100},{id:3,name:"<PERSON><PERSON><PERSON>",rollNumber:3,previousGrade:"B1",currentMarks:82,maxMarks:100},{id:4,name:"<PERSON>",rollNumber:4,previousGrade:"A1",currentMarks:92,maxMarks:100},{id:5,name:"Ankitha Patel",rollNumber:5,previousGrade:"A2",currentMarks:85,maxMarks:100},{id:6,name:"Sirisha Nair",rollNumber:6,previousGrade:"A1",currentMarks:96,maxMarks:100},{id:7,name:"Priya Agarwal",rollNumber:7,previousGrade:"B2",currentMarks:78,maxMarks:100}],ee=(r,j)=>{const d=r/j*100;return d>=91?"A1":d>=81?"A2":d>=71?"B1":d>=61?"B2":d>=51?"C1":d>=41?"C2":d>=33?"D":"E"},W=r=>{switch(r){case"A1":case"A2":return"success";case"B1":case"B2":return"info";case"C1":case"C2":return"warning";case"D":case"E":return"error";default:return"default"}},ie=()=>{const r=z();Z();const[j,d]=u.useState("10-A"),[f,E]=u.useState("Mathematics"),[M,B]=u.useState("Unit Test 1"),[S,v]=u.useState({}),[N,C]=u.useState(!1);u.useEffect(()=>{const a={};G.forEach(s=>{a[s.id]={marks:s.currentMarks,maxMarks:s.maxMarks}}),v(a)},[]);const $=(a,s)=>{v(x=>({...x,[a]:{...x[a],marks:parseInt(s)||0}}))},F=(a,s)=>{v(x=>({...x,[a]:{...x[a],maxMarks:parseInt(s)||100}}))},I=async()=>{C(!0);try{await new Promise(a=>setTimeout(a,1e3)),alert("Grades saved successfully!")}catch(a){console.error("Error saving grades:",a)}finally{C(!1)}},g=(()=>{const a=Object.values(S).map(o=>o.marks/o.maxMarks*100),s=a.reduce((o,P)=>o+P,0)/a.length,x=Math.max(...a),p=Math.min(...a);return{average:s.toFixed(1),highest:x.toFixed(1),lowest:p.toFixed(1),totalStudents:a.length}})();return e.jsxs(l,{sx:{maxWidth:1200,mx:"auto",p:3},children:[e.jsx(_.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:e.jsxs(l,{sx:{mb:4},children:[e.jsx(n,{variant:"h4",sx:{fontWeight:600,mb:1,background:`linear-gradient(135deg, ${r.palette.primary.main} 0%, ${r.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"Grade Entry"}),e.jsx(n,{variant:"body1",color:"text.secondary",children:"Enter and manage student grades with Indian grading system"})]})}),e.jsx(h,{sx:{mb:4},children:e.jsx(m,{children:e.jsxs(c,{container:!0,spacing:3,alignItems:"center",children:[e.jsx(c,{item:!0,xs:12,md:3,children:e.jsxs(b,{fullWidth:!0,children:[e.jsx(k,{children:"Class"}),e.jsxs(y,{value:j,onChange:a=>d(a.target.value),label:"Class",children:[e.jsx(t,{value:"9-A",children:"Class 9-A"}),e.jsx(t,{value:"9-B",children:"Class 9-B"}),e.jsx(t,{value:"10-A",children:"Class 10-A"}),e.jsx(t,{value:"10-B",children:"Class 10-B"})]})]})}),e.jsx(c,{item:!0,xs:12,md:3,children:e.jsxs(b,{fullWidth:!0,children:[e.jsx(k,{children:"Subject"}),e.jsxs(y,{value:f,onChange:a=>E(a.target.value),label:"Subject",children:[e.jsx(t,{value:"Mathematics",children:"Mathematics"}),e.jsx(t,{value:"Science",children:"Science"}),e.jsx(t,{value:"English",children:"English"}),e.jsx(t,{value:"Hindi",children:"Hindi"}),e.jsx(t,{value:"Social Studies",children:"Social Studies"}),e.jsx(t,{value:"Telugu",children:"Telugu"})]})]})}),e.jsx(c,{item:!0,xs:12,md:3,children:e.jsxs(b,{fullWidth:!0,children:[e.jsx(k,{children:"Test/Assessment"}),e.jsxs(y,{value:M,onChange:a=>B(a.target.value),label:"Test/Assessment",children:[e.jsx(t,{value:"Unit Test 1",children:"Unit Test 1"}),e.jsx(t,{value:"Unit Test 2",children:"Unit Test 2"}),e.jsx(t,{value:"Mid Term",children:"Mid Term Exam"}),e.jsx(t,{value:"Final Term",children:"Final Term Exam"}),e.jsx(t,{value:"Assignment",children:"Assignment"}),e.jsx(t,{value:"Project",children:"Project Work"})]})]})}),e.jsx(c,{item:!0,xs:12,md:3,children:e.jsx(R,{direction:"row",spacing:2,children:e.jsx(U,{variant:"contained",startIcon:e.jsx(D,{}),onClick:I,loading:N,sx:{background:`linear-gradient(135deg, ${r.palette.primary.main} 0%, ${r.palette.secondary.main} 100%)`},children:"Save Grades"})})})]})})}),e.jsxs(c,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(c,{item:!0,xs:12,md:3,children:e.jsx(h,{sx:{background:`linear-gradient(135deg, ${r.palette.primary.main} 0%, ${r.palette.primary.dark} 100%)`,color:"white"},children:e.jsx(m,{children:e.jsxs(l,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(l,{children:[e.jsxs(n,{variant:"h4",sx:{fontWeight:600},children:[g.average,"%"]}),e.jsx(n,{variant:"body2",sx:{opacity:.9},children:"Class Average"})]}),e.jsx(H,{sx:{fontSize:40,opacity:.8}})]})})})}),e.jsx(c,{item:!0,xs:12,md:3,children:e.jsx(h,{sx:{background:`linear-gradient(135deg, ${r.palette.success.main} 0%, ${r.palette.success.dark} 100%)`,color:"white"},children:e.jsx(m,{children:e.jsxs(l,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(l,{children:[e.jsxs(n,{variant:"h4",sx:{fontWeight:600},children:[g.highest,"%"]}),e.jsx(n,{variant:"body2",sx:{opacity:.9},children:"Highest Score"})]}),e.jsx(L,{sx:{fontSize:40,opacity:.8}})]})})})}),e.jsx(c,{item:!0,xs:12,md:3,children:e.jsx(h,{sx:{background:`linear-gradient(135deg, ${r.palette.warning.main} 0%, ${r.palette.warning.dark} 100%)`,color:"white"},children:e.jsx(m,{children:e.jsxs(l,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(l,{children:[e.jsxs(n,{variant:"h4",sx:{fontWeight:600},children:[g.lowest,"%"]}),e.jsx(n,{variant:"body2",sx:{opacity:.9},children:"Lowest Score"})]}),e.jsx(O,{sx:{fontSize:40,opacity:.8}})]})})})}),e.jsx(c,{item:!0,xs:12,md:3,children:e.jsx(h,{sx:{background:`linear-gradient(135deg, ${r.palette.info.main} 0%, ${r.palette.info.dark} 100%)`,color:"white"},children:e.jsx(m,{children:e.jsxs(l,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(l,{children:[e.jsx(n,{variant:"h4",sx:{fontWeight:600},children:g.totalStudents}),e.jsx(n,{variant:"body2",sx:{opacity:.9},children:"Total Students"})]}),e.jsx(K,{sx:{fontSize:40,opacity:.8}})]})})})})]}),e.jsx(h,{children:e.jsxs(m,{children:[e.jsxs(n,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:["Grade Entry - ",j," | ",f," | ",M]}),e.jsx(Y,{component:q,variant:"outlined",children:e.jsxs(J,{children:[e.jsx(Q,{children:e.jsxs(T,{children:[e.jsx(i,{children:"Roll No."}),e.jsx(i,{children:"Student Name"}),e.jsx(i,{children:"Previous Grade"}),e.jsx(i,{children:"Marks Obtained"}),e.jsx(i,{children:"Max Marks"}),e.jsx(i,{children:"Percentage"}),e.jsx(i,{children:"Current Grade"})]})}),e.jsx(V,{children:G.map(a=>{const s=S[a.id],x=s?s.marks/s.maxMarks*100:0,p=s?ee(s.marks,s.maxMarks):"E";return e.jsxs(T,{hover:!0,children:[e.jsx(i,{children:e.jsx(n,{variant:"body2",sx:{fontWeight:600},children:a.rollNumber})}),e.jsx(i,{children:e.jsxs(l,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx(X,{sx:{width:32,height:32},children:a.name.charAt(0)}),e.jsx(n,{variant:"body2",sx:{fontWeight:500},children:a.name})]})}),e.jsx(i,{children:e.jsx(w,{label:a.previousGrade,color:W(a.previousGrade),size:"small"})}),e.jsx(i,{children:e.jsx(A,{size:"small",type:"number",value:(s==null?void 0:s.marks)||"",onChange:o=>$(a.id,o.target.value),sx:{width:80},inputProps:{min:0,max:(s==null?void 0:s.maxMarks)||100}})}),e.jsx(i,{children:e.jsx(A,{size:"small",type:"number",value:(s==null?void 0:s.maxMarks)||100,onChange:o=>F(a.id,o.target.value),sx:{width:80},inputProps:{min:1}})}),e.jsx(i,{children:e.jsxs(n,{variant:"body2",sx:{fontWeight:500},children:[x.toFixed(1),"%"]})}),e.jsx(i,{children:e.jsx(w,{label:p,color:W(p),size:"small"})})]},a.id)})})]})})]})})]})};export{ie as default};
//# sourceMappingURL=GradeEntry-CesnzI9J.js.map
