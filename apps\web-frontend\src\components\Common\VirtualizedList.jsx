/**
 * VidyaMitra Platform - Virtualized List Component
 * 
 * High-performance virtualized list for large datasets with Indian educational context
 */

import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Typography,
  Skeleton,
  useTheme,
  alpha,
} from '@mui/material';
import { FixedSizeList as WindowedList } from 'react-window';
import { motion } from 'framer-motion';

const VirtualizedList = ({
  items = [],
  itemHeight = 72,
  height = 400,
  width = '100%',
  renderItem,
  loading = false,
  loadingCount = 10,
  onItemClick,
  searchTerm = '',
  filterFn,
  sortFn,
  emptyMessage = 'No items found',
  className,
  ...props
}) => {
  const theme = useTheme();
  const listRef = useRef();
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 0 });

  // Filter and sort items
  const processedItems = useMemo(() => {
    let filtered = items;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(item => {
        const searchableText = typeof item === 'string' 
          ? item 
          : Object.values(item).join(' ').toLowerCase();
        return searchableText.includes(searchTerm.toLowerCase());
      });
    }

    // Apply custom filter
    if (filterFn) {
      filtered = filtered.filter(filterFn);
    }

    // Apply sorting
    if (sortFn) {
      filtered = [...filtered].sort(sortFn);
    }

    return filtered;
  }, [items, searchTerm, filterFn, sortFn]);

  // Handle scroll to update visible range
  const handleItemsRendered = ({ visibleStartIndex, visibleStopIndex }) => {
    setVisibleRange({ start: visibleStartIndex, end: visibleStopIndex });
  };

  // Default item renderer
  const defaultRenderItem = ({ index, style }) => {
    const item = processedItems[index];
    
    if (!item) {
      return (
        <div style={style}>
          <ListItem>
            <Skeleton variant="rectangular" width="100%" height={itemHeight - 16} />
          </ListItem>
        </div>
      );
    }

    return (
      <motion.div
        style={style}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.2, delay: index * 0.02 }}
      >
        <ListItem
          button
          onClick={() => onItemClick?.(item, index)}
          sx={{
            borderRadius: 1,
            mb: 0.5,
            mx: 1,
            '&:hover': {
              backgroundColor: alpha(theme.palette.primary.main, 0.08),
              transform: 'translateX(4px)',
              transition: 'all 0.2s ease',
            },
          }}
        >
          {typeof item === 'string' ? (
            <ListItemText primary={item} />
          ) : (
            <>
              {item.avatar && (
                <ListItemAvatar>
                  <Avatar src={item.avatar} alt={item.name}>
                    {item.name?.charAt(0)}
                  </Avatar>
                </ListItemAvatar>
              )}
              <ListItemText
                primary={item.name || item.title}
                secondary={item.description || item.subtitle}
              />
            </>
          )}
        </ListItem>
      </motion.div>
    );
  };

  // Loading skeleton
  const LoadingSkeleton = () => (
    <Box sx={{ p: 1 }}>
      {Array.from({ length: loadingCount }).map((_, index) => (
        <Box key={index} sx={{ mb: 1 }}>
          <Skeleton
            variant="rectangular"
            height={itemHeight}
            sx={{ borderRadius: 1 }}
          />
        </Box>
      ))}
    </Box>
  );

  // Empty state
  const EmptyState = () => (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        p: 3,
      }}
    >
      <Typography variant="h6" color="text.secondary" gutterBottom>
        {emptyMessage}
      </Typography>
      {searchTerm && (
        <Typography variant="body2" color="text.secondary">
          Try adjusting your search criteria
        </Typography>
      )}
    </Box>
  );

  if (loading) {
    return <LoadingSkeleton />;
  }

  if (processedItems.length === 0) {
    return <EmptyState />;
  }

  return (
    <Box
      className={className}
      sx={{
        height,
        width,
        border: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
        borderRadius: 2,
        overflow: 'hidden',
        backgroundColor: theme.palette.background.paper,
        ...props.sx,
      }}
    >
      <WindowedList
        ref={listRef}
        height={height}
        width={width}
        itemCount={processedItems.length}
        itemSize={itemHeight}
        onItemsRendered={handleItemsRendered}
        overscanCount={5}
        {...props}
      >
        {renderItem || defaultRenderItem}
      </WindowedList>
    </Box>
  );
};

// Specialized component for student lists
export const VirtualizedStudentList = ({
  students = [],
  onStudentClick,
  searchTerm = '',
  classFilter = null,
  gradeFilter = null,
  ...props
}) => {
  const theme = useTheme();

  const renderStudentItem = ({ index, style }) => {
    const student = students[index];
    
    if (!student) return null;

    return (
      <motion.div
        style={style}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2, delay: index * 0.01 }}
      >
        <ListItem
          button
          onClick={() => onStudentClick?.(student)}
          sx={{
            borderRadius: 1,
            mb: 0.5,
            mx: 1,
            background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.4)} 100%)`,
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            '&:hover': {
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
              transform: 'translateY(-2px)',
              boxShadow: theme.shadows[4],
              transition: 'all 0.3s ease',
            },
          }}
        >
          <ListItemAvatar>
            <Avatar
              src={student.profilePhoto}
              sx={{
                width: 48,
                height: 48,
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                color: 'white',
                fontWeight: 600,
              }}
            >
              {student.firstName?.charAt(0)}{student.lastName?.charAt(0)}
            </Avatar>
          </ListItemAvatar>
          
          <ListItemText
            primary={
              <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                {student.firstName} {student.lastName}
              </Typography>
            }
            secondary={
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Class {student.grade}-{student.section} • Roll No: {student.rollNumber}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {student.board} Board • Attendance: {student.attendancePercentage}%
                </Typography>
              </Box>
            }
          />
          
          <Box sx={{ textAlign: 'right' }}>
            <Typography
              variant="caption"
              sx={{
                px: 1,
                py: 0.5,
                borderRadius: 1,
                backgroundColor: student.attendancePercentage >= 90 
                  ? alpha(theme.palette.success.main, 0.1)
                  : student.attendancePercentage >= 75
                  ? alpha(theme.palette.warning.main, 0.1)
                  : alpha(theme.palette.error.main, 0.1),
                color: student.attendancePercentage >= 90 
                  ? theme.palette.success.main
                  : student.attendancePercentage >= 75
                  ? theme.palette.warning.main
                  : theme.palette.error.main,
              }}
            >
              {student.overallGrade || 'N/A'}
            </Typography>
          </Box>
        </ListItem>
      </motion.div>
    );
  };

  const filterStudents = (student) => {
    if (classFilter && student.grade !== classFilter) return false;
    if (gradeFilter && student.overallGrade !== gradeFilter) return false;
    return true;
  };

  const sortStudents = (a, b) => {
    // Sort by class, then by roll number
    if (a.grade !== b.grade) {
      return a.grade - b.grade;
    }
    return (a.rollNumber || 0) - (b.rollNumber || 0);
  };

  return (
    <VirtualizedList
      items={students}
      renderItem={renderStudentItem}
      searchTerm={searchTerm}
      filterFn={filterStudents}
      sortFn={sortStudents}
      itemHeight={80}
      emptyMessage="No students found"
      {...props}
    />
  );
};

// Specialized component for grade lists
export const VirtualizedGradeList = ({
  grades = [],
  onGradeClick,
  subject = '',
  ...props
}) => {
  const theme = useTheme();

  const renderGradeItem = ({ index, style }) => {
    const grade = grades[index];
    
    if (!grade) return null;

    const getGradeColor = (gradeValue) => {
      if (gradeValue === 'A1' || gradeValue === 'A2') return theme.palette.success.main;
      if (gradeValue === 'B1' || gradeValue === 'B2') return theme.palette.info.main;
      if (gradeValue === 'C1' || gradeValue === 'C2') return theme.palette.warning.main;
      return theme.palette.error.main;
    };

    return (
      <motion.div
        style={style}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.2, delay: index * 0.01 }}
      >
        <ListItem
          button
          onClick={() => onGradeClick?.(grade)}
          sx={{
            borderRadius: 1,
            mb: 0.5,
            mx: 1,
            background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
            backdropFilter: 'blur(8px)',
            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            '&:hover': {
              background: `linear-gradient(135deg, ${alpha(getGradeColor(grade.grade), 0.1)} 0%, ${alpha(getGradeColor(grade.grade), 0.05)} 100%)`,
              transform: 'translateX(4px)',
              transition: 'all 0.2s ease',
            },
          }}
        >
          <ListItemText
            primary={
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                  {grade.studentName}
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    color: getGradeColor(grade.grade),
                    fontWeight: 600,
                  }}
                >
                  {grade.grade}
                </Typography>
              </Box>
            }
            secondary={
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                <Typography variant="body2" color="text.secondary">
                  {grade.subject} • {grade.assessment}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {grade.marks}/100 • {grade.percentage}%
                </Typography>
              </Box>
            }
          />
        </ListItem>
      </motion.div>
    );
  };

  return (
    <VirtualizedList
      items={grades}
      renderItem={renderGradeItem}
      itemHeight={72}
      emptyMessage="No grades found"
      {...props}
    />
  );
};

export default VirtualizedList;
