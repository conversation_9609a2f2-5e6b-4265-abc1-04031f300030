{"version": 3, "file": "TeacherDashboard-1-qBGxVt.js", "sources": ["../../src/components/Dashboard/TeacherDashboard.jsx"], "sourcesContent": ["/**\n * VidyaMitra Platform - Teacher Dashboard Component\n *\n * Comprehensive teacher dashboard with grade entry, student progress tracking,\n * and class management features for Indian educational context\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Grid,\n  Button,\n  Avatar,\n  Chip,\n  LinearProgress,\n  IconButton,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Stack,\n  Alert,\n  Fab,\n  useTheme,\n  alpha,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Badge,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemAvatar,\n  Divider,\n} from '@mui/material';\nimport {\n  School,\n  People,\n  Assessment,\n  TrendingUp,\n  Add,\n  Edit,\n  Visibility,\n  NotificationImportant,\n  CalendarToday,\n  Assignment,\n  Grade,\n  EmojiEvents,\n  Warning,\n  Notifications,\n  Search,\n  Schedule,\n  ArrowForward,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\nimport { <PERSON>, Doughnut, Bar, Pie } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  ArcElement,\n  Title,\n  Tooltip as ChartTooltip,\n  Legend,\n  Filler,\n} from 'chart.js';\nimport { useTranslation } from 'react-i18next';\n\n// Register Chart.js components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  ArcElement,\n  Title,\n  ChartTooltip,\n  Legend,\n  Filler\n);\n\n// Sample teacher data with Indian context\nconst teacherData = {\n  name: 'Mrs. Priya Sharma',\n  employeeId: 'TCH001',\n  subjects: ['Mathematics', 'Physics'],\n  classes: [\n    { grade: 10, section: 'A', students: 35, board: 'CBSE' },\n    { grade: 10, section: 'B', students: 32, board: 'CBSE' },\n    { grade: 9, section: 'A', students: 38, board: 'CBSE' },\n  ],\n  totalStudents: 105,\n  pendingGrades: 12,\n  upcomingTests: 3,\n  recentActivity: [\n    { type: 'grade', message: 'Graded Mathematics test for Class 10-A', time: '2 hours ago' },\n    { type: 'attendance', message: 'Marked attendance for Class 9-A', time: '4 hours ago' },\n    { type: 'swot', message: 'Updated SWOT analysis for Sanju Reddy', time: '1 day ago' },\n  ],\n};\n\n// Sample student performance data with Indian names\nconst studentPerformanceData = [\n  { id: 1, name: 'Sanju Kumar Reddy', class: '10-A', subject: 'Mathematics', lastGrade: 'A1', attendance: 95, status: 'Excellent' },\n  { id: 2, name: 'Niraimathi Selvam', class: '10-A', subject: 'Mathematics', lastGrade: 'A2', attendance: 92, status: 'Good' },\n  { id: 3, name: 'Mahesh Reddy', class: '10-B', subject: 'Mathematics', lastGrade: 'B1', attendance: 88, status: 'Average' },\n  { id: 4, name: 'Ravi Teja Sharma', class: '9-A', subject: 'Physics', lastGrade: 'A1', attendance: 94, status: 'Excellent' },\n  { id: 5, name: 'Ankitha Patel', class: '10-A', subject: 'Mathematics', lastGrade: 'A2', attendance: 90, status: 'Good' },\n  { id: 6, name: 'Sirisha Nair', class: '10-B', subject: 'Physics', lastGrade: 'A1', attendance: 96, status: 'Excellent' },\n  { id: 7, name: 'Priya Agarwal', class: '9-A', subject: 'Mathematics', lastGrade: 'B2', attendance: 85, status: 'Average' },\n];\n\n// Class Overview Card Component\nconst ClassOverviewCard = ({ classData, loading }) => {\n  const theme = useTheme();\n\n  if (loading) {\n    return (\n      <Card sx={{ height: '100%' }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>Class Overview</Typography>\n          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n            <LinearProgress />\n            <LinearProgress />\n            <LinearProgress />\n          </Box>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n          Class Overview\n        </Typography>\n        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">Students:</Typography>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>{classData.totalStudents}</Typography>\n          </Box>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">Avg GPA:</Typography>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main }}>\n              {classData.avgGPA}\n            </Typography>\n          </Box>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">Attendance:</Typography>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.success.main }}>\n              {classData.attendance}%\n            </Typography>\n          </Box>\n        </Box>\n      </CardContent>\n    </Card>\n  );\n};\n\n// Performance Distribution Chart Component\nconst PerformanceDistributionCard = ({ performanceData, loading }) => {\n  if (loading) {\n    return (\n      <Card sx={{ height: '100%' }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>Performance Distribution</Typography>\n          <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n            <LinearProgress sx={{ width: '80%' }} />\n          </Box>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const chartData = {\n    labels: ['A', 'B', 'C', 'D', 'F'],\n    datasets: [\n      {\n        label: 'Students',\n        data: [performanceData.A, performanceData.B, performanceData.C, performanceData.D, performanceData.F],\n        backgroundColor: [\n          '#4CAF50',\n          '#2196F3',\n          '#FF9800',\n          '#FF5722',\n          '#F44336'\n        ],\n        borderWidth: 0,\n        borderRadius: 4,\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false,\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        grid: {\n          display: false,\n        },\n      },\n      x: {\n        grid: {\n          display: false,\n        },\n      },\n    },\n  };\n\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n          Performance Distribution\n        </Typography>\n        <Box sx={{ height: 200 }}>\n          <Bar data={chartData} options={chartOptions} />\n        </Box>\n        <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 2 }}>\n          {Object.entries(performanceData).map(([grade, count]) => (\n            <Box key={grade} sx={{ textAlign: 'center' }}>\n              <Typography variant=\"body2\" color=\"text.secondary\">{grade}:</Typography>\n              <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>{count}</Typography>\n            </Box>\n          ))}\n        </Box>\n      </CardContent>\n    </Card>\n  );\n};\n\n// Attendance Calendar Heat Map Component\nconst AttendanceCalendarCard = ({ attendanceData, loading }) => {\n  if (loading) {\n    return (\n      <Card sx={{ height: '100%' }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>Attendance</Typography>\n          <Box sx={{ height: 150, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n            <LinearProgress sx={{ width: '80%' }} />\n          </Box>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  // Simplified calendar view for demo\n  const calendarDays = Array.from({ length: 30 }, (_, i) => ({\n    day: i + 1,\n    status: Math.random() > 0.1 ? 'present' : Math.random() > 0.5 ? 'absent' : 'late'\n  }));\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'present': return '#4CAF50';\n      case 'absent': return '#F44336';\n      case 'late': return '#FF9800';\n      default: return '#E0E0E0';\n    }\n  };\n\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n          Attendance\n        </Typography>\n        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: 1, mb: 2 }}>\n          {calendarDays.slice(0, 21).map((day) => (\n            <Box\n              key={day.day}\n              sx={{\n                width: 24,\n                height: 24,\n                borderRadius: 1,\n                backgroundColor: getStatusColor(day.status),\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '0.75rem',\n                color: 'white',\n                fontWeight: 500\n              }}\n            >\n              {day.day}\n            </Box>\n          ))}\n        </Box>\n        <Box sx={{ display: 'flex', gap: 2, fontSize: '0.75rem' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n            <Box sx={{ width: 12, height: 12, backgroundColor: '#4CAF50', borderRadius: 1 }} />\n            <Typography variant=\"caption\">Present</Typography>\n          </Box>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n            <Box sx={{ width: 12, height: 12, backgroundColor: '#FF9800', borderRadius: 1 }} />\n            <Typography variant=\"caption\">Late</Typography>\n          </Box>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n            <Box sx={{ width: 12, height: 12, backgroundColor: '#F44336', borderRadius: 1 }} />\n            <Typography variant=\"caption\">Absent</Typography>\n          </Box>\n        </Box>\n      </CardContent>\n    </Card>\n  );\n};\n\n// Behavior Incidents Card Component\nconst BehaviorIncidentsCard = ({ behaviorData, loading }) => {\n  if (loading) {\n    return (\n      <Card sx={{ height: '100%' }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>Behavior Incidents</Typography>\n          <LinearProgress />\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const chartData = {\n    labels: ['Positive', 'Negative'],\n    datasets: [\n      {\n        data: [behaviorData.positive, behaviorData.negative],\n        backgroundColor: ['#4CAF50', '#FF5722'],\n        borderWidth: 0,\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'bottom',\n      },\n    },\n  };\n\n  return (\n    <Card sx={{ height: '100%' }}>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n          Behavior Incidents\n        </Typography>\n        <Box sx={{ height: 150 }}>\n          <Pie data={chartData} options={chartOptions} />\n        </Box>\n        <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 2 }}>\n          <Box sx={{ textAlign: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">Positive:</Typography>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#4CAF50' }}>\n              {behaviorData.positive}\n            </Typography>\n          </Box>\n          <Box sx={{ textAlign: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">Negative:</Typography>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#FF5722' }}>\n              {behaviorData.negative}\n            </Typography>\n          </Box>\n        </Box>\n      </CardContent>\n    </Card>\n  );\n};\n\n// Students Requiring Attention Component\nconst StudentsRequiringAttention = ({ studentsData, loading }) => {\n  if (loading) {\n    return (\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>Students Requiring Attention</Typography>\n          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n            <LinearProgress />\n            <LinearProgress />\n            <LinearProgress />\n          </Box>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n          Students Requiring Attention\n        </Typography>\n        <List>\n          {studentsData.map((student, index) => (\n            <React.Fragment key={student.id}>\n              <ListItem\n                sx={{\n                  px: 0,\n                  '&:hover': {\n                    backgroundColor: alpha('#000', 0.04),\n                    borderRadius: 1,\n                  },\n                }}\n              >\n                <ListItemAvatar>\n                  <Avatar sx={{ bgcolor: student.severity === 'high' ? 'error.main' : 'warning.main' }}>\n                    {student.severity === 'high' ? <Warning /> : <Schedule />}\n                  </Avatar>\n                </ListItemAvatar>\n                <ListItemText\n                  primary={\n                    <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                      {student.name}\n                    </Typography>\n                  }\n                  secondary={student.reason}\n                />\n                <Button\n                  size=\"small\"\n                  endIcon={<ArrowForward />}\n                  sx={{ textTransform: 'none' }}\n                >\n                  View\n                </Button>\n              </ListItem>\n              {index < studentsData.length - 1 && <Divider />}\n            </React.Fragment>\n          ))}\n        </List>\n      </CardContent>\n    </Card>\n  );\n};\n\nconst TeacherDashboard = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const { t } = useTranslation(['common', 'dashboard']);\n  const [selectedClass, setSelectedClass] = useState(teacherData.classes[0]);\n  const [loading, setLoading] = useState(false);\n\n  // Chart data for class performance\n  const classPerformanceData = {\n    labels: ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'],\n    datasets: [\n      {\n        label: 'Grade Distribution',\n        data: [12, 15, 8, 6, 3, 1],\n        backgroundColor: [\n          theme.palette.success.main,\n          theme.palette.success.light,\n          theme.palette.info.main,\n          theme.palette.info.light,\n          theme.palette.warning.main,\n          theme.palette.error.main,\n        ],\n        borderWidth: 2,\n        borderColor: '#fff',\n      },\n    ],\n  };\n\n  // Attendance trend data\n  const attendanceTrendData = {\n    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],\n    datasets: [\n      {\n        label: 'Class 10-A',\n        data: [95, 92, 94, 96, 93, 89],\n        borderColor: theme.palette.primary.main,\n        backgroundColor: alpha(theme.palette.primary.main, 0.1),\n        fill: true,\n        tension: 0.4,\n      },\n      {\n        label: 'Class 10-B',\n        data: [88, 90, 87, 91, 89, 85],\n        borderColor: theme.palette.secondary.main,\n        backgroundColor: alpha(theme.palette.secondary.main, 0.1),\n        fill: true,\n        tension: 0.4,\n      },\n    ],\n  };\n\n  const handleGradeEntry = () => {\n    navigate('/dashboard/grades');\n  };\n\n  const handleAttendance = () => {\n    navigate('/dashboard/attendance');\n  };\n\n  const handleStudentProfile = (studentId) => {\n    navigate(`/dashboard/students/${studentId}`);\n  };\n\n  const handleSWOTAnalysis = (studentId) => {\n    navigate(`/dashboard/students/${studentId}/swot`);\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'Excellent':\n        return 'success';\n      case 'Good':\n        return 'info';\n      case 'Average':\n        return 'warning';\n      case 'Needs Attention':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  return (\n    <Box sx={{ maxWidth: 1400, mx: 'auto', p: 3 }}>\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Box sx={{ mb: 4 }}>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              fontWeight: 600,\n              mb: 1,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n            }}\n          >\n            Teacher Dashboard\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Welcome back, {teacherData.name}! Manage your classes and track student progress.\n          </Typography>\n        </Box>\n      </motion.div>\n\n      {/* Quick Stats */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.1 }}\n          >\n            <Card\n              sx={{\n                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n                color: 'white',\n                cursor: 'pointer',\n                transition: 'transform 0.2s ease',\n                '&:hover': {\n                  transform: 'translateY(-4px)',\n                },\n              }}\n            >\n              <CardContent>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                      {teacherData.totalStudents}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                      Total Students\n                    </Typography>\n                  </Box>\n                  <People sx={{ fontSize: 40, opacity: 0.8 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n          >\n            <Card\n              sx={{\n                background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,\n                color: 'white',\n                cursor: 'pointer',\n                transition: 'transform 0.2s ease',\n                '&:hover': {\n                  transform: 'translateY(-4px)',\n                },\n              }}\n              onClick={handleGradeEntry}\n            >\n              <CardContent>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                      {teacherData.pendingGrades}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                      Pending Grades\n                    </Typography>\n                  </Box>\n                  <Grade sx={{ fontSize: 40, opacity: 0.8 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.3 }}\n          >\n            <Card\n              sx={{\n                background: `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,\n                color: 'white',\n                cursor: 'pointer',\n                transition: 'transform 0.2s ease',\n                '&:hover': {\n                  transform: 'translateY(-4px)',\n                },\n              }}\n            >\n              <CardContent>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                      {teacherData.upcomingTests}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                      Upcoming Tests\n                    </Typography>\n                  </Box>\n                  <Assignment sx={{ fontSize: 40, opacity: 0.8 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n          >\n            <Card\n              sx={{\n                background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,\n                color: 'white',\n                cursor: 'pointer',\n                transition: 'transform 0.2s ease',\n                '&:hover': {\n                  transform: 'translateY(-4px)',\n                },\n              }}\n              onClick={handleAttendance}\n            >\n              <CardContent>\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                  <Box>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                      92%\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                      Avg Attendance\n                    </Typography>\n                  </Box>\n                  <CalendarToday sx={{ fontSize: 40, opacity: 0.8 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </Grid>\n      </Grid>\n\n      {/* Quick Actions */}\n      <Card sx={{ mb: 4 }}>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n            Quick Actions\n          </Typography>\n          <Stack direction=\"row\" spacing={2} flexWrap=\"wrap\" useFlexGap>\n            <Button\n              variant=\"contained\"\n              startIcon={<Grade />}\n              onClick={handleGradeEntry}\n              sx={{\n                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              }}\n            >\n              Enter Grades\n            </Button>\n            <Button\n              variant=\"outlined\"\n              startIcon={<CalendarToday />}\n              onClick={handleAttendance}\n            >\n              Mark Attendance\n            </Button>\n            <Button\n              variant=\"outlined\"\n              startIcon={<Assessment />}\n              onClick={() => navigate('/dashboard/swot/wizard')}\n            >\n              SWOT Analysis\n            </Button>\n            <Button\n              variant=\"outlined\"\n              startIcon={<Assignment />}\n              onClick={() => navigate('/dashboard/reports/generate')}\n            >\n              Generate Reports\n            </Button>\n            <Button\n              variant=\"outlined\"\n              startIcon={<Add />}\n              onClick={() => navigate('/dashboard/students/register')}\n            >\n              Add Student\n            </Button>\n          </Stack>\n        </CardContent>\n      </Card>\n\n      {/* Charts and Analytics */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={8}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Weekly Attendance Trends\n              </Typography>\n              <Box sx={{ height: 300 }}>\n                <Line\n                  data={attendanceTrendData}\n                  options={{\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                      legend: {\n                        position: 'top',\n                      },\n                    },\n                    scales: {\n                      y: {\n                        beginAtZero: false,\n                        min: 80,\n                        max: 100,\n                      },\n                    },\n                  }}\n                />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Grade Distribution\n              </Typography>\n              <Box sx={{ height: 300 }}>\n                <Doughnut\n                  data={classPerformanceData}\n                  options={{\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                      legend: {\n                        position: 'bottom',\n                      },\n                    },\n                  }}\n                />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Student Performance Table */}\n      <Card>\n        <CardContent>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n              Recent Student Performance\n            </Typography>\n            <Button\n              variant=\"outlined\"\n              size=\"small\"\n              onClick={() => navigate('/dashboard/students')}\n            >\n              View All Students\n            </Button>\n          </Box>\n\n          <TableContainer component={Paper} variant=\"outlined\">\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Student Name</TableCell>\n                  <TableCell>Class</TableCell>\n                  <TableCell>Subject</TableCell>\n                  <TableCell>Last Grade</TableCell>\n                  <TableCell>Attendance</TableCell>\n                  <TableCell>Status</TableCell>\n                  <TableCell>Actions</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {studentPerformanceData.map((student) => (\n                  <TableRow key={student.id} hover>\n                    <TableCell>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                        <Avatar sx={{ width: 32, height: 32 }}>\n                          {student.name.charAt(0)}\n                        </Avatar>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                          {student.name}\n                        </Typography>\n                      </Box>\n                    </TableCell>\n                    <TableCell>{student.class}</TableCell>\n                    <TableCell>{student.subject}</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={student.lastGrade}\n                        color={student.lastGrade.startsWith('A') ? 'success' : 'warning'}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                        <LinearProgress\n                          variant=\"determinate\"\n                          value={student.attendance}\n                          sx={{ width: 60, height: 6, borderRadius: 3 }}\n                          color={student.attendance >= 90 ? 'success' : student.attendance >= 75 ? 'warning' : 'error'}\n                        />\n                        <Typography variant=\"body2\">{student.attendance}%</Typography>\n                      </Box>\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        label={student.status}\n                        color={getStatusColor(student.status)}\n                        size=\"small\"\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Stack direction=\"row\" spacing={1}>\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleStudentProfile(student.id)}\n                          color=\"primary\"\n                        >\n                          <Visibility />\n                        </IconButton>\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleSWOTAnalysis(student.id)}\n                          color=\"secondary\"\n                        >\n                          <Assessment />\n                        </IconButton>\n                      </Stack>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </CardContent>\n      </Card>\n\n      {/* Floating Action Button */}\n      <Fab\n        color=\"primary\"\n        sx={{\n          position: 'fixed',\n          bottom: 24,\n          right: 24,\n          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n        }}\n        onClick={() => navigate('/dashboard/students/register')}\n      >\n        <Add />\n      </Fab>\n    </Box>\n  );\n};\n\nexport default TeacherDashboard;\n"], "names": ["ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "ArcElement", "Title", "ChartTooltip", "Legend", "Filler", "teacher<PERSON><PERSON>", "studentPerformanceData", "TeacherDashboard", "theme", "useTheme", "navigate", "useNavigate", "t", "useTranslation", "selectedClass", "setSelectedClass", "useState", "loading", "setLoading", "classPerformanceData", "attendanceTrendData", "alpha", "handleGradeEntry", "handleAttendance", "handleStudentProfile", "studentId", "handleSWOTAnalysis", "getStatusColor", "status", "jsxs", "Box", "jsx", "motion", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "People", "Grade", "Assignment", "CalendarToday", "<PERSON><PERSON>", "<PERSON><PERSON>", "Assessment", "Add", "Line", "Doughnut", "TableContainer", "Paper", "Table", "TableHead", "TableRow", "TableCell", "TableBody", "student", "Avatar", "Chip", "LinearProgress", "IconButton", "Visibility", "Fab"], "mappings": "idAgFAA,EAAQ,SACNC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACF,EAGA,MAAMC,EAAc,CAClB,KAAM,oBAGN,QAAS,CACP,CAAE,MAAO,GAAI,QAAS,IAAK,SAAU,GAAI,MAAO,MAAO,EACvD,CAAE,MAAO,GAAI,QAAS,IAAK,SAAU,GAAI,MAAO,MAAO,EACvD,CAAE,MAAO,EAAG,QAAS,IAAK,SAAU,GAAI,MAAO,MAAO,CACxD,EACA,cAAe,IACf,cAAe,GACf,cAAe,CAMjB,EAGMC,GAAyB,CAC7B,CAAE,GAAI,EAAG,KAAM,oBAAqB,MAAO,OAAQ,QAAS,cAAe,UAAW,KAAM,WAAY,GAAI,OAAQ,WAAY,EAChI,CAAE,GAAI,EAAG,KAAM,oBAAqB,MAAO,OAAQ,QAAS,cAAe,UAAW,KAAM,WAAY,GAAI,OAAQ,MAAO,EAC3H,CAAE,GAAI,EAAG,KAAM,eAAgB,MAAO,OAAQ,QAAS,cAAe,UAAW,KAAM,WAAY,GAAI,OAAQ,SAAU,EACzH,CAAE,GAAI,EAAG,KAAM,mBAAoB,MAAO,MAAO,QAAS,UAAW,UAAW,KAAM,WAAY,GAAI,OAAQ,WAAY,EAC1H,CAAE,GAAI,EAAG,KAAM,gBAAiB,MAAO,OAAQ,QAAS,cAAe,UAAW,KAAM,WAAY,GAAI,OAAQ,MAAO,EACvH,CAAE,GAAI,EAAG,KAAM,eAAgB,MAAO,OAAQ,QAAS,UAAW,UAAW,KAAM,WAAY,GAAI,OAAQ,WAAY,EACvH,CAAE,GAAI,EAAG,KAAM,gBAAiB,MAAO,MAAO,QAAS,cAAe,UAAW,KAAM,WAAY,GAAI,OAAQ,SAAU,CAC3H,EA4UMC,GAAmB,IAAM,CAC7B,MAAMC,EAAQC,EAAS,EACjBC,EAAWC,GAAY,EACvB,CAAE,EAAAC,EAAE,EAAIC,GAAe,CAAC,SAAU,WAAW,CAAC,EAC9C,CAACC,GAAeC,EAAgB,EAAIC,WAASX,EAAY,QAAQ,CAAC,CAAC,EACnE,CAACY,GAASC,EAAU,EAAIF,EAAAA,SAAS,EAAK,EAGtCG,EAAuB,CAC3B,OAAQ,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EAC3C,SAAU,CACR,CACE,MAAO,qBACP,KAAM,CAAC,GAAI,GAAI,EAAG,EAAG,EAAG,CAAC,EACzB,gBAAiB,CACfX,EAAM,QAAQ,QAAQ,KACtBA,EAAM,QAAQ,QAAQ,MACtBA,EAAM,QAAQ,KAAK,KACnBA,EAAM,QAAQ,KAAK,MACnBA,EAAM,QAAQ,QAAQ,KACtBA,EAAM,QAAQ,MAAM,IACtB,EACA,YAAa,EACb,YAAa,MAAA,CACf,CAEJ,EAGMY,EAAsB,CAC1B,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,KAAK,EACjD,SAAU,CACR,CACE,MAAO,aACP,KAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAC7B,YAAaZ,EAAM,QAAQ,QAAQ,KACnC,gBAAiBa,EAAMb,EAAM,QAAQ,QAAQ,KAAM,EAAG,EACtD,KAAM,GACN,QAAS,EACX,EACA,CACE,MAAO,aACP,KAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAC7B,YAAaA,EAAM,QAAQ,UAAU,KACrC,gBAAiBa,EAAMb,EAAM,QAAQ,UAAU,KAAM,EAAG,EACxD,KAAM,GACN,QAAS,EAAA,CACX,CAEJ,EAEMc,EAAmB,IAAM,CAC7BZ,EAAS,mBAAmB,CAC9B,EAEMa,EAAmB,IAAM,CAC7Bb,EAAS,uBAAuB,CAClC,EAEMc,EAAwBC,GAAc,CACjCf,EAAA,uBAAuBe,CAAS,EAAE,CAC7C,EAEMC,EAAsBD,GAAc,CAC/Bf,EAAA,uBAAuBe,CAAS,OAAO,CAClD,EAEME,EAAkBC,GAAW,CACjC,OAAQA,EAAQ,CACd,IAAK,YACI,MAAA,UACT,IAAK,OACI,MAAA,OACT,IAAK,UACI,MAAA,UACT,IAAK,kBACI,MAAA,QACT,QACS,MAAA,SAAA,CAEb,EAGE,OAAAC,OAACC,EAAI,CAAA,GAAI,CAAE,SAAU,KAAM,GAAI,OAAQ,EAAG,CAAA,EAExC,SAAA,CAAAC,EAAA,IAACC,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,GAAI,EAC9B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,EAAI,EAE5B,gBAACF,EAAI,CAAA,GAAI,CAAE,GAAI,CACb,EAAA,SAAA,CAAAC,EAAA,IAACE,EAAA,CACC,QAAQ,KACR,GAAI,CACF,WAAY,IACZ,GAAI,EACJ,WAAY,2BAA2BzB,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,SACrG,qBAAsB,OACtB,oBAAqB,cACrB,eAAgB,MAClB,EACD,SAAA,mBAAA,CAED,EACCqB,EAAA,KAAAI,EAAA,CAAW,QAAQ,QAAQ,MAAM,iBAAiB,SAAA,CAAA,iBAClC5B,EAAY,KAAK,mDAAA,CAClC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAGAwB,EAAAA,KAACK,EAAK,CAAA,UAAS,GAAC,QAAS,EAAG,GAAI,CAAE,GAAI,CAAA,EACpC,SAAA,CAACH,EAAAA,IAAAG,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EAAG,GAAI,EAC5B,SAAAH,EAAA,IAACC,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,GAAK,MAAO,EAAI,EAExC,SAAAD,EAAA,IAACI,EAAA,CACC,GAAI,CACF,WAAY,2BAA2B3B,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,QAAQ,IAAI,SACnG,MAAO,QACP,OAAQ,UACR,WAAY,sBACZ,UAAW,CACT,UAAW,kBAAA,CAEf,EAEA,SAACuB,EAAA,IAAAK,EAAA,CACC,SAACP,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,eAChE,EAAA,SAAA,CAAAD,OAACC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GAAA,EACxC,SAAA5B,EAAY,aACf,CAAA,EACA0B,EAAAA,IAACE,GAAW,QAAQ,QAAQ,GAAI,CAAE,QAAS,EAAI,EAAG,SAElD,gBAAA,CAAA,CAAA,EACF,EACAF,MAACM,GAAO,GAAI,CAAE,SAAU,GAAI,QAAS,GAAO,CAAA,CAAA,CAAA,CAC9C,CACF,CAAA,CAAA,CAAA,CACF,CAAA,EAEJ,EAEAN,EAAAA,IAACG,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EAAG,GAAI,EAC5B,SAAAH,EAAA,IAACC,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,GAAK,MAAO,EAAI,EAExC,SAAAD,EAAA,IAACI,EAAA,CACC,GAAI,CACF,WAAY,2BAA2B3B,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,QAAQ,IAAI,SACnG,MAAO,QACP,OAAQ,UACR,WAAY,sBACZ,UAAW,CACT,UAAW,kBAAA,CAEf,EACA,QAASc,EAET,SAACS,EAAA,IAAAK,EAAA,CACC,SAACP,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,eAChE,EAAA,SAAA,CAAAD,OAACC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GAAA,EACxC,SAAA5B,EAAY,aACf,CAAA,EACA0B,EAAAA,IAACE,GAAW,QAAQ,QAAQ,GAAI,CAAE,QAAS,EAAI,EAAG,SAElD,gBAAA,CAAA,CAAA,EACF,EACAF,MAACO,GAAM,GAAI,CAAE,SAAU,GAAI,QAAS,GAAO,CAAA,CAAA,CAAA,CAC7C,CACF,CAAA,CAAA,CAAA,CACF,CAAA,EAEJ,EAEAP,EAAAA,IAACG,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EAAG,GAAI,EAC5B,SAAAH,EAAA,IAACC,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,GAAK,MAAO,EAAI,EAExC,SAAAD,EAAA,IAACI,EAAA,CACC,GAAI,CACF,WAAY,2BAA2B3B,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,QAAQ,IAAI,SACnG,MAAO,QACP,OAAQ,UACR,WAAY,sBACZ,UAAW,CACT,UAAW,kBAAA,CAEf,EAEA,SAACuB,EAAA,IAAAK,EAAA,CACC,SAACP,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,eAChE,EAAA,SAAA,CAAAD,OAACC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GAAA,EACxC,SAAA5B,EAAY,aACf,CAAA,EACA0B,EAAAA,IAACE,GAAW,QAAQ,QAAQ,GAAI,CAAE,QAAS,EAAI,EAAG,SAElD,gBAAA,CAAA,CAAA,EACF,EACAF,MAACQ,GAAW,GAAI,CAAE,SAAU,GAAI,QAAS,GAAO,CAAA,CAAA,CAAA,CAClD,CACF,CAAA,CAAA,CAAA,CACF,CAAA,EAEJ,EAEAR,EAAAA,IAACG,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EAAG,GAAI,EAC5B,SAAAH,EAAA,IAACC,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,GAAK,MAAO,EAAI,EAExC,SAAAD,EAAA,IAACI,EAAA,CACC,GAAI,CACF,WAAY,2BAA2B3B,EAAM,QAAQ,KAAK,IAAI,QAAQA,EAAM,QAAQ,KAAK,IAAI,SAC7F,MAAO,QACP,OAAQ,UACR,WAAY,sBACZ,UAAW,CACT,UAAW,kBAAA,CAEf,EACA,QAASe,EAET,SAACQ,EAAA,IAAAK,EAAA,CACC,SAACP,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,eAChE,EAAA,SAAA,CAAAD,OAACC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GAAA,EAAO,SAElD,KAAA,CAAA,EACAF,EAAAA,IAACE,GAAW,QAAQ,QAAQ,GAAI,CAAE,QAAS,EAAI,EAAG,SAElD,gBAAA,CAAA,CAAA,EACF,EACAF,MAACS,GAAc,GAAI,CAAE,SAAU,GAAI,QAAS,GAAO,CAAA,CAAA,CAAA,CACrD,CACF,CAAA,CAAA,CAAA,CACF,CAAA,CAEJ,CAAA,CAAA,EACF,EAGAT,EAAAA,IAACI,GAAK,GAAI,CAAE,GAAI,CAAE,EAChB,gBAACC,EACC,CAAA,SAAA,CAAAL,EAAA,IAACE,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,gBAAA,EACAJ,EAAAA,KAACY,GAAM,UAAU,MAAM,QAAS,EAAG,SAAS,OAAO,WAAU,GAC3D,SAAA,CAAAV,EAAA,IAACW,EAAA,CACC,QAAQ,YACR,gBAAYJ,EAAM,EAAA,EAClB,QAAShB,EACT,GAAI,CACF,WAAY,2BAA2Bd,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,QACvG,EACD,SAAA,cAAA,CAED,EACAuB,EAAA,IAACW,EAAA,CACC,QAAQ,WACR,gBAAYF,EAAc,EAAA,EAC1B,QAASjB,EACV,SAAA,iBAAA,CAED,EACAQ,EAAA,IAACW,EAAA,CACC,QAAQ,WACR,gBAAYC,EAAW,EAAA,EACvB,QAAS,IAAMjC,EAAS,wBAAwB,EACjD,SAAA,eAAA,CAED,EACAqB,EAAA,IAACW,EAAA,CACC,QAAQ,WACR,gBAAYH,EAAW,EAAA,EACvB,QAAS,IAAM7B,EAAS,6BAA6B,EACtD,SAAA,kBAAA,CAED,EACAqB,EAAA,IAACW,EAAA,CACC,QAAQ,WACR,gBAAYE,EAAI,EAAA,EAChB,QAAS,IAAMlC,EAAS,8BAA8B,EACvD,SAAA,aAAA,CAAA,CAED,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGAmB,EAAAA,KAACK,EAAK,CAAA,UAAS,GAAC,QAAS,EAAG,GAAI,CAAE,GAAI,CAAA,EACpC,SAAA,CAACH,EAAA,IAAAG,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAH,EAAAA,IAACI,EACC,CAAA,SAAAN,EAAAA,KAACO,EACC,CAAA,SAAA,CAAAL,EAAA,IAACE,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,2BAAA,QACCH,EAAI,CAAA,GAAI,CAAE,OAAQ,KACjB,SAAAC,EAAA,IAACc,EAAA,CACC,KAAMzB,EACN,QAAS,CACP,WAAY,GACZ,oBAAqB,GACrB,QAAS,CACP,OAAQ,CACN,SAAU,KAAA,CAEd,EACA,OAAQ,CACN,EAAG,CACD,YAAa,GACb,IAAK,GACL,IAAK,GAAA,CACP,CACF,CACF,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAEAW,EAAA,IAACG,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAH,EAAAA,IAACI,EACC,CAAA,SAAAN,EAAAA,KAACO,EACC,CAAA,SAAA,CAAAL,EAAA,IAACE,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,qBAAA,QACCH,EAAI,CAAA,GAAI,CAAE,OAAQ,KACjB,SAAAC,EAAA,IAACe,EAAA,CACC,KAAM3B,EACN,QAAS,CACP,WAAY,GACZ,oBAAqB,GACrB,QAAS,CACP,OAAQ,CACN,SAAU,QAAA,CACZ,CACF,CACF,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,EACF,EAGAY,EAAA,IAACI,EACC,CAAA,SAAAN,EAAAA,KAACO,EACC,CAAA,SAAA,CAACP,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,eAAgB,gBAAiB,WAAY,SAAU,GAAI,CAAA,EACrF,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,IAAK,MAAO,cAAe,EAAG,SAEzE,4BAAA,CAAA,EACAF,EAAA,IAACW,EAAA,CACC,QAAQ,WACR,KAAK,QACL,QAAS,IAAMhC,EAAS,qBAAqB,EAC9C,SAAA,mBAAA,CAAA,CAED,EACF,QAECqC,EAAe,CAAA,UAAWC,EAAO,QAAQ,WACxC,gBAACC,EACC,CAAA,SAAA,CAAClB,EAAA,IAAAmB,EAAA,CACC,gBAACC,EACC,CAAA,SAAA,CAAApB,EAAAA,IAACqB,GAAU,SAAY,cAAA,CAAA,EACvBrB,EAAAA,IAACqB,GAAU,SAAK,OAAA,CAAA,EAChBrB,EAAAA,IAACqB,GAAU,SAAO,SAAA,CAAA,EAClBrB,EAAAA,IAACqB,GAAU,SAAU,YAAA,CAAA,EACrBrB,EAAAA,IAACqB,GAAU,SAAU,YAAA,CAAA,EACrBrB,EAAAA,IAACqB,GAAU,SAAM,QAAA,CAAA,EACjBrB,EAAAA,IAACqB,GAAU,SAAO,SAAA,CAAA,CAAA,CAAA,CACpB,CACF,CAAA,EACArB,EAAAA,IAACsB,GACE,SAAuB/C,GAAA,IAAKgD,GAC3BzB,EAAAA,KAACsB,EAA0B,CAAA,MAAK,GAC9B,SAAA,CAACpB,EAAA,IAAAqB,EAAA,CACC,SAACvB,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,CACrD,EAAA,SAAA,CAAAC,EAAA,IAACwB,EAAO,CAAA,GAAI,CAAE,MAAO,GAAI,OAAQ,EAAG,EACjC,SAAQD,EAAA,KAAK,OAAO,CAAC,CACxB,CAAA,EACAvB,EAAAA,IAACE,EAAW,CAAA,QAAQ,QAAQ,GAAI,CAAE,WAAY,GAC3C,EAAA,SAAAqB,EAAQ,IACX,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACAvB,EAAAA,IAACqB,EAAW,CAAA,SAAAE,EAAQ,KAAM,CAAA,EAC1BvB,EAAAA,IAACqB,EAAW,CAAA,SAAAE,EAAQ,OAAQ,CAAA,QAC3BF,EACC,CAAA,SAAArB,EAAA,IAACyB,EAAA,CACC,MAAOF,EAAQ,UACf,MAAOA,EAAQ,UAAU,WAAW,GAAG,EAAI,UAAY,UACvD,KAAK,OAAA,CAAA,EAET,EACCvB,EAAA,IAAAqB,EAAA,CACC,SAACvB,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,CACrD,EAAA,SAAA,CAAAC,EAAA,IAAC0B,EAAA,CACC,QAAQ,cACR,MAAOH,EAAQ,WACf,GAAI,CAAE,MAAO,GAAI,OAAQ,EAAG,aAAc,CAAE,EAC5C,MAAOA,EAAQ,YAAc,GAAK,UAAYA,EAAQ,YAAc,GAAK,UAAY,OAAA,CACvF,EACAzB,EAAAA,KAACI,EAAW,CAAA,QAAQ,QAAS,SAAA,CAAQqB,EAAA,WAAW,GAAA,CAAC,CAAA,CAAA,CAAA,CACnD,CACF,CAAA,QACCF,EACC,CAAA,SAAArB,EAAA,IAACyB,EAAA,CACC,MAAOF,EAAQ,OACf,MAAO3B,EAAe2B,EAAQ,MAAM,EACpC,KAAK,QACL,QAAQ,UAAA,CAAA,EAEZ,QACCF,EACC,CAAA,SAAAvB,OAACY,GAAM,UAAU,MAAM,QAAS,EAC9B,SAAA,CAAAV,EAAA,IAAC2B,EAAA,CACC,KAAK,QACL,QAAS,IAAMlC,EAAqB8B,EAAQ,EAAE,EAC9C,MAAM,UAEN,eAACK,EAAW,CAAA,CAAA,CAAA,CACd,EACA5B,EAAA,IAAC2B,EAAA,CACC,KAAK,QACL,QAAS,IAAMhC,EAAmB4B,EAAQ,EAAE,EAC5C,MAAM,YAEN,eAACX,EAAW,CAAA,CAAA,CAAA,CAAA,CACd,CAAA,CACF,CACF,CAAA,CAAA,GAxDaW,EAAQ,EAyDvB,CACD,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGAvB,EAAA,IAAC6B,EAAA,CACC,MAAM,UACN,GAAI,CACF,SAAU,QACV,OAAQ,GACR,MAAO,GACP,WAAY,2BAA2BpD,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,QACvG,EACA,QAAS,IAAME,EAAS,8BAA8B,EAEtD,eAACkC,EAAI,CAAA,CAAA,CAAA,CAAA,CACP,EACF,CAEJ"}