import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  IconButton,
  Button,
  Fade,
  Skeleton,
  useTheme,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Badge,
} from '@mui/material';
import {
  CalendarToday as CalendarIcon,
  School as SchoolIcon,
  EmojiEvents as FestivalIcon,
  Assessment as ExamIcon,
  Groups as MeetingIcon,
  Celebration as CelebrationIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';

// Indian Academic Calendar with cultural events and festivals
const IndianAcademicCalendar = ({ selectedBoard = 'CBSE', selectedTerm = 'current', loading = false }) => {
  const theme = useTheme();
  const [calendarData, setCalendarData] = useState({});
  const [upcomingEvents, setUpcomingEvents] = useState([]);

  useEffect(() => {
    const loadCalendarData = async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const academicCalendar = {
        CBSE: {
          academicYear: '2024-25',
          terms: {
            term1: {
              start: 'April 1, 2024',
              end: 'September 30, 2024',
              exams: 'October 1-15, 2024',
            },
            term2: {
              start: 'November 1, 2024',
              end: 'March 31, 2025',
              exams: 'April 1-15, 2025',
            },
          },
          boardExams: {
            classX: 'February 15 - March 15, 2025',
            classXII: 'February 15 - April 4, 2025',
          },
        },
        ICSE: {
          academicYear: '2024-25',
          terms: {
            term1: {
              start: 'June 1, 2024',
              end: 'October 31, 2024',
              exams: 'November 1-15, 2024',
            },
            term2: {
              start: 'December 1, 2024',
              end: 'April 30, 2025',
              exams: 'May 1-15, 2025',
            },
          },
          boardExams: {
            classX: 'February 20 - March 25, 2025',
            classXII: 'February 5 - March 30, 2025',
          },
        },
        STATE: {
          academicYear: '2024-25',
          terms: {
            term1: {
              start: 'June 15, 2024',
              end: 'November 30, 2024',
              exams: 'December 1-15, 2024',
            },
            term2: {
              start: 'January 1, 2025',
              end: 'May 31, 2025',
              exams: 'June 1-15, 2025',
            },
          },
          boardExams: {
            classX: 'March 1 - March 25, 2025',
            classXII: 'March 1 - April 10, 2025',
          },
        },
      };

      const events = [
        {
          id: 1,
          title: 'Diwali Celebration',
          date: 'November 12, 2024',
          type: 'festival',
          priority: 'high',
          description: 'School-wide Diwali celebration with cultural programs',
          icon: CelebrationIcon,
          color: '#FF9933',
          cultural: true,
        },
        {
          id: 2,
          title: `${selectedBoard} Term 1 Exams`,
          date: academicCalendar[selectedBoard]?.terms.term1.exams,
          type: 'exam',
          priority: 'high',
          description: 'First term examinations',
          icon: ExamIcon,
          color: '#D81159',
          urgent: true,
        },
        {
          id: 3,
          title: 'Parent-Teacher Conference',
          date: 'November 25, 2024',
          type: 'meeting',
          priority: 'medium',
          description: 'Quarterly parent-teacher meetings',
          icon: MeetingIcon,
          color: '#2E5BA8',
        },
        {
          id: 4,
          title: 'Children\'s Day Celebration',
          date: 'November 14, 2024',
          type: 'festival',
          priority: 'medium',
          description: 'Celebrating Pandit Nehru\'s birthday',
          icon: CelebrationIcon,
          color: '#138808',
          cultural: true,
        },
        {
          id: 5,
          title: 'Winter Break',
          date: 'December 25, 2024 - January 5, 2025',
          type: 'holiday',
          priority: 'low',
          description: 'Winter vacation period',
          icon: ScheduleIcon,
          color: '#F4C430',
        },
        {
          id: 6,
          title: 'Republic Day Celebration',
          date: 'January 26, 2025',
          type: 'national',
          priority: 'high',
          description: 'National holiday celebration',
          icon: FestivalIcon,
          color: '#FF9933',
          cultural: true,
        },
        {
          id: 7,
          title: 'Saraswati Puja',
          date: 'February 3, 2025',
          type: 'festival',
          priority: 'medium',
          description: 'Goddess of knowledge worship',
          icon: SchoolIcon,
          color: '#2E5BA8',
          cultural: true,
        },
        {
          id: 8,
          title: `${selectedBoard} Board Exam Registration`,
          date: 'December 10, 2024',
          type: 'registration',
          priority: 'high',
          description: 'Last date for board exam registration',
          icon: WarningIcon,
          color: '#D81159',
          urgent: true,
        },
      ];

      setCalendarData(academicCalendar[selectedBoard]);
      setUpcomingEvents(events);
    };

    loadCalendarData();
  }, [selectedBoard]);

  const getEventTypeColor = (type) => {
    switch (type) {
      case 'festival': return '#FF9933';
      case 'exam': return '#D81159';
      case 'meeting': return '#2E5BA8';
      case 'holiday': return '#F4C430';
      case 'national': return '#138808';
      case 'registration': return '#D81159';
      default: return '#616161';
    }
  };

  const EventCard = ({ event, index }) => {
    const IconComponent = event.icon;
    
    return (
      <Fade in timeout={300 + index * 100}>
        <Card
          sx={{
            mb: 2,
            border: event.urgent ? `2px solid ${event.color}` : '1px solid #E0E0E0',
            bgcolor: event.cultural ? `${event.color}05` : 'background.paper',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: theme.shadows[4],
            },
            transition: 'all 0.3s ease-in-out',
          }}
        >
          <CardContent sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar
                sx={{
                  bgcolor: event.color,
                  width: 40,
                  height: 40,
                }}
              >
                <IconComponent sx={{ fontSize: 20 }} />
              </Avatar>
              <Box sx={{ flex: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>
                    {event.title}
                  </Typography>
                  {event.urgent && (
                    <Badge
                      badgeContent="Urgent"
                      color="error"
                      sx={{
                        '& .MuiBadge-badge': {
                          fontSize: '0.6rem',
                          height: 16,
                          minWidth: 16,
                        },
                      }}
                    />
                  )}
                  {event.cultural && (
                    <Chip
                      label="Cultural"
                      size="small"
                      sx={{
                        bgcolor: `${event.color}20`,
                        color: event.color,
                        fontSize: '0.7rem',
                        height: 20,
                      }}
                    />
                  )}
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                  {event.date}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {event.description}
                </Typography>
              </Box>
              <Chip
                label={event.type.toUpperCase()}
                size="small"
                sx={{
                  bgcolor: `${event.color}15`,
                  color: event.color,
                  fontWeight: 600,
                }}
              />
            </Box>
          </CardContent>
        </Card>
      </Fade>
    );
  };

  if (loading) {
    return (
      <Box>
        <Skeleton variant="text" width="60%" height={40} sx={{ mb: 3 }} />
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Skeleton variant="text" width="50%" height={32} />
                {[...Array(4)].map((_, index) => (
                  <Skeleton key={index} variant="text" width="90%" height={20} sx={{ mb: 1 }} />
                ))}
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Skeleton variant="text" width="50%" height={32} />
                {[...Array(5)].map((_, index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Skeleton variant="circular" width={40} height={40} />
                    <Box sx={{ flex: 1 }}>
                      <Skeleton variant="text" width="80%" />
                      <Skeleton variant="text" width="60%" />
                    </Box>
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h5" sx={{ fontWeight: 700, mb: 3, color: 'primary.main' }}>
        Indian Academic Calendar - {selectedBoard} Board
      </Typography>

      <Grid container spacing={3}>
        {/* Academic Year Overview */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <CalendarIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Academic Year {calendarData.academicYear}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedBoard} Board Schedule
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2 }}>
                  Term Structure:
                </Typography>
                {calendarData.terms && Object.entries(calendarData.terms).map(([termKey, term]) => (
                  <Box key={termKey} sx={{ mb: 2, p: 2, bgcolor: 'action.hover', borderRadius: 2 }}>
                    <Typography variant="body2" sx={{ fontWeight: 600, textTransform: 'capitalize' }}>
                      {termKey.replace(/(\d+)/, ' $1')}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Classes: {term.start} - {term.end}
                    </Typography>
                    <br />
                    <Typography variant="caption" color="text.secondary">
                      Exams: {term.exams}
                    </Typography>
                  </Box>
                ))}
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2 }}>
                  Board Examinations:
                </Typography>
                {calendarData.boardExams && Object.entries(calendarData.boardExams).map(([classKey, dates]) => (
                  <Box key={classKey} sx={{ mb: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {classKey.replace(/([A-Z])/g, ' $1').trim()}: {dates}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Upcoming Events */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                  <FestivalIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Upcoming Events & Festivals
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Cultural and academic calendar
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
                {upcomingEvents.map((event, index) => (
                  <EventCard key={event.id} event={event} index={index} />
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default IndianAcademicCalendar;
