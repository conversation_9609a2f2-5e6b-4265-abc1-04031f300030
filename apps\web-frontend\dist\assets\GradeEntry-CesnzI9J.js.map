{"version": 3, "file": "GradeEntry-CesnzI9J.js", "sources": ["../../src/components/Grades/GradeEntry.jsx"], "sourcesContent": ["/**\n * <PERSON>idyaMitra Platform - Grade Entry Component\n * \n * Grade entry interface for teachers with Indian grading system support\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Grid,\n  Button,\n  Avatar,\n  TextField,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Stack,\n  Alert,\n  Chip,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Save,\n  Print,\n  Download,\n  Grade,\n  Assessment,\n  TrendingUp,\n  TrendingDown,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\n\n// Sample grade data with Indian student names\nconst gradeData = [\n  { id: 1, name: '<PERSON><PERSON>', rollNumber: 1, previousGrade: 'A1', currentMarks: 95, maxMarks: 100 },\n  { id: 2, name: '<PERSON><PERSON><PERSON><PERSON>', rollNumber: 2, previousGrade: 'A2', currentMarks: 88, maxMarks: 100 },\n  { id: 3, name: '<PERSON><PERSON><PERSON>', rollNumber: 3, previousGrade: 'B1', currentMarks: 82, maxMarks: 100 },\n  { id: 4, name: '<PERSON>', rollNumber: 4, previousGrade: 'A1', currentMarks: 92, maxMarks: 100 },\n  { id: 5, name: 'Ankitha Patel', rollNumber: 5, previousGrade: 'A2', currentMarks: 85, maxMarks: 100 },\n  { id: 6, name: 'Sirisha Nair', rollNumber: 6, previousGrade: 'A1', currentMarks: 96, maxMarks: 100 },\n  { id: 7, name: 'Priya Agarwal', rollNumber: 7, previousGrade: 'B2', currentMarks: 78, maxMarks: 100 },\n];\n\n// Indian grading system\nconst getGradeFromMarks = (marks, maxMarks) => {\n  const percentage = (marks / maxMarks) * 100;\n  if (percentage >= 91) return 'A1';\n  if (percentage >= 81) return 'A2';\n  if (percentage >= 71) return 'B1';\n  if (percentage >= 61) return 'B2';\n  if (percentage >= 51) return 'C1';\n  if (percentage >= 41) return 'C2';\n  if (percentage >= 33) return 'D';\n  return 'E';\n};\n\nconst getGradeColor = (grade) => {\n  switch (grade) {\n    case 'A1':\n    case 'A2':\n      return 'success';\n    case 'B1':\n    case 'B2':\n      return 'info';\n    case 'C1':\n    case 'C2':\n      return 'warning';\n    case 'D':\n    case 'E':\n      return 'error';\n    default:\n      return 'default';\n  }\n};\n\nconst GradeEntry = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const [selectedClass, setSelectedClass] = useState('10-A');\n  const [selectedSubject, setSelectedSubject] = useState('Mathematics');\n  const [selectedTest, setSelectedTest] = useState('Unit Test 1');\n  const [grades, setGrades] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    // Initialize grades state\n    const initialGrades = {};\n    gradeData.forEach(student => {\n      initialGrades[student.id] = {\n        marks: student.currentMarks,\n        maxMarks: student.maxMarks,\n      };\n    });\n    setGrades(initialGrades);\n  }, []);\n\n  const handleMarksChange = (studentId, marks) => {\n    setGrades(prev => ({\n      ...prev,\n      [studentId]: {\n        ...prev[studentId],\n        marks: parseInt(marks) || 0,\n      }\n    }));\n  };\n\n  const handleMaxMarksChange = (studentId, maxMarks) => {\n    setGrades(prev => ({\n      ...prev,\n      [studentId]: {\n        ...prev[studentId],\n        maxMarks: parseInt(maxMarks) || 100,\n      }\n    }));\n  };\n\n  const handleSaveGrades = async () => {\n    setLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      alert('Grades saved successfully!');\n    } catch (error) {\n      console.error('Error saving grades:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateStatistics = () => {\n    const allMarks = Object.values(grades).map(g => (g.marks / g.maxMarks) * 100);\n    const average = allMarks.reduce((sum, mark) => sum + mark, 0) / allMarks.length;\n    const highest = Math.max(...allMarks);\n    const lowest = Math.min(...allMarks);\n    \n    return {\n      average: average.toFixed(1),\n      highest: highest.toFixed(1),\n      lowest: lowest.toFixed(1),\n      totalStudents: allMarks.length,\n    };\n  };\n\n  const stats = calculateStatistics();\n\n  return (\n    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Box sx={{ mb: 4 }}>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              fontWeight: 600,\n              mb: 1,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n            }}\n          >\n            Grade Entry\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Enter and manage student grades with Indian grading system\n          </Typography>\n        </Box>\n      </motion.div>\n\n      {/* Controls */}\n      <Card sx={{ mb: 4 }}>\n        <CardContent>\n          <Grid container spacing={3} alignItems=\"center\">\n            <Grid item xs={12} md={3}>\n              <FormControl fullWidth>\n                <InputLabel>Class</InputLabel>\n                <Select\n                  value={selectedClass}\n                  onChange={(e) => setSelectedClass(e.target.value)}\n                  label=\"Class\"\n                >\n                  <MenuItem value=\"9-A\">Class 9-A</MenuItem>\n                  <MenuItem value=\"9-B\">Class 9-B</MenuItem>\n                  <MenuItem value=\"10-A\">Class 10-A</MenuItem>\n                  <MenuItem value=\"10-B\">Class 10-B</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <FormControl fullWidth>\n                <InputLabel>Subject</InputLabel>\n                <Select\n                  value={selectedSubject}\n                  onChange={(e) => setSelectedSubject(e.target.value)}\n                  label=\"Subject\"\n                >\n                  <MenuItem value=\"Mathematics\">Mathematics</MenuItem>\n                  <MenuItem value=\"Science\">Science</MenuItem>\n                  <MenuItem value=\"English\">English</MenuItem>\n                  <MenuItem value=\"Hindi\">Hindi</MenuItem>\n                  <MenuItem value=\"Social Studies\">Social Studies</MenuItem>\n                  <MenuItem value=\"Telugu\">Telugu</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <FormControl fullWidth>\n                <InputLabel>Test/Assessment</InputLabel>\n                <Select\n                  value={selectedTest}\n                  onChange={(e) => setSelectedTest(e.target.value)}\n                  label=\"Test/Assessment\"\n                >\n                  <MenuItem value=\"Unit Test 1\">Unit Test 1</MenuItem>\n                  <MenuItem value=\"Unit Test 2\">Unit Test 2</MenuItem>\n                  <MenuItem value=\"Mid Term\">Mid Term Exam</MenuItem>\n                  <MenuItem value=\"Final Term\">Final Term Exam</MenuItem>\n                  <MenuItem value=\"Assignment\">Assignment</MenuItem>\n                  <MenuItem value=\"Project\">Project Work</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <Stack direction=\"row\" spacing={2}>\n                <Button\n                  variant=\"contained\"\n                  startIcon={<Save />}\n                  onClick={handleSaveGrades}\n                  loading={loading}\n                  sx={{\n                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n                  }}\n                >\n                  Save Grades\n                </Button>\n              </Stack>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      {/* Statistics */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {stats.average}%\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Class Average\n                  </Typography>\n                </Box>\n                <Assessment sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {stats.highest}%\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Highest Score\n                  </Typography>\n                </Box>\n                <TrendingUp sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {stats.lowest}%\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Lowest Score\n                  </Typography>\n                </Box>\n                <TrendingDown sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {stats.totalStudents}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Total Students\n                  </Typography>\n                </Box>\n                <Grade sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Grade Entry Table */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n            Grade Entry - {selectedClass} | {selectedSubject} | {selectedTest}\n          </Typography>\n\n          <TableContainer component={Paper} variant=\"outlined\">\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Roll No.</TableCell>\n                  <TableCell>Student Name</TableCell>\n                  <TableCell>Previous Grade</TableCell>\n                  <TableCell>Marks Obtained</TableCell>\n                  <TableCell>Max Marks</TableCell>\n                  <TableCell>Percentage</TableCell>\n                  <TableCell>Current Grade</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {gradeData.map((student) => {\n                  const currentGrade = grades[student.id];\n                  const percentage = currentGrade ? (currentGrade.marks / currentGrade.maxMarks) * 100 : 0;\n                  const grade = currentGrade ? getGradeFromMarks(currentGrade.marks, currentGrade.maxMarks) : 'E';\n                  \n                  return (\n                    <TableRow key={student.id} hover>\n                      <TableCell>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                          {student.rollNumber}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                          <Avatar sx={{ width: 32, height: 32 }}>\n                            {student.name.charAt(0)}\n                          </Avatar>\n                          <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                            {student.name}\n                          </Typography>\n                        </Box>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={student.previousGrade}\n                          color={getGradeColor(student.previousGrade)}\n                          size=\"small\"\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <TextField\n                          size=\"small\"\n                          type=\"number\"\n                          value={currentGrade?.marks || ''}\n                          onChange={(e) => handleMarksChange(student.id, e.target.value)}\n                          sx={{ width: 80 }}\n                          inputProps={{ min: 0, max: currentGrade?.maxMarks || 100 }}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <TextField\n                          size=\"small\"\n                          type=\"number\"\n                          value={currentGrade?.maxMarks || 100}\n                          onChange={(e) => handleMaxMarksChange(student.id, e.target.value)}\n                          sx={{ width: 80 }}\n                          inputProps={{ min: 1 }}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                          {percentage.toFixed(1)}%\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={grade}\n                          color={getGradeColor(grade)}\n                          size=\"small\"\n                        />\n                      </TableCell>\n                    </TableRow>\n                  );\n                })}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default GradeEntry;\n"], "names": ["gradeData", "getGradeFromMarks", "marks", "maxMarks", "percentage", "getGradeColor", "grade", "GradeEntry", "theme", "useTheme", "useNavigate", "selectedClass", "setSelectedClass", "useState", "selectedSubject", "setSelectedSubject", "selectedTest", "setSelectedTest", "grades", "setGrades", "loading", "setLoading", "useEffect", "initialGrades", "student", "handleMarksChange", "studentId", "prev", "handleMaxMarksChange", "handleSaveGrades", "resolve", "error", "stats", "allMarks", "g", "average", "sum", "mark", "highest", "lowest", "jsxs", "Box", "jsx", "motion", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "e", "MenuItem", "<PERSON><PERSON>", "<PERSON><PERSON>", "Save", "Assessment", "TrendingUp", "TrendingDown", "Grade", "TableContainer", "Paper", "Table", "TableHead", "TableRow", "TableCell", "TableBody", "currentGrade", "Avatar", "Chip", "TextField"], "mappings": "uWA8CA,MAAMA,EAAY,CAChB,CAAE,GAAI,EAAG,KAAM,oBAAqB,WAAY,EAAG,cAAe,KAAM,aAAc,GAAI,SAAU,GAAI,EACxG,CAAE,GAAI,EAAG,KAAM,oBAAqB,WAAY,EAAG,cAAe,KAAM,aAAc,GAAI,SAAU,GAAI,EACxG,CAAE,GAAI,EAAG,KAAM,eAAgB,WAAY,EAAG,cAAe,KAAM,aAAc,GAAI,SAAU,GAAI,EACnG,CAAE,GAAI,EAAG,KAAM,mBAAoB,WAAY,EAAG,cAAe,KAAM,aAAc,GAAI,SAAU,GAAI,EACvG,CAAE,GAAI,EAAG,KAAM,gBAAiB,WAAY,EAAG,cAAe,KAAM,aAAc,GAAI,SAAU,GAAI,EACpG,CAAE,GAAI,EAAG,KAAM,eAAgB,WAAY,EAAG,cAAe,KAAM,aAAc,GAAI,SAAU,GAAI,EACnG,CAAE,GAAI,EAAG,KAAM,gBAAiB,WAAY,EAAG,cAAe,KAAM,aAAc,GAAI,SAAU,GAAI,CACtG,EAGMC,GAAoB,CAACC,EAAOC,IAAa,CACvC,MAAAC,EAAcF,EAAQC,EAAY,IACpC,OAAAC,GAAc,GAAW,KACzBA,GAAc,GAAW,KACzBA,GAAc,GAAW,KACzBA,GAAc,GAAW,KACzBA,GAAc,GAAW,KACzBA,GAAc,GAAW,KACzBA,GAAc,GAAW,IACtB,GACT,EAEMC,EAAiBC,GAAU,CAC/B,OAAQA,EAAO,CACb,IAAK,KACL,IAAK,KACI,MAAA,UACT,IAAK,KACL,IAAK,KACI,MAAA,OACT,IAAK,KACL,IAAK,KACI,MAAA,UACT,IAAK,IACL,IAAK,IACI,MAAA,QACT,QACS,MAAA,SAAA,CAEb,EAEMC,GAAa,IAAM,CACvB,MAAMC,EAAQC,EAAS,EACNC,EAAY,EAC7B,KAAM,CAACC,EAAeC,CAAgB,EAAIC,EAAAA,SAAS,MAAM,EACnD,CAACC,EAAiBC,CAAkB,EAAIF,EAAAA,SAAS,aAAa,EAC9D,CAACG,EAAcC,CAAe,EAAIJ,EAAAA,SAAS,aAAa,EACxD,CAACK,EAAQC,CAAS,EAAIN,EAAAA,SAAS,CAAA,CAAE,EACjC,CAACO,EAASC,CAAU,EAAIR,EAAAA,SAAS,EAAK,EAE5CS,EAAAA,UAAU,IAAM,CAEd,MAAMC,EAAgB,CAAC,EACvBvB,EAAU,QAAmBwB,GAAA,CACbD,EAAAC,EAAQ,EAAE,EAAI,CAC1B,MAAOA,EAAQ,aACf,SAAUA,EAAQ,QACpB,CAAA,CACD,EACDL,EAAUI,CAAa,CACzB,EAAG,EAAE,EAEC,MAAAE,EAAoB,CAACC,EAAWxB,IAAU,CAC9CiB,EAAmBQ,IAAA,CACjB,GAAGA,EACH,CAACD,CAAS,EAAG,CACX,GAAGC,EAAKD,CAAS,EACjB,MAAO,SAASxB,CAAK,GAAK,CAAA,CAC5B,EACA,CACJ,EAEM0B,EAAuB,CAACF,EAAWvB,IAAa,CACpDgB,EAAmBQ,IAAA,CACjB,GAAGA,EACH,CAACD,CAAS,EAAG,CACX,GAAGC,EAAKD,CAAS,EACjB,SAAU,SAASvB,CAAQ,GAAK,GAAA,CAClC,EACA,CACJ,EAEM0B,EAAmB,SAAY,CACnCR,EAAW,EAAI,EACX,GAAA,CAEF,MAAM,IAAI,QAAQS,GAAW,WAAWA,EAAS,GAAI,CAAC,EACtD,MAAM,4BAA4B,QAC3BC,EAAO,CACN,QAAA,MAAM,uBAAwBA,CAAK,CAAA,QAC3C,CACAV,EAAW,EAAK,CAAA,CAEpB,EAgBMW,GAdsB,IAAM,CAC1B,MAAAC,EAAW,OAAO,OAAOf,CAAM,EAAE,IAAIgB,GAAMA,EAAE,MAAQA,EAAE,SAAY,GAAG,EACtEC,EAAUF,EAAS,OAAO,CAACG,EAAKC,IAASD,EAAMC,EAAM,CAAC,EAAIJ,EAAS,OACnEK,EAAU,KAAK,IAAI,GAAGL,CAAQ,EAC9BM,EAAS,KAAK,IAAI,GAAGN,CAAQ,EAE5B,MAAA,CACL,QAASE,EAAQ,QAAQ,CAAC,EAC1B,QAASG,EAAQ,QAAQ,CAAC,EAC1B,OAAQC,EAAO,QAAQ,CAAC,EACxB,cAAeN,EAAS,MAC1B,CACF,GAEkC,EAGhC,OAAAO,OAACC,EAAI,CAAA,GAAI,CAAE,SAAU,KAAM,GAAI,OAAQ,EAAG,CAAA,EAExC,SAAA,CAAAC,EAAA,IAACC,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,GAAI,EAC9B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,EAAI,EAE5B,gBAACF,EAAI,CAAA,GAAI,CAAE,GAAI,CACb,EAAA,SAAA,CAAAC,EAAA,IAACE,EAAA,CACC,QAAQ,KACR,GAAI,CACF,WAAY,IACZ,GAAI,EACJ,WAAY,2BAA2BpC,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,SACrG,qBAAsB,OACtB,oBAAqB,cACrB,eAAgB,MAClB,EACD,SAAA,aAAA,CAED,QACCoC,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAEnD,4DAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,QAGCC,EAAK,CAAA,GAAI,CAAE,GAAI,GACd,SAAAH,EAAA,IAACI,EACC,CAAA,SAAAN,OAACO,GAAK,UAAS,GAAC,QAAS,EAAG,WAAW,SACrC,SAAA,CAACL,EAAA,IAAAK,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAP,EAAAA,KAACQ,EAAY,CAAA,UAAS,GACpB,SAAA,CAAAN,EAAAA,IAACO,GAAW,SAAK,OAAA,CAAA,EACjBT,EAAA,KAACU,EAAA,CACC,MAAOvC,EACP,SAAWwC,GAAMvC,EAAiBuC,EAAE,OAAO,KAAK,EAChD,MAAM,QAEN,SAAA,CAACT,EAAA,IAAAU,EAAA,CAAS,MAAM,MAAM,SAAS,YAAA,EAC9BV,EAAA,IAAAU,EAAA,CAAS,MAAM,MAAM,SAAS,YAAA,EAC9BV,EAAA,IAAAU,EAAA,CAAS,MAAM,OAAO,SAAU,aAAA,EAChCV,EAAA,IAAAU,EAAA,CAAS,MAAM,OAAO,SAAU,YAAA,CAAA,CAAA,CAAA,CAAA,CACnC,CAAA,CACF,CACF,CAAA,EACAV,EAAA,IAACK,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAP,EAAAA,KAACQ,EAAY,CAAA,UAAS,GACpB,SAAA,CAAAN,EAAAA,IAACO,GAAW,SAAO,SAAA,CAAA,EACnBT,EAAA,KAACU,EAAA,CACC,MAAOpC,EACP,SAAWqC,GAAMpC,EAAmBoC,EAAE,OAAO,KAAK,EAClD,MAAM,UAEN,SAAA,CAACT,EAAA,IAAAU,EAAA,CAAS,MAAM,cAAc,SAAW,cAAA,EACxCV,EAAA,IAAAU,EAAA,CAAS,MAAM,UAAU,SAAO,UAAA,EAChCV,EAAA,IAAAU,EAAA,CAAS,MAAM,UAAU,SAAO,UAAA,EAChCV,EAAA,IAAAU,EAAA,CAAS,MAAM,QAAQ,SAAK,QAAA,EAC5BV,EAAA,IAAAU,EAAA,CAAS,MAAM,iBAAiB,SAAc,iBAAA,EAC9CV,EAAA,IAAAU,EAAA,CAAS,MAAM,SAAS,SAAM,QAAA,CAAA,CAAA,CAAA,CAAA,CACjC,CAAA,CACF,CACF,CAAA,EACAV,EAAA,IAACK,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAP,EAAAA,KAACQ,EAAY,CAAA,UAAS,GACpB,SAAA,CAAAN,EAAAA,IAACO,GAAW,SAAe,iBAAA,CAAA,EAC3BT,EAAA,KAACU,EAAA,CACC,MAAOlC,EACP,SAAWmC,GAAMlC,EAAgBkC,EAAE,OAAO,KAAK,EAC/C,MAAM,kBAEN,SAAA,CAACT,EAAA,IAAAU,EAAA,CAAS,MAAM,cAAc,SAAW,cAAA,EACxCV,EAAA,IAAAU,EAAA,CAAS,MAAM,cAAc,SAAW,cAAA,EACxCV,EAAA,IAAAU,EAAA,CAAS,MAAM,WAAW,SAAa,gBAAA,EACvCV,EAAA,IAAAU,EAAA,CAAS,MAAM,aAAa,SAAe,kBAAA,EAC3CV,EAAA,IAAAU,EAAA,CAAS,MAAM,aAAa,SAAU,aAAA,EACtCV,EAAA,IAAAU,EAAA,CAAS,MAAM,UAAU,SAAY,cAAA,CAAA,CAAA,CAAA,CAAA,CACxC,CAAA,CACF,CACF,CAAA,EACCV,EAAA,IAAAK,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAACL,EAAA,IAAAW,EAAA,CAAM,UAAU,MAAM,QAAS,EAC9B,SAAAX,EAAA,IAACY,EAAA,CACC,QAAQ,YACR,gBAAYC,EAAK,EAAA,EACjB,QAAS1B,EACT,QAAAT,EACA,GAAI,CACF,WAAY,2BAA2BZ,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,QACvG,EACD,SAAA,aAAA,GAGH,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGAgC,EAAAA,KAACO,EAAK,CAAA,UAAS,GAAC,QAAS,EAAG,GAAI,CAAE,GAAI,CAAA,EACpC,SAAA,CAAAL,MAACK,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAL,EAAA,IAACG,EAAA,CACC,GAAI,CACF,WAAY,2BAA2BrC,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,QAAQ,IAAI,SACnG,MAAO,OACT,EAEA,SAACkC,EAAA,IAAAI,EAAA,CACC,SAACN,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,eAChE,EAAA,SAAA,CAAAD,OAACC,EACC,CAAA,SAAA,CAAAD,OAACI,GAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GACxC,EAAA,SAAA,CAAMZ,EAAA,QAAQ,GAAA,EACjB,EACAU,EAAAA,IAACE,GAAW,QAAQ,QAAQ,GAAI,CAAE,QAAS,EAAI,EAAG,SAElD,eAAA,CAAA,CAAA,EACF,EACAF,MAACc,GAAW,GAAI,CAAE,SAAU,GAAI,QAAS,GAAO,CAAA,CAAA,CAAA,CAClD,CACF,CAAA,CAAA,CAAA,EAEJ,QACCT,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAL,EAAA,IAACG,EAAA,CACC,GAAI,CACF,WAAY,2BAA2BrC,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,QAAQ,IAAI,SACnG,MAAO,OACT,EAEA,SAACkC,EAAA,IAAAI,EAAA,CACC,SAACN,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,eAChE,EAAA,SAAA,CAAAD,OAACC,EACC,CAAA,SAAA,CAAAD,OAACI,GAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GACxC,EAAA,SAAA,CAAMZ,EAAA,QAAQ,GAAA,EACjB,EACAU,EAAAA,IAACE,GAAW,QAAQ,QAAQ,GAAI,CAAE,QAAS,EAAI,EAAG,SAElD,eAAA,CAAA,CAAA,EACF,EACAF,MAACe,GAAW,GAAI,CAAE,SAAU,GAAI,QAAS,GAAO,CAAA,CAAA,CAAA,CAClD,CACF,CAAA,CAAA,CAAA,EAEJ,QACCV,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAL,EAAA,IAACG,EAAA,CACC,GAAI,CACF,WAAY,2BAA2BrC,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,QAAQ,IAAI,SACnG,MAAO,OACT,EAEA,SAACkC,EAAA,IAAAI,EAAA,CACC,SAACN,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,eAChE,EAAA,SAAA,CAAAD,OAACC,EACC,CAAA,SAAA,CAAAD,OAACI,GAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GACxC,EAAA,SAAA,CAAMZ,EAAA,OAAO,GAAA,EAChB,EACAU,EAAAA,IAACE,GAAW,QAAQ,QAAQ,GAAI,CAAE,QAAS,EAAI,EAAG,SAElD,cAAA,CAAA,CAAA,EACF,EACAF,MAACgB,GAAa,GAAI,CAAE,SAAU,GAAI,QAAS,GAAO,CAAA,CAAA,CAAA,CACpD,CACF,CAAA,CAAA,CAAA,EAEJ,QACCX,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAL,EAAA,IAACG,EAAA,CACC,GAAI,CACF,WAAY,2BAA2BrC,EAAM,QAAQ,KAAK,IAAI,QAAQA,EAAM,QAAQ,KAAK,IAAI,SAC7F,MAAO,OACT,EAEA,SAACkC,EAAA,IAAAI,EAAA,CACC,SAACN,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,eAChE,EAAA,SAAA,CAAAD,OAACC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GAAA,EACxC,SAAAZ,EAAM,aACT,CAAA,EACAU,EAAAA,IAACE,GAAW,QAAQ,QAAQ,GAAI,CAAE,QAAS,EAAI,EAAG,SAElD,gBAAA,CAAA,CAAA,EACF,EACAF,MAACiB,GAAM,GAAI,CAAE,SAAU,GAAI,QAAS,GAAO,CAAA,CAAA,CAAA,CAC7C,CACF,CAAA,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,EAGAjB,EAAA,IAACG,EACC,CAAA,SAAAL,EAAAA,KAACM,EACC,CAAA,SAAA,CAACN,EAAAA,KAAAI,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAA,EAAkB,SAAA,CAAA,iBAC/DjC,EAAc,MAAIG,EAAgB,MAAIE,CAAA,EACvD,QAEC4C,EAAe,CAAA,UAAWC,EAAO,QAAQ,WACxC,gBAACC,EACC,CAAA,SAAA,CAACpB,EAAA,IAAAqB,EAAA,CACC,gBAACC,EACC,CAAA,SAAA,CAAAtB,EAAAA,IAACuB,GAAU,SAAQ,UAAA,CAAA,EACnBvB,EAAAA,IAACuB,GAAU,SAAY,cAAA,CAAA,EACvBvB,EAAAA,IAACuB,GAAU,SAAc,gBAAA,CAAA,EACzBvB,EAAAA,IAACuB,GAAU,SAAc,gBAAA,CAAA,EACzBvB,EAAAA,IAACuB,GAAU,SAAS,WAAA,CAAA,EACpBvB,EAAAA,IAACuB,GAAU,SAAU,YAAA,CAAA,EACrBvB,EAAAA,IAACuB,GAAU,SAAa,eAAA,CAAA,CAAA,CAAA,CAC1B,CACF,CAAA,EACCvB,EAAA,IAAAwB,EAAA,CACE,SAAUlE,EAAA,IAAKwB,GAAY,CACpB,MAAA2C,EAAejD,EAAOM,EAAQ,EAAE,EAChCpB,EAAa+D,EAAgBA,EAAa,MAAQA,EAAa,SAAY,IAAM,EACjF7D,EAAQ6D,EAAelE,GAAkBkE,EAAa,MAAOA,EAAa,QAAQ,EAAI,IAG1F,OAAA3B,EAAA,KAACwB,EAA0B,CAAA,MAAK,GAC9B,SAAA,CAAAtB,EAAA,IAACuB,EACC,CAAA,SAAAvB,EAAA,IAACE,EAAW,CAAA,QAAQ,QAAQ,GAAI,CAAE,WAAY,KAC3C,SAAQpB,EAAA,UACX,CAAA,EACF,EACCkB,EAAA,IAAAuB,EAAA,CACC,SAACzB,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,CACrD,EAAA,SAAA,CAAAC,EAAA,IAAC0B,EAAO,CAAA,GAAI,CAAE,MAAO,GAAI,OAAQ,EAAG,EACjC,SAAQ5C,EAAA,KAAK,OAAO,CAAC,CACxB,CAAA,EACAkB,EAAAA,IAACE,EAAW,CAAA,QAAQ,QAAQ,GAAI,CAAE,WAAY,GAC3C,EAAA,SAAApB,EAAQ,IACX,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QACCyC,EACC,CAAA,SAAAvB,EAAA,IAAC2B,EAAA,CACC,MAAO7C,EAAQ,cACf,MAAOnB,EAAcmB,EAAQ,aAAa,EAC1C,KAAK,OAAA,CAAA,EAET,QACCyC,EACC,CAAA,SAAAvB,EAAA,IAAC4B,EAAA,CACC,KAAK,QACL,KAAK,SACL,OAAOH,GAAA,YAAAA,EAAc,QAAS,GAC9B,SAAWhB,GAAM1B,EAAkBD,EAAQ,GAAI2B,EAAE,OAAO,KAAK,EAC7D,GAAI,CAAE,MAAO,EAAG,EAChB,WAAY,CAAE,IAAK,EAAG,KAAKgB,GAAA,YAAAA,EAAc,WAAY,GAAI,CAAA,CAAA,EAE7D,QACCF,EACC,CAAA,SAAAvB,EAAA,IAAC4B,EAAA,CACC,KAAK,QACL,KAAK,SACL,OAAOH,GAAA,YAAAA,EAAc,WAAY,IACjC,SAAWhB,GAAMvB,EAAqBJ,EAAQ,GAAI2B,EAAE,OAAO,KAAK,EAChE,GAAI,CAAE,MAAO,EAAG,EAChB,WAAY,CAAE,IAAK,CAAE,CAAA,CAAA,EAEzB,EACAT,EAAAA,IAACuB,EACC,CAAA,SAAAzB,EAAAA,KAACI,EAAW,CAAA,QAAQ,QAAQ,GAAI,CAAE,WAAY,GAAA,EAC3C,SAAA,CAAAxC,EAAW,QAAQ,CAAC,EAAE,GAAA,CAAA,CACzB,CACF,CAAA,QACC6D,EACC,CAAA,SAAAvB,EAAA,IAAC2B,EAAA,CACC,MAAO/D,EACP,MAAOD,EAAcC,CAAK,EAC1B,KAAK,OAAA,CAAA,CAET,CAAA,CAAA,CAAA,EAtDakB,EAAQ,EAuDvB,CAAA,CAEH,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ"}