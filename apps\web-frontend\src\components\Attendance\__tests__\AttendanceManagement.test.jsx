/**
 * VidyaMitra Platform - AttendanceManagement Component Tests
 * 
 * Comprehensive unit tests for attendance management with Indian educational context
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import AttendanceManagement from '../AttendanceManagement';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
  },
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

describe('AttendanceManagement Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders attendance management interface', () => {
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    expect(screen.getByText('Attendance Management')).toBeInTheDocument();
    expect(screen.getByText('Mark and track student attendance efficiently')).toBeInTheDocument();
  });

  test('displays class and date selection', () => {
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    expect(screen.getByText('Select Class')).toBeInTheDocument();
    expect(screen.getByText('Select Date')).toBeInTheDocument();
    expect(screen.getByText('Select Subject')).toBeInTheDocument();
  });

  test('shows Indian class structure', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Open class dropdown
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);

    // Should show Indian class structure
    await waitFor(() => {
      expect(screen.getByText('Class 10-A')).toBeInTheDocument();
      expect(screen.getByText('Class 10-B')).toBeInTheDocument();
      expect(screen.getByText('Class 9-A')).toBeInTheDocument();
      expect(screen.getByText('Class 9-B')).toBeInTheDocument();
    });
  });

  test('displays student list with Indian names', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Select class
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    // Should display student list
    await waitFor(() => {
      expect(screen.getByText('Sanju Kumar Reddy')).toBeInTheDocument();
      expect(screen.getByText('Niraimathi Selvam')).toBeInTheDocument();
      expect(screen.getByText('Mahesh Reddy')).toBeInTheDocument();
      expect(screen.getByText('Ravi Teja Sharma')).toBeInTheDocument();
      expect(screen.getByText('Ankitha Patel')).toBeInTheDocument();
    });
  });

  test('allows marking attendance for individual students', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Select class
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    await waitFor(() => {
      // Find attendance buttons for first student
      const presentButtons = screen.getAllByLabelText(/mark present/i);
      expect(presentButtons.length).toBeGreaterThan(0);
    });

    // Mark student as present
    const presentButtons = screen.getAllByLabelText(/mark present/i);
    await user.click(presentButtons[0]);

    // Should show present status
    await waitFor(() => {
      expect(screen.getByText('Present')).toBeInTheDocument();
    });
  });

  test('supports bulk attendance marking', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Select class
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    await waitFor(() => {
      expect(screen.getByText('Mark All Present')).toBeInTheDocument();
      expect(screen.getByText('Mark All Absent')).toBeInTheDocument();
    });

    // Mark all present
    await user.click(screen.getByText('Mark All Present'));

    // Should mark all students as present
    await waitFor(() => {
      const presentStatuses = screen.getAllByText('Present');
      expect(presentStatuses.length).toBeGreaterThan(1);
    });
  });

  test('displays attendance statistics', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Select class
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    await waitFor(() => {
      expect(screen.getByText('Attendance Summary')).toBeInTheDocument();
      expect(screen.getByText('Total Students')).toBeInTheDocument();
      expect(screen.getByText('Present')).toBeInTheDocument();
      expect(screen.getByText('Absent')).toBeInTheDocument();
      expect(screen.getByText('Attendance Rate')).toBeInTheDocument();
    });
  });

  test('handles late arrival marking', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Select class
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    await waitFor(() => {
      const lateButtons = screen.getAllByLabelText(/mark late/i);
      expect(lateButtons.length).toBeGreaterThan(0);
    });

    // Mark student as late
    const lateButtons = screen.getAllByLabelText(/mark late/i);
    await user.click(lateButtons[0]);

    // Should show late status
    await waitFor(() => {
      expect(screen.getByText('Late')).toBeInTheDocument();
    });
  });

  test('supports attendance notes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Select class and navigate to student
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    await waitFor(() => {
      const noteButtons = screen.getAllByLabelText(/add note/i);
      expect(noteButtons.length).toBeGreaterThan(0);
    });

    // Add note for student
    const noteButtons = screen.getAllByLabelText(/add note/i);
    await user.click(noteButtons[0]);

    // Should open note dialog
    await waitFor(() => {
      expect(screen.getByText('Add Attendance Note')).toBeInTheDocument();
    });

    // Add note
    const noteInput = screen.getByLabelText(/note/i);
    await user.type(noteInput, 'Student was sick');
    await user.click(screen.getByRole('button', { name: /save note/i }));

    // Should save note
    await waitFor(() => {
      expect(screen.getByText('Note saved successfully')).toBeInTheDocument();
    });
  });

  test('displays attendance history', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Navigate to history tab
    await user.click(screen.getByText('Attendance History'));

    await waitFor(() => {
      expect(screen.getByText('Weekly Attendance')).toBeInTheDocument();
      expect(screen.getByText('Monthly Attendance')).toBeInTheDocument();
    });
  });

  test('supports attendance report generation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Navigate to reports section
    await user.click(screen.getByText('Generate Report'));

    await waitFor(() => {
      expect(screen.getByText('Attendance Report')).toBeInTheDocument();
      expect(screen.getByText('Select Date Range')).toBeInTheDocument();
      expect(screen.getByText('Select Format')).toBeInTheDocument();
    });

    // Generate report
    await user.click(screen.getByRole('button', { name: /generate/i }));

    // Should show success message
    await waitFor(() => {
      expect(screen.getByText('Report generated successfully')).toBeInTheDocument();
    });
  });

  test('handles subject-wise attendance', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Select subject
    const subjectSelect = screen.getByLabelText(/subject/i);
    await user.click(subjectSelect);

    await waitFor(() => {
      expect(screen.getByText('Mathematics')).toBeInTheDocument();
      expect(screen.getByText('Physics')).toBeInTheDocument();
      expect(screen.getByText('Chemistry')).toBeInTheDocument();
      expect(screen.getByText('English')).toBeInTheDocument();
      expect(screen.getByText('Hindi')).toBeInTheDocument();
    });

    await user.click(screen.getByText('Mathematics'));

    // Should update attendance context
    await waitFor(() => {
      expect(screen.getByText('Mathematics - Class 10-A')).toBeInTheDocument();
    });
  });

  test('validates required fields', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Try to save without selecting class
    const saveButton = screen.getByRole('button', { name: /save attendance/i });
    await user.click(saveButton);

    // Should show validation error
    await waitFor(() => {
      expect(screen.getByText('Please select a class')).toBeInTheDocument();
    });
  });

  test('supports accessibility features', () => {
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Check for proper heading structure
    expect(screen.getByRole('heading', { name: /attendance management/i })).toBeInTheDocument();

    // Check for proper form labels
    expect(screen.getByLabelText(/class/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/date/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/subject/i)).toBeInTheDocument();

    // Check for proper button accessibility
    expect(screen.getByRole('button', { name: /save attendance/i })).toBeInTheDocument();
  });

  test('displays real-time attendance count', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Select class
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    await waitFor(() => {
      expect(screen.getByText('0/25 Present')).toBeInTheDocument();
    });

    // Mark a student present
    const presentButtons = screen.getAllByLabelText(/mark present/i);
    await user.click(presentButtons[0]);

    // Should update count
    await waitFor(() => {
      expect(screen.getByText('1/25 Present')).toBeInTheDocument();
    });
  });

  test('handles attendance submission', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Complete attendance marking
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    await waitFor(() => {
      const presentButtons = screen.getAllByLabelText(/mark present/i);
      user.click(presentButtons[0]);
    });

    // Submit attendance
    const saveButton = screen.getByRole('button', { name: /save attendance/i });
    await user.click(saveButton);

    // Should show success message
    await waitFor(() => {
      expect(screen.getByText('Attendance saved successfully')).toBeInTheDocument();
    });
  });
});

// Integration tests
describe('AttendanceManagement Integration', () => {
  test('complete attendance workflow', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Complete workflow
    // 1. Select class
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    // 2. Select subject
    const subjectSelect = screen.getByLabelText(/subject/i);
    await user.click(subjectSelect);
    await user.click(screen.getByText('Mathematics'));

    // 3. Mark attendance for multiple students
    await waitFor(() => {
      const presentButtons = screen.getAllByLabelText(/mark present/i);
      expect(presentButtons.length).toBeGreaterThan(0);
    });

    const presentButtons = screen.getAllByLabelText(/mark present/i);
    const absentButtons = screen.getAllByLabelText(/mark absent/i);
    
    // Mark some present, some absent
    await user.click(presentButtons[0]);
    await user.click(presentButtons[1]);
    await user.click(absentButtons[2]);

    // 4. Save attendance
    const saveButton = screen.getByRole('button', { name: /save attendance/i });
    await user.click(saveButton);

    // Should complete successfully
    await waitFor(() => {
      expect(screen.getByText('Attendance saved successfully')).toBeInTheDocument();
    });
  });

  test('attendance data consistency', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AttendanceManagement />
      </TestWrapper>
    );

    // Select class and mark attendance
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10-A'));

    await waitFor(() => {
      const presentButtons = screen.getAllByLabelText(/mark present/i);
      user.click(presentButtons[0]);
      user.click(presentButtons[1]);
    });

    // Check statistics update
    await waitFor(() => {
      expect(screen.getByText('2/25 Present')).toBeInTheDocument();
      expect(screen.getByText('8%')).toBeInTheDocument(); // Attendance rate
    });
  });
});
