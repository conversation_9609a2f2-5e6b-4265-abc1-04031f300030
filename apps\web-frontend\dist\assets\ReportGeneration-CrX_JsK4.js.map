{"version": 3, "file": "ReportGeneration-CrX_JsK4.js", "sources": ["../../src/components/Reports/ReportGeneration.jsx"], "sourcesContent": ["/**\n * VidyaMitra Platform - Report Generation Component\n * \n * Automated report card generation with Indian educational context\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Grid,\n  Button,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Checkbox,\n  FormControlLabel,\n  FormGroup,\n  Stack,\n  Alert,\n  LinearProgress,\n  Chip,\n  Avatar,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemAvatar,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Print,\n  Download,\n  Email,\n  Preview,\n  Assessment,\n  School,\n  CalendarToday,\n  CheckCircle,\n  Schedule,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\n\n// Sample report templates\nconst reportTemplates = [\n  {\n    id: 'progress_report',\n    name: 'Student Progress Report',\n    description: 'Comprehensive academic and behavioral progress report',\n    icon: Assessment,\n    color: 'primary',\n  },\n  {\n    id: 'report_card',\n    name: 'Report Card',\n    description: 'Traditional report card with grades and attendance',\n    icon: School,\n    color: 'success',\n  },\n  {\n    id: 'swot_report',\n    name: 'SWOT Analysis Report',\n    description: 'Detailed SWOT analysis with recommendations',\n    icon: Assessment,\n    color: 'info',\n  },\n  {\n    id: 'attendance_report',\n    name: 'Attendance Report',\n    description: 'Monthly attendance summary and patterns',\n    icon: CalendarToday,\n    color: 'warning',\n  },\n];\n\n// Sample students for report generation\nconst students = [\n  { id: 1, name: 'Sanju Kumar Reddy', class: '10-A', rollNumber: 1 },\n  { id: 2, name: 'Niraimathi Selvam', class: '10-A', rollNumber: 2 },\n  { id: 3, name: 'Mahesh Reddy', class: '10-B', rollNumber: 3 },\n  { id: 4, name: 'Ravi Teja Sharma', class: '9-A', rollNumber: 4 },\n  { id: 5, name: 'Ankitha Patel', class: '10-A', rollNumber: 5 },\n  { id: 6, name: 'Sirisha Nair', class: '10-B', rollNumber: 6 },\n  { id: 7, name: 'Priya Agarwal', class: '9-A', rollNumber: 7 },\n];\n\nconst ReportGeneration = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const [selectedTemplate, setSelectedTemplate] = useState('');\n  const [selectedClass, setSelectedClass] = useState('');\n  const [selectedStudents, setSelectedStudents] = useState([]);\n  const [reportPeriod, setReportPeriod] = useState('');\n  const [includeOptions, setIncludeOptions] = useState({\n    grades: true,\n    attendance: true,\n    behavior: true,\n    swot: false,\n    parentComments: false,\n  });\n  const [generating, setGenerating] = useState(false);\n  const [progress, setProgress] = useState(0);\n\n  const handleStudentSelection = (studentId) => {\n    setSelectedStudents(prev => \n      prev.includes(studentId)\n        ? prev.filter(id => id !== studentId)\n        : [...prev, studentId]\n    );\n  };\n\n  const handleSelectAllStudents = () => {\n    const classStudents = students.filter(student => \n      selectedClass ? student.class === selectedClass : true\n    );\n    setSelectedStudents(classStudents.map(student => student.id));\n  };\n\n  const handleDeselectAllStudents = () => {\n    setSelectedStudents([]);\n  };\n\n  const handleIncludeOptionChange = (option) => (event) => {\n    setIncludeOptions(prev => ({\n      ...prev,\n      [option]: event.target.checked,\n    }));\n  };\n\n  const handleGenerateReports = async () => {\n    if (!selectedTemplate || selectedStudents.length === 0) {\n      alert('Please select a template and at least one student');\n      return;\n    }\n\n    setGenerating(true);\n    setProgress(0);\n\n    try {\n      // Simulate report generation progress\n      for (let i = 0; i <= 100; i += 10) {\n        await new Promise(resolve => setTimeout(resolve, 200));\n        setProgress(i);\n      }\n\n      alert(`Successfully generated ${selectedStudents.length} reports!`);\n    } catch (error) {\n      console.error('Error generating reports:', error);\n    } finally {\n      setGenerating(false);\n      setProgress(0);\n    }\n  };\n\n  const filteredStudents = selectedClass \n    ? students.filter(student => student.class === selectedClass)\n    : students;\n\n  const selectedTemplate_obj = reportTemplates.find(template => template.id === selectedTemplate);\n\n  return (\n    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Box sx={{ mb: 4 }}>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              fontWeight: 600,\n              mb: 1,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n            }}\n          >\n            Report Generation\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Generate comprehensive student reports with Indian educational context\n          </Typography>\n        </Box>\n      </motion.div>\n\n      <Grid container spacing={3}>\n        {/* Configuration Panel */}\n        <Grid item xs={12} md={8}>\n          {/* Template Selection */}\n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Select Report Template\n              </Typography>\n              <Grid container spacing={2}>\n                {reportTemplates.map((template) => (\n                  <Grid item xs={12} sm={6} key={template.id}>\n                    <Card\n                      sx={{\n                        cursor: 'pointer',\n                        border: selectedTemplate === template.id ? `2px solid ${theme.palette[template.color].main}` : '1px solid',\n                        borderColor: selectedTemplate === template.id ? `${template.color}.main` : 'divider',\n                        transition: 'all 0.2s ease',\n                        '&:hover': {\n                          transform: 'translateY(-2px)',\n                          boxShadow: theme.shadows[4],\n                        },\n                      }}\n                      onClick={() => setSelectedTemplate(template.id)}\n                    >\n                      <CardContent>\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>\n                          <template.icon color={template.color} />\n                          <Typography variant=\"subtitle1\" sx={{ fontWeight: 600 }}>\n                            {template.name}\n                          </Typography>\n                        </Box>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          {template.description}\n                        </Typography>\n                      </CardContent>\n                    </Card>\n                  </Grid>\n                ))}\n              </Grid>\n            </CardContent>\n          </Card>\n\n          {/* Class and Period Selection */}\n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Report Configuration\n              </Typography>\n              <Grid container spacing={3}>\n                <Grid item xs={12} md={4}>\n                  <FormControl fullWidth>\n                    <InputLabel>Class</InputLabel>\n                    <Select\n                      value={selectedClass}\n                      onChange={(e) => setSelectedClass(e.target.value)}\n                      label=\"Class\"\n                    >\n                      <MenuItem value=\"\">All Classes</MenuItem>\n                      <MenuItem value=\"9-A\">Class 9-A</MenuItem>\n                      <MenuItem value=\"9-B\">Class 9-B</MenuItem>\n                      <MenuItem value=\"10-A\">Class 10-A</MenuItem>\n                      <MenuItem value=\"10-B\">Class 10-B</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Grid>\n                <Grid item xs={12} md={4}>\n                  <FormControl fullWidth>\n                    <InputLabel>Report Period</InputLabel>\n                    <Select\n                      value={reportPeriod}\n                      onChange={(e) => setReportPeriod(e.target.value)}\n                      label=\"Report Period\"\n                    >\n                      <MenuItem value=\"Q1_2024\">Q1 2024-2025</MenuItem>\n                      <MenuItem value=\"Q2_2024\">Q2 2024-2025</MenuItem>\n                      <MenuItem value=\"Q3_2024\">Q3 2024-2025</MenuItem>\n                      <MenuItem value=\"Q4_2024\">Q4 2024-2025</MenuItem>\n                      <MenuItem value=\"ANNUAL_2024\">Annual 2024-2025</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Grid>\n                <Grid item xs={12} md={4}>\n                  <Stack direction=\"row\" spacing={1}>\n                    <Button\n                      variant=\"outlined\"\n                      size=\"small\"\n                      onClick={handleSelectAllStudents}\n                    >\n                      Select All\n                    </Button>\n                    <Button\n                      variant=\"outlined\"\n                      size=\"small\"\n                      onClick={handleDeselectAllStudents}\n                    >\n                      Deselect All\n                    </Button>\n                  </Stack>\n                </Grid>\n              </Grid>\n            </CardContent>\n          </Card>\n\n          {/* Include Options */}\n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Include in Report\n              </Typography>\n              <FormGroup>\n                <Grid container spacing={2}>\n                  <Grid item xs={12} sm={6}>\n                    <FormControlLabel\n                      control={\n                        <Checkbox\n                          checked={includeOptions.grades}\n                          onChange={handleIncludeOptionChange('grades')}\n                        />\n                      }\n                      label=\"Academic Grades\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <FormControlLabel\n                      control={\n                        <Checkbox\n                          checked={includeOptions.attendance}\n                          onChange={handleIncludeOptionChange('attendance')}\n                        />\n                      }\n                      label=\"Attendance Record\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <FormControlLabel\n                      control={\n                        <Checkbox\n                          checked={includeOptions.behavior}\n                          onChange={handleIncludeOptionChange('behavior')}\n                        />\n                      }\n                      label=\"Behavioral Assessment\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <FormControlLabel\n                      control={\n                        <Checkbox\n                          checked={includeOptions.swot}\n                          onChange={handleIncludeOptionChange('swot')}\n                        />\n                      }\n                      label=\"SWOT Analysis\"\n                    />\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <FormControlLabel\n                      control={\n                        <Checkbox\n                          checked={includeOptions.parentComments}\n                          onChange={handleIncludeOptionChange('parentComments')}\n                        />\n                      }\n                      label=\"Parent Comments Section\"\n                    />\n                  </Grid>\n                </Grid>\n              </FormGroup>\n            </CardContent>\n          </Card>\n\n          {/* Student Selection */}\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Select Students ({selectedStudents.length} selected)\n              </Typography>\n              <List sx={{ maxHeight: 300, overflow: 'auto' }}>\n                {filteredStudents.map((student) => (\n                  <ListItem\n                    key={student.id}\n                    sx={{\n                      cursor: 'pointer',\n                      borderRadius: 1,\n                      mb: 1,\n                      border: selectedStudents.includes(student.id) ? `2px solid ${theme.palette.primary.main}` : '1px solid',\n                      borderColor: selectedStudents.includes(student.id) ? 'primary.main' : 'divider',\n                      background: selectedStudents.includes(student.id) ? alpha(theme.palette.primary.main, 0.05) : 'transparent',\n                    }}\n                    onClick={() => handleStudentSelection(student.id)}\n                  >\n                    <ListItemAvatar>\n                      <Avatar sx={{ bgcolor: selectedStudents.includes(student.id) ? 'primary.main' : 'grey.400' }}>\n                        {student.name.charAt(0)}\n                      </Avatar>\n                    </ListItemAvatar>\n                    <ListItemText\n                      primary={student.name}\n                      secondary={`Class ${student.class} - Roll No. ${student.rollNumber}`}\n                    />\n                    {selectedStudents.includes(student.id) && (\n                      <CheckCircle color=\"primary\" />\n                    )}\n                  </ListItem>\n                ))}\n              </List>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Summary Panel */}\n        <Grid item xs={12} md={4}>\n          <Card sx={{ position: 'sticky', top: 24 }}>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n                Report Summary\n              </Typography>\n\n              {selectedTemplate_obj && (\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 600 }}>\n                    {selectedTemplate_obj.name}\n                  </Typography>\n                  <Typography variant=\"body2\">\n                    {selectedTemplate_obj.description}\n                  </Typography>\n                </Alert>\n              )}\n\n              <Stack spacing={2} sx={{ mb: 3 }}>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Selected Students:\n                  </Typography>\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n                    {selectedStudents.length}\n                  </Typography>\n                </Box>\n\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Class Filter:\n                  </Typography>\n                  <Typography variant=\"body1\">\n                    {selectedClass || 'All Classes'}\n                  </Typography>\n                </Box>\n\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Report Period:\n                  </Typography>\n                  <Typography variant=\"body1\">\n                    {reportPeriod || 'Not selected'}\n                  </Typography>\n                </Box>\n              </Stack>\n\n              {generating && (\n                <Box sx={{ mb: 3 }}>\n                  <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                    Generating Reports... {progress}%\n                  </Typography>\n                  <LinearProgress variant=\"determinate\" value={progress} />\n                </Box>\n              )}\n\n              <Stack spacing={2}>\n                <Button\n                  variant=\"contained\"\n                  fullWidth\n                  startIcon={<Assessment />}\n                  onClick={handleGenerateReports}\n                  disabled={!selectedTemplate || selectedStudents.length === 0 || generating}\n                  sx={{\n                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n                  }}\n                >\n                  {generating ? 'Generating...' : 'Generate Reports'}\n                </Button>\n\n                <Button\n                  variant=\"outlined\"\n                  fullWidth\n                  startIcon={<Preview />}\n                  disabled={!selectedTemplate}\n                >\n                  Preview Template\n                </Button>\n\n                <Button\n                  variant=\"outlined\"\n                  fullWidth\n                  startIcon={<Download />}\n                  disabled={selectedStudents.length === 0}\n                >\n                  Download All\n                </Button>\n\n                <Button\n                  variant=\"outlined\"\n                  fullWidth\n                  startIcon={<Email />}\n                  disabled={selectedStudents.length === 0}\n                >\n                  Email to Parents\n                </Button>\n              </Stack>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default ReportGeneration;\n"], "names": ["reportTemplates", "Assessment", "School", "CalendarToday", "students", "ReportGeneration", "theme", "useTheme", "useNavigate", "selectedTemplate", "setSelectedTemplate", "useState", "selectedClass", "setSelectedClass", "selectedStudents", "setSelectedStudents", "reportPeriod", "setReportPeriod", "includeOptions", "setIncludeOptions", "generating", "setGenerating", "progress", "setProgress", "handleStudentSelection", "studentId", "prev", "id", "handleSelectAllStudents", "classStudents", "student", "handleDeselectAllStudents", "handleIncludeOptionChange", "option", "event", "handleGenerateReports", "i", "resolve", "error", "filteredStudents", "selectedTemplate_obj", "template", "jsxs", "Box", "jsx", "motion", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "FormControl", "InputLabel", "Select", "e", "MenuItem", "<PERSON><PERSON>", "<PERSON><PERSON>", "FormGroup", "FormControlLabel", "Checkbox", "List", "ListItem", "alpha", "ListItemAvatar", "Avatar", "ListItemText", "CheckCircle", "<PERSON><PERSON>", "LinearProgress", "Preview", "Download", "Email"], "mappings": "mYAgDA,MAAMA,EAAkB,CACtB,CACE,GAAI,kBACJ,KAAM,0BACN,YAAa,wDACb,KAAMC,EACN,MAAO,SACT,EACA,CACE,GAAI,cACJ,KAAM,cACN,YAAa,qDACb,KAAMC,EACN,MAAO,SACT,EACA,CACE,GAAI,cACJ,KAAM,uBACN,YAAa,8CACb,KAAMD,EACN,MAAO,MACT,EACA,CACE,GAAI,oBACJ,KAAM,oBACN,YAAa,0CACb,KAAME,EACN,MAAO,SAAA,CAEX,EAGMC,EAAW,CACf,CAAE,GAAI,EAAG,KAAM,oBAAqB,MAAO,OAAQ,WAAY,CAAE,EACjE,CAAE,GAAI,EAAG,KAAM,oBAAqB,MAAO,OAAQ,WAAY,CAAE,EACjE,CAAE,GAAI,EAAG,KAAM,eAAgB,MAAO,OAAQ,WAAY,CAAE,EAC5D,CAAE,GAAI,EAAG,KAAM,mBAAoB,MAAO,MAAO,WAAY,CAAE,EAC/D,CAAE,GAAI,EAAG,KAAM,gBAAiB,MAAO,OAAQ,WAAY,CAAE,EAC7D,CAAE,GAAI,EAAG,KAAM,eAAgB,MAAO,OAAQ,WAAY,CAAE,EAC5D,CAAE,GAAI,EAAG,KAAM,gBAAiB,MAAO,MAAO,WAAY,CAAE,CAC9D,EAEMC,GAAmB,IAAM,CAC7B,MAAMC,EAAQC,EAAS,EACNC,GAAY,EAC7B,KAAM,CAACC,EAAkBC,CAAmB,EAAIC,EAAAA,SAAS,EAAE,EACrD,CAACC,EAAeC,CAAgB,EAAIF,EAAAA,SAAS,EAAE,EAC/C,CAACG,EAAkBC,CAAmB,EAAIJ,EAAAA,SAAS,CAAA,CAAE,EACrD,CAACK,EAAcC,CAAe,EAAIN,EAAAA,SAAS,EAAE,EAC7C,CAACO,EAAgBC,CAAiB,EAAIR,WAAS,CACnD,OAAQ,GACR,WAAY,GACZ,SAAU,GACV,KAAM,GACN,eAAgB,EAAA,CACjB,EACK,CAACS,EAAYC,CAAa,EAAIV,EAAAA,SAAS,EAAK,EAC5C,CAACW,EAAUC,CAAW,EAAIZ,EAAAA,SAAS,CAAC,EAEpCa,EAA0BC,GAAc,CAC5CV,EACEW,GAAAA,EAAK,SAASD,CAAS,EACnBC,EAAK,OAAaC,GAAAA,IAAOF,CAAS,EAClC,CAAC,GAAGC,EAAMD,CAAS,CACzB,CACF,EAEMG,EAA0B,IAAM,CACpC,MAAMC,EAAgBzB,EAAS,OAC7B0B,GAAAlB,EAAgBkB,EAAQ,QAAUlB,EAAgB,EACpD,EACAG,EAAoBc,EAAc,IAAeC,GAAAA,EAAQ,EAAE,CAAC,CAC9D,EAEMC,EAA4B,IAAM,CACtChB,EAAoB,CAAA,CAAE,CACxB,EAEMiB,EAA6BC,GAAYC,GAAU,CACvDf,EAA2BO,IAAA,CACzB,GAAGA,EACH,CAACO,CAAM,EAAGC,EAAM,OAAO,OAAA,EACvB,CACJ,EAEMC,EAAwB,SAAY,CACxC,GAAI,CAAC1B,GAAoBK,EAAiB,SAAW,EAAG,CACtD,MAAM,mDAAmD,EACzD,MAAA,CAGFO,EAAc,EAAI,EAClBE,EAAY,CAAC,EAET,GAAA,CAEF,QAASa,EAAI,EAAGA,GAAK,IAAKA,GAAK,GAC7B,MAAM,IAAI,QAAQC,GAAW,WAAWA,EAAS,GAAG,CAAC,EACrDd,EAAYa,CAAC,EAGT,MAAA,0BAA0BtB,EAAiB,MAAM,WAAW,QAC3DwB,EAAO,CACN,QAAA,MAAM,4BAA6BA,CAAK,CAAA,QAChD,CACAjB,EAAc,EAAK,EACnBE,EAAY,CAAC,CAAA,CAEjB,EAEMgB,EAAmB3B,EACrBR,EAAS,UAAkB0B,EAAQ,QAAUlB,CAAa,EAC1DR,EAEEoC,EAAuBxC,EAAgB,KAAiByC,GAAAA,EAAS,KAAOhC,CAAgB,EAG5F,OAAAiC,OAACC,EAAI,CAAA,GAAI,CAAE,SAAU,KAAM,GAAI,OAAQ,EAAG,CAAA,EAExC,SAAA,CAAAC,EAAA,IAACC,GAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,GAAI,EAC9B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,EAAI,EAE5B,gBAACF,EAAI,CAAA,GAAI,CAAE,GAAI,CACb,EAAA,SAAA,CAAAC,EAAA,IAACE,EAAA,CACC,QAAQ,KACR,GAAI,CACF,WAAY,IACZ,GAAI,EACJ,WAAY,2BAA2BxC,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,SACrG,qBAAsB,OACtB,oBAAqB,cACrB,eAAgB,MAClB,EACD,SAAA,mBAAA,CAED,QACCwC,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAEnD,wEAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAECJ,EAAA,KAAAK,EAAA,CAAK,UAAS,GAAC,QAAS,EAEvB,SAAA,CAAAL,OAACK,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EAErB,SAAA,CAAAH,EAAAA,IAACI,GAAK,GAAI,CAAE,GAAI,CAAE,EAChB,gBAACC,EACC,CAAA,SAAA,CAAAL,EAAA,IAACE,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,yBAAA,QACCC,EAAK,CAAA,UAAS,GAAC,QAAS,EACtB,SAAgB/C,EAAA,IAAKyC,SACnBM,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAH,EAAA,IAACI,EAAA,CACC,GAAI,CACF,OAAQ,UACR,OAAQvC,IAAqBgC,EAAS,GAAK,aAAanC,EAAM,QAAQmC,EAAS,KAAK,EAAE,IAAI,GAAK,YAC/F,YAAahC,IAAqBgC,EAAS,GAAK,GAAGA,EAAS,KAAK,QAAU,UAC3E,WAAY,gBACZ,UAAW,CACT,UAAW,mBACX,UAAWnC,EAAM,QAAQ,CAAC,CAAA,CAE9B,EACA,QAAS,IAAMI,EAAoB+B,EAAS,EAAE,EAE9C,gBAACQ,EACC,CAAA,SAAA,CAACP,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,EAAG,GAAI,CAAA,EAC5D,SAAA,CAAAC,EAAA,IAACH,EAAS,KAAT,CAAc,MAAOA,EAAS,MAAO,EACtCG,EAAAA,IAACE,EAAW,CAAA,QAAQ,YAAY,GAAI,CAAE,WAAY,GAC/C,EAAA,SAAAL,EAAS,IACZ,CAAA,CAAA,EACF,QACCK,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAC/B,WAAS,WACZ,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,GAxB2BL,EAAS,EA0BxC,CACD,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGAG,EAAAA,IAACI,GAAK,GAAI,CAAE,GAAI,CAAE,EAChB,gBAACC,EACC,CAAA,SAAA,CAAAL,EAAA,IAACE,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,uBAAA,EACCJ,EAAA,KAAAK,EAAA,CAAK,UAAS,GAAC,QAAS,EACvB,SAAA,CAACH,EAAA,IAAAG,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAL,EAAAA,KAACQ,EAAY,CAAA,UAAS,GACpB,SAAA,CAAAN,EAAAA,IAACO,GAAW,SAAK,OAAA,CAAA,EACjBT,EAAA,KAACU,EAAA,CACC,MAAOxC,EACP,SAAWyC,GAAMxC,EAAiBwC,EAAE,OAAO,KAAK,EAChD,MAAM,QAEN,SAAA,CAACT,EAAA,IAAAU,EAAA,CAAS,MAAM,GAAG,SAAW,cAAA,EAC7BV,EAAA,IAAAU,EAAA,CAAS,MAAM,MAAM,SAAS,YAAA,EAC9BV,EAAA,IAAAU,EAAA,CAAS,MAAM,MAAM,SAAS,YAAA,EAC9BV,EAAA,IAAAU,EAAA,CAAS,MAAM,OAAO,SAAU,aAAA,EAChCV,EAAA,IAAAU,EAAA,CAAS,MAAM,OAAO,SAAU,YAAA,CAAA,CAAA,CAAA,CAAA,CACnC,CAAA,CACF,CACF,CAAA,EACAV,EAAA,IAACG,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAL,EAAAA,KAACQ,EAAY,CAAA,UAAS,GACpB,SAAA,CAAAN,EAAAA,IAACO,GAAW,SAAa,eAAA,CAAA,EACzBT,EAAA,KAACU,EAAA,CACC,MAAOpC,EACP,SAAWqC,GAAMpC,EAAgBoC,EAAE,OAAO,KAAK,EAC/C,MAAM,gBAEN,SAAA,CAACT,EAAA,IAAAU,EAAA,CAAS,MAAM,UAAU,SAAY,eAAA,EACrCV,EAAA,IAAAU,EAAA,CAAS,MAAM,UAAU,SAAY,eAAA,EACrCV,EAAA,IAAAU,EAAA,CAAS,MAAM,UAAU,SAAY,eAAA,EACrCV,EAAA,IAAAU,EAAA,CAAS,MAAM,UAAU,SAAY,eAAA,EACrCV,EAAA,IAAAU,EAAA,CAAS,MAAM,cAAc,SAAgB,kBAAA,CAAA,CAAA,CAAA,CAAA,CAChD,CAAA,CACF,CACF,CAAA,EACCV,EAAA,IAAAG,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAACL,EAAAA,KAAAa,EAAA,CAAM,UAAU,MAAM,QAAS,EAC9B,SAAA,CAAAX,EAAA,IAACY,EAAA,CACC,QAAQ,WACR,KAAK,QACL,QAAS5B,EACV,SAAA,YAAA,CAED,EACAgB,EAAA,IAACY,EAAA,CACC,QAAQ,WACR,KAAK,QACL,QAASzB,EACV,SAAA,cAAA,CAAA,CAED,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGAa,EAAAA,IAACI,GAAK,GAAI,CAAE,GAAI,CAAE,EAChB,gBAACC,EACC,CAAA,SAAA,CAAAL,EAAA,IAACE,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,oBAAA,QACCW,EACC,CAAA,SAAAf,OAACK,GAAK,UAAS,GAAC,QAAS,EACvB,SAAA,CAAAH,MAACG,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAH,EAAA,IAACc,EAAA,CACC,QACEd,EAAA,IAACe,EAAA,CACC,QAASzC,EAAe,OACxB,SAAUc,EAA0B,QAAQ,CAAA,CAC9C,EAEF,MAAM,iBAAA,CAAA,EAEV,QACCe,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAH,EAAA,IAACc,EAAA,CACC,QACEd,EAAA,IAACe,EAAA,CACC,QAASzC,EAAe,WACxB,SAAUc,EAA0B,YAAY,CAAA,CAClD,EAEF,MAAM,mBAAA,CAAA,EAEV,QACCe,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAH,EAAA,IAACc,EAAA,CACC,QACEd,EAAA,IAACe,EAAA,CACC,QAASzC,EAAe,SACxB,SAAUc,EAA0B,UAAU,CAAA,CAChD,EAEF,MAAM,uBAAA,CAAA,EAEV,QACCe,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAH,EAAA,IAACc,EAAA,CACC,QACEd,EAAA,IAACe,EAAA,CACC,QAASzC,EAAe,KACxB,SAAUc,EAA0B,MAAM,CAAA,CAC5C,EAEF,MAAM,eAAA,CAAA,EAEV,QACCe,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAH,EAAA,IAACc,EAAA,CACC,QACEd,EAAA,IAACe,EAAA,CACC,QAASzC,EAAe,eACxB,SAAUc,EAA0B,gBAAgB,CAAA,CACtD,EAEF,MAAM,yBAAA,CAAA,CAEV,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGAY,EAAA,IAACI,EACC,CAAA,SAAAN,EAAAA,KAACO,EACC,CAAA,SAAA,CAACP,EAAAA,KAAAI,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAA,EAAkB,SAAA,CAAA,oBAC5DhC,EAAiB,OAAO,YAAA,EAC5C,EACC8B,EAAAA,IAAAgB,EAAA,CAAK,GAAI,CAAE,UAAW,IAAK,SAAU,MAAA,EACnC,SAAArB,EAAiB,IAAKT,GACrBY,EAAA,KAACmB,EAAA,CAEC,GAAI,CACF,OAAQ,UACR,aAAc,EACd,GAAI,EACJ,OAAQ/C,EAAiB,SAASgB,EAAQ,EAAE,EAAI,aAAaxB,EAAM,QAAQ,QAAQ,IAAI,GAAK,YAC5F,YAAaQ,EAAiB,SAASgB,EAAQ,EAAE,EAAI,eAAiB,UACtE,WAAYhB,EAAiB,SAASgB,EAAQ,EAAE,EAAIgC,EAAMxD,EAAM,QAAQ,QAAQ,KAAM,GAAI,EAAI,aAChG,EACA,QAAS,IAAMkB,EAAuBM,EAAQ,EAAE,EAEhD,SAAA,CAAAc,EAAAA,IAACmB,GACC,SAACnB,MAAAoB,EAAA,CAAO,GAAI,CAAE,QAASlD,EAAiB,SAASgB,EAAQ,EAAE,EAAI,eAAiB,UAC7E,EAAA,SAAAA,EAAQ,KAAK,OAAO,CAAC,CACxB,CAAA,EACF,EACAc,EAAA,IAACqB,EAAA,CACC,QAASnC,EAAQ,KACjB,UAAW,SAASA,EAAQ,KAAK,eAAeA,EAAQ,UAAU,EAAA,CACpE,EACChB,EAAiB,SAASgB,EAAQ,EAAE,GAClCc,EAAAA,IAAAsB,EAAA,CAAY,MAAM,SAAU,CAAA,CAAA,CAAA,EArB1BpC,EAAQ,EAAA,CAwBhB,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,QAGCiB,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAH,MAACI,EAAK,CAAA,GAAI,CAAE,SAAU,SAAU,IAAK,IACnC,gBAACC,EACC,CAAA,SAAA,CAAAL,EAAA,IAACE,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAe,EAAG,SAEhF,iBAAA,EAECN,UACE2B,GAAM,CAAA,SAAS,OAAO,GAAI,CAAE,GAAI,CAC/B,EAAA,SAAA,CAACvB,EAAAA,IAAAE,EAAA,CAAW,QAAQ,YAAY,GAAI,CAAE,WAAY,GAAA,EAC/C,SAAAN,EAAqB,IACxB,CAAA,EACCI,EAAA,IAAAE,EAAA,CAAW,QAAQ,QACjB,WAAqB,WACxB,CAAA,CAAA,EACF,EAGFJ,OAACa,GAAM,QAAS,EAAG,GAAI,CAAE,GAAI,CAC3B,EAAA,SAAA,CAAAb,OAACC,EACC,CAAA,SAAA,CAAAC,MAACE,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAEnD,qBAAA,EACAF,EAAAA,IAACE,EAAW,CAAA,QAAQ,KAAK,GAAI,CAAE,WAAY,GACxC,EAAA,SAAAhC,EAAiB,MACpB,CAAA,CAAA,EACF,SAEC6B,EACC,CAAA,SAAA,CAAAC,MAACE,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAEnD,gBAAA,EACCF,EAAA,IAAAE,EAAA,CAAW,QAAQ,QACjB,YAAiB,aACpB,CAAA,CAAA,EACF,SAECH,EACC,CAAA,SAAA,CAAAC,MAACE,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAEnD,iBAAA,EACCF,EAAA,IAAAE,EAAA,CAAW,QAAQ,QACjB,YAAgB,cACnB,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEC1B,GACEsB,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,GAAI,CACb,EAAA,SAAA,CAAAD,OAACI,GAAW,QAAQ,QAAQ,GAAI,CAAE,GAAI,CAAK,EAAA,SAAA,CAAA,yBAClBxB,EAAS,GAAA,EAClC,EACCsB,EAAA,IAAAwB,GAAA,CAAe,QAAQ,cAAc,MAAO9C,CAAU,CAAA,CAAA,EACzD,EAGFoB,EAAAA,KAACa,EAAM,CAAA,QAAS,EACd,SAAA,CAAAX,EAAA,IAACY,EAAA,CACC,QAAQ,YACR,UAAS,GACT,gBAAYvD,EAAW,EAAA,EACvB,QAASkC,EACT,SAAU,CAAC1B,GAAoBK,EAAiB,SAAW,GAAKM,EAChE,GAAI,CACF,WAAY,2BAA2Bd,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,QACvG,EAEC,WAAa,gBAAkB,kBAAA,CAClC,EAEAsC,EAAA,IAACY,EAAA,CACC,QAAQ,WACR,UAAS,GACT,gBAAYa,GAAQ,EAAA,EACpB,SAAU,CAAC5D,EACZ,SAAA,kBAAA,CAED,EAEAmC,EAAA,IAACY,EAAA,CACC,QAAQ,WACR,UAAS,GACT,gBAAYc,GAAS,EAAA,EACrB,SAAUxD,EAAiB,SAAW,EACvC,SAAA,cAAA,CAED,EAEA8B,EAAA,IAACY,EAAA,CACC,QAAQ,WACR,UAAS,GACT,gBAAYe,GAAM,EAAA,EAClB,SAAUzD,EAAiB,SAAW,EACvC,SAAA,kBAAA,CAAA,CAED,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ"}