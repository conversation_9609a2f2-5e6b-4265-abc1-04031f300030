/**
 * VidyaMitra Platform - Report Generation Component
 * 
 * Automated report card generation with Indian educational context
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Stack,
  Alert,
  LinearProgress,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Print,
  Download,
  Email,
  Preview,
  Assessment,
  School,
  CalendarToday,
  CheckCircle,
  Schedule,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

// Sample report templates
const reportTemplates = [
  {
    id: 'progress_report',
    name: 'Student Progress Report',
    description: 'Comprehensive academic and behavioral progress report',
    icon: Assessment,
    color: 'primary',
  },
  {
    id: 'report_card',
    name: 'Report Card',
    description: 'Traditional report card with grades and attendance',
    icon: School,
    color: 'success',
  },
  {
    id: 'swot_report',
    name: 'SWOT Analysis Report',
    description: 'Detailed SWOT analysis with recommendations',
    icon: Assessment,
    color: 'info',
  },
  {
    id: 'attendance_report',
    name: 'Attendance Report',
    description: 'Monthly attendance summary and patterns',
    icon: CalendarToday,
    color: 'warning',
  },
];

// Sample students for report generation
const students = [
  { id: 1, name: 'Sanju Kumar Reddy', class: '10-A', rollNumber: 1 },
  { id: 2, name: 'Niraimathi Selvam', class: '10-A', rollNumber: 2 },
  { id: 3, name: 'Mahesh Reddy', class: '10-B', rollNumber: 3 },
  { id: 4, name: 'Ravi Teja Sharma', class: '9-A', rollNumber: 4 },
  { id: 5, name: 'Ankitha Patel', class: '10-A', rollNumber: 5 },
  { id: 6, name: 'Sirisha Nair', class: '10-B', rollNumber: 6 },
  { id: 7, name: 'Priya Agarwal', class: '9-A', rollNumber: 7 },
];

const ReportGeneration = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [selectedStudents, setSelectedStudents] = useState([]);
  const [reportPeriod, setReportPeriod] = useState('');
  const [includeOptions, setIncludeOptions] = useState({
    grades: true,
    attendance: true,
    behavior: true,
    swot: false,
    parentComments: false,
  });
  const [generating, setGenerating] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleStudentSelection = (studentId) => {
    setSelectedStudents(prev => 
      prev.includes(studentId)
        ? prev.filter(id => id !== studentId)
        : [...prev, studentId]
    );
  };

  const handleSelectAllStudents = () => {
    const classStudents = students.filter(student => 
      selectedClass ? student.class === selectedClass : true
    );
    setSelectedStudents(classStudents.map(student => student.id));
  };

  const handleDeselectAllStudents = () => {
    setSelectedStudents([]);
  };

  const handleIncludeOptionChange = (option) => (event) => {
    setIncludeOptions(prev => ({
      ...prev,
      [option]: event.target.checked,
    }));
  };

  const handleGenerateReports = async () => {
    if (!selectedTemplate || selectedStudents.length === 0) {
      alert('Please select a template and at least one student');
      return;
    }

    setGenerating(true);
    setProgress(0);

    try {
      // Simulate report generation progress
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 200));
        setProgress(i);
      }

      alert(`Successfully generated ${selectedStudents.length} reports!`);
    } catch (error) {
      console.error('Error generating reports:', error);
    } finally {
      setGenerating(false);
      setProgress(0);
    }
  };

  const filteredStudents = selectedClass 
    ? students.filter(student => student.class === selectedClass)
    : students;

  const selectedTemplate_obj = reportTemplates.find(template => template.id === selectedTemplate);

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 600,
              mb: 1,
              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
            }}
          >
            Report Generation
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Generate comprehensive student reports with Indian educational context
          </Typography>
        </Box>
      </motion.div>

      <Grid container spacing={3}>
        {/* Configuration Panel */}
        <Grid item xs={12} md={8}>
          {/* Template Selection */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                Select Report Template
              </Typography>
              <Grid container spacing={2}>
                {reportTemplates.map((template) => (
                  <Grid item xs={12} sm={6} key={template.id}>
                    <Card
                      sx={{
                        cursor: 'pointer',
                        border: selectedTemplate === template.id ? `2px solid ${theme.palette[template.color].main}` : '1px solid',
                        borderColor: selectedTemplate === template.id ? `${template.color}.main` : 'divider',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: theme.shadows[4],
                        },
                      }}
                      onClick={() => setSelectedTemplate(template.id)}
                    >
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                          <template.icon color={template.color} />
                          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                            {template.name}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          {template.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>

          {/* Class and Period Selection */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                Report Configuration
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Class</InputLabel>
                    <Select
                      value={selectedClass}
                      onChange={(e) => setSelectedClass(e.target.value)}
                      label="Class"
                    >
                      <MenuItem value="">All Classes</MenuItem>
                      <MenuItem value="9-A">Class 9-A</MenuItem>
                      <MenuItem value="9-B">Class 9-B</MenuItem>
                      <MenuItem value="10-A">Class 10-A</MenuItem>
                      <MenuItem value="10-B">Class 10-B</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Report Period</InputLabel>
                    <Select
                      value={reportPeriod}
                      onChange={(e) => setReportPeriod(e.target.value)}
                      label="Report Period"
                    >
                      <MenuItem value="Q1_2024">Q1 2024-2025</MenuItem>
                      <MenuItem value="Q2_2024">Q2 2024-2025</MenuItem>
                      <MenuItem value="Q3_2024">Q3 2024-2025</MenuItem>
                      <MenuItem value="Q4_2024">Q4 2024-2025</MenuItem>
                      <MenuItem value="ANNUAL_2024">Annual 2024-2025</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Stack direction="row" spacing={1}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={handleSelectAllStudents}
                    >
                      Select All
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={handleDeselectAllStudents}
                    >
                      Deselect All
                    </Button>
                  </Stack>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Include Options */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                Include in Report
              </Typography>
              <FormGroup>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeOptions.grades}
                          onChange={handleIncludeOptionChange('grades')}
                        />
                      }
                      label="Academic Grades"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeOptions.attendance}
                          onChange={handleIncludeOptionChange('attendance')}
                        />
                      }
                      label="Attendance Record"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeOptions.behavior}
                          onChange={handleIncludeOptionChange('behavior')}
                        />
                      }
                      label="Behavioral Assessment"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeOptions.swot}
                          onChange={handleIncludeOptionChange('swot')}
                        />
                      }
                      label="SWOT Analysis"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={includeOptions.parentComments}
                          onChange={handleIncludeOptionChange('parentComments')}
                        />
                      }
                      label="Parent Comments Section"
                    />
                  </Grid>
                </Grid>
              </FormGroup>
            </CardContent>
          </Card>

          {/* Student Selection */}
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                Select Students ({selectedStudents.length} selected)
              </Typography>
              <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                {filteredStudents.map((student) => (
                  <ListItem
                    key={student.id}
                    sx={{
                      cursor: 'pointer',
                      borderRadius: 1,
                      mb: 1,
                      border: selectedStudents.includes(student.id) ? `2px solid ${theme.palette.primary.main}` : '1px solid',
                      borderColor: selectedStudents.includes(student.id) ? 'primary.main' : 'divider',
                      background: selectedStudents.includes(student.id) ? alpha(theme.palette.primary.main, 0.05) : 'transparent',
                    }}
                    onClick={() => handleStudentSelection(student.id)}
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: selectedStudents.includes(student.id) ? 'primary.main' : 'grey.400' }}>
                        {student.name.charAt(0)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={student.name}
                      secondary={`Class ${student.class} - Roll No. ${student.rollNumber}`}
                    />
                    {selectedStudents.includes(student.id) && (
                      <CheckCircle color="primary" />
                    )}
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Summary Panel */}
        <Grid item xs={12} md={4}>
          <Card sx={{ position: 'sticky', top: 24 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                Report Summary
              </Typography>

              {selectedTemplate_obj && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    {selectedTemplate_obj.name}
                  </Typography>
                  <Typography variant="body2">
                    {selectedTemplate_obj.description}
                  </Typography>
                </Alert>
              )}

              <Stack spacing={2} sx={{ mb: 3 }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Selected Students:
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {selectedStudents.length}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Class Filter:
                  </Typography>
                  <Typography variant="body1">
                    {selectedClass || 'All Classes'}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Report Period:
                  </Typography>
                  <Typography variant="body1">
                    {reportPeriod || 'Not selected'}
                  </Typography>
                </Box>
              </Stack>

              {generating && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    Generating Reports... {progress}%
                  </Typography>
                  <LinearProgress variant="determinate" value={progress} />
                </Box>
              )}

              <Stack spacing={2}>
                <Button
                  variant="contained"
                  fullWidth
                  startIcon={<Assessment />}
                  onClick={handleGenerateReports}
                  disabled={!selectedTemplate || selectedStudents.length === 0 || generating}
                  sx={{
                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                  }}
                >
                  {generating ? 'Generating...' : 'Generate Reports'}
                </Button>

                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<Preview />}
                  disabled={!selectedTemplate}
                >
                  Preview Template
                </Button>

                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<Download />}
                  disabled={selectedStudents.length === 0}
                >
                  Download All
                </Button>

                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<Email />}
                  disabled={selectedStudents.length === 0}
                >
                  Email to Parents
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ReportGeneration;
