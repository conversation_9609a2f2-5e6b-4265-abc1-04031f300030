/**
 * VidyaMitra Platform - Student Profile Component
 * 
 * Comprehensive student profile display with academic performance,
 * SWOT analysis, and Indian educational context
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Avatar,
  Grid,
  Chip,
  Button,
  Tab,
  Tabs,
  LinearProgress,
  IconButton,
  Divider,
  Stack,
  Alert,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Person,
  School,
  Assessment,
  TrendingUp,
  Edit,
  Print,
  Share,
  Phone,
  Email,
  LocationOn,
  CalendarToday,
  Star,
  EmojiEvents,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useParams, useNavigate } from 'react-router-dom';
import { Line, Radar, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  RadialLinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  RadialLinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// Sample student data (in real app, this would come from API)
const sampleStudentData = {
  id: 1,
  firstName: 'Sanju',
  middleName: 'Kumar',
  lastName: 'Reddy',
  admissionNumber: 'VMS2024001',
  grade: 10,
  section: 'A',
  rollNumber: 15,
  board: 'CBSE',
  dateOfBirth: '2008-05-15',
  gender: 'Male',
  bloodGroup: 'B+',
  profilePhoto: null,
  
  // Contact Information
  address: 'H.No 12-34, Jubilee Hills, Hyderabad',
  city: 'Hyderabad',
  state: 'Telangana',
  pincode: '500033',
  phone: '+91 9876543210',
  email: '<EMAIL>',
  
  // Parent Information
  fatherName: 'Rajesh Kumar Reddy',
  fatherOccupation: 'Software Engineer',
  fatherPhone: '+91 9876543211',
  motherName: 'Priya Reddy',
  motherOccupation: 'Teacher',
  motherPhone: '+91 9876543212',
  
  // Academic Performance
  currentGPA: 8.7,
  attendance: 92,
  subjects: [
    { name: 'Mathematics', grade: 'A1', marks: 95, teacher: 'Mrs. Sharma' },
    { name: 'Science', grade: 'A1', marks: 92, teacher: 'Mr. Patel' },
    { name: 'English', grade: 'A2', marks: 88, teacher: 'Ms. Johnson' },
    { name: 'Hindi', grade: 'A1', marks: 94, teacher: 'Mrs. Gupta' },
    { name: 'Social Studies', grade: 'A2', marks: 86, teacher: 'Mr. Singh' },
    { name: 'Telugu', grade: 'A1', marks: 96, teacher: 'Mrs. Rao' },
  ],
  
  // Performance Trends
  performanceTrends: [
    { month: 'Apr', gpa: 8.2 },
    { month: 'May', gpa: 8.4 },
    { month: 'Jun', gpa: 8.6 },
    { month: 'Jul', gpa: 8.5 },
    { month: 'Aug', gpa: 8.7 },
    { month: 'Sep', gpa: 8.8 },
  ],
  
  // SWOT Analysis
  swotAnalysis: {
    strengths: ['Strong in Mathematics', 'Good leadership skills', 'Excellent attendance'],
    weaknesses: ['Needs improvement in English writing', 'Shy in group discussions'],
    opportunities: ['Science Olympiad participation', 'Student council elections'],
    threats: ['Increased competition', 'Time management challenges'],
  },
  
  // Achievements
  achievements: [
    { title: 'Mathematics Olympiad - District Level', date: '2024-03-15', type: 'Academic' },
    { title: 'Best Student of the Month', date: '2024-02-28', type: 'Behavioral' },
    { title: 'Science Fair - First Prize', date: '2024-01-20', type: 'Academic' },
  ],
  
  // Behavioral Assessment
  behavioralScores: {
    discipline: 9,
    teamwork: 8,
    leadership: 9,
    creativity: 7,
    communication: 6,
    responsibility: 9,
  },
};

const StudentProfile = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { studentId } = useParams();
  const [activeTab, setActiveTab] = useState(0);
  const [student, setStudent] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call to fetch student data
    const fetchStudentData = async () => {
      setLoading(true);
      try {
        // In real app, fetch data based on studentId
        await new Promise(resolve => setTimeout(resolve, 1000));
        setStudent(sampleStudentData);
      } catch (error) {
        console.error('Error fetching student data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStudentData();
  }, [studentId]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleEditProfile = () => {
    navigate(`/dashboard/students/${studentId}/edit`);
  };

  const handleSWOTAnalysis = () => {
    navigate(`/dashboard/students/${studentId}/swot`);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <LinearProgress sx={{ width: 300 }} />
      </Box>
    );
  }

  if (!student) {
    return (
      <Alert severity="error">
        Student not found. Please check the student ID and try again.
      </Alert>
    );
  }

  // Chart configurations
  const performanceChartData = {
    labels: student.performanceTrends.map(trend => trend.month),
    datasets: [
      {
        label: 'GPA Trend',
        data: student.performanceTrends.map(trend => trend.gpa),
        borderColor: theme.palette.primary.main,
        backgroundColor: alpha(theme.palette.primary.main, 0.1),
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const behavioralRadarData = {
    labels: Object.keys(student.behavioralScores).map(key => 
      key.charAt(0).toUpperCase() + key.slice(1)
    ),
    datasets: [
      {
        label: 'Behavioral Assessment',
        data: Object.values(student.behavioralScores),
        borderColor: theme.palette.secondary.main,
        backgroundColor: alpha(theme.palette.secondary.main, 0.2),
        pointBackgroundColor: theme.palette.secondary.main,
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: theme.palette.secondary.main,
      },
    ],
  };

  const subjectPerformanceData = {
    labels: student.subjects.map(subject => subject.name),
    datasets: [
      {
        label: 'Marks',
        data: student.subjects.map(subject => subject.marks),
        backgroundColor: student.subjects.map((_, index) => 
          `hsl(${(index * 60) % 360}, 70%, 60%)`
        ),
        borderColor: student.subjects.map((_, index) => 
          `hsl(${(index * 60) % 360}, 70%, 50%)`
        ),
        borderWidth: 2,
      },
    ],
  };

  return (
    <Box sx={{ maxWidth: 1400, mx: 'auto', p: 3 }}>
      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card sx={{ mb: 3, overflow: 'visible' }}>
          <CardContent sx={{ p: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                <Avatar
                  src={student.profilePhoto}
                  sx={{
                    width: 120,
                    height: 120,
                    border: `4px solid ${theme.palette.primary.main}`,
                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                  }}
                >
                  <Person sx={{ fontSize: 60 }} />
                </Avatar>
                
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                    {student.firstName} {student.middleName} {student.lastName}
                  </Typography>
                  <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                    Class {student.grade} - Section {student.section}
                  </Typography>
                  <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                    Admission No: {student.admissionNumber} | Roll No: {student.rollNumber}
                  </Typography>
                  
                  <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
                    <Chip
                      label={student.board}
                      color="primary"
                      variant="filled"
                      sx={{ fontWeight: 500 }}
                    />
                    <Chip
                      label={`GPA: ${student.currentGPA}`}
                      color="success"
                      variant="outlined"
                      icon={<Star />}
                    />
                    <Chip
                      label={`${student.attendance}% Attendance`}
                      color={student.attendance >= 90 ? 'success' : student.attendance >= 75 ? 'warning' : 'error'}
                      variant="outlined"
                    />
                  </Stack>
                </Box>
              </Box>
              
              <Stack direction="row" spacing={1}>
                <IconButton onClick={handleEditProfile} color="primary">
                  <Edit />
                </IconButton>
                <IconButton color="primary">
                  <Print />
                </IconButton>
                <IconButton color="primary">
                  <Share />
                </IconButton>
                <Button
                  variant="contained"
                  startIcon={<Assessment />}
                  onClick={handleSWOTAnalysis}
                  sx={{
                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                  }}
                >
                  SWOT Analysis
                </Button>
              </Stack>
            </Box>
          </CardContent>
        </Card>
      </motion.div>

      {/* Tabs Navigation */}
      <Card sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            '& .MuiTab-root': {
              minHeight: 64,
              fontWeight: 500,
            },
          }}
        >
          <Tab icon={<Person />} label="Personal Info" />
          <Tab icon={<School />} label="Academic Performance" />
          <Tab icon={<Assessment />} label="SWOT Analysis" />
          <Tab icon={<EmojiEvents />} label="Achievements" />
        </Tabs>
      </Card>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        {activeTab === 0 && <PersonalInfoTab student={student} />}
        {activeTab === 1 && (
          <AcademicPerformanceTab 
            student={student} 
            performanceChartData={performanceChartData}
            subjectPerformanceData={subjectPerformanceData}
            behavioralRadarData={behavioralRadarData}
          />
        )}
        {activeTab === 2 && <SWOTAnalysisTab student={student} />}
        {activeTab === 3 && <AchievementsTab student={student} />}
      </motion.div>
    </Box>
  );
};

// Tab Components
const PersonalInfoTab = ({ student }) => {
  const theme = useTheme();

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
              Basic Information
            </Typography>
            <Stack spacing={2}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CalendarToday color="action" />
                <Typography variant="body2" color="text.secondary">Date of Birth:</Typography>
                <Typography variant="body2">{student.dateOfBirth}</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Person color="action" />
                <Typography variant="body2" color="text.secondary">Gender:</Typography>
                <Typography variant="body2">{student.gender}</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="body2" color="text.secondary">Blood Group:</Typography>
                <Typography variant="body2">{student.bloodGroup}</Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
              Contact Information
            </Typography>
            <Stack spacing={2}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LocationOn color="action" />
                <Typography variant="body2" color="text.secondary">Address:</Typography>
                <Typography variant="body2">{student.address}</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Phone color="action" />
                <Typography variant="body2" color="text.secondary">Phone:</Typography>
                <Typography variant="body2">{student.phone}</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Email color="action" />
                <Typography variant="body2" color="text.secondary">Email:</Typography>
                <Typography variant="body2">{student.email}</Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
              Parent/Guardian Information
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 500 }}>Father's Details</Typography>
                <Stack spacing={1}>
                  <Typography variant="body2">Name: {student.fatherName}</Typography>
                  <Typography variant="body2">Occupation: {student.fatherOccupation}</Typography>
                  <Typography variant="body2">Phone: {student.fatherPhone}</Typography>
                </Stack>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 500 }}>Mother's Details</Typography>
                <Stack spacing={1}>
                  <Typography variant="body2">Name: {student.motherName}</Typography>
                  <Typography variant="body2">Occupation: {student.motherOccupation}</Typography>
                  <Typography variant="body2">Phone: {student.motherPhone}</Typography>
                </Stack>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

const AcademicPerformanceTab = ({ student, performanceChartData, subjectPerformanceData, behavioralRadarData }) => {
  return (
    <Grid container spacing={3}>
      {/* Performance Overview */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
              Performance Overview
            </Typography>
            <Stack spacing={2}>
              <Box>
                <Typography variant="body2" color="text.secondary">Current GPA</Typography>
                <Typography variant="h4" color="primary.main" sx={{ fontWeight: 600 }}>
                  {student.currentGPA}
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">Attendance</Typography>
                <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                  {student.attendance}%
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">Class Rank</Typography>
                <Typography variant="h4" color="secondary.main" sx={{ fontWeight: 600 }}>
                  3rd
                </Typography>
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Grid>

      {/* GPA Trend Chart */}
      <Grid item xs={12} md={8}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
              GPA Trend
            </Typography>
            <Box sx={{ height: 300 }}>
              <Line
                data={performanceChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: false,
                      min: 7,
                      max: 10,
                    },
                  },
                }}
              />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Subject Performance */}
      <Grid item xs={12} md={8}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
              Subject Performance
            </Typography>
            <Box sx={{ height: 300 }}>
              <Bar
                data={subjectPerformanceData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                      max: 100,
                    },
                  },
                }}
              />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Behavioral Assessment */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
              Behavioral Assessment
            </Typography>
            <Box sx={{ height: 300 }}>
              <Radar
                data={behavioralRadarData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false,
                    },
                  },
                  scales: {
                    r: {
                      beginAtZero: true,
                      max: 10,
                    },
                  },
                }}
              />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Subject Details */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
              Subject Details
            </Typography>
            <Grid container spacing={2}>
              {student.subjects.map((subject, index) => (
                <Grid item xs={12} sm={6} md={4} key={index}>
                  <Card variant="outlined">
                    <CardContent sx={{ p: 2 }}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                        {subject.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        Teacher: {subject.teacher}
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Chip
                          label={subject.grade}
                          color={subject.grade.startsWith('A') ? 'success' : 'warning'}
                          size="small"
                        />
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          {subject.marks}%
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

const SWOTAnalysisTab = ({ student }) => {
  const theme = useTheme();

  const swotCategories = [
    {
      title: 'Strengths',
      items: student.swotAnalysis.strengths,
      color: theme.palette.success.main,
      icon: '💪',
    },
    {
      title: 'Weaknesses',
      items: student.swotAnalysis.weaknesses,
      color: theme.palette.error.main,
      icon: '⚠️',
    },
    {
      title: 'Opportunities',
      items: student.swotAnalysis.opportunities,
      color: theme.palette.info.main,
      icon: '🚀',
    },
    {
      title: 'Threats',
      items: student.swotAnalysis.threats,
      color: theme.palette.warning.main,
      icon: '⚡',
    },
  ];

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600 }}>
          SWOT Analysis Overview
        </Typography>
        <Button
          variant="contained"
          startIcon={<Assessment />}
          sx={{
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
          }}
        >
          Update SWOT
        </Button>
      </Box>

      <Grid container spacing={3}>
        {swotCategories.map((category, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card
              sx={{
                height: '100%',
                border: `2px solid ${alpha(category.color, 0.2)}`,
                background: `linear-gradient(135deg, ${alpha(category.color, 0.05)} 0%, ${alpha(category.color, 0.02)} 100%)`,
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <Typography variant="h2" sx={{ fontSize: 24 }}>
                    {category.icon}
                  </Typography>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      color: category.color,
                    }}
                  >
                    {category.title}
                  </Typography>
                </Box>

                <Stack spacing={1}>
                  {category.items.map((item, itemIndex) => (
                    <Box
                      key={itemIndex}
                      sx={{
                        p: 2,
                        borderRadius: 1,
                        background: alpha(category.color, 0.1),
                        border: `1px solid ${alpha(category.color, 0.2)}`,
                      }}
                    >
                      <Typography variant="body2">
                        {item}
                      </Typography>
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* SWOT Matrix Visualization */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
            SWOT Matrix
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Box
                sx={{
                  p: 2,
                  border: `2px solid ${theme.palette.success.main}`,
                  borderRadius: 1,
                  background: alpha(theme.palette.success.main, 0.05),
                  minHeight: 150,
                }}
              >
                <Typography variant="subtitle1" sx={{ fontWeight: 600, color: 'success.main', mb: 1 }}>
                  Strengths (Internal Positive)
                </Typography>
                {student.swotAnalysis.strengths.map((strength, index) => (
                  <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
                    • {strength}
                  </Typography>
                ))}
              </Box>
            </Grid>
            <Grid item xs={6}>
              <Box
                sx={{
                  p: 2,
                  border: `2px solid ${theme.palette.error.main}`,
                  borderRadius: 1,
                  background: alpha(theme.palette.error.main, 0.05),
                  minHeight: 150,
                }}
              >
                <Typography variant="subtitle1" sx={{ fontWeight: 600, color: 'error.main', mb: 1 }}>
                  Weaknesses (Internal Negative)
                </Typography>
                {student.swotAnalysis.weaknesses.map((weakness, index) => (
                  <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
                    • {weakness}
                  </Typography>
                ))}
              </Box>
            </Grid>
            <Grid item xs={6}>
              <Box
                sx={{
                  p: 2,
                  border: `2px solid ${theme.palette.info.main}`,
                  borderRadius: 1,
                  background: alpha(theme.palette.info.main, 0.05),
                  minHeight: 150,
                }}
              >
                <Typography variant="subtitle1" sx={{ fontWeight: 600, color: 'info.main', mb: 1 }}>
                  Opportunities (External Positive)
                </Typography>
                {student.swotAnalysis.opportunities.map((opportunity, index) => (
                  <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
                    • {opportunity}
                  </Typography>
                ))}
              </Box>
            </Grid>
            <Grid item xs={6}>
              <Box
                sx={{
                  p: 2,
                  border: `2px solid ${theme.palette.warning.main}`,
                  borderRadius: 1,
                  background: alpha(theme.palette.warning.main, 0.05),
                  minHeight: 150,
                }}
              >
                <Typography variant="subtitle1" sx={{ fontWeight: 600, color: 'warning.main', mb: 1 }}>
                  Threats (External Negative)
                </Typography>
                {student.swotAnalysis.threats.map((threat, index) => (
                  <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
                    • {threat}
                  </Typography>
                ))}
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

const AchievementsTab = ({ student }) => {
  const theme = useTheme();

  const getAchievementIcon = (type) => {
    switch (type) {
      case 'Academic':
        return <School sx={{ color: theme.palette.primary.main }} />;
      case 'Behavioral':
        return <EmojiEvents sx={{ color: theme.palette.secondary.main }} />;
      default:
        return <Star sx={{ color: theme.palette.warning.main }} />;
    }
  };

  const getAchievementColor = (type) => {
    switch (type) {
      case 'Academic':
        return theme.palette.primary.main;
      case 'Behavioral':
        return theme.palette.secondary.main;
      default:
        return theme.palette.warning.main;
    }
  };

  return (
    <Box>
      <Typography variant="h5" sx={{ fontWeight: 600, mb: 3 }}>
        Achievements & Recognition
      </Typography>

      <Grid container spacing={3}>
        {student.achievements.map((achievement, index) => (
          <Grid item xs={12} md={6} key={index}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card
                sx={{
                  border: `2px solid ${alpha(getAchievementColor(achievement.type), 0.2)}`,
                  background: `linear-gradient(135deg, ${alpha(getAchievementColor(achievement.type), 0.05)} 0%, ${alpha(getAchievementColor(achievement.type), 0.02)} 100%)`,
                  transition: 'transform 0.2s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                  },
                }}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    {getAchievementIcon(achievement.type)}
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {achievement.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {new Date(achievement.date).toLocaleDateString('en-IN', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}
                      </Typography>
                    </Box>
                  </Box>

                  <Chip
                    label={achievement.type}
                    size="small"
                    sx={{
                      backgroundColor: getAchievementColor(achievement.type),
                      color: 'white',
                      fontWeight: 500,
                    }}
                  />
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Achievement Statistics */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
            Achievement Statistics
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h3" color="primary.main" sx={{ fontWeight: 600 }}>
                  {student.achievements.filter(a => a.type === 'Academic').length}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Academic Awards
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h3" color="secondary.main" sx={{ fontWeight: 600 }}>
                  {student.achievements.filter(a => a.type === 'Behavioral').length}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Behavioral Recognition
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h3" color="warning.main" sx={{ fontWeight: 600 }}>
                  {student.achievements.length}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Total Achievements
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default StudentProfile;
