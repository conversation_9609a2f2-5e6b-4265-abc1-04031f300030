/**
 * VidyaMitra Platform - Modern Metric Card Component
 * 
 * Contemporary metric cards with glassmorphism, animations, and interactive elements
 * Features animated counters, progress indicators, and smooth hover effects
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  IconButton,
  LinearProgress,
  Chip,
  useTheme,
  alpha,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  MoreVert,
  InfoOutlined,
  ArrowUpward,
  ArrowDownward,
} from '@mui/icons-material';
import { motion, useSpring, useTransform } from 'framer-motion';

// Animated Counter Component
const AnimatedCounter = ({ value, duration = 2000, suffix = '', prefix = '' }) => {
  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    let startTime;
    let animationFrame;

    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = Math.floor(easeOutQuart * value);
      
      setDisplayValue(currentValue);

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [value, duration]);

  return (
    <span>
      {prefix}{displayValue.toLocaleString()}{suffix}
    </span>
  );
};

// Animated Progress Bar Component
const AnimatedProgress = ({ value, color = 'primary', delay = 0 }) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const timer = setTimeout(() => {
      setProgress(value);
    }, delay);

    return () => clearTimeout(timer);
  }, [value, delay]);

  return (
    <LinearProgress
      variant="determinate"
      value={progress}
      color={color}
      sx={{
        height: 8,
        borderRadius: 4,
        backgroundColor: alpha('#000', 0.1),
        '& .MuiLinearProgress-bar': {
          borderRadius: 4,
          transition: 'transform 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        },
      }}
    />
  );
};

// Trend Indicator Component
const TrendIndicator = ({ trend, value, label }) => {
  const theme = useTheme();
  const isPositive = trend === 'up';
  
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 0.5,
        px: 1,
        py: 0.5,
        borderRadius: 1,
        backgroundColor: alpha(
          isPositive ? theme.palette.success.main : theme.palette.error.main,
          0.1
        ),
      }}
    >
      {isPositive ? (
        <ArrowUpward sx={{ fontSize: 16, color: theme.palette.success.main }} />
      ) : (
        <ArrowDownward sx={{ fontSize: 16, color: theme.palette.error.main }} />
      )}
      <Typography
        variant="caption"
        sx={{
          fontWeight: 600,
          color: isPositive ? theme.palette.success.main : theme.palette.error.main,
        }}
      >
        {value}%
      </Typography>
      <Typography variant="caption" color="text.secondary">
        {label}
      </Typography>
    </Box>
  );
};

// Main Modern Metric Card Component
const ModernMetricCard = ({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  trendValue,
  trendLabel = 'vs last month',
  progress,
  progressLabel,
  color = 'primary',
  variant = 'default',
  onClick,
  loading = false,
  actionIcon,
  onActionClick,
  tooltip,
  gradient = false,
  glassmorphism = true,
}) => {
  const theme = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  const cardVariants = {
    initial: { 
      scale: 1, 
      y: 0,
      boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
    },
    hover: { 
      scale: 1.02, 
      y: -8,
      boxShadow: '0px 20px 40px rgba(46, 91, 168, 0.15)',
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 20,
      },
    },
  };

  const iconVariants = {
    initial: { rotate: 0, scale: 1 },
    hover: { 
      rotate: 5, 
      scale: 1.1,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 10,
      },
    },
  };

  const getCardBackground = () => {
    if (gradient) {
      return theme.palette.gradients[color] || theme.palette.gradients.primary;
    }
    
    if (glassmorphism) {
      return alpha(theme.palette.background.paper, 0.8);
    }
    
    return theme.palette.background.paper;
  };

  const getIconColor = () => {
    if (gradient) return '#FFFFFF';
    return theme.palette[color]?.main || theme.palette.primary.main;
  };

  const getTextColor = () => {
    if (gradient) return '#FFFFFF';
    return theme.palette.text.primary;
  };

  return (
    <Tooltip title={tooltip} arrow placement="top">
      <motion.div
        variants={cardVariants}
        initial="initial"
        whileHover="hover"
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
      >
        <Card
          onClick={onClick}
          sx={{
            height: '100%',
            cursor: onClick ? 'pointer' : 'default',
            borderRadius: 3,
            background: getCardBackground(),
            backdropFilter: glassmorphism ? 'blur(20px)' : 'none',
            border: glassmorphism 
              ? `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
              : 'none',
            overflow: 'hidden',
            position: 'relative',
            '&::before': gradient ? {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: getCardBackground(),
              zIndex: -1,
            } : {},
          }}
        >
          <CardContent sx={{ p: 3, height: '100%' }}>
            {/* Header */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'flex-start',
                justifyContent: 'space-between',
                mb: 2,
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <motion.div variants={iconVariants}>
                  <Avatar
                    sx={{
                      bgcolor: gradient 
                        ? alpha('#FFFFFF', 0.2)
                        : alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.1),
                      color: getIconColor(),
                      width: 56,
                      height: 56,
                    }}
                  >
                    {loading ? (
                      <CircularProgress size={24} color="inherit" />
                    ) : (
                      <Icon sx={{ fontSize: 28 }} />
                    )}
                  </Avatar>
                </motion.div>
                
                <Box>
                  <Typography
                    variant="body2"
                    sx={{
                      color: gradient 
                        ? alpha('#FFFFFF', 0.8)
                        : theme.palette.text.secondary,
                      fontWeight: 500,
                      mb: 0.5,
                    }}
                  >
                    {title}
                  </Typography>
                  {subtitle && (
                    <Typography
                      variant="caption"
                      sx={{
                        color: gradient 
                          ? alpha('#FFFFFF', 0.6)
                          : theme.palette.text.disabled,
                      }}
                    >
                      {subtitle}
                    </Typography>
                  )}
                </Box>
              </Box>

              {(actionIcon || onActionClick) && (
                <IconButton
                  size="small"
                  onClick={onActionClick}
                  sx={{
                    color: gradient 
                      ? alpha('#FFFFFF', 0.8)
                      : theme.palette.text.secondary,
                  }}
                >
                  {actionIcon || <MoreVert />}
                </IconButton>
              )}
            </Box>

            {/* Main Value */}
            <Box sx={{ mb: 2 }}>
              <Typography
                variant="h3"
                sx={{
                  fontWeight: 800,
                  color: getTextColor(),
                  lineHeight: 1,
                  mb: 1,
                }}
              >
                {loading ? (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CircularProgress size={20} color="inherit" />
                    <span>--</span>
                  </Box>
                ) : typeof value === 'number' ? (
                  <AnimatedCounter value={value} />
                ) : (
                  value
                )}
              </Typography>

              {/* Trend Indicator */}
              {trend && trendValue && (
                <TrendIndicator
                  trend={trend}
                  value={trendValue}
                  label={trendLabel}
                />
              )}
            </Box>

            {/* Progress Bar */}
            {progress !== undefined && (
              <Box sx={{ mb: 1 }}>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    mb: 1,
                  }}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      color: gradient 
                        ? alpha('#FFFFFF', 0.8)
                        : theme.palette.text.secondary,
                      fontWeight: 500,
                    }}
                  >
                    {progressLabel || 'Progress'}
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      color: getTextColor(),
                      fontWeight: 600,
                    }}
                  >
                    {progress}%
                  </Typography>
                </Box>
                <AnimatedProgress
                  value={progress}
                  color={color}
                  delay={500}
                />
              </Box>
            )}

            {/* Additional Content Slot */}
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{
                opacity: isHovered ? 1 : 0,
                height: isHovered ? 'auto' : 0,
              }}
              transition={{ duration: 0.3 }}
            >
              <Box sx={{ mt: 2, pt: 2, borderTop: `1px solid ${alpha(getTextColor(), 0.1)}` }}>
                <Typography
                  variant="caption"
                  sx={{
                    color: gradient 
                      ? alpha('#FFFFFF', 0.6)
                      : theme.palette.text.disabled,
                  }}
                >
                  Click for detailed view
                </Typography>
              </Box>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </Tooltip>
  );
};

export default ModernMetricCard;
