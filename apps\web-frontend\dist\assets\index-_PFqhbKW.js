const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/StudentRegistration-B1REbrCi.js","assets/mui-Cjipzt4F.js","assets/vendor-BfWiUekA.js","assets/charts-Dx2u7Eir.js","assets/StudentProfile-BDX8WOY5.js","assets/AttendanceManagement-4vnBIZnH.js","assets/GradeEntry-CesnzI9J.js","assets/TeacherDashboard-1-qBGxVt.js","assets/SWOTWizard-KQTt7XPx.js","assets/ReportGeneration-CrX_JsK4.js"])))=>i.map(i=>d[i]);
import{c as fl,j as s,T as pl,C as Ys,u as q,a as zt,b as K,d as F,e as ie,B as y,A as se,f as It,g as b,I as le,M as Js,h as ml,i as gl,L as jt,k as W,l as xl,S as Pe,G as T,m as pe,n as re,P as He,o as Fr,p as yl,q as kr,r as Ht,s as je,t as Rr,F as Xs,v as bl,N as vl,w as _t,x as Se,y as jl,R as Sl,z as Zs,D as wl,E as Cl,H as et,J as Qs,K as dt,O as Or,Q as Pl,U as Al,V as ea,W as he,X as De,Y as Kt,Z as ta,_ as cn,$ as na,a0 as Mr,a1 as Cn,a2 as gt,a3 as Q,a4 as nr,a5 as rr,a6 as ir,a7 as U,a8 as Lr,a9 as Vn,aa as Tl,ab as fe,ac as El,ad as Fl,ae as kl,af as Rl,ag as Ol,ah as Ml,ai as Ll,aj as ra,ak as ia,al as sa,am as Pn,an as Il,ao as Dl,ap as Bl,aq as xt,ar as Ce,as as Fe,at as Vl,au as aa,av as oa,aw as Be,ax as la,ay as Wl,az as Ir,aA as Dr,aB as Nl,aC as ut,aD as $l,aE as Ul,aF as zl,aG as ca,aH as Hl,aI as _l,aJ as Kl,aK as Gl,aL as ql,aM as Yl,aN as Jl,aO as Xl,aP as Zl,aQ as Ql,aR as ec,aS as tc,aT as nc}from"./mui-Cjipzt4F.js";import{c as rc,r as m,R as ic,d as Ft,e as sc,a as we}from"./vendor-BfWiUekA.js";import{C as ua,a as ac,L as oc,P as da,b as ha,B as lc,p as cc,c as fa,d as pa,A as uc,e as dc,f as hi,D as hc,R as fc,i as pc,g as mc}from"./charts-Dx2u7Eir.js";function gc(t,e){for(var n=0;n<e.length;n++){const r=e[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in t)){const a=Object.getOwnPropertyDescriptor(r,i);a&&Object.defineProperty(t,i,a.get?a:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const a of i)if(a.type==="childList")for(const o of a.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const a={};return i.integrity&&(a.integrity=i.integrity),i.referrerPolicy&&(a.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?a.credentials="include":i.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function r(i){if(i.ep)return;i.ep=!0;const a=n(i);fetch(i.href,a)}})();var sr={},fi=rc;sr.createRoot=fi.createRoot,sr.hydrateRoot=fi.hydrateRoot;const xc="modulepreload",yc=function(t){return"/"+t},pi={},it=function(e,n,r){let i=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),l=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));i=Promise.allSettled(n.map(c=>{if(c=yc(c),c in pi)return;pi[c]=!0;const u=c.endsWith(".css"),d=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${d}`))return;const h=document.createElement("link");if(h.rel=u?"stylesheet":xc,u||(h.as="script"),h.crossOrigin="",h.href=c,l&&h.setAttribute("nonce",l),document.head.appendChild(h),u)return new Promise((f,p)=>{h.addEventListener("load",f),h.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${c}`)))})}))}function a(o){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=o,window.dispatchEvent(l),!l.defaultPrevented)throw o}return i.then(o=>{for(const l of o||[])l.status==="rejected"&&a(l.reason);return e().catch(a)})};/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Dt(){return Dt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Dt.apply(this,arguments)}var Ve;(function(t){t.Pop="POP",t.Push="PUSH",t.Replace="REPLACE"})(Ve||(Ve={}));const mi="popstate";function bc(t){t===void 0&&(t={});function e(r,i){let{pathname:a,search:o,hash:l}=r.location;return ar("",{pathname:a,search:o,hash:l},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:un(i)}return jc(e,n,null,t)}function te(t,e){if(t===!1||t===null||typeof t>"u")throw new Error(e)}function ma(t,e){if(!t){typeof console<"u"&&console.warn(e);try{throw new Error(e)}catch{}}}function vc(){return Math.random().toString(36).substr(2,8)}function gi(t,e){return{usr:t.state,key:t.key,idx:e}}function ar(t,e,n,r){return n===void 0&&(n=null),Dt({pathname:typeof t=="string"?t:t.pathname,search:"",hash:""},typeof e=="string"?St(e):e,{state:n,key:e&&e.key||r||vc()})}function un(t){let{pathname:e="/",search:n="",hash:r=""}=t;return n&&n!=="?"&&(e+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function St(t){let e={};if(t){let n=t.indexOf("#");n>=0&&(e.hash=t.substr(n),t=t.substr(0,n));let r=t.indexOf("?");r>=0&&(e.search=t.substr(r),t=t.substr(0,r)),t&&(e.pathname=t)}return e}function jc(t,e,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:a=!1}=r,o=i.history,l=Ve.Pop,c=null,u=d();u==null&&(u=0,o.replaceState(Dt({},o.state,{idx:u}),""));function d(){return(o.state||{idx:null}).idx}function h(){l=Ve.Pop;let j=d(),C=j==null?null:j-u;u=j,c&&c({action:l,location:x.location,delta:C})}function f(j,C){l=Ve.Push;let v=ar(x.location,j,C);u=d()+1;let w=gi(v,u),P=x.createHref(v);try{o.pushState(w,"",P)}catch(E){if(E instanceof DOMException&&E.name==="DataCloneError")throw E;i.location.assign(P)}a&&c&&c({action:l,location:x.location,delta:1})}function p(j,C){l=Ve.Replace;let v=ar(x.location,j,C);u=d();let w=gi(v,u),P=x.createHref(v);o.replaceState(w,"",P),a&&c&&c({action:l,location:x.location,delta:0})}function g(j){let C=i.location.origin!=="null"?i.location.origin:i.location.href,v=typeof j=="string"?j:un(j);return v=v.replace(/ $/,"%20"),te(C,"No window.location.(origin|href) available to create URL for href: "+v),new URL(v,C)}let x={get action(){return l},get location(){return t(i,o)},listen(j){if(c)throw new Error("A history only accepts one active listener");return i.addEventListener(mi,h),c=j,()=>{i.removeEventListener(mi,h),c=null}},createHref(j){return e(i,j)},createURL:g,encodeLocation(j){let C=g(j);return{pathname:C.pathname,search:C.search,hash:C.hash}},push:f,replace:p,go(j){return o.go(j)}};return x}var xi;(function(t){t.data="data",t.deferred="deferred",t.redirect="redirect",t.error="error"})(xi||(xi={}));function Sc(t,e,n){return n===void 0&&(n="/"),wc(t,e,n)}function wc(t,e,n,r){let i=typeof e=="string"?St(e):e,a=Br(i.pathname||"/",n);if(a==null)return null;let o=ga(t);Cc(o);let l=null;for(let c=0;l==null&&c<o.length;++c){let u=Dc(a);l=Mc(o[c],u)}return l}function ga(t,e,n,r){e===void 0&&(e=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(a,o,l)=>{let c={relativePath:l===void 0?a.path||"":l,caseSensitive:a.caseSensitive===!0,childrenIndex:o,route:a};c.relativePath.startsWith("/")&&(te(c.relativePath.startsWith(r),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(r.length));let u=We([r,c.relativePath]),d=n.concat(c);a.children&&a.children.length>0&&(te(a.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),ga(a.children,e,d,u)),!(a.path==null&&!a.index)&&e.push({path:u,score:Rc(u,a.index),routesMeta:d})};return t.forEach((a,o)=>{var l;if(a.path===""||!((l=a.path)!=null&&l.includes("?")))i(a,o);else for(let c of xa(a.path))i(a,o,c)}),e}function xa(t){let e=t.split("/");if(e.length===0)return[];let[n,...r]=e,i=n.endsWith("?"),a=n.replace(/\?$/,"");if(r.length===0)return i?[a,""]:[a];let o=xa(r.join("/")),l=[];return l.push(...o.map(c=>c===""?a:[a,c].join("/"))),i&&l.push(...o),l.map(c=>t.startsWith("/")&&c===""?"/":c)}function Cc(t){t.sort((e,n)=>e.score!==n.score?n.score-e.score:Oc(e.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Pc=/^:[\w-]+$/,Ac=3,Tc=2,Ec=1,Fc=10,kc=-2,yi=t=>t==="*";function Rc(t,e){let n=t.split("/"),r=n.length;return n.some(yi)&&(r+=kc),e&&(r+=Tc),n.filter(i=>!yi(i)).reduce((i,a)=>i+(Pc.test(a)?Ac:a===""?Ec:Fc),r)}function Oc(t,e){return t.length===e.length&&t.slice(0,-1).every((r,i)=>r===e[i])?t[t.length-1]-e[e.length-1]:0}function Mc(t,e,n){let{routesMeta:r}=t,i={},a="/",o=[];for(let l=0;l<r.length;++l){let c=r[l],u=l===r.length-1,d=a==="/"?e:e.slice(a.length)||"/",h=Lc({path:c.relativePath,caseSensitive:c.caseSensitive,end:u},d),f=c.route;if(!h)return null;Object.assign(i,h.params),o.push({params:i,pathname:We([a,h.pathname]),pathnameBase:Nc(We([a,h.pathnameBase])),route:f}),h.pathnameBase!=="/"&&(a=We([a,h.pathnameBase]))}return o}function Lc(t,e){typeof t=="string"&&(t={path:t,caseSensitive:!1,end:!0});let[n,r]=Ic(t.path,t.caseSensitive,t.end),i=e.match(n);if(!i)return null;let a=i[0],o=a.replace(/(.)\/+$/,"$1"),l=i.slice(1);return{params:r.reduce((u,d,h)=>{let{paramName:f,isOptional:p}=d;if(f==="*"){let x=l[h]||"";o=a.slice(0,a.length-x.length).replace(/(.)\/+$/,"$1")}const g=l[h];return p&&!g?u[f]=void 0:u[f]=(g||"").replace(/%2F/g,"/"),u},{}),pathname:a,pathnameBase:o,pattern:t}}function Ic(t,e,n){e===void 0&&(e=!1),n===void 0&&(n=!0),ma(t==="*"||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were '+('"'+t.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+t.replace(/\*$/,"/*")+'".'));let r=[],i="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,l,c)=>(r.push({paramName:l,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return t.endsWith("*")?(r.push({paramName:"*"}),i+=t==="*"||t==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":t!==""&&t!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,e?void 0:"i"),r]}function Dc(t){try{return t.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(e){return ma(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+e+").")),t}}function Br(t,e){if(e==="/")return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let n=e.endsWith("/")?e.length-1:e.length,r=t.charAt(n);return r&&r!=="/"?null:t.slice(n)||"/"}function Bc(t,e){e===void 0&&(e="/");let{pathname:n,search:r="",hash:i=""}=typeof t=="string"?St(t):t;return{pathname:n?n.startsWith("/")?n:Vc(n,e):e,search:$c(r),hash:Uc(i)}}function Vc(t,e){let n=e.replace(/\/+$/,"").split("/");return t.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function Wn(t,e,n,r){return"Cannot include a '"+t+"' character in a manually specified "+("`to."+e+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Wc(t){return t.filter((e,n)=>n===0||e.route.path&&e.route.path.length>0)}function Vr(t,e){let n=Wc(t);return e?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Wr(t,e,n,r){r===void 0&&(r=!1);let i;typeof t=="string"?i=St(t):(i=Dt({},t),te(!i.pathname||!i.pathname.includes("?"),Wn("?","pathname","search",i)),te(!i.pathname||!i.pathname.includes("#"),Wn("#","pathname","hash",i)),te(!i.search||!i.search.includes("#"),Wn("#","search","hash",i)));let a=t===""||i.pathname==="",o=a?"/":i.pathname,l;if(o==null)l=n;else{let h=e.length-1;if(!r&&o.startsWith("..")){let f=o.split("/");for(;f[0]==="..";)f.shift(),h-=1;i.pathname=f.join("/")}l=h>=0?e[h]:"/"}let c=Bc(i,l),u=o&&o!=="/"&&o.endsWith("/"),d=(a||o===".")&&n.endsWith("/");return!c.pathname.endsWith("/")&&(u||d)&&(c.pathname+="/"),c}const We=t=>t.join("/").replace(/\/\/+/g,"/"),Nc=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),$c=t=>!t||t==="?"?"":t.startsWith("?")?t:"?"+t,Uc=t=>!t||t==="#"?"":t.startsWith("#")?t:"#"+t;function zc(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.internal=="boolean"&&"data"in t}const ya=["post","put","patch","delete"];new Set(ya);const Hc=["get",...ya];new Set(Hc);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Bt(){return Bt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Bt.apply(this,arguments)}const Nr=m.createContext(null),_c=m.createContext(null),_e=m.createContext(null),An=m.createContext(null),Ee=m.createContext({outlet:null,matches:[],isDataRoute:!1}),ba=m.createContext(null);function Kc(t,e){let{relative:n}=e===void 0?{}:e;wt()||te(!1);let{basename:r,navigator:i}=m.useContext(_e),{hash:a,pathname:o,search:l}=ja(t,{relative:n}),c=o;return r!=="/"&&(c=o==="/"?r:We([r,o])),i.createHref({pathname:c,search:l,hash:a})}function wt(){return m.useContext(An)!=null}function Gt(){return wt()||te(!1),m.useContext(An).location}function va(t){m.useContext(_e).static||m.useLayoutEffect(t)}function ge(){let{isDataRoute:t}=m.useContext(Ee);return t?lu():Gc()}function Gc(){wt()||te(!1);let t=m.useContext(Nr),{basename:e,future:n,navigator:r}=m.useContext(_e),{matches:i}=m.useContext(Ee),{pathname:a}=Gt(),o=JSON.stringify(Vr(i,n.v7_relativeSplatPath)),l=m.useRef(!1);return va(()=>{l.current=!0}),m.useCallback(function(u,d){if(d===void 0&&(d={}),!l.current)return;if(typeof u=="number"){r.go(u);return}let h=Wr(u,JSON.parse(o),a,d.relative==="path");t==null&&e!=="/"&&(h.pathname=h.pathname==="/"?e:We([e,h.pathname])),(d.replace?r.replace:r.push)(h,d.state,d)},[e,r,o,a,t])}const qc=m.createContext(null);function Yc(t){let e=m.useContext(Ee).outlet;return e&&m.createElement(qc.Provider,{value:t},e)}function Jc(){let{matches:t}=m.useContext(Ee),e=t[t.length-1];return e?e.params:{}}function ja(t,e){let{relative:n}=e===void 0?{}:e,{future:r}=m.useContext(_e),{matches:i}=m.useContext(Ee),{pathname:a}=Gt(),o=JSON.stringify(Vr(i,r.v7_relativeSplatPath));return m.useMemo(()=>Wr(t,JSON.parse(o),a,n==="path"),[t,o,a,n])}function Xc(t,e){return Zc(t,e)}function Zc(t,e,n,r){wt()||te(!1);let{navigator:i}=m.useContext(_e),{matches:a}=m.useContext(Ee),o=a[a.length-1],l=o?o.params:{};o&&o.pathname;let c=o?o.pathnameBase:"/";o&&o.route;let u=Gt(),d;if(e){var h;let j=typeof e=="string"?St(e):e;c==="/"||(h=j.pathname)!=null&&h.startsWith(c)||te(!1),d=j}else d=u;let f=d.pathname||"/",p=f;if(c!=="/"){let j=c.replace(/^\//,"").split("/");p="/"+f.replace(/^\//,"").split("/").slice(j.length).join("/")}let g=Sc(t,{pathname:p}),x=ru(g&&g.map(j=>Object.assign({},j,{params:Object.assign({},l,j.params),pathname:We([c,i.encodeLocation?i.encodeLocation(j.pathname).pathname:j.pathname]),pathnameBase:j.pathnameBase==="/"?c:We([c,i.encodeLocation?i.encodeLocation(j.pathnameBase).pathname:j.pathnameBase])})),a,n,r);return e&&x?m.createElement(An.Provider,{value:{location:Bt({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:Ve.Pop}},x):x}function Qc(){let t=ou(),e=zc(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),n=t instanceof Error?t.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return m.createElement(m.Fragment,null,m.createElement("h2",null,"Unexpected Application Error!"),m.createElement("h3",{style:{fontStyle:"italic"}},e),n?m.createElement("pre",{style:i},n):null,null)}const eu=m.createElement(Qc,null);class tu extends m.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,n){return n.location!==e.location||n.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:n.error,location:n.location,revalidation:e.revalidation||n.revalidation}}componentDidCatch(e,n){console.error("React Router caught the following error during render",e,n)}render(){return this.state.error!==void 0?m.createElement(Ee.Provider,{value:this.props.routeContext},m.createElement(ba.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function nu(t){let{routeContext:e,match:n,children:r}=t,i=m.useContext(Nr);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),m.createElement(Ee.Provider,{value:e},r)}function ru(t,e,n,r){var i;if(e===void 0&&(e=[]),n===void 0&&(n=null),r===void 0&&(r=null),t==null){var a;if(!n)return null;if(n.errors)t=n.matches;else if((a=r)!=null&&a.v7_partialHydration&&e.length===0&&!n.initialized&&n.matches.length>0)t=n.matches;else return null}let o=t,l=(i=n)==null?void 0:i.errors;if(l!=null){let d=o.findIndex(h=>h.route.id&&(l==null?void 0:l[h.route.id])!==void 0);d>=0||te(!1),o=o.slice(0,Math.min(o.length,d+1))}let c=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<o.length;d++){let h=o[d];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(u=d),h.route.id){let{loaderData:f,errors:p}=n,g=h.route.loader&&f[h.route.id]===void 0&&(!p||p[h.route.id]===void 0);if(h.route.lazy||g){c=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((d,h,f)=>{let p,g=!1,x=null,j=null;n&&(p=l&&h.route.id?l[h.route.id]:void 0,x=h.route.errorElement||eu,c&&(u<0&&f===0?(cu("route-fallback"),g=!0,j=null):u===f&&(g=!0,j=h.route.hydrateFallbackElement||null)));let C=e.concat(o.slice(0,f+1)),v=()=>{let w;return p?w=x:g?w=j:h.route.Component?w=m.createElement(h.route.Component,null):h.route.element?w=h.route.element:w=d,m.createElement(nu,{match:h,routeContext:{outlet:d,matches:C,isDataRoute:n!=null},children:w})};return n&&(h.route.ErrorBoundary||h.route.errorElement||f===0)?m.createElement(tu,{location:n.location,revalidation:n.revalidation,component:x,error:p,children:v(),routeContext:{outlet:null,matches:C,isDataRoute:!0}}):v()},null)}var Sa=function(t){return t.UseBlocker="useBlocker",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t}(Sa||{}),wa=function(t){return t.UseBlocker="useBlocker",t.UseLoaderData="useLoaderData",t.UseActionData="useActionData",t.UseRouteError="useRouteError",t.UseNavigation="useNavigation",t.UseRouteLoaderData="useRouteLoaderData",t.UseMatches="useMatches",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t.UseRouteId="useRouteId",t}(wa||{});function iu(t){let e=m.useContext(Nr);return e||te(!1),e}function su(t){let e=m.useContext(_c);return e||te(!1),e}function au(t){let e=m.useContext(Ee);return e||te(!1),e}function Ca(t){let e=au(),n=e.matches[e.matches.length-1];return n.route.id||te(!1),n.route.id}function ou(){var t;let e=m.useContext(ba),n=su(),r=Ca();return e!==void 0?e:(t=n.errors)==null?void 0:t[r]}function lu(){let{router:t}=iu(Sa.UseNavigateStable),e=Ca(wa.UseNavigateStable),n=m.useRef(!1);return va(()=>{n.current=!0}),m.useCallback(function(i,a){a===void 0&&(a={}),n.current&&(typeof i=="number"?t.navigate(i):t.navigate(i,Bt({fromRouteId:e},a)))},[t,e])}const bi={};function cu(t,e,n){bi[t]||(bi[t]=!0)}function uu(t,e){t==null||t.v7_startTransition,t==null||t.v7_relativeSplatPath}function du(t){let{to:e,replace:n,state:r,relative:i}=t;wt()||te(!1);let{future:a,static:o}=m.useContext(_e),{matches:l}=m.useContext(Ee),{pathname:c}=Gt(),u=ge(),d=Wr(e,Vr(l,a.v7_relativeSplatPath),c,i==="path"),h=JSON.stringify(d);return m.useEffect(()=>u(JSON.parse(h),{replace:n,state:r,relative:i}),[u,h,i,n,r]),null}function hu(t){return Yc(t.context)}function X(t){te(!1)}function fu(t){let{basename:e="/",children:n=null,location:r,navigationType:i=Ve.Pop,navigator:a,static:o=!1,future:l}=t;wt()&&te(!1);let c=e.replace(/^\/*/,"/"),u=m.useMemo(()=>({basename:c,navigator:a,static:o,future:Bt({v7_relativeSplatPath:!1},l)}),[c,l,a,o]);typeof r=="string"&&(r=St(r));let{pathname:d="/",search:h="",hash:f="",state:p=null,key:g="default"}=r,x=m.useMemo(()=>{let j=Br(d,c);return j==null?null:{location:{pathname:j,search:h,hash:f,state:p,key:g},navigationType:i}},[c,d,h,f,p,g,i]);return x==null?null:m.createElement(_e.Provider,{value:u},m.createElement(An.Provider,{children:n,value:x}))}function pu(t){let{children:e,location:n}=t;return Xc(or(e),n)}new Promise(()=>{});function or(t,e){e===void 0&&(e=[]);let n=[];return m.Children.forEach(t,(r,i)=>{if(!m.isValidElement(r))return;let a=[...e,i];if(r.type===m.Fragment){n.push.apply(n,or(r.props.children,a));return}r.type!==X&&te(!1),!r.props.index||!r.props.children||te(!1);let o={id:r.props.id||a.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=or(r.props.children,a)),n.push(o)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function lr(){return lr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},lr.apply(this,arguments)}function mu(t,e){if(t==null)return{};var n={},r=Object.keys(t),i,a;for(a=0;a<r.length;a++)i=r[a],!(e.indexOf(i)>=0)&&(n[i]=t[i]);return n}function gu(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}function xu(t,e){return t.button===0&&(!e||e==="_self")&&!gu(t)}const yu=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],bu="6";try{window.__reactRouterVersion=bu}catch{}const vu="startTransition",vi=ic[vu];function ju(t){let{basename:e,children:n,future:r,window:i}=t,a=m.useRef();a.current==null&&(a.current=bc({window:i,v5Compat:!0}));let o=a.current,[l,c]=m.useState({action:o.action,location:o.location}),{v7_startTransition:u}=r||{},d=m.useCallback(h=>{u&&vi?vi(()=>c(h)):c(h)},[c,u]);return m.useLayoutEffect(()=>o.listen(d),[o,d]),m.useEffect(()=>uu(r),[r]),m.createElement(fu,{basename:e,children:n,location:l.location,navigationType:l.action,navigator:o,future:r})}const Su=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",wu=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Pa=m.forwardRef(function(e,n){let{onClick:r,relative:i,reloadDocument:a,replace:o,state:l,target:c,to:u,preventScrollReset:d,viewTransition:h}=e,f=mu(e,yu),{basename:p}=m.useContext(_e),g,x=!1;if(typeof u=="string"&&wu.test(u)&&(g=u,Su))try{let w=new URL(window.location.href),P=u.startsWith("//")?new URL(w.protocol+u):new URL(u),E=Br(P.pathname,p);P.origin===w.origin&&E!=null?u=E+P.search+P.hash:x=!0}catch{}let j=Kc(u,{relative:i}),C=Cu(u,{replace:o,state:l,target:c,preventScrollReset:d,relative:i,viewTransition:h});function v(w){r&&r(w),w.defaultPrevented||C(w)}return m.createElement("a",lr({},f,{href:g||j,onClick:x||a?r:v,ref:n,target:c}))});var ji;(function(t){t.UseScrollRestoration="useScrollRestoration",t.UseSubmit="useSubmit",t.UseSubmitFetcher="useSubmitFetcher",t.UseFetcher="useFetcher",t.useViewTransitionState="useViewTransitionState"})(ji||(ji={}));var Si;(function(t){t.UseFetcher="useFetcher",t.UseFetchers="useFetchers",t.UseScrollRestoration="useScrollRestoration"})(Si||(Si={}));function Cu(t,e){let{target:n,replace:r,state:i,preventScrollReset:a,relative:o,viewTransition:l}=e===void 0?{}:e,c=ge(),u=Gt(),d=ja(t,{relative:o});return m.useCallback(h=>{if(xu(h,n)){h.preventDefault();let f=r!==void 0?r:un(u)===un(d);c(t,{replace:f,state:i,preventScrollReset:a,relative:o,viewTransition:l})}},[u,c,d,r,i,n,t,a,o,l])}const Pu=(t="light")=>{const e=t==="light";return fl({palette:{mode:t,primary:{50:e?"#E8F4FD":"#0F172A",100:e?"#C5E4FA":"#1E293B",200:e?"#9FD3F7":"#334155",300:e?"#79C2F4":"#475569",400:e?"#5CB5F1":"#64748B",500:e?"#2E5BA8":"#4A90E2",600:e?"#1E4A97":"#6BA3E8",700:e?"#1A4086":"#8BB8ED",800:e?"#163675":"#ABCDF2",900:e?"#0F2654":"#CBE2F7",main:e?"#2E5BA8":"#4A90E2",light:e?"#5CB5F1":"#6BA3E8",dark:e?"#1A4086":"#2E5BA8",contrastText:"#FFFFFF"},secondary:{50:e?"#FFF8E1":"#1A0F00",100:e?"#FFECB3":"#331F00",200:e?"#FFE082":"#4D2E00",300:e?"#FFD54F":"#663D00",400:e?"#FFCA28":"#804D00",500:e?"#FF9933":"#FFB366",600:e?"#FFB300":"#FFCC80",700:e?"#FF8F00":"#FFD699",800:e?"#FF6F00":"#FFE0B3",900:e?"#E65100":"#FFEBCC",main:e?"#FF9933":"#FFB366",light:e?"#FFCA28":"#FFCC80",dark:e?"#FF8F00":"#FF9933",contrastText:e?"#000000":"#FFFFFF"},success:{main:e?"#00C853":"#4CAF50",light:e?"#4CAF50":"#81C784",dark:e?"#00A046":"#388E3C",contrastText:"#FFFFFF"},warning:{main:e?"#FF9800":"#FFB74D",light:e?"#FFB74D":"#FFCC80",dark:e?"#F57C00":"#FF9800",contrastText:e?"#000000":"#FFFFFF"},error:{main:e?"#F44336":"#EF5350",light:e?"#EF5350":"#E57373",dark:e?"#D32F2F":"#C62828",contrastText:"#FFFFFF"},info:{main:e?"#2196F3":"#42A5F5",light:e?"#42A5F5":"#64B5F6",dark:e?"#1976D2":"#1565C0",contrastText:"#FFFFFF"},background:{default:e?"#F8FAFC":"#0F172A",paper:e?"#FFFFFF":"#1E293B",surface:e?"#F1F5F9":"#334155",elevated:e?"#FFFFFF":"#475569"},text:{primary:e?"#1E293B":"#F1F5F9",secondary:e?"#64748B":"#94A3B8",disabled:e?"#CBD5E1":"#475569"},divider:e?"#E2E8F0":"#334155",glass:{primary:e?"rgba(255, 255, 255, 0.25)":"rgba(255, 255, 255, 0.05)",secondary:e?"rgba(46, 91, 168, 0.1)":"rgba(74, 144, 226, 0.1)",backdrop:e?"rgba(255, 255, 255, 0.8)":"rgba(15, 23, 42, 0.8)",surface:e?"rgba(248, 250, 252, 0.8)":"rgba(30, 41, 59, 0.8)"},gradients:{primary:e?"linear-gradient(135deg, #2E5BA8 0%, #4A90E2 100%)":"linear-gradient(135deg, #4A90E2 0%, #6BA3E8 100%)",secondary:e?"linear-gradient(135deg, #FF9933 0%, #FFB366 100%)":"linear-gradient(135deg, #FFB366 0%, #FFCC80 100%)",success:e?"linear-gradient(135deg, #00C853 0%, #4CAF50 100%)":"linear-gradient(135deg, #4CAF50 0%, #81C784 100%)",surface:e?"linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%)":"linear-gradient(135deg, #1E293B 0%, #334155 100%)",glass:e?"linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)":"linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%)"},board:{CBSE:e?"#2E5BA8":"#4A90E2",ICSE:e?"#FF9933":"#FFB366",STATE:e?"#00C853":"#4CAF50",IB:e?"#9C27B0":"#BA68C8"}},typography:{fontFamily:["Inter","Roboto","Noto Sans","-apple-system","BlinkMacSystemFont",'"Segoe UI"',"Arial","sans-serif"].join(","),h1:{fontSize:"clamp(2.5rem, 2rem + 2vw, 4rem)",fontWeight:600,lineHeight:1.1,letterSpacing:"-0.025em",color:e?"#1E293B":"#F1F5F9"},h2:{fontSize:"clamp(2rem, 1.7rem + 1.5vw, 3rem)",fontWeight:500,lineHeight:1.2,letterSpacing:"-0.025em",color:e?"#1E293B":"#F1F5F9"},h3:{fontSize:"clamp(1.75rem, 1.5rem + 1.25vw, 2.5rem)",fontWeight:500,lineHeight:1.25,color:e?"#1E293B":"#F1F5F9"},h4:{fontSize:"clamp(1.5rem, 1.3rem + 1vw, 2rem)",fontWeight:500,lineHeight:1.3,color:e?"#1E293B":"#F1F5F9"},h5:{fontSize:"clamp(1.25rem, 1.1rem + 0.75vw, 1.75rem)",fontWeight:500,lineHeight:1.35,color:e?"#1E293B":"#F1F5F9"},h6:{fontSize:"clamp(1.125rem, 1rem + 0.625vw, 1.5rem)",fontWeight:500,lineHeight:1.4,color:e?"#1E293B":"#F1F5F9"},body1:{fontSize:"1rem",lineHeight:1.6,fontWeight:400,color:e?"#334155":"#CBD5E1"},body2:{fontSize:"0.875rem",lineHeight:1.5,fontWeight:400,color:e?"#475569":"#94A3B8"},button:{fontSize:"0.875rem",fontWeight:500,textTransform:"none",letterSpacing:"0.025em"}},spacing:4,shape:{borderRadius:16,modernRadius:20,glassRadius:24},shadows:["none",e?"0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.08)":"0px 1px 3px rgba(0, 0, 0, 0.24), 0px 1px 2px rgba(0, 0, 0, 0.16)",e?"0px 3px 6px rgba(0, 0, 0, 0.16), 0px 2px 4px rgba(0, 0, 0, 0.08)":"0px 3px 6px rgba(0, 0, 0, 0.32), 0px 2px 4px rgba(0, 0, 0, 0.16)",e?"0px 6px 12px rgba(0, 0, 0, 0.16), 0px 4px 8px rgba(0, 0, 0, 0.08)":"0px 6px 12px rgba(0, 0, 0, 0.32), 0px 4px 8px rgba(0, 0, 0, 0.16)",e?"0px 8px 16px rgba(0, 0, 0, 0.16), 0px 6px 12px rgba(0, 0, 0, 0.08)":"0px 8px 16px rgba(0, 0, 0, 0.32), 0px 6px 12px rgba(0, 0, 0, 0.16)",e?"0px 12px 24px rgba(0, 0, 0, 0.16), 0px 8px 16px rgba(0, 0, 0, 0.08)":"0px 12px 24px rgba(0, 0, 0, 0.32), 0px 8px 16px rgba(0, 0, 0, 0.16)",e?"0px 8px 32px rgba(46, 91, 168, 0.15), 0px 1px 0px rgba(255, 255, 255, 0.05) inset":"0px 8px 32px rgba(0, 0, 0, 0.4), 0px 1px 0px rgba(255, 255, 255, 0.1) inset"],transitions:{easing:{easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)",modern:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",bounce:"cubic-bezier(0.68, -0.55, 0.265, 1.55)"},duration:{shortest:150,shorter:200,short:250,standard:300,complex:375,modern:400,enteringScreen:225,leavingScreen:195}},components:{MuiCard:{styleOverrides:{root:{borderRadius:20,border:e?"1px solid rgba(226, 232, 240, 0.8)":"1px solid rgba(51, 65, 85, 0.8)",backdropFilter:"blur(20px)",background:e?"rgba(255, 255, 255, 0.8)":"rgba(30, 41, 59, 0.8)",transition:"all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)","&:hover":{transform:"translateY(-4px)",boxShadow:e?"0px 20px 40px rgba(46, 91, 168, 0.15)":"0px 20px 40px rgba(0, 0, 0, 0.4)"}}}},MuiButton:{styleOverrides:{root:{borderRadius:12,textTransform:"none",fontWeight:600,padding:"12px 24px",transition:"all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)","&:hover":{transform:"translateY(-2px)"}},contained:{boxShadow:"0px 4px 12px rgba(46, 91, 168, 0.3)","&:hover":{boxShadow:"0px 8px 24px rgba(46, 91, 168, 0.4)"}}}},MuiChip:{styleOverrides:{root:{borderRadius:12,fontWeight:500,backdropFilter:"blur(10px)"}}}}})},Au=m.createContext(),Tu=({children:t})=>{const[e,n]=m.useState(()=>localStorage.getItem("vidyamitra-theme-mode")||"light"),r=Pu(e),i=()=>{const c=e==="light"?"dark":"light";n(c),localStorage.setItem("vidyamitra-theme-mode",c)},a=c=>{(c==="light"||c==="dark")&&(n(c),localStorage.setItem("vidyamitra-theme-mode",c))},o=()=>{const u=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";a(u)};m.useEffect(()=>{const c=window.matchMedia("(prefers-color-scheme: dark)"),u=d=>{localStorage.getItem("vidyamitra-theme-mode")||n(d.matches?"dark":"light")};return c.addEventListener("change",u),()=>{c.removeEventListener("change",u)}},[]);const l={mode:e,theme:r,toggleMode:i,setThemeMode:a,setSystemMode:o,isDark:e==="dark",isLight:e==="light"};return s.jsx(Au.Provider,{value:l,children:s.jsxs(pl,{theme:r,children:[s.jsx(Ys,{}),t]})})},D=t=>typeof t=="string",Pt=()=>{let t,e;const n=new Promise((r,i)=>{t=r,e=i});return n.resolve=t,n.reject=e,n},wi=t=>t==null?"":""+t,Eu=(t,e,n)=>{t.forEach(r=>{e[r]&&(n[r]=e[r])})},Fu=/###/g,Ci=t=>t&&t.indexOf("###")>-1?t.replace(Fu,"."):t,Pi=t=>!t||D(t),kt=(t,e,n)=>{const r=D(e)?e.split("."):e;let i=0;for(;i<r.length-1;){if(Pi(t))return{};const a=Ci(r[i]);!t[a]&&n&&(t[a]=new n),Object.prototype.hasOwnProperty.call(t,a)?t=t[a]:t={},++i}return Pi(t)?{}:{obj:t,k:Ci(r[i])}},Ai=(t,e,n)=>{const{obj:r,k:i}=kt(t,e,Object);if(r!==void 0||e.length===1){r[i]=n;return}let a=e[e.length-1],o=e.slice(0,e.length-1),l=kt(t,o,Object);for(;l.obj===void 0&&o.length;)a=`${o[o.length-1]}.${a}`,o=o.slice(0,o.length-1),l=kt(t,o,Object),l&&l.obj&&typeof l.obj[`${l.k}.${a}`]<"u"&&(l.obj=void 0);l.obj[`${l.k}.${a}`]=n},ku=(t,e,n,r)=>{const{obj:i,k:a}=kt(t,e,Object);i[a]=i[a]||[],i[a].push(n)},dn=(t,e)=>{const{obj:n,k:r}=kt(t,e);if(n)return n[r]},Ru=(t,e,n)=>{const r=dn(t,n);return r!==void 0?r:dn(e,n)},Aa=(t,e,n)=>{for(const r in e)r!=="__proto__"&&r!=="constructor"&&(r in t?D(t[r])||t[r]instanceof String||D(e[r])||e[r]instanceof String?n&&(t[r]=e[r]):Aa(t[r],e[r],n):t[r]=e[r]);return t},lt=t=>t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Ou={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const Mu=t=>D(t)?t.replace(/[&<>"'\/]/g,e=>Ou[e]):t;class Lu{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const n=this.regExpMap.get(e);if(n!==void 0)return n;const r=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,r),this.regExpQueue.push(e),r}}const Iu=[" ",",","?","!",";"],Du=new Lu(20),Bu=(t,e,n)=>{e=e||"",n=n||"";const r=Iu.filter(o=>e.indexOf(o)<0&&n.indexOf(o)<0);if(r.length===0)return!0;const i=Du.getRegExp(`(${r.map(o=>o==="?"?"\\?":o).join("|")})`);let a=!i.test(t);if(!a){const o=t.indexOf(n);o>0&&!i.test(t.substring(0,o))&&(a=!0)}return a},cr=function(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:".";if(!t)return;if(t[e])return t[e];const r=e.split(n);let i=t;for(let a=0;a<r.length;){if(!i||typeof i!="object")return;let o,l="";for(let c=a;c<r.length;++c)if(c!==a&&(l+=n),l+=r[c],o=i[l],o!==void 0){if(["string","number","boolean"].indexOf(typeof o)>-1&&c<r.length-1)continue;a+=c-a+1;break}i=o}return i},hn=t=>t&&t.replace("_","-"),Vu={type:"logger",log(t){this.output("log",t)},warn(t){this.output("warn",t)},error(t){this.output("error",t)},output(t,e){console&&console[t]&&console[t].apply(console,e)}};class fn{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.init(e,n)}init(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.prefix=n.prefix||"i18next:",this.logger=e||Vu,this.options=n,this.debug=n.debug}log(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return this.forward(n,"log","",!0)}warn(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return this.forward(n,"warn","",!0)}error(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return this.forward(n,"error","")}deprecate(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return this.forward(n,"warn","WARNING DEPRECATED: ",!0)}forward(e,n,r,i){return i&&!this.debug?null:(D(e[0])&&(e[0]=`${r}${this.prefix} ${e[0]}`),this.logger[n](e))}create(e){return new fn(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new fn(this.logger,e)}}var Ae=new fn;class Tn{constructor(){this.observers={}}on(e,n){return e.split(" ").forEach(r=>{this.observers[r]||(this.observers[r]=new Map);const i=this.observers[r].get(n)||0;this.observers[r].set(n,i+1)}),this}off(e,n){if(this.observers[e]){if(!n){delete this.observers[e];return}this.observers[e].delete(n)}}emit(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];this.observers[e]&&Array.from(this.observers[e].entries()).forEach(o=>{let[l,c]=o;for(let u=0;u<c;u++)l(...r)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(o=>{let[l,c]=o;for(let u=0;u<c;u++)l.apply(l,[e,...r])})}}class Ti extends Tn{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const n=this.options.ns.indexOf(e);n>-1&&this.options.ns.splice(n,1)}getResource(e,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const a=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator,o=i.ignoreJSONStructure!==void 0?i.ignoreJSONStructure:this.options.ignoreJSONStructure;let l;e.indexOf(".")>-1?l=e.split("."):(l=[e,n],r&&(Array.isArray(r)?l.push(...r):D(r)&&a?l.push(...r.split(a)):l.push(r)));const c=dn(this.data,l);return!c&&!n&&!r&&e.indexOf(".")>-1&&(e=l[0],n=l[1],r=l.slice(2).join(".")),c||!o||!D(r)?c:cr(this.data&&this.data[e]&&this.data[e][n],r,a)}addResource(e,n,r,i){let a=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{silent:!1};const o=a.keySeparator!==void 0?a.keySeparator:this.options.keySeparator;let l=[e,n];r&&(l=l.concat(o?r.split(o):r)),e.indexOf(".")>-1&&(l=e.split("."),i=n,n=l[1]),this.addNamespaces(n),Ai(this.data,l,i),a.silent||this.emit("added",e,n,r,i)}addResources(e,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{silent:!1};for(const a in r)(D(r[a])||Array.isArray(r[a]))&&this.addResource(e,n,a,r[a],{silent:!0});i.silent||this.emit("added",e,n,r)}addResourceBundle(e,n,r,i,a){let o=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{silent:!1,skipCopy:!1},l=[e,n];e.indexOf(".")>-1&&(l=e.split("."),i=r,r=n,n=l[1]),this.addNamespaces(n);let c=dn(this.data,l)||{};o.skipCopy||(r=JSON.parse(JSON.stringify(r))),i?Aa(c,r,a):c={...c,...r},Ai(this.data,l,c),o.silent||this.emit("added",e,n,r)}removeResourceBundle(e,n){this.hasResourceBundle(e,n)&&delete this.data[e][n],this.removeNamespaces(n),this.emit("removed",e,n)}hasResourceBundle(e,n){return this.getResource(e,n)!==void 0}getResourceBundle(e,n){return n||(n=this.options.defaultNS),this.options.compatibilityAPI==="v1"?{...this.getResource(e,n)}:this.getResource(e,n)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const n=this.getDataByLanguage(e);return!!(n&&Object.keys(n)||[]).find(i=>n[i]&&Object.keys(n[i]).length>0)}toJSON(){return this.data}}var Ta={processors:{},addPostProcessor(t){this.processors[t.name]=t},handle(t,e,n,r,i){return t.forEach(a=>{this.processors[a]&&(e=this.processors[a].process(e,n,r,i))}),e}};const Ei={};class pn extends Tn{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),Eu(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=Ae.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};if(e==null)return!1;const r=this.resolve(e,n);return r&&r.res!==void 0}extractFromKey(e,n){let r=n.nsSeparator!==void 0?n.nsSeparator:this.options.nsSeparator;r===void 0&&(r=":");const i=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator;let a=n.ns||this.options.defaultNS||[];const o=r&&e.indexOf(r)>-1,l=!this.options.userDefinedKeySeparator&&!n.keySeparator&&!this.options.userDefinedNsSeparator&&!n.nsSeparator&&!Bu(e,r,i);if(o&&!l){const c=e.match(this.interpolator.nestingRegexp);if(c&&c.length>0)return{key:e,namespaces:D(a)?[a]:a};const u=e.split(r);(r!==i||r===i&&this.options.ns.indexOf(u[0])>-1)&&(a=u.shift()),e=u.join(i)}return{key:e,namespaces:D(a)?[a]:a}}translate(e,n,r){if(typeof n!="object"&&this.options.overloadTranslationOptionHandler&&(n=this.options.overloadTranslationOptionHandler(arguments)),typeof n=="object"&&(n={...n}),n||(n={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const i=n.returnDetails!==void 0?n.returnDetails:this.options.returnDetails,a=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator,{key:o,namespaces:l}=this.extractFromKey(e[e.length-1],n),c=l[l.length-1],u=n.lng||this.language,d=n.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(u&&u.toLowerCase()==="cimode"){if(d){const P=n.nsSeparator||this.options.nsSeparator;return i?{res:`${c}${P}${o}`,usedKey:o,exactUsedKey:o,usedLng:u,usedNS:c,usedParams:this.getUsedParamsDetails(n)}:`${c}${P}${o}`}return i?{res:o,usedKey:o,exactUsedKey:o,usedLng:u,usedNS:c,usedParams:this.getUsedParamsDetails(n)}:o}const h=this.resolve(e,n);let f=h&&h.res;const p=h&&h.usedKey||o,g=h&&h.exactUsedKey||o,x=Object.prototype.toString.apply(f),j=["[object Number]","[object Function]","[object RegExp]"],C=n.joinArrays!==void 0?n.joinArrays:this.options.joinArrays,v=!this.i18nFormat||this.i18nFormat.handleAsObject,w=!D(f)&&typeof f!="boolean"&&typeof f!="number";if(v&&f&&w&&j.indexOf(x)<0&&!(D(C)&&Array.isArray(f))){if(!n.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const P=this.options.returnedObjectHandler?this.options.returnedObjectHandler(p,f,{...n,ns:l}):`key '${o} (${this.language})' returned an object instead of string.`;return i?(h.res=P,h.usedParams=this.getUsedParamsDetails(n),h):P}if(a){const P=Array.isArray(f),E=P?[]:{},k=P?g:p;for(const R in f)if(Object.prototype.hasOwnProperty.call(f,R)){const O=`${k}${a}${R}`;E[R]=this.translate(O,{...n,joinArrays:!1,ns:l}),E[R]===O&&(E[R]=f[R])}f=E}}else if(v&&D(C)&&Array.isArray(f))f=f.join(C),f&&(f=this.extendTranslation(f,e,n,r));else{let P=!1,E=!1;const k=n.count!==void 0&&!D(n.count),R=pn.hasDefaultValue(n),O=k?this.pluralResolver.getSuffix(u,n.count,n):"",I=n.ordinal&&k?this.pluralResolver.getSuffix(u,n.count,{ordinal:!1}):"",Y=k&&!n.ordinal&&n.count===0&&this.pluralResolver.shouldUseIntlApi(),Z=Y&&n[`defaultValue${this.options.pluralSeparator}zero`]||n[`defaultValue${O}`]||n[`defaultValue${I}`]||n.defaultValue;!this.isValidLookup(f)&&R&&(P=!0,f=Z),this.isValidLookup(f)||(E=!0,f=o);const ce=(n.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&E?void 0:f,z=R&&Z!==f&&this.options.updateMissing;if(E||P||z){if(this.logger.log(z?"updateKey":"missingKey",u,c,o,z?Z:f),a){const B=this.resolve(o,{...n,keySeparator:!1});B&&B.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let S=[];const A=this.languageUtils.getFallbackCodes(this.options.fallbackLng,n.lng||this.language);if(this.options.saveMissingTo==="fallback"&&A&&A[0])for(let B=0;B<A.length;B++)S.push(A[B]);else this.options.saveMissingTo==="all"?S=this.languageUtils.toResolveHierarchy(n.lng||this.language):S.push(n.lng||this.language);const M=(B,N,V)=>{const $=R&&V!==f?V:ce;this.options.missingKeyHandler?this.options.missingKeyHandler(B,c,N,$,z,n):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(B,c,N,$,z,n),this.emit("missingKey",B,c,N,f)};this.options.saveMissing&&(this.options.saveMissingPlurals&&k?S.forEach(B=>{const N=this.pluralResolver.getSuffixes(B,n);Y&&n[`defaultValue${this.options.pluralSeparator}zero`]&&N.indexOf(`${this.options.pluralSeparator}zero`)<0&&N.push(`${this.options.pluralSeparator}zero`),N.forEach(V=>{M([B],o+V,n[`defaultValue${V}`]||Z)})}):M(S,o,Z))}f=this.extendTranslation(f,e,n,h,r),E&&f===o&&this.options.appendNamespaceToMissingKey&&(f=`${c}:${o}`),(E||P)&&this.options.parseMissingKeyHandler&&(this.options.compatibilityAPI!=="v1"?f=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${c}:${o}`:o,P?f:void 0):f=this.options.parseMissingKeyHandler(f))}return i?(h.res=f,h.usedParams=this.getUsedParamsDetails(n),h):f}extendTranslation(e,n,r,i,a){var o=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...r},r.lng||this.language||i.usedLng,i.usedNS,i.usedKey,{resolved:i});else if(!r.skipInterpolation){r.interpolation&&this.interpolator.init({...r,interpolation:{...this.options.interpolation,...r.interpolation}});const u=D(e)&&(r&&r.interpolation&&r.interpolation.skipOnVariables!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let d;if(u){const f=e.match(this.interpolator.nestingRegexp);d=f&&f.length}let h=r.replace&&!D(r.replace)?r.replace:r;if(this.options.interpolation.defaultVariables&&(h={...this.options.interpolation.defaultVariables,...h}),e=this.interpolator.interpolate(e,h,r.lng||this.language||i.usedLng,r),u){const f=e.match(this.interpolator.nestingRegexp),p=f&&f.length;d<p&&(r.nest=!1)}!r.lng&&this.options.compatibilityAPI!=="v1"&&i&&i.res&&(r.lng=this.language||i.usedLng),r.nest!==!1&&(e=this.interpolator.nest(e,function(){for(var f=arguments.length,p=new Array(f),g=0;g<f;g++)p[g]=arguments[g];return a&&a[0]===p[0]&&!r.context?(o.logger.warn(`It seems you are nesting recursively key: ${p[0]} in key: ${n[0]}`),null):o.translate(...p,n)},r)),r.interpolation&&this.interpolator.reset()}const l=r.postProcess||this.options.postProcess,c=D(l)?[l]:l;return e!=null&&c&&c.length&&r.applyPostProcessor!==!1&&(e=Ta.handle(c,e,n,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...i,usedParams:this.getUsedParamsDetails(r)},...r}:r,this)),e}resolve(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r,i,a,o,l;return D(e)&&(e=[e]),e.forEach(c=>{if(this.isValidLookup(r))return;const u=this.extractFromKey(c,n),d=u.key;i=d;let h=u.namespaces;this.options.fallbackNS&&(h=h.concat(this.options.fallbackNS));const f=n.count!==void 0&&!D(n.count),p=f&&!n.ordinal&&n.count===0&&this.pluralResolver.shouldUseIntlApi(),g=n.context!==void 0&&(D(n.context)||typeof n.context=="number")&&n.context!=="",x=n.lngs?n.lngs:this.languageUtils.toResolveHierarchy(n.lng||this.language,n.fallbackLng);h.forEach(j=>{this.isValidLookup(r)||(l=j,!Ei[`${x[0]}-${j}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(l)&&(Ei[`${x[0]}-${j}`]=!0,this.logger.warn(`key "${i}" for languages "${x.join(", ")}" won't get resolved as namespace "${l}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),x.forEach(C=>{if(this.isValidLookup(r))return;o=C;const v=[d];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(v,d,C,j,n);else{let P;f&&(P=this.pluralResolver.getSuffix(C,n.count,n));const E=`${this.options.pluralSeparator}zero`,k=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(f&&(v.push(d+P),n.ordinal&&P.indexOf(k)===0&&v.push(d+P.replace(k,this.options.pluralSeparator)),p&&v.push(d+E)),g){const R=`${d}${this.options.contextSeparator}${n.context}`;v.push(R),f&&(v.push(R+P),n.ordinal&&P.indexOf(k)===0&&v.push(R+P.replace(k,this.options.pluralSeparator)),p&&v.push(R+E))}}let w;for(;w=v.pop();)this.isValidLookup(r)||(a=w,r=this.getResource(C,j,w,n))}))})}),{res:r,usedKey:i,exactUsedKey:a,usedLng:o,usedNS:l}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,n,r,i):this.resourceStore.getResource(e,n,r,i)}getUsedParamsDetails(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const n=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],r=e.replace&&!D(e.replace);let i=r?e.replace:e;if(r&&typeof e.count<"u"&&(i.count=e.count),this.options.interpolation.defaultVariables&&(i={...this.options.interpolation.defaultVariables,...i}),!r){i={...i};for(const a of n)delete i[a]}return i}static hasDefaultValue(e){const n="defaultValue";for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&n===r.substring(0,n.length)&&e[r]!==void 0)return!0;return!1}}const Nn=t=>t.charAt(0).toUpperCase()+t.slice(1);class Fi{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=Ae.create("languageUtils")}getScriptPartFromCode(e){if(e=hn(e),!e||e.indexOf("-")<0)return null;const n=e.split("-");return n.length===2||(n.pop(),n[n.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(n.join("-"))}getLanguagePartFromCode(e){if(e=hn(e),!e||e.indexOf("-")<0)return e;const n=e.split("-");return this.formatLanguageCode(n[0])}formatLanguageCode(e){if(D(e)&&e.indexOf("-")>-1){if(typeof Intl<"u"&&typeof Intl.getCanonicalLocales<"u")try{let i=Intl.getCanonicalLocales(e)[0];if(i&&this.options.lowerCaseLng&&(i=i.toLowerCase()),i)return i}catch{}const n=["hans","hant","latn","cyrl","cans","mong","arab"];let r=e.split("-");return this.options.lowerCaseLng?r=r.map(i=>i.toLowerCase()):r.length===2?(r[0]=r[0].toLowerCase(),r[1]=r[1].toUpperCase(),n.indexOf(r[1].toLowerCase())>-1&&(r[1]=Nn(r[1].toLowerCase()))):r.length===3&&(r[0]=r[0].toLowerCase(),r[1].length===2&&(r[1]=r[1].toUpperCase()),r[0]!=="sgn"&&r[2].length===2&&(r[2]=r[2].toUpperCase()),n.indexOf(r[1].toLowerCase())>-1&&(r[1]=Nn(r[1].toLowerCase())),n.indexOf(r[2].toLowerCase())>-1&&(r[2]=Nn(r[2].toLowerCase()))),r.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let n;return e.forEach(r=>{if(n)return;const i=this.formatLanguageCode(r);(!this.options.supportedLngs||this.isSupportedCode(i))&&(n=i)}),!n&&this.options.supportedLngs&&e.forEach(r=>{if(n)return;const i=this.getLanguagePartFromCode(r);if(this.isSupportedCode(i))return n=i;n=this.options.supportedLngs.find(a=>{if(a===i)return a;if(!(a.indexOf("-")<0&&i.indexOf("-")<0)&&(a.indexOf("-")>0&&i.indexOf("-")<0&&a.substring(0,a.indexOf("-"))===i||a.indexOf(i)===0&&i.length>1))return a})}),n||(n=this.getFallbackCodes(this.options.fallbackLng)[0]),n}getFallbackCodes(e,n){if(!e)return[];if(typeof e=="function"&&(e=e(n)),D(e)&&(e=[e]),Array.isArray(e))return e;if(!n)return e.default||[];let r=e[n];return r||(r=e[this.getScriptPartFromCode(n)]),r||(r=e[this.formatLanguageCode(n)]),r||(r=e[this.getLanguagePartFromCode(n)]),r||(r=e.default),r||[]}toResolveHierarchy(e,n){const r=this.getFallbackCodes(n||this.options.fallbackLng||[],e),i=[],a=o=>{o&&(this.isSupportedCode(o)?i.push(o):this.logger.warn(`rejecting language code not found in supportedLngs: ${o}`))};return D(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&a(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&a(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&a(this.getLanguagePartFromCode(e))):D(e)&&a(this.formatLanguageCode(e)),r.forEach(o=>{i.indexOf(o)<0&&a(this.formatLanguageCode(o))}),i}}let Wu=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],Nu={1:t=>+(t>1),2:t=>+(t!=1),3:t=>0,4:t=>t%10==1&&t%100!=11?0:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?1:2,5:t=>t==0?0:t==1?1:t==2?2:t%100>=3&&t%100<=10?3:t%100>=11?4:5,6:t=>t==1?0:t>=2&&t<=4?1:2,7:t=>t==1?0:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?1:2,8:t=>t==1?0:t==2?1:t!=8&&t!=11?2:3,9:t=>+(t>=2),10:t=>t==1?0:t==2?1:t<7?2:t<11?3:4,11:t=>t==1||t==11?0:t==2||t==12?1:t>2&&t<20?2:3,12:t=>+(t%10!=1||t%100==11),13:t=>+(t!==0),14:t=>t==1?0:t==2?1:t==3?2:3,15:t=>t%10==1&&t%100!=11?0:t%10>=2&&(t%100<10||t%100>=20)?1:2,16:t=>t%10==1&&t%100!=11?0:t!==0?1:2,17:t=>t==1||t%10==1&&t%100!=11?0:1,18:t=>t==0?0:t==1?1:2,19:t=>t==1?0:t==0||t%100>1&&t%100<11?1:t%100>10&&t%100<20?2:3,20:t=>t==1?0:t==0||t%100>0&&t%100<20?1:2,21:t=>t%100==1?1:t%100==2?2:t%100==3||t%100==4?3:0,22:t=>t==1?0:t==2?1:(t<0||t>10)&&t%10==0?2:3};const $u=["v1","v2","v3"],Uu=["v4"],ki={zero:0,one:1,two:2,few:3,many:4,other:5},zu=()=>{const t={};return Wu.forEach(e=>{e.lngs.forEach(n=>{t[n]={numbers:e.nr,plurals:Nu[e.fc]}})}),t};class Hu{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.languageUtils=e,this.options=n,this.logger=Ae.create("pluralResolver"),(!this.options.compatibilityJSON||Uu.includes(this.options.compatibilityJSON))&&(typeof Intl>"u"||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=zu(),this.pluralRulesCache={}}addRule(e,n){this.rules[e]=n}clearCache(){this.pluralRulesCache={}}getRule(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.shouldUseIntlApi()){const r=hn(e==="dev"?"en":e),i=n.ordinal?"ordinal":"cardinal",a=JSON.stringify({cleanedCode:r,type:i});if(a in this.pluralRulesCache)return this.pluralRulesCache[a];let o;try{o=new Intl.PluralRules(r,{type:i})}catch{if(!e.match(/-|_/))return;const c=this.languageUtils.getLanguagePartFromCode(e);o=this.getRule(c,n)}return this.pluralRulesCache[a]=o,o}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=this.getRule(e,n);return this.shouldUseIntlApi()?r&&r.resolvedOptions().pluralCategories.length>1:r&&r.numbers.length>1}getPluralFormsOfKey(e,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.getSuffixes(e,r).map(i=>`${n}${i}`)}getSuffixes(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=this.getRule(e,n);return r?this.shouldUseIntlApi()?r.resolvedOptions().pluralCategories.sort((i,a)=>ki[i]-ki[a]).map(i=>`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${i}`):r.numbers.map(i=>this.getSuffix(e,i,n)):[]}getSuffix(e,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const i=this.getRule(e,r);return i?this.shouldUseIntlApi()?`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${i.select(n)}`:this.getSuffixRetroCompatible(i,n):(this.logger.warn(`no plural rule found for: ${e}`),"")}getSuffixRetroCompatible(e,n){const r=e.noAbs?e.plurals(n):e.plurals(Math.abs(n));let i=e.numbers[r];this.options.simplifyPluralSuffix&&e.numbers.length===2&&e.numbers[0]===1&&(i===2?i="plural":i===1&&(i=""));const a=()=>this.options.prepend&&i.toString()?this.options.prepend+i.toString():i.toString();return this.options.compatibilityJSON==="v1"?i===1?"":typeof i=="number"?`_plural_${i.toString()}`:a():this.options.compatibilityJSON==="v2"||this.options.simplifyPluralSuffix&&e.numbers.length===2&&e.numbers[0]===1?a():this.options.prepend&&r.toString()?this.options.prepend+r.toString():r.toString()}shouldUseIntlApi(){return!$u.includes(this.options.compatibilityJSON)}}const Ri=function(t,e,n){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:".",i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,a=Ru(t,e,n);return!a&&i&&D(n)&&(a=cr(t,n,r),a===void 0&&(a=cr(e,n,r))),a},$n=t=>t.replace(/\$/g,"$$$$");class _u{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=Ae.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(n=>n),this.init(e)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});const{escape:n,escapeValue:r,useRawValueToEscape:i,prefix:a,prefixEscaped:o,suffix:l,suffixEscaped:c,formatSeparator:u,unescapeSuffix:d,unescapePrefix:h,nestingPrefix:f,nestingPrefixEscaped:p,nestingSuffix:g,nestingSuffixEscaped:x,nestingOptionsSeparator:j,maxReplaces:C,alwaysFormat:v}=e.interpolation;this.escape=n!==void 0?n:Mu,this.escapeValue=r!==void 0?r:!0,this.useRawValueToEscape=i!==void 0?i:!1,this.prefix=a?lt(a):o||"{{",this.suffix=l?lt(l):c||"}}",this.formatSeparator=u||",",this.unescapePrefix=d?"":h||"-",this.unescapeSuffix=this.unescapePrefix?"":d||"",this.nestingPrefix=f?lt(f):p||lt("$t("),this.nestingSuffix=g?lt(g):x||lt(")"),this.nestingOptionsSeparator=j||",",this.maxReplaces=C||1e3,this.alwaysFormat=v!==void 0?v:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(n,r)=>n&&n.source===r?(n.lastIndex=0,n):new RegExp(r,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,n,r,i){let a,o,l;const c=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=p=>{if(p.indexOf(this.formatSeparator)<0){const C=Ri(n,c,p,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(C,void 0,r,{...i,...n,interpolationkey:p}):C}const g=p.split(this.formatSeparator),x=g.shift().trim(),j=g.join(this.formatSeparator).trim();return this.format(Ri(n,c,x,this.options.keySeparator,this.options.ignoreJSONStructure),j,r,{...i,...n,interpolationkey:x})};this.resetRegExp();const d=i&&i.missingInterpolationHandler||this.options.missingInterpolationHandler,h=i&&i.interpolation&&i.interpolation.skipOnVariables!==void 0?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:p=>$n(p)},{regex:this.regexp,safeValue:p=>this.escapeValue?$n(this.escape(p)):$n(p)}].forEach(p=>{for(l=0;a=p.regex.exec(e);){const g=a[1].trim();if(o=u(g),o===void 0)if(typeof d=="function"){const j=d(e,a,i);o=D(j)?j:""}else if(i&&Object.prototype.hasOwnProperty.call(i,g))o="";else if(h){o=a[0];continue}else this.logger.warn(`missed to pass in variable ${g} for interpolating ${e}`),o="";else!D(o)&&!this.useRawValueToEscape&&(o=wi(o));const x=p.safeValue(o);if(e=e.replace(a[0],x),h?(p.regex.lastIndex+=o.length,p.regex.lastIndex-=a[0].length):p.regex.lastIndex=0,l++,l>=this.maxReplaces)break}}),e}nest(e,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i,a,o;const l=(c,u)=>{const d=this.nestingOptionsSeparator;if(c.indexOf(d)<0)return c;const h=c.split(new RegExp(`${d}[ ]*{`));let f=`{${h[1]}`;c=h[0],f=this.interpolate(f,o);const p=f.match(/'/g),g=f.match(/"/g);(p&&p.length%2===0&&!g||g.length%2!==0)&&(f=f.replace(/'/g,'"'));try{o=JSON.parse(f),u&&(o={...u,...o})}catch(x){return this.logger.warn(`failed parsing options string in nesting for key ${c}`,x),`${c}${d}${f}`}return o.defaultValue&&o.defaultValue.indexOf(this.prefix)>-1&&delete o.defaultValue,c};for(;i=this.nestingRegexp.exec(e);){let c=[];o={...r},o=o.replace&&!D(o.replace)?o.replace:o,o.applyPostProcessor=!1,delete o.defaultValue;let u=!1;if(i[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(i[1])){const d=i[1].split(this.formatSeparator).map(h=>h.trim());i[1]=d.shift(),c=d,u=!0}if(a=n(l.call(this,i[1].trim(),o),o),a&&i[0]===e&&!D(a))return a;D(a)||(a=wi(a)),a||(this.logger.warn(`missed to resolve ${i[1]} for nesting ${e}`),a=""),u&&(a=c.reduce((d,h)=>this.format(d,h,r.lng,{...r,interpolationkey:i[1].trim()}),a.trim())),e=e.replace(i[0],a),this.regexp.lastIndex=0}return e}}const Ku=t=>{let e=t.toLowerCase().trim();const n={};if(t.indexOf("(")>-1){const r=t.split("(");e=r[0].toLowerCase().trim();const i=r[1].substring(0,r[1].length-1);e==="currency"&&i.indexOf(":")<0?n.currency||(n.currency=i.trim()):e==="relativetime"&&i.indexOf(":")<0?n.range||(n.range=i.trim()):i.split(";").forEach(o=>{if(o){const[l,...c]=o.split(":"),u=c.join(":").trim().replace(/^'+|'+$/g,""),d=l.trim();n[d]||(n[d]=u),u==="false"&&(n[d]=!1),u==="true"&&(n[d]=!0),isNaN(u)||(n[d]=parseInt(u,10))}})}return{formatName:e,formatOptions:n}},ct=t=>{const e={};return(n,r,i)=>{let a=i;i&&i.interpolationkey&&i.formatParams&&i.formatParams[i.interpolationkey]&&i[i.interpolationkey]&&(a={...a,[i.interpolationkey]:void 0});const o=r+JSON.stringify(a);let l=e[o];return l||(l=t(hn(r),i),e[o]=l),l(n)}};class Gu{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=Ae.create("formatter"),this.options=e,this.formats={number:ct((n,r)=>{const i=new Intl.NumberFormat(n,{...r});return a=>i.format(a)}),currency:ct((n,r)=>{const i=new Intl.NumberFormat(n,{...r,style:"currency"});return a=>i.format(a)}),datetime:ct((n,r)=>{const i=new Intl.DateTimeFormat(n,{...r});return a=>i.format(a)}),relativetime:ct((n,r)=>{const i=new Intl.RelativeTimeFormat(n,{...r});return a=>i.format(a,r.range||"day")}),list:ct((n,r)=>{const i=new Intl.ListFormat(n,{...r});return a=>i.format(a)})},this.init(e)}init(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};this.formatSeparator=n.interpolation.formatSeparator||","}add(e,n){this.formats[e.toLowerCase().trim()]=n}addCached(e,n){this.formats[e.toLowerCase().trim()]=ct(n)}format(e,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const a=n.split(this.formatSeparator);if(a.length>1&&a[0].indexOf("(")>1&&a[0].indexOf(")")<0&&a.find(l=>l.indexOf(")")>-1)){const l=a.findIndex(c=>c.indexOf(")")>-1);a[0]=[a[0],...a.splice(1,l)].join(this.formatSeparator)}return a.reduce((l,c)=>{const{formatName:u,formatOptions:d}=Ku(c);if(this.formats[u]){let h=l;try{const f=i&&i.formatParams&&i.formatParams[i.interpolationkey]||{},p=f.locale||f.lng||i.locale||i.lng||r;h=this.formats[u](l,p,{...d,...i,...f})}catch(f){this.logger.warn(f)}return h}else this.logger.warn(`there was no format function for ${u}`);return l},e)}}const qu=(t,e)=>{t.pending[e]!==void 0&&(delete t.pending[e],t.pendingCount--)};class Yu extends Tn{constructor(e,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};super(),this.backend=e,this.store=n,this.services=r,this.languageUtils=r.languageUtils,this.options=i,this.logger=Ae.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=i.maxParallelReads||10,this.readingCalls=0,this.maxRetries=i.maxRetries>=0?i.maxRetries:5,this.retryTimeout=i.retryTimeout>=1?i.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(r,i.backend,i)}queueLoad(e,n,r,i){const a={},o={},l={},c={};return e.forEach(u=>{let d=!0;n.forEach(h=>{const f=`${u}|${h}`;!r.reload&&this.store.hasResourceBundle(u,h)?this.state[f]=2:this.state[f]<0||(this.state[f]===1?o[f]===void 0&&(o[f]=!0):(this.state[f]=1,d=!1,o[f]===void 0&&(o[f]=!0),a[f]===void 0&&(a[f]=!0),c[h]===void 0&&(c[h]=!0)))}),d||(l[u]=!0)}),(Object.keys(a).length||Object.keys(o).length)&&this.queue.push({pending:o,pendingCount:Object.keys(o).length,loaded:{},errors:[],callback:i}),{toLoad:Object.keys(a),pending:Object.keys(o),toLoadLanguages:Object.keys(l),toLoadNamespaces:Object.keys(c)}}loaded(e,n,r){const i=e.split("|"),a=i[0],o=i[1];n&&this.emit("failedLoading",a,o,n),!n&&r&&this.store.addResourceBundle(a,o,r,void 0,void 0,{skipCopy:!0}),this.state[e]=n?-1:2,n&&r&&(this.state[e]=0);const l={};this.queue.forEach(c=>{ku(c.loaded,[a],o),qu(c,e),n&&c.errors.push(n),c.pendingCount===0&&!c.done&&(Object.keys(c.loaded).forEach(u=>{l[u]||(l[u]={});const d=c.loaded[u];d.length&&d.forEach(h=>{l[u][h]===void 0&&(l[u][h]=!0)})}),c.done=!0,c.errors.length?c.callback(c.errors):c.callback())}),this.emit("loaded",l),this.queue=this.queue.filter(c=>!c.done)}read(e,n,r){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,a=arguments.length>4&&arguments[4]!==void 0?arguments[4]:this.retryTimeout,o=arguments.length>5?arguments[5]:void 0;if(!e.length)return o(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:n,fcName:r,tried:i,wait:a,callback:o});return}this.readingCalls++;const l=(u,d)=>{if(this.readingCalls--,this.waitingReads.length>0){const h=this.waitingReads.shift();this.read(h.lng,h.ns,h.fcName,h.tried,h.wait,h.callback)}if(u&&d&&i<this.maxRetries){setTimeout(()=>{this.read.call(this,e,n,r,i+1,a*2,o)},a);return}o(u,d)},c=this.backend[r].bind(this.backend);if(c.length===2){try{const u=c(e,n);u&&typeof u.then=="function"?u.then(d=>l(null,d)).catch(l):l(null,u)}catch(u){l(u)}return}return c(e,n,l)}prepareLoading(e,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),i&&i();D(e)&&(e=this.languageUtils.toResolveHierarchy(e)),D(n)&&(n=[n]);const a=this.queueLoad(e,n,r,i);if(!a.toLoad.length)return a.pending.length||i(),null;a.toLoad.forEach(o=>{this.loadOne(o)})}load(e,n,r){this.prepareLoading(e,n,{},r)}reload(e,n,r){this.prepareLoading(e,n,{reload:!0},r)}loadOne(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";const r=e.split("|"),i=r[0],a=r[1];this.read(i,a,"read",void 0,void 0,(o,l)=>{o&&this.logger.warn(`${n}loading namespace ${a} for language ${i} failed`,o),!o&&l&&this.logger.log(`${n}loaded namespace ${a} for language ${i}`,l),this.loaded(e,o,l)})}saveMissing(e,n,r,i,a){let o=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{},l=arguments.length>6&&arguments[6]!==void 0?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(n)){this.logger.warn(`did not save key "${r}" as the namespace "${n}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(r==null||r==="")){if(this.backend&&this.backend.create){const c={...o,isUpdate:a},u=this.backend.create.bind(this.backend);if(u.length<6)try{let d;u.length===5?d=u(e,n,r,i,c):d=u(e,n,r,i),d&&typeof d.then=="function"?d.then(h=>l(null,h)).catch(l):l(null,d)}catch(d){l(d)}else u(e,n,r,i,l,c)}!e||!e[0]||this.store.addResource(e[0],n,r,i)}}}const Oi=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:t=>{let e={};if(typeof t[1]=="object"&&(e=t[1]),D(t[1])&&(e.defaultValue=t[1]),D(t[2])&&(e.tDescription=t[2]),typeof t[2]=="object"||typeof t[3]=="object"){const n=t[3]||t[2];Object.keys(n).forEach(r=>{e[r]=n[r]})}return e},interpolation:{escapeValue:!0,format:t=>t,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),Mi=t=>(D(t.ns)&&(t.ns=[t.ns]),D(t.fallbackLng)&&(t.fallbackLng=[t.fallbackLng]),D(t.fallbackNS)&&(t.fallbackNS=[t.fallbackNS]),t.supportedLngs&&t.supportedLngs.indexOf("cimode")<0&&(t.supportedLngs=t.supportedLngs.concat(["cimode"])),t),Qt=()=>{},Ju=t=>{Object.getOwnPropertyNames(Object.getPrototypeOf(t)).forEach(n=>{typeof t[n]=="function"&&(t[n]=t[n].bind(t))})};class Vt extends Tn{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;if(super(),this.options=Mi(e),this.services={},this.logger=Ae,this.modules={external:[]},Ju(this),n&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,n),this;setTimeout(()=>{this.init(e,n)},0)}}init(){var e=this;let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,typeof n=="function"&&(r=n,n={}),!n.defaultNS&&n.defaultNS!==!1&&n.ns&&(D(n.ns)?n.defaultNS=n.ns:n.ns.indexOf("translation")<0&&(n.defaultNS=n.ns[0]));const i=Oi();this.options={...i,...this.options,...Mi(n)},this.options.compatibilityAPI!=="v1"&&(this.options.interpolation={...i.interpolation,...this.options.interpolation}),n.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=n.keySeparator),n.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=n.nsSeparator);const a=d=>d?typeof d=="function"?new d:d:null;if(!this.options.isClone){this.modules.logger?Ae.init(a(this.modules.logger),this.options):Ae.init(null,this.options);let d;this.modules.formatter?d=this.modules.formatter:typeof Intl<"u"&&(d=Gu);const h=new Fi(this.options);this.store=new Ti(this.options.resources,this.options);const f=this.services;f.logger=Ae,f.resourceStore=this.store,f.languageUtils=h,f.pluralResolver=new Hu(h,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),d&&(!this.options.interpolation.format||this.options.interpolation.format===i.interpolation.format)&&(f.formatter=a(d),f.formatter.init(f,this.options),this.options.interpolation.format=f.formatter.format.bind(f.formatter)),f.interpolator=new _u(this.options),f.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},f.backendConnector=new Yu(a(this.modules.backend),f.resourceStore,f,this.options),f.backendConnector.on("*",function(p){for(var g=arguments.length,x=new Array(g>1?g-1:0),j=1;j<g;j++)x[j-1]=arguments[j];e.emit(p,...x)}),this.modules.languageDetector&&(f.languageDetector=a(this.modules.languageDetector),f.languageDetector.init&&f.languageDetector.init(f,this.options.detection,this.options)),this.modules.i18nFormat&&(f.i18nFormat=a(this.modules.i18nFormat),f.i18nFormat.init&&f.i18nFormat.init(this)),this.translator=new pn(this.services,this.options),this.translator.on("*",function(p){for(var g=arguments.length,x=new Array(g>1?g-1:0),j=1;j<g;j++)x[j-1]=arguments[j];e.emit(p,...x)}),this.modules.external.forEach(p=>{p.init&&p.init(this)})}if(this.format=this.options.interpolation.format,r||(r=Qt),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const d=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);d.length>0&&d[0]!=="dev"&&(this.options.lng=d[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(d=>{this[d]=function(){return e.store[d](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(d=>{this[d]=function(){return e.store[d](...arguments),e}});const c=Pt(),u=()=>{const d=(h,f)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),c.resolve(f),r(h,f)};if(this.languages&&this.options.compatibilityAPI!=="v1"&&!this.isInitialized)return d(null,this.t.bind(this));this.changeLanguage(this.options.lng,d)};return this.options.resources||!this.options.initImmediate?u():setTimeout(u,0),c}loadResources(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Qt;const i=D(e)?e:this.language;if(typeof e=="function"&&(r=e),!this.options.resources||this.options.partialBundledLanguages){if(i&&i.toLowerCase()==="cimode"&&(!this.options.preload||this.options.preload.length===0))return r();const a=[],o=l=>{if(!l||l==="cimode")return;this.services.languageUtils.toResolveHierarchy(l).forEach(u=>{u!=="cimode"&&a.indexOf(u)<0&&a.push(u)})};i?o(i):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(c=>o(c)),this.options.preload&&this.options.preload.forEach(l=>o(l)),this.services.backendConnector.load(a,this.options.ns,l=>{!l&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),r(l)})}else r(null)}reloadResources(e,n,r){const i=Pt();return typeof e=="function"&&(r=e,e=void 0),typeof n=="function"&&(r=n,n=void 0),e||(e=this.languages),n||(n=this.options.ns),r||(r=Qt),this.services.backendConnector.reload(e,n,a=>{i.resolve(),r(a)}),i}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&Ta.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1))for(let n=0;n<this.languages.length;n++){const r=this.languages[n];if(!(["cimode","dev"].indexOf(r)>-1)&&this.store.hasLanguageSomeTranslations(r)){this.resolvedLanguage=r;break}}}changeLanguage(e,n){var r=this;this.isLanguageChangingTo=e;const i=Pt();this.emit("languageChanging",e);const a=c=>{this.language=c,this.languages=this.services.languageUtils.toResolveHierarchy(c),this.resolvedLanguage=void 0,this.setResolvedLanguage(c)},o=(c,u)=>{u?(a(u),this.translator.changeLanguage(u),this.isLanguageChangingTo=void 0,this.emit("languageChanged",u),this.logger.log("languageChanged",u)):this.isLanguageChangingTo=void 0,i.resolve(function(){return r.t(...arguments)}),n&&n(c,function(){return r.t(...arguments)})},l=c=>{!e&&!c&&this.services.languageDetector&&(c=[]);const u=D(c)?c:this.services.languageUtils.getBestMatchFromCodes(c);u&&(this.language||a(u),this.translator.language||this.translator.changeLanguage(u),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(u)),this.loadResources(u,d=>{o(d,u)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?l(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(l):this.services.languageDetector.detect(l):l(e),i}getFixedT(e,n,r){var i=this;const a=function(o,l){let c;if(typeof l!="object"){for(var u=arguments.length,d=new Array(u>2?u-2:0),h=2;h<u;h++)d[h-2]=arguments[h];c=i.options.overloadTranslationOptionHandler([o,l].concat(d))}else c={...l};c.lng=c.lng||a.lng,c.lngs=c.lngs||a.lngs,c.ns=c.ns||a.ns,c.keyPrefix!==""&&(c.keyPrefix=c.keyPrefix||r||a.keyPrefix);const f=i.options.keySeparator||".";let p;return c.keyPrefix&&Array.isArray(o)?p=o.map(g=>`${c.keyPrefix}${f}${g}`):p=c.keyPrefix?`${c.keyPrefix}${f}${o}`:o,i.t(p,c)};return D(e)?a.lng=e:a.lngs=e,a.ns=n,a.keyPrefix=r,a}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const r=n.lng||this.resolvedLanguage||this.languages[0],i=this.options?this.options.fallbackLng:!1,a=this.languages[this.languages.length-1];if(r.toLowerCase()==="cimode")return!0;const o=(l,c)=>{const u=this.services.backendConnector.state[`${l}|${c}`];return u===-1||u===0||u===2};if(n.precheck){const l=n.precheck(this,o);if(l!==void 0)return l}return!!(this.hasResourceBundle(r,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||o(r,e)&&(!i||o(a,e)))}loadNamespaces(e,n){const r=Pt();return this.options.ns?(D(e)&&(e=[e]),e.forEach(i=>{this.options.ns.indexOf(i)<0&&this.options.ns.push(i)}),this.loadResources(i=>{r.resolve(),n&&n(i)}),r):(n&&n(),Promise.resolve())}loadLanguages(e,n){const r=Pt();D(e)&&(e=[e]);const i=this.options.preload||[],a=e.filter(o=>i.indexOf(o)<0&&this.services.languageUtils.isSupportedCode(o));return a.length?(this.options.preload=i.concat(a),this.loadResources(o=>{r.resolve(),n&&n(o)}),r):(n&&n(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!e)return"rtl";const n=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],r=this.services&&this.services.languageUtils||new Fi(Oi());return n.indexOf(r.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;return new Vt(e,n)}cloneInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Qt;const r=e.forkResourceStore;r&&delete e.forkResourceStore;const i={...this.options,...e,isClone:!0},a=new Vt(i);return(e.debug!==void 0||e.prefix!==void 0)&&(a.logger=a.logger.clone(e)),["store","services","language"].forEach(l=>{a[l]=this[l]}),a.services={...this.services},a.services.utils={hasLoadedNamespace:a.hasLoadedNamespace.bind(a)},r&&(a.store=new Ti(this.store.data,i),a.services.resourceStore=a.store),a.translator=new pn(a.services,i),a.translator.on("*",function(l){for(var c=arguments.length,u=new Array(c>1?c-1:0),d=1;d<c;d++)u[d-1]=arguments[d];a.emit(l,...u)}),a.init(i,n),a.translator.options=i,a.translator.backendConnector.services.utils={hasLoadedNamespace:a.hasLoadedNamespace.bind(a)},a}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const ae=Vt.createInstance();ae.createInstance=Vt.createInstance;ae.createInstance;ae.dir;ae.init;ae.loadResources;ae.reloadResources;ae.use;ae.changeLanguage;ae.getFixedT;ae.t;ae.exists;ae.setDefaultNamespace;ae.hasLoadedNamespace;ae.loadNamespaces;ae.loadLanguages;function Xu(){if(console&&console.warn){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];typeof e[0]=="string"&&(e[0]=`react-i18next:: ${e[0]}`),console.warn(...e)}}const Li={};function ur(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];typeof e[0]=="string"&&Li[e[0]]||(typeof e[0]=="string"&&(Li[e[0]]=new Date),Xu(...e))}const Ea=(t,e)=>()=>{if(t.isInitialized)e();else{const n=()=>{setTimeout(()=>{t.off("initialized",n)},0),e()};t.on("initialized",n)}};function Ii(t,e,n){t.loadNamespaces(e,Ea(t,n))}function Di(t,e,n,r){typeof n=="string"&&(n=[n]),n.forEach(i=>{t.options.ns.indexOf(i)<0&&t.options.ns.push(i)}),t.loadLanguages(e,Ea(t,r))}function Zu(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const r=e.languages[0],i=e.options?e.options.fallbackLng:!1,a=e.languages[e.languages.length-1];if(r.toLowerCase()==="cimode")return!0;const o=(l,c)=>{const u=e.services.backendConnector.state[`${l}|${c}`];return u===-1||u===2};return n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&e.services.backendConnector.backend&&e.isLanguageChangingTo&&!o(e.isLanguageChangingTo,t)?!1:!!(e.hasResourceBundle(r,t)||!e.services.backendConnector.backend||e.options.resources&&!e.options.partialBundledLanguages||o(r,t)&&(!i||o(a,t)))}function Qu(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return!e.languages||!e.languages.length?(ur("i18n.languages were undefined or empty",e.languages),!0):e.options.ignoreJSONStructure!==void 0?e.hasLoadedNamespace(t,{lng:n.lng,precheck:(i,a)=>{if(n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&i.services.backendConnector.backend&&i.isLanguageChangingTo&&!a(i.isLanguageChangingTo,t))return!1}}):Zu(t,e,n)}const ed=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,td={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},nd=t=>td[t],rd=t=>t.replace(ed,nd);let dr={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:rd};function id(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};dr={...dr,...t}}function sd(){return dr}let Fa;function ad(t){Fa=t}function od(){return Fa}const ld={type:"3rdParty",init(t){id(t.options.react),ad(t)}},cd=m.createContext();class ud{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(n=>{this.usedNamespaces[n]||(this.usedNamespaces[n]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const dd=(t,e)=>{const n=m.useRef();return m.useEffect(()=>{n.current=t},[t,e]),n.current};function ye(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{i18n:n}=e,{i18n:r,defaultNS:i}=m.useContext(cd)||{},a=n||r||od();if(a&&!a.reportNamespaces&&(a.reportNamespaces=new ud),!a){ur("You will need to pass in an i18next instance by using initReactI18next");const w=(E,k)=>typeof k=="string"?k:k&&typeof k=="object"&&typeof k.defaultValue=="string"?k.defaultValue:Array.isArray(E)?E[E.length-1]:E,P=[w,{},!1];return P.t=w,P.i18n={},P.ready=!1,P}a.options.react&&a.options.react.wait!==void 0&&ur("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const o={...sd(),...a.options.react,...e},{useSuspense:l,keyPrefix:c}=o;let u=t||i||a.options&&a.options.defaultNS;u=typeof u=="string"?[u]:u||["translation"],a.reportNamespaces.addUsedNamespaces&&a.reportNamespaces.addUsedNamespaces(u);const d=(a.isInitialized||a.initializedStoreOnce)&&u.every(w=>Qu(w,a,o));function h(){return a.getFixedT(e.lng||null,o.nsMode==="fallback"?u:u[0],c)}const[f,p]=m.useState(h);let g=u.join();e.lng&&(g=`${e.lng}${g}`);const x=dd(g),j=m.useRef(!0);m.useEffect(()=>{const{bindI18n:w,bindI18nStore:P}=o;j.current=!0,!d&&!l&&(e.lng?Di(a,e.lng,u,()=>{j.current&&p(h)}):Ii(a,u,()=>{j.current&&p(h)})),d&&x&&x!==g&&j.current&&p(h);function E(){j.current&&p(h)}return w&&a&&a.on(w,E),P&&a&&a.store.on(P,E),()=>{j.current=!1,w&&a&&w.split(" ").forEach(k=>a.off(k,E)),P&&a&&P.split(" ").forEach(k=>a.store.off(k,E))}},[a,g]);const C=m.useRef(!0);m.useEffect(()=>{j.current&&!C.current&&p(h),C.current=!1},[a,c]);const v=[f,a,d];if(v.t=f,v.i18n=a,v.ready=d,d||!d&&!l)return v;throw new Promise(w=>{e.lng?Di(a,e.lng,u,()=>w()):Ii(a,u,()=>w())})}function hr(t){"@babel/helpers - typeof";return hr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},hr(t)}function ka(){return typeof XMLHttpRequest=="function"||(typeof XMLHttpRequest>"u"?"undefined":hr(XMLHttpRequest))==="object"}function hd(t){return!!t&&typeof t.then=="function"}function fd(t){return hd(t)?t:Promise.resolve(t)}function pd(t){throw new Error('Could not dynamically require "'+t+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var fr={exports:{}},en={exports:{}},Bi;function md(){return Bi||(Bi=1,function(t,e){var n=typeof globalThis<"u"&&globalThis||typeof self<"u"&&self||typeof Ft<"u"&&Ft,r=function(){function a(){this.fetch=!1,this.DOMException=n.DOMException}return a.prototype=n,new a}();(function(a){(function(o){var l=typeof a<"u"&&a||typeof self<"u"&&self||typeof l<"u"&&l,c={searchParams:"URLSearchParams"in l,iterable:"Symbol"in l&&"iterator"in Symbol,blob:"FileReader"in l&&"Blob"in l&&function(){try{return new Blob,!0}catch{return!1}}(),formData:"FormData"in l,arrayBuffer:"ArrayBuffer"in l};function u(S){return S&&DataView.prototype.isPrototypeOf(S)}if(c.arrayBuffer)var d=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],h=ArrayBuffer.isView||function(S){return S&&d.indexOf(Object.prototype.toString.call(S))>-1};function f(S){if(typeof S!="string"&&(S=String(S)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(S)||S==="")throw new TypeError('Invalid character in header field name: "'+S+'"');return S.toLowerCase()}function p(S){return typeof S!="string"&&(S=String(S)),S}function g(S){var A={next:function(){var M=S.shift();return{done:M===void 0,value:M}}};return c.iterable&&(A[Symbol.iterator]=function(){return A}),A}function x(S){this.map={},S instanceof x?S.forEach(function(A,M){this.append(M,A)},this):Array.isArray(S)?S.forEach(function(A){this.append(A[0],A[1])},this):S&&Object.getOwnPropertyNames(S).forEach(function(A){this.append(A,S[A])},this)}x.prototype.append=function(S,A){S=f(S),A=p(A);var M=this.map[S];this.map[S]=M?M+", "+A:A},x.prototype.delete=function(S){delete this.map[f(S)]},x.prototype.get=function(S){return S=f(S),this.has(S)?this.map[S]:null},x.prototype.has=function(S){return this.map.hasOwnProperty(f(S))},x.prototype.set=function(S,A){this.map[f(S)]=p(A)},x.prototype.forEach=function(S,A){for(var M in this.map)this.map.hasOwnProperty(M)&&S.call(A,this.map[M],M,this)},x.prototype.keys=function(){var S=[];return this.forEach(function(A,M){S.push(M)}),g(S)},x.prototype.values=function(){var S=[];return this.forEach(function(A){S.push(A)}),g(S)},x.prototype.entries=function(){var S=[];return this.forEach(function(A,M){S.push([M,A])}),g(S)},c.iterable&&(x.prototype[Symbol.iterator]=x.prototype.entries);function j(S){if(S.bodyUsed)return Promise.reject(new TypeError("Already read"));S.bodyUsed=!0}function C(S){return new Promise(function(A,M){S.onload=function(){A(S.result)},S.onerror=function(){M(S.error)}})}function v(S){var A=new FileReader,M=C(A);return A.readAsArrayBuffer(S),M}function w(S){var A=new FileReader,M=C(A);return A.readAsText(S),M}function P(S){for(var A=new Uint8Array(S),M=new Array(A.length),B=0;B<A.length;B++)M[B]=String.fromCharCode(A[B]);return M.join("")}function E(S){if(S.slice)return S.slice(0);var A=new Uint8Array(S.byteLength);return A.set(new Uint8Array(S)),A.buffer}function k(){return this.bodyUsed=!1,this._initBody=function(S){this.bodyUsed=this.bodyUsed,this._bodyInit=S,S?typeof S=="string"?this._bodyText=S:c.blob&&Blob.prototype.isPrototypeOf(S)?this._bodyBlob=S:c.formData&&FormData.prototype.isPrototypeOf(S)?this._bodyFormData=S:c.searchParams&&URLSearchParams.prototype.isPrototypeOf(S)?this._bodyText=S.toString():c.arrayBuffer&&c.blob&&u(S)?(this._bodyArrayBuffer=E(S.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):c.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(S)||h(S))?this._bodyArrayBuffer=E(S):this._bodyText=S=Object.prototype.toString.call(S):this._bodyText="",this.headers.get("content-type")||(typeof S=="string"?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):c.searchParams&&URLSearchParams.prototype.isPrototypeOf(S)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},c.blob&&(this.blob=function(){var S=j(this);if(S)return S;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){if(this._bodyArrayBuffer){var S=j(this);return S||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}else return this.blob().then(v)}),this.text=function(){var S=j(this);if(S)return S;if(this._bodyBlob)return w(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(P(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},c.formData&&(this.formData=function(){return this.text().then(Y)}),this.json=function(){return this.text().then(JSON.parse)},this}var R=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function O(S){var A=S.toUpperCase();return R.indexOf(A)>-1?A:S}function I(S,A){if(!(this instanceof I))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');A=A||{};var M=A.body;if(S instanceof I){if(S.bodyUsed)throw new TypeError("Already read");this.url=S.url,this.credentials=S.credentials,A.headers||(this.headers=new x(S.headers)),this.method=S.method,this.mode=S.mode,this.signal=S.signal,!M&&S._bodyInit!=null&&(M=S._bodyInit,S.bodyUsed=!0)}else this.url=String(S);if(this.credentials=A.credentials||this.credentials||"same-origin",(A.headers||!this.headers)&&(this.headers=new x(A.headers)),this.method=O(A.method||this.method||"GET"),this.mode=A.mode||this.mode||null,this.signal=A.signal||this.signal,this.referrer=null,(this.method==="GET"||this.method==="HEAD")&&M)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(M),(this.method==="GET"||this.method==="HEAD")&&(A.cache==="no-store"||A.cache==="no-cache")){var B=/([?&])_=[^&]*/;if(B.test(this.url))this.url=this.url.replace(B,"$1_="+new Date().getTime());else{var N=/\?/;this.url+=(N.test(this.url)?"&":"?")+"_="+new Date().getTime()}}}I.prototype.clone=function(){return new I(this,{body:this._bodyInit})};function Y(S){var A=new FormData;return S.trim().split("&").forEach(function(M){if(M){var B=M.split("="),N=B.shift().replace(/\+/g," "),V=B.join("=").replace(/\+/g," ");A.append(decodeURIComponent(N),decodeURIComponent(V))}}),A}function Z(S){var A=new x,M=S.replace(/\r?\n[\t ]+/g," ");return M.split("\r").map(function(B){return B.indexOf(`
`)===0?B.substr(1,B.length):B}).forEach(function(B){var N=B.split(":"),V=N.shift().trim();if(V){var $=N.join(":").trim();A.append(V,$)}}),A}k.call(I.prototype);function H(S,A){if(!(this instanceof H))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');A||(A={}),this.type="default",this.status=A.status===void 0?200:A.status,this.ok=this.status>=200&&this.status<300,this.statusText=A.statusText===void 0?"":""+A.statusText,this.headers=new x(A.headers),this.url=A.url||"",this._initBody(S)}k.call(H.prototype),H.prototype.clone=function(){return new H(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new x(this.headers),url:this.url})},H.error=function(){var S=new H(null,{status:0,statusText:""});return S.type="error",S};var ce=[301,302,303,307,308];H.redirect=function(S,A){if(ce.indexOf(A)===-1)throw new RangeError("Invalid status code");return new H(null,{status:A,headers:{location:S}})},o.DOMException=l.DOMException;try{new o.DOMException}catch{o.DOMException=function(A,M){this.message=A,this.name=M;var B=Error(A);this.stack=B.stack},o.DOMException.prototype=Object.create(Error.prototype),o.DOMException.prototype.constructor=o.DOMException}function z(S,A){return new Promise(function(M,B){var N=new I(S,A);if(N.signal&&N.signal.aborted)return B(new o.DOMException("Aborted","AbortError"));var V=new XMLHttpRequest;function $(){V.abort()}V.onload=function(){var ue={status:V.status,statusText:V.statusText,headers:Z(V.getAllResponseHeaders()||"")};ue.url="responseURL"in V?V.responseURL:ue.headers.get("X-Request-URL");var qe="response"in V?V.response:V.responseText;setTimeout(function(){M(new H(qe,ue))},0)},V.onerror=function(){setTimeout(function(){B(new TypeError("Network request failed"))},0)},V.ontimeout=function(){setTimeout(function(){B(new TypeError("Network request failed"))},0)},V.onabort=function(){setTimeout(function(){B(new o.DOMException("Aborted","AbortError"))},0)};function Ge(ue){try{return ue===""&&l.location.href?l.location.href:ue}catch{return ue}}V.open(N.method,Ge(N.url),!0),N.credentials==="include"?V.withCredentials=!0:N.credentials==="omit"&&(V.withCredentials=!1),"responseType"in V&&(c.blob?V.responseType="blob":c.arrayBuffer&&N.headers.get("Content-Type")&&N.headers.get("Content-Type").indexOf("application/octet-stream")!==-1&&(V.responseType="arraybuffer")),A&&typeof A.headers=="object"&&!(A.headers instanceof x)?Object.getOwnPropertyNames(A.headers).forEach(function(ue){V.setRequestHeader(ue,p(A.headers[ue]))}):N.headers.forEach(function(ue,qe){V.setRequestHeader(qe,ue)}),N.signal&&(N.signal.addEventListener("abort",$),V.onreadystatechange=function(){V.readyState===4&&N.signal.removeEventListener("abort",$)}),V.send(typeof N._bodyInit>"u"?null:N._bodyInit)})}return z.polyfill=!0,l.fetch||(l.fetch=z,l.Headers=x,l.Request=I,l.Response=H),o.Headers=x,o.Request=I,o.Response=H,o.fetch=z,o})({})})(r),r.fetch.ponyfill=!0,delete r.fetch.polyfill;var i=n.fetch?n:r;e=i.fetch,e.default=i.fetch,e.fetch=i.fetch,e.Headers=i.Headers,e.Request=i.Request,e.Response=i.Response,t.exports=e}(en,en.exports)),en.exports}(function(t,e){var n=typeof fetch=="function"?fetch:void 0;if(typeof Ft<"u"&&Ft.fetch?n=Ft.fetch:typeof window<"u"&&window.fetch&&(n=window.fetch),typeof pd<"u"&&typeof window>"u"){var r=n||md();r.default&&(r=r.default),e.default=r,t.exports=e.default}})(fr,fr.exports);var Ra=fr.exports;const Oa=sc(Ra),Vi=gc({__proto__:null,default:Oa},[Ra]);function Wi(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Ni(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Wi(Object(n),!0).forEach(function(r){gd(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Wi(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function gd(t,e,n){return(e=xd(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function xd(t){var e=yd(t,"string");return rt(e)=="symbol"?e:e+""}function yd(t,e){if(rt(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(rt(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function rt(t){"@babel/helpers - typeof";return rt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},rt(t)}var $e=typeof fetch=="function"?fetch:void 0;typeof global<"u"&&global.fetch?$e=global.fetch:typeof window<"u"&&window.fetch&&($e=window.fetch);var Wt;ka()&&(typeof global<"u"&&global.XMLHttpRequest?Wt=global.XMLHttpRequest:typeof window<"u"&&window.XMLHttpRequest&&(Wt=window.XMLHttpRequest));var mn;typeof ActiveXObject=="function"&&(typeof global<"u"&&global.ActiveXObject?mn=global.ActiveXObject:typeof window<"u"&&window.ActiveXObject&&(mn=window.ActiveXObject));!$e&&Vi&&!Wt&&!mn&&($e=Oa||Vi);typeof $e!="function"&&($e=void 0);var pr=function(e,n){if(n&&rt(n)==="object"){var r="";for(var i in n)r+="&"+encodeURIComponent(i)+"="+encodeURIComponent(n[i]);if(!r)return e;e=e+(e.indexOf("?")!==-1?"&":"?")+r.slice(1)}return e},$i=function(e,n,r,i){var a=function(c){if(!c.ok)return r(c.statusText||"Error",{status:c.status});c.text().then(function(u){r(null,{status:c.status,data:u})}).catch(r)};if(i){var o=i(e,n);if(o instanceof Promise){o.then(a).catch(r);return}}typeof fetch=="function"?fetch(e,n).then(a).catch(r):$e(e,n).then(a).catch(r)},Ui=!1,bd=function(e,n,r,i){e.queryStringParams&&(n=pr(n,e.queryStringParams));var a=Ni({},typeof e.customHeaders=="function"?e.customHeaders():e.customHeaders);typeof window>"u"&&typeof global<"u"&&typeof global.process<"u"&&global.process.versions&&global.process.versions.node&&(a["User-Agent"]="i18next-http-backend (node/".concat(global.process.version,"; ").concat(global.process.platform," ").concat(global.process.arch,")")),r&&(a["Content-Type"]="application/json");var o=typeof e.requestOptions=="function"?e.requestOptions(r):e.requestOptions,l=Ni({method:r?"POST":"GET",body:r?e.stringify(r):void 0,headers:a},Ui?{}:o),c=typeof e.alternateFetch=="function"&&e.alternateFetch.length>=1?e.alternateFetch:void 0;try{$i(n,l,i,c)}catch(u){if(!o||Object.keys(o).length===0||!u.message||u.message.indexOf("not implemented")<0)return i(u);try{Object.keys(o).forEach(function(d){delete l[d]}),$i(n,l,i,c),Ui=!0}catch(d){i(d)}}},vd=function(e,n,r,i){r&&rt(r)==="object"&&(r=pr("",r).slice(1)),e.queryStringParams&&(n=pr(n,e.queryStringParams));try{var a;Wt?a=new Wt:a=new mn("MSXML2.XMLHTTP.3.0"),a.open(r?"POST":"GET",n,1),e.crossDomain||a.setRequestHeader("X-Requested-With","XMLHttpRequest"),a.withCredentials=!!e.withCredentials,r&&a.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),a.overrideMimeType&&a.overrideMimeType("application/json");var o=e.customHeaders;if(o=typeof o=="function"?o():o,o)for(var l in o)a.setRequestHeader(l,o[l]);a.onreadystatechange=function(){a.readyState>3&&i(a.status>=400?a.statusText:null,{status:a.status,data:a.responseText})},a.send(r)}catch(c){console&&console.log(c)}},jd=function(e,n,r,i){if(typeof r=="function"&&(i=r,r=void 0),i=i||function(){},$e&&n.indexOf("file:")!==0)return bd(e,n,r,i);if(ka()||typeof ActiveXObject=="function")return vd(e,n,r,i);i(new Error("No fetch and no xhr implementation found!"))};function yt(t){"@babel/helpers - typeof";return yt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},yt(t)}function zi(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Un(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?zi(Object(n),!0).forEach(function(r){Ma(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):zi(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Sd(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function wd(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,La(r.key),r)}}function Cd(t,e,n){return e&&wd(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Ma(t,e,n){return(e=La(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function La(t){var e=Pd(t,"string");return yt(e)=="symbol"?e:e+""}function Pd(t,e){if(yt(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(yt(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Ad=function(){return{loadPath:"/locales/{{lng}}/{{ns}}.json",addPath:"/locales/add/{{lng}}/{{ns}}",parse:function(n){return JSON.parse(n)},stringify:JSON.stringify,parsePayload:function(n,r,i){return Ma({},r,i||"")},parseLoadPayload:function(n,r){},request:jd,reloadInterval:typeof window<"u"?!1:60*60*1e3,customHeaders:{},queryStringParams:{},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"}}},Ia=function(){function t(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};Sd(this,t),this.services=e,this.options=n,this.allOptions=r,this.type="backend",this.init(e,n,r)}return Cd(t,[{key:"init",value:function(n){var r=this,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(this.services=n,this.options=Un(Un(Un({},Ad()),this.options||{}),i),this.allOptions=a,this.services&&this.options.reloadInterval){var o=setInterval(function(){return r.reload()},this.options.reloadInterval);yt(o)==="object"&&typeof o.unref=="function"&&o.unref()}}},{key:"readMulti",value:function(n,r,i){this._readAny(n,n,r,r,i)}},{key:"read",value:function(n,r,i){this._readAny([n],n,[r],r,i)}},{key:"_readAny",value:function(n,r,i,a,o){var l=this,c=this.options.loadPath;typeof this.options.loadPath=="function"&&(c=this.options.loadPath(n,i)),c=fd(c),c.then(function(u){if(!u)return o(null,{});var d=l.services.interpolator.interpolate(u,{lng:n.join("+"),ns:i.join("+")});l.loadUrl(d,o,r,a)})}},{key:"loadUrl",value:function(n,r,i,a){var o=this,l=typeof i=="string"?[i]:i,c=typeof a=="string"?[a]:a,u=this.options.parseLoadPayload(l,c);this.options.request(this.options,n,u,function(d,h){if(h&&(h.status>=500&&h.status<600||!h.status))return r("failed loading "+n+"; status code: "+h.status,!0);if(h&&h.status>=400&&h.status<500)return r("failed loading "+n+"; status code: "+h.status,!1);if(!h&&d&&d.message){var f=d.message.toLowerCase(),p=["failed","fetch","network","load"].find(function(j){return f.indexOf(j)>-1});if(p)return r("failed loading "+n+": "+d.message,!0)}if(d)return r(d,!1);var g,x;try{typeof h.data=="string"?g=o.options.parse(h.data,i,a):g=h.data}catch{x="failed parsing "+n+" to json"}if(x)return r(x,!1);r(null,g)})}},{key:"create",value:function(n,r,i,a,o){var l=this;if(this.options.addPath){typeof n=="string"&&(n=[n]);var c=this.options.parsePayload(r,i,a),u=0,d=[],h=[];n.forEach(function(f){var p=l.options.addPath;typeof l.options.addPath=="function"&&(p=l.options.addPath(f,r));var g=l.services.interpolator.interpolate(p,{lng:f,ns:r});l.options.request(l.options,g,c,function(x,j){u+=1,d.push(x),h.push(j),u===n.length&&typeof o=="function"&&o(d,h)})})}}},{key:"reload",value:function(){var n=this,r=this.services,i=r.backendConnector,a=r.languageUtils,o=r.logger,l=i.language;if(!(l&&l.toLowerCase()==="cimode")){var c=[],u=function(h){var f=a.toResolveHierarchy(h);f.forEach(function(p){c.indexOf(p)<0&&c.push(p)})};u(l),this.allOptions.preload&&this.allOptions.preload.forEach(function(d){return u(d)}),c.forEach(function(d){n.allOptions.ns.forEach(function(h){i.read(d,h,"read",null,null,function(f,p){f&&o.warn("loading namespace ".concat(h," for language ").concat(d," failed"),f),!f&&p&&o.log("loaded namespace ".concat(h," for language ").concat(d),p),i.loaded("".concat(d,"|").concat(h),f,p)})})})}}}])}();Ia.type="backend";const Da=m.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),En=m.createContext({}),Fn=m.createContext(null),kn=typeof document<"u",$r=kn?m.useLayoutEffect:m.useEffect,Ba=m.createContext({strict:!1}),Ur=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),Td="framerAppearId",Va="data-"+Ur(Td);function Ed(t,e,n,r){const{visualElement:i}=m.useContext(En),a=m.useContext(Ba),o=m.useContext(Fn),l=m.useContext(Da).reducedMotion,c=m.useRef();r=r||a.renderer,!c.current&&r&&(c.current=r(t,{visualState:e,parent:i,props:n,presenceContext:o,blockInitialAnimation:o?o.initial===!1:!1,reducedMotionConfig:l}));const u=c.current;m.useInsertionEffect(()=>{u&&u.update(n,o)});const d=m.useRef(!!(n[Va]&&!window.HandoffComplete));return $r(()=>{u&&(u.render(),d.current&&u.animationState&&u.animationState.animateChanges())}),m.useEffect(()=>{u&&(u.updateFeatures(),!d.current&&u.animationState&&u.animationState.animateChanges(),d.current&&(d.current=!1,window.HandoffComplete=!0))}),u}function ht(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function Fd(t,e,n){return m.useCallback(r=>{r&&t.mount&&t.mount(r),e&&(r?e.mount(r):e.unmount()),n&&(typeof n=="function"?n(r):ht(n)&&(n.current=r))},[e])}function Nt(t){return typeof t=="string"||Array.isArray(t)}function Rn(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}const zr=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Hr=["initial",...zr];function On(t){return Rn(t.animate)||Hr.some(e=>Nt(t[e]))}function Wa(t){return!!(On(t)||t.variants)}function kd(t,e){if(On(t)){const{initial:n,animate:r}=t;return{initial:n===!1||Nt(n)?n:void 0,animate:Nt(r)?r:void 0}}return t.inherit!==!1?e:{}}function Rd(t){const{initial:e,animate:n}=kd(t,m.useContext(En));return m.useMemo(()=>({initial:e,animate:n}),[Hi(e),Hi(n)])}function Hi(t){return Array.isArray(t)?t.join(" "):t}const _i={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},$t={};for(const t in _i)$t[t]={isEnabled:e=>_i[t].some(n=>!!e[n])};function Od(t){for(const e in t)$t[e]={...$t[e],...t[e]}}const _r=m.createContext({}),Na=m.createContext({}),Md=Symbol.for("motionComponentSymbol");function Ld({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:r,Component:i}){t&&Od(t);function a(l,c){let u;const d={...m.useContext(Da),...l,layoutId:Id(l)},{isStatic:h}=d,f=Rd(l),p=r(l,h);if(!h&&kn){f.visualElement=Ed(i,p,d,e);const g=m.useContext(Na),x=m.useContext(Ba).strict;f.visualElement&&(u=f.visualElement.loadFeatures(d,x,t,g))}return m.createElement(En.Provider,{value:f},u&&f.visualElement?m.createElement(u,{visualElement:f.visualElement,...d}):null,n(i,l,Fd(p,f.visualElement,c),p,h,f.visualElement))}const o=m.forwardRef(a);return o[Md]=i,o}function Id({layoutId:t}){const e=m.useContext(_r).id;return e&&t!==void 0?e+"-"+t:t}function Dd(t){function e(r,i={}){return Ld(t(r,i))}if(typeof Proxy>"u")return e;const n=new Map;return new Proxy(e,{get:(r,i)=>(n.has(i)||n.set(i,e(i)),n.get(i))})}const Bd=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Kr(t){return typeof t!="string"||t.includes("-")?!1:!!(Bd.indexOf(t)>-1||/[A-Z]/.test(t))}const gn={};function Vd(t){Object.assign(gn,t)}const qt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],st=new Set(qt);function $a(t,{layout:e,layoutId:n}){return st.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!gn[t]||t==="opacity")}const me=t=>!!(t&&t.getVelocity),Wd={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Nd=qt.length;function $d(t,{enableHardwareAcceleration:e=!0,allowTransformNone:n=!0},r,i){let a="";for(let o=0;o<Nd;o++){const l=qt[o];if(t[l]!==void 0){const c=Wd[l]||l;a+=`${c}(${t[l]}) `}}return e&&!t.z&&(a+="translateZ(0)"),a=a.trim(),i?a=i(t,r?"":a):n&&r&&(a="none"),a}const Ua=t=>e=>typeof e=="string"&&e.startsWith(t),za=Ua("--"),mr=Ua("var(--"),Ud=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,zd=(t,e)=>e&&typeof t=="number"?e.transform(t):t,Ue=(t,e,n)=>Math.min(Math.max(n,t),e),at={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},Rt={...at,transform:t=>Ue(0,1,t)},tn={...at,default:1},Ot=t=>Math.round(t*1e5)/1e5,Mn=/(-)?([\d]*\.?[\d])+/g,Ha=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Hd=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Yt(t){return typeof t=="string"}const Jt=t=>({test:e=>Yt(e)&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Le=Jt("deg"),Te=Jt("%"),L=Jt("px"),_d=Jt("vh"),Kd=Jt("vw"),Ki={...Te,parse:t=>Te.parse(t)/100,transform:t=>Te.transform(t*100)},Gi={...at,transform:Math.round},_a={borderWidth:L,borderTopWidth:L,borderRightWidth:L,borderBottomWidth:L,borderLeftWidth:L,borderRadius:L,radius:L,borderTopLeftRadius:L,borderTopRightRadius:L,borderBottomRightRadius:L,borderBottomLeftRadius:L,width:L,maxWidth:L,height:L,maxHeight:L,size:L,top:L,right:L,bottom:L,left:L,padding:L,paddingTop:L,paddingRight:L,paddingBottom:L,paddingLeft:L,margin:L,marginTop:L,marginRight:L,marginBottom:L,marginLeft:L,rotate:Le,rotateX:Le,rotateY:Le,rotateZ:Le,scale:tn,scaleX:tn,scaleY:tn,scaleZ:tn,skew:Le,skewX:Le,skewY:Le,distance:L,translateX:L,translateY:L,translateZ:L,x:L,y:L,z:L,perspective:L,transformPerspective:L,opacity:Rt,originX:Ki,originY:Ki,originZ:L,zIndex:Gi,fillOpacity:Rt,strokeOpacity:Rt,numOctaves:Gi};function Gr(t,e,n,r){const{style:i,vars:a,transform:o,transformOrigin:l}=t;let c=!1,u=!1,d=!0;for(const h in e){const f=e[h];if(za(h)){a[h]=f;continue}const p=_a[h],g=zd(f,p);if(st.has(h)){if(c=!0,o[h]=g,!d)continue;f!==(p.default||0)&&(d=!1)}else h.startsWith("origin")?(u=!0,l[h]=g):i[h]=g}if(e.transform||(c||r?i.transform=$d(t.transform,n,d,r):i.transform&&(i.transform="none")),u){const{originX:h="50%",originY:f="50%",originZ:p=0}=l;i.transformOrigin=`${h} ${f} ${p}`}}const qr=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Ka(t,e,n){for(const r in e)!me(e[r])&&!$a(r,n)&&(t[r]=e[r])}function Gd({transformTemplate:t},e,n){return m.useMemo(()=>{const r=qr();return Gr(r,e,{enableHardwareAcceleration:!n},t),Object.assign({},r.vars,r.style)},[e])}function qd(t,e,n){const r=t.style||{},i={};return Ka(i,r,t),Object.assign(i,Gd(t,e,n)),t.transformValues?t.transformValues(i):i}function Yd(t,e,n){const r={},i=qd(t,e,n);return t.drag&&t.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(r.tabIndex=0),r.style=i,r}const Jd=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function xn(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Jd.has(t)}let Ga=t=>!xn(t);function Xd(t){t&&(Ga=e=>e.startsWith("on")?!xn(e):t(e))}try{Xd(require("@emotion/is-prop-valid").default)}catch{}function Zd(t,e,n){const r={};for(const i in t)i==="values"&&typeof t.values=="object"||(Ga(i)||n===!0&&xn(i)||!e&&!xn(i)||t.draggable&&i.startsWith("onDrag"))&&(r[i]=t[i]);return r}function qi(t,e,n){return typeof t=="string"?t:L.transform(e+n*t)}function Qd(t,e,n){const r=qi(e,t.x,t.width),i=qi(n,t.y,t.height);return`${r} ${i}`}const eh={offset:"stroke-dashoffset",array:"stroke-dasharray"},th={offset:"strokeDashoffset",array:"strokeDasharray"};function nh(t,e,n=1,r=0,i=!0){t.pathLength=1;const a=i?eh:th;t[a.offset]=L.transform(-r);const o=L.transform(e),l=L.transform(n);t[a.array]=`${o} ${l}`}function Yr(t,{attrX:e,attrY:n,attrScale:r,originX:i,originY:a,pathLength:o,pathSpacing:l=1,pathOffset:c=0,...u},d,h,f){if(Gr(t,u,d,f),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:p,style:g,dimensions:x}=t;p.transform&&(x&&(g.transform=p.transform),delete p.transform),x&&(i!==void 0||a!==void 0||g.transform)&&(g.transformOrigin=Qd(x,i!==void 0?i:.5,a!==void 0?a:.5)),e!==void 0&&(p.x=e),n!==void 0&&(p.y=n),r!==void 0&&(p.scale=r),o!==void 0&&nh(p,o,l,c,!1)}const qa=()=>({...qr(),attrs:{}}),Jr=t=>typeof t=="string"&&t.toLowerCase()==="svg";function rh(t,e,n,r){const i=m.useMemo(()=>{const a=qa();return Yr(a,e,{enableHardwareAcceleration:!1},Jr(r),t.transformTemplate),{...a.attrs,style:{...a.style}}},[e]);if(t.style){const a={};Ka(a,t.style,t),i.style={...a,...i.style}}return i}function ih(t=!1){return(n,r,i,{latestValues:a},o)=>{const c=(Kr(n)?rh:Yd)(r,a,o,n),d={...Zd(r,typeof n=="string",t),...c,ref:i},{children:h}=r,f=m.useMemo(()=>me(h)?h.get():h,[h]);return m.createElement(n,{...d,children:f})}}function Ya(t,{style:e,vars:n},r,i){Object.assign(t.style,e,i&&i.getProjectionStyles(r));for(const a in n)t.style.setProperty(a,n[a])}const Ja=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Xa(t,e,n,r){Ya(t,e,void 0,r);for(const i in e.attrs)t.setAttribute(Ja.has(i)?i:Ur(i),e.attrs[i])}function Xr(t,e){const{style:n}=t,r={};for(const i in n)(me(n[i])||e.style&&me(e.style[i])||$a(i,t))&&(r[i]=n[i]);return r}function Za(t,e){const n=Xr(t,e);for(const r in t)if(me(t[r])||me(e[r])){const i=qt.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=t[r]}return n}function Zr(t,e,n,r={},i={}){return typeof e=="function"&&(e=e(n!==void 0?n:t.custom,r,i)),typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"&&(e=e(n!==void 0?n:t.custom,r,i)),e}function Qa(t){const e=m.useRef(null);return e.current===null&&(e.current=t()),e.current}const yn=t=>Array.isArray(t),sh=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),ah=t=>yn(t)?t[t.length-1]||0:t;function on(t){const e=me(t)?t.get():t;return sh(e)?e.toValue():e}function oh({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},r,i,a){const o={latestValues:lh(r,i,a,t),renderState:e()};return n&&(o.mount=l=>n(r,l,o)),o}const eo=t=>(e,n)=>{const r=m.useContext(En),i=m.useContext(Fn),a=()=>oh(t,e,r,i);return n?a():Qa(a)};function lh(t,e,n,r){const i={},a=r(t,{});for(const f in a)i[f]=on(a[f]);let{initial:o,animate:l}=t;const c=On(t),u=Wa(t);e&&u&&!c&&t.inherit!==!1&&(o===void 0&&(o=e.initial),l===void 0&&(l=e.animate));let d=n?n.initial===!1:!1;d=d||o===!1;const h=d?l:o;return h&&typeof h!="boolean"&&!Rn(h)&&(Array.isArray(h)?h:[h]).forEach(p=>{const g=Zr(t,p);if(!g)return;const{transitionEnd:x,transition:j,...C}=g;for(const v in C){let w=C[v];if(Array.isArray(w)){const P=d?w.length-1:0;w=w[P]}w!==null&&(i[v]=w)}for(const v in x)i[v]=x[v]}),i}const ee=t=>t;class Yi{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){const n=this.order.indexOf(e);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}function ch(t){let e=new Yi,n=new Yi,r=0,i=!1,a=!1;const o=new WeakSet,l={schedule:(c,u=!1,d=!1)=>{const h=d&&i,f=h?e:n;return u&&o.add(c),f.add(c)&&h&&i&&(r=e.order.length),c},cancel:c=>{n.remove(c),o.delete(c)},process:c=>{if(i){a=!0;return}if(i=!0,[e,n]=[n,e],n.clear(),r=e.order.length,r)for(let u=0;u<r;u++){const d=e.order[u];d(c),o.has(d)&&(l.schedule(d),t())}i=!1,a&&(a=!1,l.process(c))}};return l}const nn=["prepare","read","update","preRender","render","postRender"],uh=40;function dh(t,e){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},a=nn.reduce((h,f)=>(h[f]=ch(()=>n=!0),h),{}),o=h=>a[h].process(i),l=()=>{const h=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(h-i.timestamp,uh),1),i.timestamp=h,i.isProcessing=!0,nn.forEach(o),i.isProcessing=!1,n&&e&&(r=!1,t(l))},c=()=>{n=!0,r=!0,i.isProcessing||t(l)};return{schedule:nn.reduce((h,f)=>{const p=a[f];return h[f]=(g,x=!1,j=!1)=>(n||c(),p.schedule(g,x,j)),h},{}),cancel:h=>nn.forEach(f=>a[f].cancel(h)),state:i,steps:a}}const{schedule:G,cancel:Me,state:oe,steps:zn}=dh(typeof requestAnimationFrame<"u"?requestAnimationFrame:ee,!0),hh={useVisualState:eo({scrapeMotionValuesFromProps:Za,createRenderState:qa,onMount:(t,e,{renderState:n,latestValues:r})=>{G.read(()=>{try{n.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),G.render(()=>{Yr(n,r,{enableHardwareAcceleration:!1},Jr(e.tagName),t.transformTemplate),Xa(e,n)})}})},fh={useVisualState:eo({scrapeMotionValuesFromProps:Xr,createRenderState:qr})};function ph(t,{forwardMotionProps:e=!1},n,r){return{...Kr(t)?hh:fh,preloadedFeatures:n,useRender:ih(e),createVisualElement:r,Component:t}}function ke(t,e,n,r={passive:!0}){return t.addEventListener(e,n,r),()=>t.removeEventListener(e,n)}const to=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function Ln(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}const mh=t=>e=>to(e)&&t(e,Ln(e));function Re(t,e,n,r){return ke(t,e,mh(n),r)}const gh=(t,e)=>n=>e(t(n)),Ne=(...t)=>t.reduce(gh);function no(t){let e=null;return()=>{const n=()=>{e=null};return e===null?(e=t,n):!1}}const Ji=no("dragHorizontal"),Xi=no("dragVertical");function ro(t){let e=!1;if(t==="y")e=Xi();else if(t==="x")e=Ji();else{const n=Ji(),r=Xi();n&&r?e=()=>{n(),r()}:(n&&n(),r&&r())}return e}function io(){const t=ro(!0);return t?(t(),!1):!0}class Ke{constructor(e){this.isMounted=!1,this.node=e}update(){}}function Zi(t,e){const n="pointer"+(e?"enter":"leave"),r="onHover"+(e?"Start":"End"),i=(a,o)=>{if(a.pointerType==="touch"||io())return;const l=t.getProps();t.animationState&&l.whileHover&&t.animationState.setActive("whileHover",e),l[r]&&G.update(()=>l[r](a,o))};return Re(t.current,n,i,{passive:!t.getProps()[r]})}class xh extends Ke{mount(){this.unmount=Ne(Zi(this.node,!0),Zi(this.node,!1))}unmount(){}}class yh extends Ke{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Ne(ke(this.node.current,"focus",()=>this.onFocus()),ke(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const so=(t,e)=>e?t===e?!0:so(t,e.parentElement):!1;function Hn(t,e){if(!e)return;const n=new PointerEvent("pointer"+t);e(n,Ln(n))}class bh extends Ke{constructor(){super(...arguments),this.removeStartListeners=ee,this.removeEndListeners=ee,this.removeAccessibleListeners=ee,this.startPointerPress=(e,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),a=Re(window,"pointerup",(l,c)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:d,globalTapTarget:h}=this.node.getProps();G.update(()=>{!h&&!so(this.node.current,l.target)?d&&d(l,c):u&&u(l,c)})},{passive:!(r.onTap||r.onPointerUp)}),o=Re(window,"pointercancel",(l,c)=>this.cancelPress(l,c),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Ne(a,o),this.startPress(e,n)},this.startAccessiblePress=()=>{const e=a=>{if(a.key!=="Enter"||this.isPressing)return;const o=l=>{l.key!=="Enter"||!this.checkPressEnd()||Hn("up",(c,u)=>{const{onTap:d}=this.node.getProps();d&&G.update(()=>d(c,u))})};this.removeEndListeners(),this.removeEndListeners=ke(this.node.current,"keyup",o),Hn("down",(l,c)=>{this.startPress(l,c)})},n=ke(this.node.current,"keydown",e),r=()=>{this.isPressing&&Hn("cancel",(a,o)=>this.cancelPress(a,o))},i=ke(this.node.current,"blur",r);this.removeAccessibleListeners=Ne(n,i)}}startPress(e,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&G.update(()=>r(e,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!io()}cancelPress(e,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&G.update(()=>r(e,n))}mount(){const e=this.node.getProps(),n=Re(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),r=ke(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Ne(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const gr=new WeakMap,_n=new WeakMap,vh=t=>{const e=gr.get(t.target);e&&e(t)},jh=t=>{t.forEach(vh)};function Sh({root:t,...e}){const n=t||document;_n.has(n)||_n.set(n,{});const r=_n.get(n),i=JSON.stringify(e);return r[i]||(r[i]=new IntersectionObserver(jh,{root:t,...e})),r[i]}function wh(t,e,n){const r=Sh(e);return gr.set(t,n),r.observe(t),()=>{gr.delete(t),r.unobserve(t)}}const Ch={some:0,all:1};class Ph extends Ke{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:a}=e,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:Ch[i]},l=c=>{const{isIntersecting:u}=c;if(this.isInView===u||(this.isInView=u,a&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:d,onViewportLeave:h}=this.node.getProps(),f=u?d:h;f&&f(c)};return wh(this.node.current,o,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(Ah(e,n))&&this.startObserver()}unmount(){}}function Ah({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const Th={inView:{Feature:Ph},tap:{Feature:bh},focus:{Feature:yh},hover:{Feature:xh}};function ao(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let r=0;r<n;r++)if(e[r]!==t[r])return!1;return!0}function Eh(t){const e={};return t.values.forEach((n,r)=>e[r]=n.get()),e}function Fh(t){const e={};return t.values.forEach((n,r)=>e[r]=n.getVelocity()),e}function In(t,e,n){const r=t.getProps();return Zr(r,e,n!==void 0?n:r.custom,Eh(t),Fh(t))}let Qr=ee;const nt=t=>t*1e3,Oe=t=>t/1e3,kh={current:!1},oo=t=>Array.isArray(t)&&typeof t[0]=="number";function lo(t){return!!(!t||typeof t=="string"&&co[t]||oo(t)||Array.isArray(t)&&t.every(lo))}const Et=([t,e,n,r])=>`cubic-bezier(${t}, ${e}, ${n}, ${r})`,co={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Et([0,.65,.55,1]),circOut:Et([.55,0,1,.45]),backIn:Et([.31,.01,.66,-.59]),backOut:Et([.33,1.53,.69,.99])};function uo(t){if(t)return oo(t)?Et(t):Array.isArray(t)?t.map(uo):co[t]}function Rh(t,e,n,{delay:r=0,duration:i,repeat:a=0,repeatType:o="loop",ease:l,times:c}={}){const u={[e]:n};c&&(u.offset=c);const d=uo(l);return Array.isArray(d)&&(u.easing=d),t.animate(u,{delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:a+1,direction:o==="reverse"?"alternate":"normal"})}function Oh(t,{repeat:e,repeatType:n="loop"}){const r=e&&n!=="loop"&&e%2===1?0:t.length-1;return t[r]}const ho=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,Mh=1e-7,Lh=12;function Ih(t,e,n,r,i){let a,o,l=0;do o=e+(n-e)/2,a=ho(o,r,i)-t,a>0?n=o:e=o;while(Math.abs(a)>Mh&&++l<Lh);return o}function Xt(t,e,n,r){if(t===e&&n===r)return ee;const i=a=>Ih(a,0,1,t,n);return a=>a===0||a===1?a:ho(i(a),e,r)}const Dh=Xt(.42,0,1,1),Bh=Xt(0,0,.58,1),fo=Xt(.42,0,.58,1),Vh=t=>Array.isArray(t)&&typeof t[0]!="number",po=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,mo=t=>e=>1-t(1-e),ei=t=>1-Math.sin(Math.acos(t)),go=mo(ei),Wh=po(ei),xo=Xt(.33,1.53,.69,.99),ti=mo(xo),Nh=po(ti),$h=t=>(t*=2)<1?.5*ti(t):.5*(2-Math.pow(2,-10*(t-1))),Uh={linear:ee,easeIn:Dh,easeInOut:fo,easeOut:Bh,circIn:ei,circInOut:Wh,circOut:go,backIn:ti,backInOut:Nh,backOut:xo,anticipate:$h},Qi=t=>{if(Array.isArray(t)){Qr(t.length===4);const[e,n,r,i]=t;return Xt(e,n,r,i)}else if(typeof t=="string")return Uh[t];return t},ni=(t,e)=>n=>!!(Yt(n)&&Hd.test(n)&&n.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(n,e)),yo=(t,e,n)=>r=>{if(!Yt(r))return r;const[i,a,o,l]=r.match(Mn);return{[t]:parseFloat(i),[e]:parseFloat(a),[n]:parseFloat(o),alpha:l!==void 0?parseFloat(l):1}},zh=t=>Ue(0,255,t),Kn={...at,transform:t=>Math.round(zh(t))},tt={test:ni("rgb","red"),parse:yo("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:r=1})=>"rgba("+Kn.transform(t)+", "+Kn.transform(e)+", "+Kn.transform(n)+", "+Ot(Rt.transform(r))+")"};function Hh(t){let e="",n="",r="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),r=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),r=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,r+=r,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const xr={test:ni("#"),parse:Hh,transform:tt.transform},ft={test:ni("hsl","hue"),parse:yo("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:r=1})=>"hsla("+Math.round(t)+", "+Te.transform(Ot(e))+", "+Te.transform(Ot(n))+", "+Ot(Rt.transform(r))+")"},de={test:t=>tt.test(t)||xr.test(t)||ft.test(t),parse:t=>tt.test(t)?tt.parse(t):ft.test(t)?ft.parse(t):xr.parse(t),transform:t=>Yt(t)?t:t.hasOwnProperty("red")?tt.transform(t):ft.transform(t)},J=(t,e,n)=>-n*t+n*e+t;function Gn(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function _h({hue:t,saturation:e,lightness:n,alpha:r}){t/=360,e/=100,n/=100;let i=0,a=0,o=0;if(!e)i=a=o=n;else{const l=n<.5?n*(1+e):n+e-n*e,c=2*n-l;i=Gn(c,l,t+1/3),a=Gn(c,l,t),o=Gn(c,l,t-1/3)}return{red:Math.round(i*255),green:Math.round(a*255),blue:Math.round(o*255),alpha:r}}const qn=(t,e,n)=>{const r=t*t;return Math.sqrt(Math.max(0,n*(e*e-r)+r))},Kh=[xr,tt,ft],Gh=t=>Kh.find(e=>e.test(t));function es(t){const e=Gh(t);let n=e.parse(t);return e===ft&&(n=_h(n)),n}const bo=(t,e)=>{const n=es(t),r=es(e),i={...n};return a=>(i.red=qn(n.red,r.red,a),i.green=qn(n.green,r.green,a),i.blue=qn(n.blue,r.blue,a),i.alpha=J(n.alpha,r.alpha,a),tt.transform(i))};function qh(t){var e,n;return isNaN(t)&&Yt(t)&&(((e=t.match(Mn))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(Ha))===null||n===void 0?void 0:n.length)||0)>0}const vo={regex:Ud,countKey:"Vars",token:"${v}",parse:ee},jo={regex:Ha,countKey:"Colors",token:"${c}",parse:de.parse},So={regex:Mn,countKey:"Numbers",token:"${n}",parse:at.parse};function Yn(t,{regex:e,countKey:n,token:r,parse:i}){const a=t.tokenised.match(e);a&&(t["num"+n]=a.length,t.tokenised=t.tokenised.replace(e,r),t.values.push(...a.map(i)))}function bn(t){const e=t.toString(),n={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&Yn(n,vo),Yn(n,jo),Yn(n,So),n}function wo(t){return bn(t).values}function Co(t){const{values:e,numColors:n,numVars:r,tokenised:i}=bn(t),a=e.length;return o=>{let l=i;for(let c=0;c<a;c++)c<r?l=l.replace(vo.token,o[c]):c<r+n?l=l.replace(jo.token,de.transform(o[c])):l=l.replace(So.token,Ot(o[c]));return l}}const Yh=t=>typeof t=="number"?0:t;function Jh(t){const e=wo(t);return Co(t)(e.map(Yh))}const ze={test:qh,parse:wo,createTransformer:Co,getAnimatableNone:Jh},Po=(t,e)=>n=>`${n>0?e:t}`;function Ao(t,e){return typeof t=="number"?n=>J(t,e,n):de.test(t)?bo(t,e):t.startsWith("var(")?Po(t,e):Eo(t,e)}const To=(t,e)=>{const n=[...t],r=n.length,i=t.map((a,o)=>Ao(a,e[o]));return a=>{for(let o=0;o<r;o++)n[o]=i[o](a);return n}},Xh=(t,e)=>{const n={...t,...e},r={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(r[i]=Ao(t[i],e[i]));return i=>{for(const a in r)n[a]=r[a](i);return n}},Eo=(t,e)=>{const n=ze.createTransformer(e),r=bn(t),i=bn(e);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?Ne(To(r.values,i.values),n):Po(t,e)},Ut=(t,e,n)=>{const r=e-t;return r===0?1:(n-t)/r},ts=(t,e)=>n=>J(t,e,n);function Zh(t){return typeof t=="number"?ts:typeof t=="string"?de.test(t)?bo:Eo:Array.isArray(t)?To:typeof t=="object"?Xh:ts}function Qh(t,e,n){const r=[],i=n||Zh(t[0]),a=t.length-1;for(let o=0;o<a;o++){let l=i(t[o],t[o+1]);if(e){const c=Array.isArray(e)?e[o]||ee:e;l=Ne(c,l)}r.push(l)}return r}function Fo(t,e,{clamp:n=!0,ease:r,mixer:i}={}){const a=t.length;if(Qr(a===e.length),a===1)return()=>e[0];t[0]>t[a-1]&&(t=[...t].reverse(),e=[...e].reverse());const o=Qh(e,r,i),l=o.length,c=u=>{let d=0;if(l>1)for(;d<t.length-2&&!(u<t[d+1]);d++);const h=Ut(t[d],t[d+1],u);return o[d](h)};return n?u=>c(Ue(t[0],t[a-1],u)):c}function ef(t,e){const n=t[t.length-1];for(let r=1;r<=e;r++){const i=Ut(0,e,r);t.push(J(n,1,i))}}function tf(t){const e=[0];return ef(e,t.length-1),e}function nf(t,e){return t.map(n=>n*e)}function rf(t,e){return t.map(()=>e||fo).splice(0,t.length-1)}function vn({duration:t=300,keyframes:e,times:n,ease:r="easeInOut"}){const i=Vh(r)?r.map(Qi):Qi(r),a={done:!1,value:e[0]},o=nf(n&&n.length===e.length?n:tf(e),t),l=Fo(o,e,{ease:Array.isArray(i)?i:rf(e,i)});return{calculatedDuration:t,next:c=>(a.value=l(c),a.done=c>=t,a)}}function ko(t,e){return e?t*(1e3/e):0}const sf=5;function Ro(t,e,n){const r=Math.max(e-sf,0);return ko(n-t(r),e-r)}const ns=.001,af=.01,of=10,lf=.05,cf=1;function uf({duration:t=800,bounce:e=.25,velocity:n=0,mass:r=1}){let i,a,o=1-e;o=Ue(lf,cf,o),t=Ue(af,of,Oe(t)),o<1?(i=u=>{const d=u*o,h=d*t,f=d-n,p=yr(u,o),g=Math.exp(-h);return ns-f/p*g},a=u=>{const h=u*o*t,f=h*n+n,p=Math.pow(o,2)*Math.pow(u,2)*t,g=Math.exp(-h),x=yr(Math.pow(u,2),o);return(-i(u)+ns>0?-1:1)*((f-p)*g)/x}):(i=u=>{const d=Math.exp(-u*t),h=(u-n)*t+1;return-.001+d*h},a=u=>{const d=Math.exp(-u*t),h=(n-u)*(t*t);return d*h});const l=5/t,c=hf(i,a,l);if(t=nt(t),isNaN(c))return{stiffness:100,damping:10,duration:t};{const u=Math.pow(c,2)*r;return{stiffness:u,damping:o*2*Math.sqrt(r*u),duration:t}}}const df=12;function hf(t,e,n){let r=n;for(let i=1;i<df;i++)r=r-t(r)/e(r);return r}function yr(t,e){return t*Math.sqrt(1-e*e)}const ff=["duration","bounce"],pf=["stiffness","damping","mass"];function rs(t,e){return e.some(n=>t[n]!==void 0)}function mf(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!rs(t,pf)&&rs(t,ff)){const n=uf(t);e={...e,...n,mass:1},e.isResolvedFromDuration=!0}return e}function Oo({keyframes:t,restDelta:e,restSpeed:n,...r}){const i=t[0],a=t[t.length-1],o={done:!1,value:i},{stiffness:l,damping:c,mass:u,duration:d,velocity:h,isResolvedFromDuration:f}=mf({...r,velocity:-Oe(r.velocity||0)}),p=h||0,g=c/(2*Math.sqrt(l*u)),x=a-i,j=Oe(Math.sqrt(l/u)),C=Math.abs(x)<5;n||(n=C?.01:2),e||(e=C?.005:.5);let v;if(g<1){const w=yr(j,g);v=P=>{const E=Math.exp(-g*j*P);return a-E*((p+g*j*x)/w*Math.sin(w*P)+x*Math.cos(w*P))}}else if(g===1)v=w=>a-Math.exp(-j*w)*(x+(p+j*x)*w);else{const w=j*Math.sqrt(g*g-1);v=P=>{const E=Math.exp(-g*j*P),k=Math.min(w*P,300);return a-E*((p+g*j*x)*Math.sinh(k)+w*x*Math.cosh(k))/w}}return{calculatedDuration:f&&d||null,next:w=>{const P=v(w);if(f)o.done=w>=d;else{let E=p;w!==0&&(g<1?E=Ro(v,w,P):E=0);const k=Math.abs(E)<=n,R=Math.abs(a-P)<=e;o.done=k&&R}return o.value=o.done?a:P,o}}}function is({keyframes:t,velocity:e=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:a=500,modifyTarget:o,min:l,max:c,restDelta:u=.5,restSpeed:d}){const h=t[0],f={done:!1,value:h},p=O=>l!==void 0&&O<l||c!==void 0&&O>c,g=O=>l===void 0?c:c===void 0||Math.abs(l-O)<Math.abs(c-O)?l:c;let x=n*e;const j=h+x,C=o===void 0?j:o(j);C!==j&&(x=C-h);const v=O=>-x*Math.exp(-O/r),w=O=>C+v(O),P=O=>{const I=v(O),Y=w(O);f.done=Math.abs(I)<=u,f.value=f.done?C:Y};let E,k;const R=O=>{p(f.value)&&(E=O,k=Oo({keyframes:[f.value,g(f.value)],velocity:Ro(w,O,f.value),damping:i,stiffness:a,restDelta:u,restSpeed:d}))};return R(0),{calculatedDuration:null,next:O=>{let I=!1;return!k&&E===void 0&&(I=!0,P(O),R(O)),E!==void 0&&O>E?k.next(O-E):(!I&&P(O),f)}}}const gf=t=>{const e=({timestamp:n})=>t(n);return{start:()=>G.update(e,!0),stop:()=>Me(e),now:()=>oe.isProcessing?oe.timestamp:performance.now()}},ss=2e4;function as(t){let e=0;const n=50;let r=t.next(e);for(;!r.done&&e<ss;)e+=n,r=t.next(e);return e>=ss?1/0:e}const xf={decay:is,inertia:is,tween:vn,keyframes:vn,spring:Oo};function jn({autoplay:t=!0,delay:e=0,driver:n=gf,keyframes:r,type:i="keyframes",repeat:a=0,repeatDelay:o=0,repeatType:l="loop",onPlay:c,onStop:u,onComplete:d,onUpdate:h,...f}){let p=1,g=!1,x,j;const C=()=>{j=new Promise($=>{x=$})};C();let v;const w=xf[i]||vn;let P;w!==vn&&typeof r[0]!="number"&&(P=Fo([0,100],r,{clamp:!1}),r=[0,100]);const E=w({...f,keyframes:r});let k;l==="mirror"&&(k=w({...f,keyframes:[...r].reverse(),velocity:-(f.velocity||0)}));let R="idle",O=null,I=null,Y=null;E.calculatedDuration===null&&a&&(E.calculatedDuration=as(E));const{calculatedDuration:Z}=E;let H=1/0,ce=1/0;Z!==null&&(H=Z+o,ce=H*(a+1)-o);let z=0;const S=$=>{if(I===null)return;p>0&&(I=Math.min(I,$)),p<0&&(I=Math.min($-ce/p,I)),O!==null?z=O:z=Math.round($-I)*p;const Ge=z-e*(p>=0?1:-1),ue=p>=0?Ge<0:Ge>ce;z=Math.max(Ge,0),R==="finished"&&O===null&&(z=ce);let qe=z,ui=E;if(a){const Bn=Math.min(z,ce)/H;let Zt=Math.floor(Bn),Ye=Bn%1;!Ye&&Bn>=1&&(Ye=1),Ye===1&&Zt--,Zt=Math.min(Zt,a+1),!!(Zt%2)&&(l==="reverse"?(Ye=1-Ye,o&&(Ye-=o/H)):l==="mirror"&&(ui=k)),qe=Ue(0,1,Ye)*H}const Ct=ue?{done:!1,value:r[0]}:ui.next(qe);P&&(Ct.value=P(Ct.value));let{done:di}=Ct;!ue&&Z!==null&&(di=p>=0?z>=ce:z<=0);const hl=O===null&&(R==="finished"||R==="running"&&di);return h&&h(Ct.value),hl&&B(),Ct},A=()=>{v&&v.stop(),v=void 0},M=()=>{R="idle",A(),x(),C(),I=Y=null},B=()=>{R="finished",d&&d(),A(),x()},N=()=>{if(g)return;v||(v=n(S));const $=v.now();c&&c(),O!==null?I=$-O:(!I||R==="finished")&&(I=$),R==="finished"&&C(),Y=I,O=null,R="running",v.start()};t&&N();const V={then($,Ge){return j.then($,Ge)},get time(){return Oe(z)},set time($){$=nt($),z=$,O!==null||!v||p===0?O=$:I=v.now()-$/p},get duration(){const $=E.calculatedDuration===null?as(E):E.calculatedDuration;return Oe($)},get speed(){return p},set speed($){$===p||!v||(p=$,V.time=Oe(z))},get state(){return R},play:N,pause:()=>{R="paused",O=z},stop:()=>{g=!0,R!=="idle"&&(R="idle",u&&u(),M())},cancel:()=>{Y!==null&&S(Y),M()},complete:()=>{R="finished"},sample:$=>(I=0,S($))};return V}function yf(t){let e;return()=>(e===void 0&&(e=t()),e)}const bf=yf(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),vf=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),rn=10,jf=2e4,Sf=(t,e)=>e.type==="spring"||t==="backgroundColor"||!lo(e.ease);function wf(t,e,{onUpdate:n,onComplete:r,...i}){if(!(bf()&&vf.has(e)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let o=!1,l,c,u=!1;const d=()=>{c=new Promise(w=>{l=w})};d();let{keyframes:h,duration:f=300,ease:p,times:g}=i;if(Sf(e,i)){const w=jn({...i,repeat:0,delay:0});let P={done:!1,value:h[0]};const E=[];let k=0;for(;!P.done&&k<jf;)P=w.sample(k),E.push(P.value),k+=rn;g=void 0,h=E,f=k-rn,p="linear"}const x=Rh(t.owner.current,e,h,{...i,duration:f,ease:p,times:g}),j=()=>{u=!1,x.cancel()},C=()=>{u=!0,G.update(j),l(),d()};return x.onfinish=()=>{u||(t.set(Oh(h,i)),r&&r(),C())},{then(w,P){return c.then(w,P)},attachTimeline(w){return x.timeline=w,x.onfinish=null,ee},get time(){return Oe(x.currentTime||0)},set time(w){x.currentTime=nt(w)},get speed(){return x.playbackRate},set speed(w){x.playbackRate=w},get duration(){return Oe(f)},play:()=>{o||(x.play(),Me(j))},pause:()=>x.pause(),stop:()=>{if(o=!0,x.playState==="idle")return;const{currentTime:w}=x;if(w){const P=jn({...i,autoplay:!1});t.setWithVelocity(P.sample(w-rn).value,P.sample(w).value,rn)}C()},complete:()=>{u||x.finish()},cancel:C}}function Cf({keyframes:t,delay:e,onUpdate:n,onComplete:r}){const i=()=>(n&&n(t[t.length-1]),r&&r(),{time:0,speed:1,duration:0,play:ee,pause:ee,stop:ee,then:a=>(a(),Promise.resolve()),cancel:ee,complete:ee});return e?jn({keyframes:[0,1],duration:0,delay:e,onComplete:i}):i()}const Pf={type:"spring",stiffness:500,damping:25,restSpeed:10},Af=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Tf={type:"keyframes",duration:.8},Ef={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Ff=(t,{keyframes:e})=>e.length>2?Tf:st.has(t)?t.startsWith("scale")?Af(e[1]):Pf:Ef,br=(t,e)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(ze.test(e)||e==="0")&&!e.startsWith("url(")),kf=new Set(["brightness","contrast","saturate","opacity"]);function Rf(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[r]=n.match(Mn)||[];if(!r)return t;const i=n.replace(r,"");let a=kf.has(e)?1:0;return r!==n&&(a*=100),e+"("+a+i+")"}const Of=/([a-z-]*)\(.*?\)/g,vr={...ze,getAnimatableNone:t=>{const e=t.match(Of);return e?e.map(Rf).join(" "):t}},Mf={..._a,color:de,backgroundColor:de,outlineColor:de,fill:de,stroke:de,borderColor:de,borderTopColor:de,borderRightColor:de,borderBottomColor:de,borderLeftColor:de,filter:vr,WebkitFilter:vr},ri=t=>Mf[t];function Mo(t,e){let n=ri(t);return n!==vr&&(n=ze),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Lo=t=>/^0[^.\s]+$/.test(t);function Lf(t){if(typeof t=="number")return t===0;if(t!==null)return t==="none"||t==="0"||Lo(t)}function If(t,e,n,r){const i=br(e,n);let a;Array.isArray(n)?a=[...n]:a=[null,n];const o=r.from!==void 0?r.from:t.get();let l;const c=[];for(let u=0;u<a.length;u++)a[u]===null&&(a[u]=u===0?o:a[u-1]),Lf(a[u])&&c.push(u),typeof a[u]=="string"&&a[u]!=="none"&&a[u]!=="0"&&(l=a[u]);if(i&&c.length&&l)for(let u=0;u<c.length;u++){const d=c[u];a[d]=Mo(e,l)}return a}function Df({when:t,delay:e,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:a,repeatType:o,repeatDelay:l,from:c,elapsed:u,...d}){return!!Object.keys(d).length}function ii(t,e){return t[e]||t.default||t}const Bf={skipAnimations:!1},si=(t,e,n,r={})=>i=>{const a=ii(r,t)||{},o=a.delay||r.delay||0;let{elapsed:l=0}=r;l=l-nt(o);const c=If(e,t,n,a),u=c[0],d=c[c.length-1],h=br(t,u),f=br(t,d);let p={keyframes:c,velocity:e.getVelocity(),ease:"easeOut",...a,delay:-l,onUpdate:g=>{e.set(g),a.onUpdate&&a.onUpdate(g)},onComplete:()=>{i(),a.onComplete&&a.onComplete()}};if(Df(a)||(p={...p,...Ff(t,p)}),p.duration&&(p.duration=nt(p.duration)),p.repeatDelay&&(p.repeatDelay=nt(p.repeatDelay)),!h||!f||kh.current||a.type===!1||Bf.skipAnimations)return Cf(p);if(!r.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){const g=wf(e,t,p);if(g)return g}return jn(p)};function Sn(t){return!!(me(t)&&t.add)}const Io=t=>/^\-?\d*\.?\d+$/.test(t);function ai(t,e){t.indexOf(e)===-1&&t.push(e)}function oi(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class li{constructor(){this.subscriptions=[]}add(e){return ai(this.subscriptions,e),()=>oi(this.subscriptions,e)}notify(e,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,r);else for(let a=0;a<i;a++){const o=this.subscriptions[a];o&&o(e,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Vf=t=>!isNaN(parseFloat(t));class Wf{constructor(e,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:a,timestamp:o}=oe;this.lastUpdated!==o&&(this.timeDelta=a,this.lastUpdated=o,G.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>G.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=Vf(this.current),this.owner=n.owner}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new li);const r=this.events[e].add(n);return e==="change"?()=>{r(),G.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,r){this.set(n),this.prev=e,this.timeDelta=r}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?ko(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function bt(t,e){return new Wf(t,e)}const Do=t=>e=>e.test(t),Nf={test:t=>t==="auto",parse:t=>t},Bo=[at,L,Te,Le,Kd,_d,Nf],At=t=>Bo.find(Do(t)),$f=[...Bo,de,ze],Uf=t=>$f.find(Do(t));function zf(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,bt(n))}function Hf(t,e){const n=In(t,e);let{transitionEnd:r={},transition:i={},...a}=n?t.makeTargetAnimatable(n,!1):{};a={...a,...r};for(const o in a){const l=ah(a[o]);zf(t,o,l)}}function _f(t,e,n){var r,i;const a=Object.keys(e).filter(l=>!t.hasValue(l)),o=a.length;if(o)for(let l=0;l<o;l++){const c=a[l],u=e[c];let d=null;Array.isArray(u)&&(d=u[0]),d===null&&(d=(i=(r=n[c])!==null&&r!==void 0?r:t.readValue(c))!==null&&i!==void 0?i:e[c]),d!=null&&(typeof d=="string"&&(Io(d)||Lo(d))?d=parseFloat(d):!Uf(d)&&ze.test(u)&&(d=Mo(c,u)),t.addValue(c,bt(d,{owner:t})),n[c]===void 0&&(n[c]=d),d!==null&&t.setBaseTarget(c,d))}}function Kf(t,e){return e?(e[t]||e.default||e).from:void 0}function Gf(t,e,n){const r={};for(const i in t){const a=Kf(i,e);if(a!==void 0)r[i]=a;else{const o=n.getValue(i);o&&(r[i]=o.get())}}return r}function qf({protectedKeys:t,needsAnimating:e},n){const r=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,r}function Yf(t,e){const n=t.get();if(Array.isArray(e)){for(let r=0;r<e.length;r++)if(e[r]!==n)return!0}else return n!==e}function Vo(t,e,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:a=t.getDefaultTransition(),transitionEnd:o,...l}=t.makeTargetAnimatable(e);const c=t.getValue("willChange");r&&(a=r);const u=[],d=i&&t.animationState&&t.animationState.getState()[i];for(const h in l){const f=t.getValue(h),p=l[h];if(!f||p===void 0||d&&qf(d,h))continue;const g={delay:n,elapsed:0,...ii(a||{},h)};if(window.HandoffAppearAnimations){const C=t.getProps()[Va];if(C){const v=window.HandoffAppearAnimations(C,h,f,G);v!==null&&(g.elapsed=v,g.isHandoff=!0)}}let x=!g.isHandoff&&!Yf(f,p);if(g.type==="spring"&&(f.getVelocity()||g.velocity)&&(x=!1),f.animation&&(x=!1),x)continue;f.start(si(h,f,p,t.shouldReduceMotion&&st.has(h)?{type:!1}:g));const j=f.animation;Sn(c)&&(c.add(h),j.then(()=>c.remove(h))),u.push(j)}return o&&Promise.all(u).then(()=>{o&&Hf(t,o)}),u}function jr(t,e,n={}){const r=In(t,e,n.custom);let{transition:i=t.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const a=r?()=>Promise.all(Vo(t,r,n)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:d,staggerDirection:h}=i;return Jf(t,e,u+c,d,h,n)}:()=>Promise.resolve(),{when:l}=i;if(l){const[c,u]=l==="beforeChildren"?[a,o]:[o,a];return c().then(()=>u())}else return Promise.all([a(),o(n.delay)])}function Jf(t,e,n=0,r=0,i=1,a){const o=[],l=(t.variantChildren.size-1)*r,c=i===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(t.variantChildren).sort(Xf).forEach((u,d)=>{u.notify("AnimationStart",e),o.push(jr(u,e,{...a,delay:n+c(d)}).then(()=>u.notify("AnimationComplete",e)))}),Promise.all(o)}function Xf(t,e){return t.sortNodePosition(e)}function Zf(t,e,n={}){t.notify("AnimationStart",e);let r;if(Array.isArray(e)){const i=e.map(a=>jr(t,a,n));r=Promise.all(i)}else if(typeof e=="string")r=jr(t,e,n);else{const i=typeof e=="function"?In(t,e,n.custom):e;r=Promise.all(Vo(t,i,n))}return r.then(()=>t.notify("AnimationComplete",e))}const Qf=[...zr].reverse(),ep=zr.length;function tp(t){return e=>Promise.all(e.map(({animation:n,options:r})=>Zf(t,n,r)))}function np(t){let e=tp(t);const n=ip();let r=!0;const i=(c,u)=>{const d=In(t,u);if(d){const{transition:h,transitionEnd:f,...p}=d;c={...c,...p,...f}}return c};function a(c){e=c(t)}function o(c,u){const d=t.getProps(),h=t.getVariantContext(!0)||{},f=[],p=new Set;let g={},x=1/0;for(let C=0;C<ep;C++){const v=Qf[C],w=n[v],P=d[v]!==void 0?d[v]:h[v],E=Nt(P),k=v===u?w.isActive:null;k===!1&&(x=C);let R=P===h[v]&&P!==d[v]&&E;if(R&&r&&t.manuallyAnimateOnMount&&(R=!1),w.protectedKeys={...g},!w.isActive&&k===null||!P&&!w.prevProp||Rn(P)||typeof P=="boolean")continue;let I=rp(w.prevProp,P)||v===u&&w.isActive&&!R&&E||C>x&&E,Y=!1;const Z=Array.isArray(P)?P:[P];let H=Z.reduce(i,{});k===!1&&(H={});const{prevResolvedValues:ce={}}=w,z={...ce,...H},S=A=>{I=!0,p.has(A)&&(Y=!0,p.delete(A)),w.needsAnimating[A]=!0};for(const A in z){const M=H[A],B=ce[A];if(g.hasOwnProperty(A))continue;let N=!1;yn(M)&&yn(B)?N=!ao(M,B):N=M!==B,N?M!==void 0?S(A):p.add(A):M!==void 0&&p.has(A)?S(A):w.protectedKeys[A]=!0}w.prevProp=P,w.prevResolvedValues=H,w.isActive&&(g={...g,...H}),r&&t.blockInitialAnimation&&(I=!1),I&&(!R||Y)&&f.push(...Z.map(A=>({animation:A,options:{type:v,...c}})))}if(p.size){const C={};p.forEach(v=>{const w=t.getBaseTarget(v);w!==void 0&&(C[v]=w)}),f.push({animation:C})}let j=!!f.length;return r&&(d.initial===!1||d.initial===d.animate)&&!t.manuallyAnimateOnMount&&(j=!1),r=!1,j?e(f):Promise.resolve()}function l(c,u,d){var h;if(n[c].isActive===u)return Promise.resolve();(h=t.variantChildren)===null||h===void 0||h.forEach(p=>{var g;return(g=p.animationState)===null||g===void 0?void 0:g.setActive(c,u)}),n[c].isActive=u;const f=o(d,c);for(const p in n)n[p].protectedKeys={};return f}return{animateChanges:o,setActive:l,setAnimateFunction:a,getState:()=>n}}function rp(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!ao(e,t):!1}function Je(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ip(){return{animate:Je(!0),whileInView:Je(),whileHover:Je(),whileTap:Je(),whileDrag:Je(),whileFocus:Je(),exit:Je()}}class sp extends Ke{constructor(e){super(e),e.animationState||(e.animationState=np(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();this.unmount(),Rn(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let ap=0;class op extends Ke{constructor(){super(...arguments),this.id=ap++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;const a=this.node.animationState.setActive("exit",!e,{custom:r??this.node.getProps().custom});n&&!e&&a.then(()=>n(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}const lp={animation:{Feature:sp},exit:{Feature:op}},os=(t,e)=>Math.abs(t-e);function cp(t,e){const n=os(t.x,e.x),r=os(t.y,e.y);return Math.sqrt(n**2+r**2)}class Wo{constructor(e,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:a=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=Xn(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,p=cp(h.offset,{x:0,y:0})>=3;if(!f&&!p)return;const{point:g}=h,{timestamp:x}=oe;this.history.push({...g,timestamp:x});const{onStart:j,onMove:C}=this.handlers;f||(j&&j(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),C&&C(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=Jn(f,this.transformPagePoint),G.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:p,onSessionEnd:g,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const j=Xn(h.type==="pointercancel"?this.lastMoveEventInfo:Jn(f,this.transformPagePoint),this.history);this.startEvent&&p&&p(h,j),g&&g(h,j)},!to(e))return;this.dragSnapToOrigin=a,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const o=Ln(e),l=Jn(o,this.transformPagePoint),{point:c}=l,{timestamp:u}=oe;this.history=[{...c,timestamp:u}];const{onSessionStart:d}=n;d&&d(e,Xn(l,this.history)),this.removeListeners=Ne(Re(this.contextWindow,"pointermove",this.handlePointerMove),Re(this.contextWindow,"pointerup",this.handlePointerUp),Re(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),Me(this.updatePoint)}}function Jn(t,e){return e?{point:e(t.point)}:t}function ls(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Xn({point:t},e){return{point:t,delta:ls(t,No(e)),offset:ls(t,up(e)),velocity:dp(e,.1)}}function up(t){return t[0]}function No(t){return t[t.length-1]}function dp(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,r=null;const i=No(t);for(;n>=0&&(r=t[n],!(i.timestamp-r.timestamp>nt(e)));)n--;if(!r)return{x:0,y:0};const a=Oe(i.timestamp-r.timestamp);if(a===0)return{x:0,y:0};const o={x:(i.x-r.x)/a,y:(i.y-r.y)/a};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function xe(t){return t.max-t.min}function Sr(t,e=0,n=.01){return Math.abs(t-e)<=n}function cs(t,e,n,r=.5){t.origin=r,t.originPoint=J(e.min,e.max,t.origin),t.scale=xe(n)/xe(e),(Sr(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=J(n.min,n.max,t.origin)-t.originPoint,(Sr(t.translate)||isNaN(t.translate))&&(t.translate=0)}function Mt(t,e,n,r){cs(t.x,e.x,n.x,r?r.originX:void 0),cs(t.y,e.y,n.y,r?r.originY:void 0)}function us(t,e,n){t.min=n.min+e.min,t.max=t.min+xe(e)}function hp(t,e,n){us(t.x,e.x,n.x),us(t.y,e.y,n.y)}function ds(t,e,n){t.min=e.min-n.min,t.max=t.min+xe(e)}function Lt(t,e,n){ds(t.x,e.x,n.x),ds(t.y,e.y,n.y)}function fp(t,{min:e,max:n},r){return e!==void 0&&t<e?t=r?J(e,t,r.min):Math.max(t,e):n!==void 0&&t>n&&(t=r?J(n,t,r.max):Math.min(t,n)),t}function hs(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function pp(t,{top:e,left:n,bottom:r,right:i}){return{x:hs(t.x,n,i),y:hs(t.y,e,r)}}function fs(t,e){let n=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,r]=[r,n]),{min:n,max:r}}function mp(t,e){return{x:fs(t.x,e.x),y:fs(t.y,e.y)}}function gp(t,e){let n=.5;const r=xe(t),i=xe(e);return i>r?n=Ut(e.min,e.max-r,t.min):r>i&&(n=Ut(t.min,t.max-i,e.min)),Ue(0,1,n)}function xp(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const wr=.35;function yp(t=wr){return t===!1?t=0:t===!0&&(t=wr),{x:ps(t,"left","right"),y:ps(t,"top","bottom")}}function ps(t,e,n){return{min:ms(t,e),max:ms(t,n)}}function ms(t,e){return typeof t=="number"?t:t[e]||0}const gs=()=>({translate:0,scale:1,origin:0,originPoint:0}),pt=()=>({x:gs(),y:gs()}),xs=()=>({min:0,max:0}),ne=()=>({x:xs(),y:xs()});function ve(t){return[t("x"),t("y")]}function $o({top:t,left:e,right:n,bottom:r}){return{x:{min:e,max:n},y:{min:t,max:r}}}function bp({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function vp(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Zn(t){return t===void 0||t===1}function Cr({scale:t,scaleX:e,scaleY:n}){return!Zn(t)||!Zn(e)||!Zn(n)}function Xe(t){return Cr(t)||Uo(t)||t.z||t.rotate||t.rotateX||t.rotateY}function Uo(t){return ys(t.x)||ys(t.y)}function ys(t){return t&&t!=="0%"}function wn(t,e,n){const r=t-n,i=e*r;return n+i}function bs(t,e,n,r,i){return i!==void 0&&(t=wn(t,i,r)),wn(t,n,r)+e}function Pr(t,e=0,n=1,r,i){t.min=bs(t.min,e,n,r,i),t.max=bs(t.max,e,n,r,i)}function zo(t,{x:e,y:n}){Pr(t.x,e.translate,e.scale,e.originPoint),Pr(t.y,n.translate,n.scale,n.originPoint)}function jp(t,e,n,r=!1){const i=n.length;if(!i)return;e.x=e.y=1;let a,o;for(let l=0;l<i;l++){a=n[l],o=a.projectionDelta;const c=a.instance;c&&c.style&&c.style.display==="contents"||(r&&a.options.layoutScroll&&a.scroll&&a!==a.root&&mt(t,{x:-a.scroll.offset.x,y:-a.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,zo(t,o)),r&&Xe(a.latestValues)&&mt(t,a.latestValues))}e.x=vs(e.x),e.y=vs(e.y)}function vs(t){return Number.isInteger(t)||t>1.0000000000001||t<.999999999999?t:1}function Ie(t,e){t.min=t.min+e,t.max=t.max+e}function js(t,e,[n,r,i]){const a=e[i]!==void 0?e[i]:.5,o=J(t.min,t.max,a);Pr(t,e[n],e[r],o,e.scale)}const Sp=["x","scaleX","originX"],wp=["y","scaleY","originY"];function mt(t,e){js(t.x,e,Sp),js(t.y,e,wp)}function Ho(t,e){return $o(vp(t.getBoundingClientRect(),e))}function Cp(t,e,n){const r=Ho(t,n),{scroll:i}=e;return i&&(Ie(r.x,i.offset.x),Ie(r.y,i.offset.y)),r}const _o=({current:t})=>t?t.ownerDocument.defaultView:null,Pp=new WeakMap;class Ap{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ne(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=d=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Ln(d,"page").point)},a=(d,h)=>{const{drag:f,dragPropagation:p,onDragStart:g}=this.getProps();if(f&&!p&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=ro(f),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ve(j=>{let C=this.getAxisMotionValue(j).get()||0;if(Te.test(C)){const{projection:v}=this.visualElement;if(v&&v.layout){const w=v.layout.layoutBox[j];w&&(C=xe(w)*(parseFloat(C)/100))}}this.originPoint[j]=C}),g&&G.update(()=>g(d,h),!1,!0);const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},o=(d,h)=>{const{dragPropagation:f,dragDirectionLock:p,onDirectionLock:g,onDrag:x}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:j}=h;if(p&&this.currentDirection===null){this.currentDirection=Tp(j),this.currentDirection!==null&&g&&g(this.currentDirection);return}this.updateAxis("x",h.point,j),this.updateAxis("y",h.point,j),this.visualElement.render(),x&&x(d,h)},l=(d,h)=>this.stop(d,h),c=()=>ve(d=>{var h;return this.getAnimationState(d)==="paused"&&((h=this.getAxisMotionValue(d).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Wo(e,{onSessionStart:i,onStart:a,onMove:o,onSessionEnd:l,resumeAnimation:c},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:_o(this.visualElement)})}stop(e,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:a}=this.getProps();a&&G.update(()=>a(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,r){const{drag:i}=this.getProps();if(!r||!sn(e,i,this.currentDirection))return;const a=this.getAxisMotionValue(e);let o=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(o=fp(o,this.constraints[e],this.elastic[e])),a.set(o)}resolveConstraints(){var e;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,a=this.constraints;n&&ht(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=pp(i.layoutBox,n):this.constraints=!1,this.elastic=yp(r),a!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&ve(o=>{this.getAxisMotionValue(o)&&(this.constraints[o]=xp(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!ht(e))return!1;const r=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const a=Cp(r,i.root,this.visualElement.getTransformPagePoint());let o=mp(i.layout.layoutBox,a);if(n){const l=n(bp(o));this.hasMutatedConstraints=!!l,l&&(o=$o(l))}return o}startAnimation(e){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:a,dragSnapToOrigin:o,onDragTransitionEnd:l}=this.getProps(),c=this.constraints||{},u=ve(d=>{if(!sn(d,n,this.currentDirection))return;let h=c&&c[d]||{};o&&(h={min:0,max:0});const f=i?200:1e6,p=i?40:1e7,g={type:"inertia",velocity:r?e[d]:0,bounceStiffness:f,bounceDamping:p,timeConstant:750,restDelta:1,restSpeed:10,...a,...h};return this.startAxisValueAnimation(d,g)});return Promise.all(u).then(l)}startAxisValueAnimation(e,n){const r=this.getAxisMotionValue(e);return r.start(si(e,r,0,n))}stopAnimation(){ve(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){ve(e=>{var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(e){const n="_drag"+e.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){ve(n=>{const{drag:r}=this.getProps();if(!sn(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,a=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:l}=i.layout.layoutBox[n];a.set(e[n]-J(o,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!ht(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};ve(o=>{const l=this.getAxisMotionValue(o);if(l){const c=l.get();i[o]=gp({min:c,max:c},this.constraints[o])}});const{transformTemplate:a}=this.visualElement.getProps();this.visualElement.current.style.transform=a?a({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),ve(o=>{if(!sn(o,e,null))return;const l=this.getAxisMotionValue(o),{min:c,max:u}=this.constraints[o];l.set(J(c,u,i[o]))})}addListeners(){if(!this.visualElement.current)return;Pp.set(this.visualElement,this);const e=this.visualElement.current,n=Re(e,"pointerdown",c=>{const{drag:u,dragListener:d=!0}=this.getProps();u&&d&&this.start(c)}),r=()=>{const{dragConstraints:c}=this.getProps();ht(c)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,a=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const o=ke(window,"resize",()=>this.scalePositionWithinConstraints()),l=i.addEventListener("didUpdate",({delta:c,hasLayoutChanged:u})=>{this.isDragging&&u&&(ve(d=>{const h=this.getAxisMotionValue(d);h&&(this.originPoint[d]+=c[d].translate,h.set(h.get()+c[d].translate))}),this.visualElement.render())});return()=>{o(),n(),a(),l&&l()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:a=!1,dragElastic:o=wr,dragMomentum:l=!0}=e;return{...e,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:a,dragElastic:o,dragMomentum:l}}}function sn(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function Tp(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class Ep extends Ke{constructor(e){super(e),this.removeGroupControls=ee,this.removeListeners=ee,this.controls=new Ap(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ee}unmount(){this.removeGroupControls(),this.removeListeners()}}const Ss=t=>(e,n)=>{t&&G.update(()=>t(e,n))};class Fp extends Ke{constructor(){super(...arguments),this.removePointerDownListener=ee}onPointerDown(e){this.session=new Wo(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:_o(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Ss(e),onStart:Ss(n),onMove:r,onEnd:(a,o)=>{delete this.session,i&&G.update(()=>i(a,o))}}}mount(){this.removePointerDownListener=Re(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function kp(){const t=m.useContext(Fn);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:n,register:r}=t,i=m.useId();return m.useEffect(()=>r(i),[]),!e&&n?[!1,()=>n&&n(i)]:[!0]}const ln={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ws(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Tt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(L.test(t))t=parseFloat(t);else return t;const n=ws(t,e.target.x),r=ws(t,e.target.y);return`${n}% ${r}%`}},Rp={correct:(t,{treeScale:e,projectionDelta:n})=>{const r=t,i=ze.parse(t);if(i.length>5)return r;const a=ze.createTransformer(t),o=typeof i[0]!="number"?1:0,l=n.x.scale*e.x,c=n.y.scale*e.y;i[0+o]/=l,i[1+o]/=c;const u=J(l,c,.5);return typeof i[2+o]=="number"&&(i[2+o]/=u),typeof i[3+o]=="number"&&(i[3+o]/=u),a(i)}};class Op extends we.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:a}=e;Vd(Mp),a&&(n.group&&n.group.add(a),r&&r.register&&i&&r.register(a),a.root.didUpdate(),a.addEventListener("animationComplete",()=>{this.safeToRemove()}),a.setOptions({...a.options,onExitComplete:()=>this.safeToRemove()})),ln.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:r,drag:i,isPresent:a}=this.props,o=r.projection;return o&&(o.isPresent=a,i||e.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),e.isPresent!==a&&(a?o.promote():o.relegate()||G.postRender(()=>{const l=o.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Ko(t){const[e,n]=kp(),r=m.useContext(_r);return we.createElement(Op,{...t,layoutGroup:r,switchLayoutGroup:m.useContext(Na),isPresent:e,safeToRemove:n})}const Mp={borderRadius:{...Tt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Tt,borderTopRightRadius:Tt,borderBottomLeftRadius:Tt,borderBottomRightRadius:Tt,boxShadow:Rp},Go=["TopLeft","TopRight","BottomLeft","BottomRight"],Lp=Go.length,Cs=t=>typeof t=="string"?parseFloat(t):t,Ps=t=>typeof t=="number"||L.test(t);function Ip(t,e,n,r,i,a){i?(t.opacity=J(0,n.opacity!==void 0?n.opacity:1,Dp(r)),t.opacityExit=J(e.opacity!==void 0?e.opacity:1,0,Bp(r))):a&&(t.opacity=J(e.opacity!==void 0?e.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<Lp;o++){const l=`border${Go[o]}Radius`;let c=As(e,l),u=As(n,l);if(c===void 0&&u===void 0)continue;c||(c=0),u||(u=0),c===0||u===0||Ps(c)===Ps(u)?(t[l]=Math.max(J(Cs(c),Cs(u),r),0),(Te.test(u)||Te.test(c))&&(t[l]+="%")):t[l]=u}(e.rotate||n.rotate)&&(t.rotate=J(e.rotate||0,n.rotate||0,r))}function As(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const Dp=qo(0,.5,go),Bp=qo(.5,.95,ee);function qo(t,e,n){return r=>r<t?0:r>e?1:n(Ut(t,e,r))}function Ts(t,e){t.min=e.min,t.max=e.max}function be(t,e){Ts(t.x,e.x),Ts(t.y,e.y)}function Es(t,e,n,r,i){return t-=e,t=wn(t,1/n,r),i!==void 0&&(t=wn(t,1/i,r)),t}function Vp(t,e=0,n=1,r=.5,i,a=t,o=t){if(Te.test(e)&&(e=parseFloat(e),e=J(o.min,o.max,e/100)-o.min),typeof e!="number")return;let l=J(a.min,a.max,r);t===a&&(l-=e),t.min=Es(t.min,e,n,l,i),t.max=Es(t.max,e,n,l,i)}function Fs(t,e,[n,r,i],a,o){Vp(t,e[n],e[r],e[i],e.scale,a,o)}const Wp=["x","scaleX","originX"],Np=["y","scaleY","originY"];function ks(t,e,n,r){Fs(t.x,e,Wp,n?n.x:void 0,r?r.x:void 0),Fs(t.y,e,Np,n?n.y:void 0,r?r.y:void 0)}function Rs(t){return t.translate===0&&t.scale===1}function Yo(t){return Rs(t.x)&&Rs(t.y)}function $p(t,e){return t.x.min===e.x.min&&t.x.max===e.x.max&&t.y.min===e.y.min&&t.y.max===e.y.max}function Jo(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function Os(t){return xe(t.x)/xe(t.y)}class Up{constructor(){this.members=[]}add(e){ai(this.members,e),e.scheduleRender()}remove(e){if(oi(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const a=this.members[i];if(a.isPresent!==!1){r=a;break}}return r?(this.promote(r),!0):!1}promote(e,n){const r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,n&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:r}=e;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Ms(t,e,n){let r="";const i=t.x.translate/e.x,a=t.y.translate/e.y;if((i||a)&&(r=`translate3d(${i}px, ${a}px, 0) `),(e.x!==1||e.y!==1)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),n){const{rotate:c,rotateX:u,rotateY:d}=n;c&&(r+=`rotate(${c}deg) `),u&&(r+=`rotateX(${u}deg) `),d&&(r+=`rotateY(${d}deg) `)}const o=t.x.scale*e.x,l=t.y.scale*e.y;return(o!==1||l!==1)&&(r+=`scale(${o}, ${l})`),r||"none"}const zp=(t,e)=>t.depth-e.depth;class Hp{constructor(){this.children=[],this.isDirty=!1}add(e){ai(this.children,e),this.isDirty=!0}remove(e){oi(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(zp),this.isDirty=!1,this.children.forEach(e)}}function _p(t,e){const n=performance.now(),r=({timestamp:i})=>{const a=i-n;a>=e&&(Me(r),t(a-e))};return G.read(r,!0),()=>Me(r)}function Kp(t){window.MotionDebug&&window.MotionDebug.record(t)}function Gp(t){return t instanceof SVGElement&&t.tagName!=="svg"}function qp(t,e,n){const r=me(t)?t:bt(t);return r.start(si("",r,e,n)),r.animation}const Ls=["","X","Y","Z"],Yp={visibility:"hidden"},Is=1e3;let Jp=0;const Ze={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Xo({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(o={},l=e==null?void 0:e()){this.id=Jp++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Ze.totalNodes=Ze.resolvedTargetDeltas=Ze.recalculatedProjection=0,this.nodes.forEach(Qp),this.nodes.forEach(im),this.nodes.forEach(sm),this.nodes.forEach(em),Kp(Ze)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let c=0;c<this.path.length;c++)this.path[c].shouldResetTransform=!0;this.root===this&&(this.nodes=new Hp)}addEventListener(o,l){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new li),this.eventHandlers.get(o).add(l)}notifyListeners(o,...l){const c=this.eventHandlers.get(o);c&&c.notify(...l)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Gp(o),this.instance=o;const{layoutId:c,layout:u,visualElement:d}=this.options;if(d&&!d.current&&d.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(u||c)&&(this.isLayoutDirty=!0),t){let h;const f=()=>this.root.updateBlockedByResize=!1;t(o,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=_p(f,250),ln.hasAnimatedSinceResize&&(ln.hasAnimatedSinceResize=!1,this.nodes.forEach(Bs))})}c&&this.root.registerSharedNode(c,this),this.options.animate!==!1&&d&&(c||u)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:p,layout:g})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||d.getDefaultTransition()||um,{onLayoutAnimationStart:j,onLayoutAnimationComplete:C}=d.getProps(),v=!this.targetLayout||!Jo(this.targetLayout,g)||p,w=!f&&p;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||w||f&&(v||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,w);const P={...ii(x,"layout"),onPlay:j,onComplete:C};(d.shouldReduceMotion||this.options.layoutRoot)&&(P.delay=0,P.type=!1),this.startAnimation(P)}else f||Bs(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=g})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Me(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(am),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let d=0;d<this.path.length;d++){const h=this.path[d];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:l,layout:c}=this.options;if(l===void 0&&!c)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Ds);return}this.isUpdating||this.nodes.forEach(nm),this.isUpdating=!1,this.nodes.forEach(rm),this.nodes.forEach(Xp),this.nodes.forEach(Zp),this.clearAllSnapshots();const l=performance.now();oe.delta=Ue(0,1e3/60,l-oe.timestamp),oe.timestamp=l,oe.isProcessing=!0,zn.update.process(oe),zn.preRender.process(oe),zn.render.process(oe),oe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(tm),this.sharedNodes.forEach(om)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,G.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){G.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let c=0;c<this.path.length;c++)this.path[c].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ne(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let l=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(l=!1),l&&(this.scroll={animationId:this.root.animationId,phase:o,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform,l=this.projectionDelta&&!Yo(this.projectionDelta),c=this.getTransformTemplate(),u=c?c(this.latestValues,""):void 0,d=u!==this.prevTransformTemplateValue;o&&(l||Xe(this.latestValues)||d)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const l=this.measurePageBox();let c=this.removeElementScroll(l);return o&&(c=this.removeTransform(c)),dm(c),{animationId:this.root.animationId,measuredBox:l,layoutBox:c,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:o}=this.options;if(!o)return ne();const l=o.measureViewportBox(),{scroll:c}=this.root;return c&&(Ie(l.x,c.offset.x),Ie(l.y,c.offset.y)),l}removeElementScroll(o){const l=ne();be(l,o);for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:d,options:h}=u;if(u!==this.root&&d&&h.layoutScroll){if(d.isRoot){be(l,o);const{scroll:f}=this.root;f&&(Ie(l.x,-f.offset.x),Ie(l.y,-f.offset.y))}Ie(l.x,d.offset.x),Ie(l.y,d.offset.y)}}return l}applyTransform(o,l=!1){const c=ne();be(c,o);for(let u=0;u<this.path.length;u++){const d=this.path[u];!l&&d.options.layoutScroll&&d.scroll&&d!==d.root&&mt(c,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),Xe(d.latestValues)&&mt(c,d.latestValues)}return Xe(this.latestValues)&&mt(c,this.latestValues),c}removeTransform(o){const l=ne();be(l,o);for(let c=0;c<this.path.length;c++){const u=this.path[c];if(!u.instance||!Xe(u.latestValues))continue;Cr(u.latestValues)&&u.updateSnapshot();const d=ne(),h=u.measurePageBox();be(d,h),ks(l,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,d)}return Xe(this.latestValues)&&ks(l,this.latestValues),l}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==oe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var l;const c=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=c.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=c.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=c.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==c;if(!(o||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=oe.timestamp,!this.targetDelta&&!this.relativeTarget){const p=this.getClosestProjectingParent();p&&p.layout&&this.animationProgress!==1?(this.relativeParent=p,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ne(),this.relativeTargetOrigin=ne(),Lt(this.relativeTargetOrigin,this.layout.layoutBox,p.layout.layoutBox),be(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ne(),this.targetWithTransforms=ne()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),hp(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):be(this.target,this.layout.layoutBox),zo(this.target,this.targetDelta)):be(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const p=this.getClosestProjectingParent();p&&!!p.resumingFrom==!!this.resumingFrom&&!p.options.layoutScroll&&p.target&&this.animationProgress!==1?(this.relativeParent=p,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ne(),this.relativeTargetOrigin=ne(),Lt(this.relativeTargetOrigin,this.target,p.target),be(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Ze.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Cr(this.parent.latestValues)||Uo(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const l=this.getLead(),c=!!this.resumingFrom||this!==l;let u=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(u=!1),c&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===oe.timestamp&&(u=!1),u)return;const{layout:d,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||h))return;be(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,p=this.treeScale.y;jp(this.layoutCorrected,this.treeScale,this.path,c),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox);const{target:g}=l;if(!g){this.projectionTransform&&(this.projectionDelta=pt(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=pt(),this.projectionDeltaWithTransform=pt());const x=this.projectionTransform;Mt(this.projectionDelta,this.layoutCorrected,g,this.latestValues),this.projectionTransform=Ms(this.projectionDelta,this.treeScale),(this.projectionTransform!==x||this.treeScale.x!==f||this.treeScale.y!==p)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",g)),Ze.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),o){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(o,l=!1){const c=this.snapshot,u=c?c.latestValues:{},d={...this.latestValues},h=pt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const f=ne(),p=c?c.source:void 0,g=this.layout?this.layout.source:void 0,x=p!==g,j=this.getStack(),C=!j||j.members.length<=1,v=!!(x&&!C&&this.options.crossfade===!0&&!this.path.some(cm));this.animationProgress=0;let w;this.mixTargetDelta=P=>{const E=P/1e3;Vs(h.x,o.x,E),Vs(h.y,o.y,E),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Lt(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),lm(this.relativeTarget,this.relativeTargetOrigin,f,E),w&&$p(this.relativeTarget,w)&&(this.isProjectionDirty=!1),w||(w=ne()),be(w,this.relativeTarget)),x&&(this.animationValues=d,Ip(d,u,this.latestValues,E,v,C)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=E},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Me(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=G.update(()=>{ln.hasAnimatedSinceResize=!0,this.currentAnimation=qp(0,Is,{...o,onUpdate:l=>{this.mixTargetDelta(l),o.onUpdate&&o.onUpdate(l)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Is),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:l,target:c,layout:u,latestValues:d}=o;if(!(!l||!c||!u)){if(this!==o&&this.layout&&u&&Zo(this.options.animationType,this.layout.layoutBox,u.layoutBox)){c=this.target||ne();const h=xe(this.layout.layoutBox.x);c.x.min=o.target.x.min,c.x.max=c.x.min+h;const f=xe(this.layout.layoutBox.y);c.y.min=o.target.y.min,c.y.max=c.y.min+f}be(l,c),mt(l,d),Mt(this.projectionDeltaWithTransform,this.layoutCorrected,l,d)}}registerSharedNode(o,l){this.sharedNodes.has(o)||this.sharedNodes.set(o,new Up),this.sharedNodes.get(o).add(l);const u=l.options.initialPromotionConfig;l.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(l):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:l}=this.options;return l?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:l}=this.options;return l?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:l,preserveFollowOpacity:c}={}){const u=this.getStack();u&&u.promote(this,c),o&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetRotation(){const{visualElement:o}=this.options;if(!o)return;let l=!1;const{latestValues:c}=o;if((c.rotate||c.rotateX||c.rotateY||c.rotateZ)&&(l=!0),!l)return;const u={};for(let d=0;d<Ls.length;d++){const h="rotate"+Ls[d];c[h]&&(u[h]=c[h],o.setStaticValue(h,0))}o.render();for(const d in u)o.setStaticValue(d,u[d]);o.scheduleRender()}getProjectionStyles(o){var l,c;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Yp;const u={visibility:""},d=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=on(o==null?void 0:o.pointerEvents)||"",u.transform=d?d(this.latestValues,""):"none",u;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=on(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!Xe(this.latestValues)&&(x.transform=d?d({},""):"none",this.hasProjected=!1),x}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),u.transform=Ms(this.projectionDeltaWithTransform,this.treeScale,f),d&&(u.transform=d(f,u.transform));const{x:p,y:g}=this.projectionDelta;u.transformOrigin=`${p.origin*100}% ${g.origin*100}% 0`,h.animationValues?u.opacity=h===this?(c=(l=f.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&c!==void 0?c:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:u.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const x in gn){if(f[x]===void 0)continue;const{correct:j,applyTo:C}=gn[x],v=u.transform==="none"?f[x]:j(f[x],h);if(C){const w=C.length;for(let P=0;P<w;P++)u[C[P]]=v}else u[x]=v}return this.options.layoutId&&(u.pointerEvents=h===this?on(o==null?void 0:o.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var l;return(l=o.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Ds),this.root.sharedNodes.clear()}}}function Xp(t){t.updateLayout()}function Zp(t){var e;const n=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=t.layout,{animationType:a}=t.options,o=n.source!==t.layout.source;a==="size"?ve(h=>{const f=o?n.measuredBox[h]:n.layoutBox[h],p=xe(f);f.min=r[h].min,f.max=f.min+p}):Zo(a,n.layoutBox,r)&&ve(h=>{const f=o?n.measuredBox[h]:n.layoutBox[h],p=xe(r[h]);f.max=f.min+p,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+p)});const l=pt();Mt(l,r,n.layoutBox);const c=pt();o?Mt(c,t.applyTransform(i,!0),n.measuredBox):Mt(c,r,n.layoutBox);const u=!Yo(l);let d=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:p}=h;if(f&&p){const g=ne();Lt(g,n.layoutBox,f.layoutBox);const x=ne();Lt(x,r,p.layoutBox),Jo(g,x)||(d=!0),h.options.layoutRoot&&(t.relativeTarget=x,t.relativeTargetOrigin=g,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:c,layoutDelta:l,hasLayoutChanged:u,hasRelativeTargetChanged:d})}else if(t.isLead()){const{onExitComplete:r}=t.options;r&&r()}t.options.transition=void 0}function Qp(t){Ze.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function em(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function tm(t){t.clearSnapshot()}function Ds(t){t.clearMeasurements()}function nm(t){t.isLayoutDirty=!1}function rm(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Bs(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function im(t){t.resolveTargetDelta()}function sm(t){t.calcProjection()}function am(t){t.resetRotation()}function om(t){t.removeLeadSnapshot()}function Vs(t,e,n){t.translate=J(e.translate,0,n),t.scale=J(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Ws(t,e,n,r){t.min=J(e.min,n.min,r),t.max=J(e.max,n.max,r)}function lm(t,e,n,r){Ws(t.x,e.x,n.x,r),Ws(t.y,e.y,n.y,r)}function cm(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const um={duration:.45,ease:[.4,0,.1,1]},Ns=t=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(t),$s=Ns("applewebkit/")&&!Ns("chrome/")?Math.round:ee;function Us(t){t.min=$s(t.min),t.max=$s(t.max)}function dm(t){Us(t.x),Us(t.y)}function Zo(t,e,n){return t==="position"||t==="preserve-aspect"&&!Sr(Os(e),Os(n),.2)}const hm=Xo({attachResizeListener:(t,e)=>ke(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Qn={current:void 0},Qo=Xo({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Qn.current){const t=new hm({});t.mount(window),t.setOptions({layoutScroll:!0}),Qn.current=t}return Qn.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),fm={pan:{Feature:Fp},drag:{Feature:Ep,ProjectionNode:Qo,MeasureLayout:Ko}},pm=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function mm(t){const e=pm.exec(t);if(!e)return[,];const[,n,r]=e;return[n,r]}function Ar(t,e,n=1){const[r,i]=mm(t);if(!r)return;const a=window.getComputedStyle(e).getPropertyValue(r);if(a){const o=a.trim();return Io(o)?parseFloat(o):o}else return mr(i)?Ar(i,e,n+1):i}function gm(t,{...e},n){const r=t.current;if(!(r instanceof Element))return{target:e,transitionEnd:n};n&&(n={...n}),t.values.forEach(i=>{const a=i.get();if(!mr(a))return;const o=Ar(a,r);o&&i.set(o)});for(const i in e){const a=e[i];if(!mr(a))continue;const o=Ar(a,r);o&&(e[i]=o,n||(n={}),n[i]===void 0&&(n[i]=a))}return{target:e,transitionEnd:n}}const xm=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),el=t=>xm.has(t),ym=t=>Object.keys(t).some(el),zs=t=>t===at||t===L,Hs=(t,e)=>parseFloat(t.split(", ")[e]),_s=(t,e)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return Hs(i[1],e);{const a=r.match(/^matrix\((.+)\)$/);return a?Hs(a[1],t):0}},bm=new Set(["x","y","z"]),vm=qt.filter(t=>!bm.has(t));function jm(t){const e=[];return vm.forEach(n=>{const r=t.getValue(n);r!==void 0&&(e.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),e.length&&t.render(),e}const vt={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:_s(4,13),y:_s(5,14)};vt.translateX=vt.x;vt.translateY=vt.y;const Sm=(t,e,n)=>{const r=e.measureViewportBox(),i=e.current,a=getComputedStyle(i),{display:o}=a,l={};o==="none"&&e.setStaticValue("display",t.display||"block"),n.forEach(u=>{l[u]=vt[u](r,a)}),e.render();const c=e.measureViewportBox();return n.forEach(u=>{const d=e.getValue(u);d&&d.jump(l[u]),t[u]=vt[u](c,a)}),t},wm=(t,e,n={},r={})=>{e={...e},r={...r};const i=Object.keys(e).filter(el);let a=[],o=!1;const l=[];if(i.forEach(c=>{const u=t.getValue(c);if(!t.hasValue(c))return;let d=n[c],h=At(d);const f=e[c];let p;if(yn(f)){const g=f.length,x=f[0]===null?1:0;d=f[x],h=At(d);for(let j=x;j<g&&f[j]!==null;j++)p?Qr(At(f[j])===p):p=At(f[j])}else p=At(f);if(h!==p)if(zs(h)&&zs(p)){const g=u.get();typeof g=="string"&&u.set(parseFloat(g)),typeof f=="string"?e[c]=parseFloat(f):Array.isArray(f)&&p===L&&(e[c]=f.map(parseFloat))}else h!=null&&h.transform&&(p!=null&&p.transform)&&(d===0||f===0)?d===0?u.set(p.transform(d)):e[c]=h.transform(f):(o||(a=jm(t),o=!0),l.push(c),r[c]=r[c]!==void 0?r[c]:e[c],u.jump(f))}),l.length){const c=l.indexOf("height")>=0?window.pageYOffset:null,u=Sm(e,t,l);return a.length&&a.forEach(([d,h])=>{t.getValue(d).set(h)}),t.render(),kn&&c!==null&&window.scrollTo({top:c}),{target:u,transitionEnd:r}}else return{target:e,transitionEnd:r}};function Cm(t,e,n,r){return ym(e)?wm(t,e,n,r):{target:e,transitionEnd:r}}const Pm=(t,e,n,r)=>{const i=gm(t,e,r);return e=i.target,r=i.transitionEnd,Cm(t,e,n,r)},Tr={current:null},tl={current:!1};function Am(){if(tl.current=!0,!!kn)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Tr.current=t.matches;t.addListener(e),e()}else Tr.current=!1}function Tm(t,e,n){const{willChange:r}=e;for(const i in e){const a=e[i],o=n[i];if(me(a))t.addValue(i,a),Sn(r)&&r.add(i);else if(me(o))t.addValue(i,bt(a,{owner:t})),Sn(r)&&r.remove(i);else if(o!==a)if(t.hasValue(i)){const l=t.getValue(i);!l.hasAnimated&&l.set(a)}else{const l=t.getStaticValue(i);t.addValue(i,bt(l!==void 0?l:a,{owner:t}))}}for(const i in n)e[i]===void 0&&t.removeValue(i);return e}const Ks=new WeakMap,nl=Object.keys($t),Em=nl.length,Gs=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],Fm=Hr.length;class km{constructor({parent:e,props:n,presenceContext:r,reducedMotionConfig:i,visualState:a},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>G.render(this.render,!1,!0);const{latestValues:l,renderState:c}=a;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.isControllingVariants=On(n),this.isVariantNode=Wa(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:u,...d}=this.scrapeMotionValuesFromProps(n,{});for(const h in d){const f=d[h];l[h]!==void 0&&me(f)&&(f.set(l[h],!1),Sn(u)&&u.add(h))}}scrapeMotionValuesFromProps(e,n){return{}}mount(e){this.current=e,Ks.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),tl.current||Am(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Tr.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Ks.delete(this.current),this.projection&&this.projection.unmount(),Me(this.notifyUpdate),Me(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,n){const r=st.has(e),i=n.on("change",o=>{this.latestValues[e]=o,this.props.onUpdate&&G.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),a=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{i(),a()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}loadFeatures({children:e,...n},r,i,a){let o,l;for(let c=0;c<Em;c++){const u=nl[c],{isEnabled:d,Feature:h,ProjectionNode:f,MeasureLayout:p}=$t[u];f&&(o=f),d(n)&&(!this.features[u]&&h&&(this.features[u]=new h(this)),p&&(l=p))}if((this.type==="html"||this.type==="svg")&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);const{layoutId:c,layout:u,drag:d,dragConstraints:h,layoutScroll:f,layoutRoot:p}=n;this.projection.setOptions({layoutId:c,layout:u,alwaysMeasureLayout:!!d||h&&ht(h),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:a,layoutScroll:f,layoutRoot:p})}return l}updateFeatures(){for(const e in this.features){const n=this.features[e];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ne()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}makeTargetAnimatable(e,n=!0){return this.makeTargetAnimatableFromInstance(e,this.props,n)}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Gs.length;r++){const i=Gs[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const a=e["on"+i];a&&(this.propEventSubscriptions[i]=this.on(i,a))}this.prevMotionValues=Tm(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<Fm;r++){const i=Hr[r],a=this.props[i];(Nt(a)||a===!1)&&(n[i]=a)}return n}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){n!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,n)),this.values.set(e,n),this.latestValues[e]=n.get()}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return r===void 0&&n!==void 0&&(r=bt(n,{owner:this}),this.addValue(e,r)),r}readValue(e){var n;return this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(n=this.getBaseTargetFromProps(this.props,e))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,e,this.options)}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=Zr(this.props,r))===null||n===void 0?void 0:n[e]:void 0;if(r&&i!==void 0)return i;const a=this.getBaseTargetFromProps(this.props,e);return a!==void 0&&!me(a)?a:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new li),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class rl extends km{sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:r}){delete n[e],delete r[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:n,...r},{transformValues:i},a){let o=Gf(r,e||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),o&&(o=i(o))),a){_f(this,r,o);const l=Pm(this,r,o,n);n=l.transitionEnd,r=l.target}return{transition:e,transitionEnd:n,...r}}}function Rm(t){return window.getComputedStyle(t)}class Om extends rl{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,n){if(st.has(n)){const r=ri(n);return r&&r.default||0}else{const r=Rm(e),i=(za(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Ho(e,n)}build(e,n,r,i){Gr(e,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(e,n){return Xr(e,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;me(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(e,n,r,i){Ya(e,n,r,i)}}class Mm extends rl{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(st.has(n)){const r=ri(n);return r&&r.default||0}return n=Ja.has(n)?n:Ur(n),e.getAttribute(n)}measureInstanceViewportBox(){return ne()}scrapeMotionValuesFromProps(e,n){return Za(e,n)}build(e,n,r,i){Yr(e,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(e,n,r,i){Xa(e,n,r,i)}mount(e){this.isSVGTag=Jr(e.tagName),super.mount(e)}}const Lm=(t,e)=>Kr(t)?new Mm(e,{enableHardwareAcceleration:!1}):new Om(e,{enableHardwareAcceleration:!0}),Im={layout:{ProjectionNode:Qo,MeasureLayout:Ko}},Dm={...lp,...Th,...fm,...Im},_=Dd((t,e)=>ph(t,e,Dm,Lm));function il(){const t=m.useRef(!1);return $r(()=>(t.current=!0,()=>{t.current=!1}),[]),t}function Bm(){const t=il(),[e,n]=m.useState(0),r=m.useCallback(()=>{t.current&&n(e+1)},[e]);return[m.useCallback(()=>G.postRender(r),[r]),e]}class Vm extends m.Component{getSnapshotBeforeUpdate(e){const n=this.props.childRef.current;if(n&&e.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function Wm({children:t,isPresent:e}){const n=m.useId(),r=m.useRef(null),i=m.useRef({width:0,height:0,top:0,left:0});return m.useInsertionEffect(()=>{const{width:a,height:o,top:l,left:c}=i.current;if(e||!r.current||!a||!o)return;r.current.dataset.motionPopId=n;const u=document.createElement("style");return document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${o}px !important;
            top: ${l}px !important;
            left: ${c}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[e]),m.createElement(Vm,{isPresent:e,childRef:r,sizeRef:i},m.cloneElement(t,{ref:r}))}const er=({children:t,initial:e,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:a,mode:o})=>{const l=Qa(Nm),c=m.useId(),u=m.useMemo(()=>({id:c,initial:e,isPresent:n,custom:i,onExitComplete:d=>{l.set(d,!0);for(const h of l.values())if(!h)return;r&&r()},register:d=>(l.set(d,!1),()=>l.delete(d))}),a?void 0:[n]);return m.useMemo(()=>{l.forEach((d,h)=>l.set(h,!1))},[n]),m.useEffect(()=>{!n&&!l.size&&r&&r()},[n]),o==="popLayout"&&(t=m.createElement(Wm,{isPresent:n},t)),m.createElement(Fn.Provider,{value:u},t)};function Nm(){return new Map}function $m(t){return m.useEffect(()=>()=>t(),[])}const Qe=t=>t.key||"";function Um(t,e){t.forEach(n=>{const r=Qe(n);e.set(r,n)})}function zm(t){const e=[];return m.Children.forEach(t,n=>{m.isValidElement(n)&&e.push(n)}),e}const Hm=({children:t,custom:e,initial:n=!0,onExitComplete:r,exitBeforeEnter:i,presenceAffectsLayout:a=!0,mode:o="sync"})=>{const l=m.useContext(_r).forceRender||Bm()[0],c=il(),u=zm(t);let d=u;const h=m.useRef(new Map).current,f=m.useRef(d),p=m.useRef(new Map).current,g=m.useRef(!0);if($r(()=>{g.current=!1,Um(u,p),f.current=d}),$m(()=>{g.current=!0,p.clear(),h.clear()}),g.current)return m.createElement(m.Fragment,null,d.map(v=>m.createElement(er,{key:Qe(v),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:a,mode:o},v)));d=[...d];const x=f.current.map(Qe),j=u.map(Qe),C=x.length;for(let v=0;v<C;v++){const w=x[v];j.indexOf(w)===-1&&!h.has(w)&&h.set(w,void 0)}return o==="wait"&&h.size&&(d=[]),h.forEach((v,w)=>{if(j.indexOf(w)!==-1)return;const P=p.get(w);if(!P)return;const E=x.indexOf(w);let k=v;if(!k){const R=()=>{h.delete(w);const O=Array.from(p.keys()).filter(I=>!j.includes(I));if(O.forEach(I=>p.delete(I)),f.current=u.filter(I=>{const Y=Qe(I);return Y===w||O.includes(Y)}),!h.size){if(c.current===!1)return;l(),r&&r()}};k=m.createElement(er,{key:Qe(P),isPresent:!1,onExitComplete:R,custom:e,presenceAffectsLayout:a,mode:o},P),h.set(w,k)}d.splice(E,0,k)}),d=d.map(v=>{const w=v.key;return h.has(w)?v:m.createElement(er,{key:Qe(v),isPresent:!0,presenceAffectsLayout:a,mode:o},v)}),m.createElement(m.Fragment,null,h.size?d:d.map(v=>m.cloneElement(v)))},_m=({value:t,duration:e=2e3,suffix:n="",prefix:r=""})=>{const[i,a]=m.useState(0);return m.useEffect(()=>{let o,l;const c=u=>{o||(o=u);const d=Math.min((u-o)/e,1),h=1-Math.pow(1-d,4),f=Math.floor(h*t);a(f),d<1&&(l=requestAnimationFrame(c))};return l=requestAnimationFrame(c),()=>{l&&cancelAnimationFrame(l)}},[t,e]),s.jsxs("span",{children:[r,i.toLocaleString(),n]})},Km=({value:t,color:e="primary",delay:n=0})=>{const[r,i]=m.useState(0);return m.useEffect(()=>{const a=setTimeout(()=>{i(t)},n);return()=>clearTimeout(a)},[t,n]),s.jsx(jt,{variant:"determinate",value:r,color:e,sx:{height:8,borderRadius:4,backgroundColor:F("#000",.1),"& .MuiLinearProgress-bar":{borderRadius:4,transition:"transform 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)"}}})},Gm=({trend:t,value:e,label:n})=>{const r=q(),i=t==="up";return s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:.5,px:1,py:.5,borderRadius:1,backgroundColor:F(i?r.palette.success.main:r.palette.error.main,.1)},children:[i?s.jsx(ml,{sx:{fontSize:16,color:r.palette.success.main}}):s.jsx(gl,{sx:{fontSize:16,color:r.palette.error.main}}),s.jsxs(b,{variant:"caption",sx:{fontWeight:600,color:i?r.palette.success.main:r.palette.error.main},children:[e,"%"]}),s.jsx(b,{variant:"caption",color:"text.secondary",children:n})]})},sl=({title:t,value:e,subtitle:n,icon:r,trend:i,trendValue:a,trendLabel:o="vs last month",progress:l,progressLabel:c,color:u="primary",variant:d="default",onClick:h,loading:f=!1,actionIcon:p,onActionClick:g,tooltip:x,gradient:j=!1,glassmorphism:C=!0})=>{var Y;const v=q(),[w,P]=m.useState(!1),E={initial:{scale:1,y:0,boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.1)"},hover:{scale:1.02,y:-8,boxShadow:"0px 20px 40px rgba(46, 91, 168, 0.15)",transition:{type:"spring",stiffness:300,damping:20}}},k={initial:{rotate:0,scale:1},hover:{rotate:5,scale:1.1,transition:{type:"spring",stiffness:400,damping:10}}},R=()=>j?v.palette.gradients[u]||v.palette.gradients.primary:C?F(v.palette.background.paper,.8):v.palette.background.paper,O=()=>{var Z;return j?"#FFFFFF":((Z=v.palette[u])==null?void 0:Z.main)||v.palette.primary.main},I=()=>j?"#FFFFFF":v.palette.text.primary;return s.jsx(zt,{title:x,arrow:!0,placement:"top",children:s.jsx(_.div,{variants:E,initial:"initial",whileHover:"hover",onHoverStart:()=>P(!0),onHoverEnd:()=>P(!1),children:s.jsx(K,{onClick:h,sx:{height:"100%",cursor:h?"pointer":"default",borderRadius:3,background:R(),backdropFilter:C?"blur(20px)":"none",border:C?`1px solid ${F(v.palette.primary.main,.1)}`:"none",overflow:"hidden",position:"relative","&::before":j?{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,background:R(),zIndex:-1}:{}},children:s.jsxs(ie,{sx:{p:3,height:"100%"},children:[s.jsxs(y,{sx:{display:"flex",alignItems:"flex-start",justifyContent:"space-between",mb:2},children:[s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:2},children:[s.jsx(_.div,{variants:k,children:s.jsx(se,{sx:{bgcolor:j?F("#FFFFFF",.2):F(((Y=v.palette[u])==null?void 0:Y.main)||v.palette.primary.main,.1),color:O(),width:56,height:56},children:f?s.jsx(It,{size:24,color:"inherit"}):s.jsx(r,{sx:{fontSize:28}})})}),s.jsxs(y,{children:[s.jsx(b,{variant:"body2",sx:{color:j?F("#FFFFFF",.8):v.palette.text.secondary,fontWeight:500,mb:.5},children:t}),n&&s.jsx(b,{variant:"caption",sx:{color:j?F("#FFFFFF",.6):v.palette.text.disabled},children:n})]})]}),(p||g)&&s.jsx(le,{size:"small",onClick:g,sx:{color:j?F("#FFFFFF",.8):v.palette.text.secondary},children:p||s.jsx(Js,{})})]}),s.jsxs(y,{sx:{mb:2},children:[s.jsx(b,{variant:"h3",sx:{fontWeight:800,color:I(),lineHeight:1,mb:1},children:f?s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:1},children:[s.jsx(It,{size:20,color:"inherit"}),s.jsx("span",{children:"--"})]}):typeof e=="number"?s.jsx(_m,{value:e}):e}),i&&a&&s.jsx(Gm,{trend:i,value:a,label:o})]}),l!==void 0&&s.jsxs(y,{sx:{mb:1},children:[s.jsxs(y,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[s.jsx(b,{variant:"caption",sx:{color:j?F("#FFFFFF",.8):v.palette.text.secondary,fontWeight:500},children:c||"Progress"}),s.jsxs(b,{variant:"caption",sx:{color:I(),fontWeight:600},children:[l,"%"]})]}),s.jsx(Km,{value:l,color:u,delay:500})]}),s.jsx(_.div,{initial:{opacity:0,height:0},animate:{opacity:w?1:0,height:w?"auto":0},transition:{duration:.3},children:s.jsx(y,{sx:{mt:2,pt:2,borderTop:`1px solid ${F(I(),.1)}`},children:s.jsx(b,{variant:"caption",sx:{color:j?F("#FFFFFF",.6):v.palette.text.disabled},children:"Click for detailed view"})})})]})})})})},qs={cbse:["Mathematics","Science","English","Hindi","Social Studies","Computer Science"],icse:["Mathematics","Physics","Chemistry","Biology","English","Hindi","History","Geography"],state:["Mathematics","Science","English","Telugu/Tamil/Regional","Social Studies","Environmental Science"],ib:["Mathematics","Sciences","English","Hindi","Individuals & Societies","Arts"]},qm=(t,e)=>{const n=qs[t]||qs.cbse,r=e.academicLevel;return n.map(i=>({subject:i,currentScore:Math.max(35,Math.min(100,r+(Math.random()-.5)*20)),previousScore:Math.max(30,Math.min(95,r+(Math.random()-.5)*25)),trend:Math.random()>.6?"improving":Math.random()>.3?"stable":"declining",assignments:Math.floor(Math.random()*10)+15,assignmentsCompleted:Math.floor(Math.random()*5)+12}))},Ym=()=>["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map(e=>({month:e,present:Math.floor(Math.random()*8)+18,total:Math.floor(Math.random()*3)+22,percentage:Math.floor(Math.random()*20)+75})),Jm=()=>{const t=["Late to class","Incomplete homework","Disruptive behavior","Excellent participation","Helped classmates","Leadership in group work","Creative thinking","Respectful behavior"],e=Math.floor(Math.random()*6)+2;return Array.from({length:e},()=>({date:new Date(2024,Math.floor(Math.random()*12),Math.floor(Math.random()*28)+1),type:t[Math.floor(Math.random()*t.length)],severity:Math.random()>.7?"high":Math.random()>.4?"medium":"low",description:"Detailed incident description would be here"}))},Xm=()=>{const t=["Cricket","Football","Basketball","Badminton","Chess","Debate Club","Science Club","Art Club","Music","Dance","Drama","Robotics","Environmental Club","Literary Society","Mathematics Olympiad"],e=Math.floor(Math.random()*4)+1;return Array.from({length:e},()=>({activity:t[Math.floor(Math.random()*t.length)],level:Math.random()>.6?"advanced":Math.random()>.3?"intermediate":"beginner",achievements:Math.random()>.7?["District Level Winner"]:Math.random()>.4?["School Level Participant"]:[]}))},Zm=t=>{const e=["Strong analytical thinking","Excellent communication skills","Creative problem solving","Leadership qualities","Team collaboration","Mathematical aptitude","Scientific reasoning","Artistic abilities","Sports excellence","Language proficiency","Technical skills"],n=["Time management","Public speaking anxiety","Difficulty with abstract concepts","Procrastination tendency","Perfectionism","Attention to detail","Organization skills","Stress management","Peer interaction","Technology adaptation"],r=["Advanced placement courses","Science fair participation","Leadership roles","Internship programs","Skill development workshops","Mentorship programs","Competition participation","Community service","Online learning platforms"],i=["Academic pressure","Peer competition","Technology distractions","Time constraints","Resource limitations","Career uncertainty","Social media influence","Health concerns","Family expectations"];return{strengths:e.sort(()=>.5-Math.random()).slice(0,3),weaknesses:n.sort(()=>.5-Math.random()).slice(0,2),opportunities:r.sort(()=>.5-Math.random()).slice(0,3),threats:i.sort(()=>.5-Math.random()).slice(0,2),lastUpdated:new Date,confidence:Math.floor(Math.random()*20)+75}},Qm=[{id:"STU001",name:"Sanju Kumar",grade:"10th",section:"A",board:"cbse",rollNumber:"CBSE2024001",academicLevel:85,region:"Telangana",parentContact:"+91 **********",email:"<EMAIL>",dateOfBirth:"2009-03-15",address:"Hyderabad, Telangana"},{id:"STU002",name:"Niraimathi Selvam",grade:"9th",section:"B",board:"state",rollNumber:"TN2024002",academicLevel:78,region:"Tamil Nadu",parentContact:"+91 **********",email:"<EMAIL>",dateOfBirth:"2010-07-22",address:"Chennai, Tamil Nadu"},{id:"STU003",name:"Mahesh Reddy",grade:"11th",section:"A",board:"cbse",rollNumber:"CBSE2024003",academicLevel:92,region:"Andhra Pradesh",parentContact:"+91 **********",email:"<EMAIL>",dateOfBirth:"2008-11-08",address:"Vijayawada, Andhra Pradesh"},{id:"STU004",name:"Ravi Teja Sharma",grade:"10th",section:"C",board:"icse",rollNumber:"ICSE2024004",academicLevel:88,region:"Karnataka",parentContact:"+91 9876543213",email:"<EMAIL>",dateOfBirth:"2009-01-30",address:"Bangalore, Karnataka"},{id:"STU005",name:"Ankitha Patel",grade:"12th",section:"A",board:"cbse",rollNumber:"CBSE2024005",academicLevel:95,region:"Gujarat",parentContact:"+91 9876543214",email:"<EMAIL>",dateOfBirth:"2007-05-12",address:"Ahmedabad, Gujarat"},{id:"STU006",name:"Sirisha Nair",grade:"9th",section:"B",board:"state",rollNumber:"KL2024006",academicLevel:82,region:"Kerala",parentContact:"+91 9876543215",email:"<EMAIL>",dateOfBirth:"2010-09-18",address:"Kochi, Kerala"},{id:"STU007",name:"Priya Agarwal",grade:"11th",section:"B",board:"cbse",rollNumber:"CBSE2024007",academicLevel:89,region:"Rajasthan",parentContact:"+91 9876543216",email:"<EMAIL>",dateOfBirth:"2008-12-03",address:"Jaipur, Rajasthan"},{id:"STU008",name:"Arjun Singh",grade:"10th",section:"A",board:"cbse",rollNumber:"CBSE2024008",academicLevel:76,region:"Punjab",parentContact:"+91 9876543217",email:"<EMAIL>",dateOfBirth:"2009-04-25",address:"Chandigarh, Punjab"},{id:"STU009",name:"Kavya Menon",grade:"12th",section:"C",board:"icse",rollNumber:"ICSE2024009",academicLevel:91,region:"Kerala",parentContact:"+91 9876543218",email:"<EMAIL>",dateOfBirth:"2007-08-14",address:"Thiruvananthapuram, Kerala"},{id:"STU010",name:"Rohit Gupta",grade:"9th",section:"A",board:"state",rollNumber:"UP2024010",academicLevel:84,region:"Uttar Pradesh",parentContact:"+91 9876543219",email:"<EMAIL>",dateOfBirth:"2010-02-28",address:"Lucknow, Uttar Pradesh"}],al=()=>Qm.map(t=>({...t,performance:qm(t.board,t),attendance:Ym(),behavioral:Jm(),extracurricular:Xm(),swotAnalysis:Zm(),overallGrade:t.academicLevel>=90?"A+":t.academicLevel>=80?"A":t.academicLevel>=70?"B":t.academicLevel>=60?"C":"D",rank:Math.floor(Math.random()*50)+1,lastUpdated:new Date})),ol=t=>{const e=t.length,n=t.reduce((i,a)=>i+a.academicLevel,0)/e,r=t.reduce((i,a)=>{const o=a.attendance.reduce((c,u)=>c+u.present,0),l=a.attendance.reduce((c,u)=>c+u.total,0);return i+o/l*100},0)/e;return{totalStudents:e,averagePerformance:Math.round(n),averageAttendance:Math.round(r),topPerformers:t.filter(i=>i.academicLevel>=90).length,needsAttention:t.filter(i=>i.academicLevel<70).length,boardDistribution:{cbse:t.filter(i=>i.board==="cbse").length,icse:t.filter(i=>i.board==="icse").length,state:t.filter(i=>i.board==="state").length,ib:t.filter(i=>i.board==="ib").length},gradeDistribution:{"9th":t.filter(i=>i.grade==="9th").length,"10th":t.filter(i=>i.grade==="10th").length,"11th":t.filter(i=>i.grade==="11th").length,"12th":t.filter(i=>i.grade==="12th").length},performanceTrends:eg(),subjectAnalysis:tg(t),attendancePatterns:ng(t),swotDistribution:rg(t)}},eg=t=>["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map(n=>({month:n,average:Math.floor(Math.random()*15)+75,cbse:Math.floor(Math.random()*15)+78,icse:Math.floor(Math.random()*15)+80,state:Math.floor(Math.random()*15)+73})),tg=t=>["Mathematics","Science","English","Hindi","Social Studies","Computer Science"].map(n=>({subject:n,averageScore:Math.floor(Math.random()*20)+70,topScore:Math.floor(Math.random()*10)+90,lowestScore:Math.floor(Math.random()*20)+45,studentsAbove80:Math.floor(Math.random()*t.length*.6),studentsBelow60:Math.floor(Math.random()*t.length*.2)})),ng=t=>["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map(n=>({month:n,averageAttendance:Math.floor(Math.random()*15)+80,totalStudents:t.length,presentStudents:Math.floor(t.length*(.8+Math.random()*.15))})),rg=t=>({strengthsDistribution:{"Analytical Thinking":Math.floor(t.length*.3),"Communication Skills":Math.floor(t.length*.25),Leadership:Math.floor(t.length*.2),Creativity:Math.floor(t.length*.35),"Technical Skills":Math.floor(t.length*.4)},weaknessesDistribution:{"Time Management":Math.floor(t.length*.4),"Public Speaking":Math.floor(t.length*.3),Organization:Math.floor(t.length*.25),"Stress Management":Math.floor(t.length*.35)},opportunitiesDistribution:{"Advanced Courses":Math.floor(t.length*.6),"Leadership Roles":Math.floor(t.length*.3),Competitions:Math.floor(t.length*.5),"Skill Development":Math.floor(t.length*.7)},threatsDistribution:{"Academic Pressure":Math.floor(t.length*.5),"Peer Competition":Math.floor(t.length*.4),"Technology Distractions":Math.floor(t.length*.6),"Time Constraints":Math.floor(t.length*.45)}}),ll=({children:t,loading:e=!1,disabled:n=!1,variant:r="contained",color:i="primary",size:a="medium",startIcon:o,endIcon:l,onClick:c,tooltip:u,fullWidth:d=!1,sx:h={},loadingText:f="Loading...",successFeedback:p=!1,errorFeedback:g=!1,...x})=>{const j=q(),[C,v]=m.useState(!1),[w,P]=m.useState(!1),[E,k]=m.useState(!1),R=async ce=>{if(!(e||n)){v(!0);try{if(c){const z=await c(ce);p&&z!==!1&&(P(!0),setTimeout(()=>P(!1),2e3))}}catch{g&&(k(!0),setTimeout(()=>k(!1),2e3))}finally{setTimeout(()=>v(!1),150)}}},O=()=>w?"success":E?"error":i,I=()=>e?s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:1},children:[s.jsx(It,{size:16,color:"inherit",sx:{color:r==="contained"?"white":j.palette[i].main}}),f]}):w?s.jsx(y,{sx:{display:"flex",alignItems:"center",gap:1},children:"✓ Success"}):E?s.jsx(y,{sx:{display:"flex",alignItems:"center",gap:1},children:"✗ Error"}):s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:1},children:[o,t,l]}),Y={initial:{scale:1},hover:{scale:1.02,transition:{type:"spring",stiffness:400,damping:25}},tap:{scale:.98,transition:{type:"spring",stiffness:400,damping:25}}},Z={borderRadius:3,textTransform:"none",fontWeight:600,minHeight:a==="small"?36:a==="large"?52:44,position:"relative",overflow:"hidden",transition:"all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)","&:hover":{transform:"translateY(-2px)",boxShadow:r==="contained"?`0px 8px 24px ${F(j.palette[O()].main,.4)}`:`0px 4px 12px ${F(j.palette[O()].main,.2)}`},"&:active":{transform:"translateY(0px)"},"&:focus-visible":{outline:`2px solid ${j.palette[O()].main}`,outlineOffset:"2px"},"&:disabled":{opacity:.6,transform:"none",boxShadow:"none"},...e&&{pointerEvents:"none"},...h},H=s.jsx(_.div,{variants:Y,initial:"initial",whileHover:!e&&!n?"hover":"initial",whileTap:!e&&!n?"tap":"initial",style:{display:d?"block":"inline-block",width:d?"100%":"auto"},children:s.jsx(W,{variant:r,color:O(),size:a,disabled:n||e,onClick:R,fullWidth:d,sx:Z,...x,children:I()})});return u?s.jsx(zt,{title:u,arrow:!0,placement:"top",children:s.jsx("span",{style:{display:d?"block":"inline-block"},children:H})}):H},ig=({children:t,onClick:e,elevation:n=2,variant:r="default",loading:i=!1,disabled:a=!1,hoverable:o=!0,clickable:l=!1,sx:c={},contentSx:u={},actions:d,header:h,footer:f,color:p="primary",gradient:g=!1,glassmorphism:x=!1,borderRadius:j=3,...C})=>{const v=q(),[w,P]=m.useState(!1),E=Z=>{a||i||!e||e(Z)},k=()=>x?v.palette.mode==="dark"?`linear-gradient(135deg, ${F("#1E293B",.8)} 0%, ${F("#334155",.6)} 100%)`:`linear-gradient(135deg, ${F("#FFFFFF",.9)} 0%, ${F("#F8FAFC",.8)} 100%)`:g?`linear-gradient(135deg, ${v.palette[p].main} 0%, ${v.palette[p].dark} 100%)`:v.palette.background.paper,R={initial:{scale:1,y:0,boxShadow:v.shadows[n]},hover:o?{scale:1.02,y:-8,boxShadow:v.shadows[Math.min(n+4,24)],transition:{type:"spring",stiffness:300,damping:20}}:{},tap:l?{scale:.98,transition:{type:"spring",stiffness:400,damping:25}}:{}},O={borderRadius:j,background:k(),backdropFilter:x?"blur(20px)":"none",border:x?`1px solid ${F(v.palette.divider,.1)}`:"none",cursor:l?"pointer":"default",transition:"all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)",position:"relative",overflow:"hidden","&:hover":o?{"& .card-content":{transform:"translateY(-2px)"},"& .card-actions":{opacity:1,transform:"translateY(0)"}}:{},...l&&{"&:focus-visible":{outline:`2px solid ${v.palette[p].main}`,outlineOffset:"2px"}},...a&&{opacity:.6,pointerEvents:"none"},...i&&{pointerEvents:"none"},...c},I={transition:"transform 0.3s ease",...u},Y=()=>s.jsxs(ie,{children:[s.jsx(Pe,{variant:"text",width:"60%",height:32,sx:{mb:2}}),s.jsx(Pe,{variant:"text",width:"100%",height:20,sx:{mb:1}}),s.jsx(Pe,{variant:"text",width:"80%",height:20,sx:{mb:2}}),s.jsx(Pe,{variant:"rectangular",width:"100%",height:120,sx:{borderRadius:2}})]});return s.jsx(_.div,{variants:R,initial:"initial",whileHover:!a&&!i?"hover":"initial",whileTap:!a&&!i&&l?"tap":"initial",onHoverStart:()=>P(!0),onHoverEnd:()=>P(!1),children:s.jsxs(K,{elevation:0,onClick:E,sx:O,tabIndex:l?0:-1,role:l?"button":void 0,...C,children:[x&&s.jsx(y,{sx:{position:"absolute",top:0,left:0,right:0,bottom:0,background:`linear-gradient(135deg, ${F("#FFFFFF",.1)} 0%, ${F("#FFFFFF",.05)} 100%)`,pointerEvents:"none",zIndex:0}}),h&&s.jsx(y,{sx:{position:"relative",zIndex:1},children:h}),s.jsx(ie,{className:"card-content",sx:I,style:{position:"relative",zIndex:1},children:i?s.jsx(Y,{}):t}),d&&s.jsx(xl,{className:"card-actions",sx:{position:"relative",zIndex:1,opacity:o?.8:1,transform:o?"translateY(4px)":"none",transition:"all 0.3s ease"},children:d}),f&&s.jsx(y,{sx:{position:"relative",zIndex:1},children:f}),l&&w&&s.jsx(y,{sx:{position:"absolute",top:"50%",left:"50%",width:0,height:0,borderRadius:"50%",background:F(v.palette[p].main,.1),transform:"translate(-50%, -50%)",animation:"ripple 0.6s linear","@keyframes ripple":{to:{width:"200%",height:"200%",opacity:0}}}})]})})},sg=({darkMode:t,onToggle:e})=>{const n=q();return s.jsx(zt,{title:t?"Switch to Light Mode":"Switch to Dark Mode",children:s.jsx(le,{onClick:e,sx:{background:F(n.palette.background.paper,.8),backdropFilter:"blur(10px)",border:`1px solid ${F(n.palette.divider,.2)}`,"&:hover":{background:F(n.palette.background.paper,.9),transform:"scale(1.05)"},transition:"all 0.3s ease"},children:s.jsx(_.div,{initial:!1,animate:{rotate:t?180:0},transition:{duration:.5,ease:"easeInOut"},children:t?s.jsx(wl,{}):s.jsx(Cl,{})})})})},ag=({darkMode:t,onThemeToggle:e,user:n})=>{var i;const r=q();return s.jsx(kr,{position:"sticky",elevation:0,sx:{background:F(r.palette.background.paper,.8),backdropFilter:"blur(20px)",borderBottom:`1px solid ${F(r.palette.divider,.1)}`,color:r.palette.text.primary},children:s.jsxs(Ht,{sx:{justifyContent:"space-between",py:1},children:[s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:2},children:[s.jsx(_.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(se,{sx:{bgcolor:r.palette.primary.main,width:48,height:48},children:s.jsx(je,{})})}),s.jsxs(y,{children:[s.jsx(b,{variant:"h5",sx:{fontWeight:800,background:`linear-gradient(135deg, ${r.palette.primary.main} 0%, ${r.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"VidyaMitra"}),s.jsx(b,{variant:"caption",color:"text.secondary",children:"Modern Educational Dashboard"})]})]}),s.jsxs(pe,{direction:"row",spacing:1,alignItems:"center",children:[s.jsx(le,{children:s.jsx(Rr,{})}),s.jsx(le,{children:s.jsx(Xs,{})}),s.jsx(bl,{badgeContent:3,color:"error",children:s.jsx(le,{children:s.jsx(vl,{})})}),s.jsx(sg,{darkMode:t,onToggle:e}),s.jsx(se,{sx:{width:40,height:40,cursor:"pointer",border:`2px solid ${r.palette.primary.main}`},children:((i=n==null?void 0:n.name)==null?void 0:i.charAt(0))||"U"})]})]})})},og=({loading:t,analyticsData:e})=>{const n=ge(),r=e?[{title:"Total Students",value:e.totalStudents,subtitle:"Active learners",icon:_t,trend:"up",trendValue:12,progress:85,progressLabel:"Enrollment",color:"primary",onClick:()=>n("/dashboard/students")},{title:"SWOT Analyses",value:e.totalStudents,subtitle:"Generated this month",icon:He,trend:"up",trendValue:8,progress:72,progressLabel:"Completion",color:"secondary",gradient:!0,onClick:()=>n("/dashboard/swot")},{title:"Average Performance",value:`${e.averagePerformance}%`,subtitle:"Class average",icon:Se,trend:"up",trendValue:5,progress:e.averagePerformance,progressLabel:"Target: 85%",color:"success",onClick:()=>n("/dashboard/analytics")},{title:"Attendance Rate",value:`${e.averageAttendance}%`,subtitle:"Overall attendance",icon:jl,trend:e.averageAttendance>=90?"up":"down",trendValue:2,progress:e.averageAttendance,progressLabel:"Target: 95%",color:"info",onClick:()=>n("/dashboard/reports")}]:[];return s.jsx(T,{container:!0,spacing:3,children:r.map((i,a)=>s.jsx(T,{item:!0,xs:12,sm:6,lg:3,children:s.jsx(_.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:a*.1,duration:.6},children:s.jsx(sl,{...i,loading:t,onClick:()=>console.log(`Clicked ${i.title}`),tooltip:`View detailed ${i.title.toLowerCase()} analytics`})})},i.title))})},lg=({loading:t,studentsData:e})=>{const n=ge(),i=(()=>{var l,c,u;if(!e||e.length===0)return[];const a=[];e.slice(0,3).forEach((d,h)=>{a.push({id:`swot_${d.id}`,type:"swot_generated",title:"SWOT Analysis Generated",description:`For student ${d.name} (${d.grade} ${d.section})`,time:`${(h+1)*5} minutes ago`,icon:He,color:"primary",onClick:()=>n(`/dashboard/students/${d.id}/swot`)})});const o=e.filter(d=>d.academicLevel<70);return o.length>0&&a.push({id:"performance_alert",type:"performance_alert",title:"Performance Alert",description:`${o.length} students need attention`,time:"15 minutes ago",icon:Se,color:"warning",onClick:()=>n("/dashboard/analytics")}),a.push({id:"new_student",type:"new_student",title:"New Student Enrolled",description:`${(l=e[e.length-1])==null?void 0:l.name} joined ${(c=e[e.length-1])==null?void 0:c.grade} ${(u=e[e.length-1])==null?void 0:u.section}`,time:"1 hour ago",icon:_t,color:"success",onClick:()=>n("/dashboard/students")}),a.slice(0,4)})();return s.jsxs(ig,{loading:t,glassmorphism:!0,hoverable:!0,sx:{height:"100%"},children:[s.jsxs(y,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[s.jsx(b,{variant:"h6",sx:{fontWeight:600},children:"Recent Activity"}),s.jsx(ll,{size:"small",variant:"outlined",startIcon:s.jsx(Sl,{}),tooltip:"Refresh activities",onClick:()=>window.location.reload(),children:"Refresh"})]}),s.jsx(pe,{spacing:2,children:i.map((a,o)=>s.jsx(_.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:o*.1},children:s.jsxs(y,{sx:{display:"flex",gap:2,p:2,borderRadius:2,cursor:a.onClick?"pointer":"default",transition:"all 0.3s ease","&:hover":{background:F("#000",.02),transform:"translateX(4px)"}},onClick:a.onClick,children:[s.jsx(se,{sx:{bgcolor:`${a.color}.main`,width:40,height:40},children:s.jsx(a.icon,{sx:{fontSize:20}})}),s.jsxs(y,{sx:{flex:1},children:[s.jsx(b,{variant:"body2",sx:{fontWeight:600,mb:.5},children:a.title}),s.jsx(b,{variant:"caption",color:"text.secondary",sx:{display:"block",mb:.5},children:a.description}),s.jsx(b,{variant:"caption",color:"text.disabled",children:a.time})]})]})},a.id))})]})},cg=()=>{q();const t=ge(),e=[{label:"Generate SWOT",icon:He,color:"primary",onClick:()=>t("/dashboard/swot")},{label:"Add Student",icon:_t,color:"secondary",onClick:()=>t("/dashboard/students")},{label:"View Reports",icon:Fr,color:"success",onClick:()=>t("/dashboard/reports")},{label:"Analytics",icon:Zs,color:"info",onClick:()=>t("/dashboard/analytics")}];return s.jsx(y,{sx:{position:"fixed",bottom:24,right:24,zIndex:1e3},children:s.jsx(pe,{spacing:2,children:e.map((n,r)=>s.jsx(_.div,{initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{delay:r*.1,type:"spring"},children:s.jsx(ll,{variant:"contained",color:n.color,onClick:n.onClick,tooltip:n.label,startIcon:s.jsx(n.icon,{}),sx:{borderRadius:"50%",minWidth:56,width:56,height:56,backdropFilter:"blur(10px)",boxShadow:"0px 8px 24px rgba(0, 0, 0, 0.15)"}})},n.label))})})},ug=()=>{const{t}=ye(["dashboard","common"]),e=q(),[n,r]=m.useState(!1),[i,a]=m.useState(!0),[o,l]=m.useState([]),[c,u]=m.useState(null),[d]=m.useState({name:"Dr. Priya Sharma",role:"Principal",school:"Delhi Public School"});m.useEffect(()=>{(async()=>{a(!0);try{await new Promise(x=>setTimeout(x,1500));const p=al(),g=ol(p);l(p),u(g)}catch(p){console.error("Error loading student data:",p)}finally{a(!1)}})()},[]);const h=()=>{r(!n)};return s.jsxs(y,{sx:{minHeight:"100vh",background:`linear-gradient(135deg, ${e.palette.background.default} 0%, ${e.palette.background.surface||e.palette.background.paper} 100%)`},children:[s.jsx(ag,{darkMode:n,onThemeToggle:h,user:d}),s.jsxs(y,{sx:{p:3},children:[s.jsx(_.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(y,{sx:{mb:4},children:[s.jsxs(b,{variant:"h4",sx:{fontWeight:700,mb:1,background:`linear-gradient(135deg, ${e.palette.primary.main} 0%, ${e.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:["Welcome back, ",d.name,"! 👋"]}),s.jsxs(b,{variant:"body1",color:"text.secondary",sx:{fontSize:"1.1rem"},children:["Here's what's happening at ",d.school," today"]})]})}),s.jsx(y,{sx:{mb:4},children:s.jsx(og,{loading:i,analyticsData:c})}),s.jsxs(T,{container:!0,spacing:3,children:[s.jsx(T,{item:!0,xs:12,lg:6,children:s.jsx(_.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.3,duration:.6},children:s.jsx(lg,{loading:i,studentsData:o})})}),s.jsx(T,{item:!0,xs:12,lg:6,children:s.jsx(_.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.4,duration:.6},children:s.jsx(K,{sx:{height:"100%"},children:s.jsxs(ie,{children:[s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:3},children:"Performance Overview"}),i?s.jsxs(pe,{spacing:2,children:[s.jsx(Pe,{variant:"rectangular",height:200}),s.jsx(Pe,{variant:"text"}),s.jsx(Pe,{variant:"text",width:"60%"})]}):s.jsx(y,{children:s.jsxs(y,{sx:{mb:3},children:[s.jsx(b,{variant:"body2",sx:{mb:2,fontWeight:500},children:"Board Performance"}),s.jsx(pe,{spacing:2,children:[{board:"CBSE",score:92,students:450},{board:"ICSE",score:89,students:320},{board:"State Board",score:85,students:477}].map((f,p)=>{var g,x,j;return s.jsx(_.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},transition:{delay:.5+p*.1},children:s.jsxs(y,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",p:2,borderRadius:2,background:F(((g=e.palette.board)==null?void 0:g[f.board])||e.palette.primary.main,.1),border:`1px solid ${F(((x=e.palette.board)==null?void 0:x[f.board])||e.palette.primary.main,.2)}`},children:[s.jsxs(y,{children:[s.jsx(b,{variant:"body2",sx:{fontWeight:600},children:f.board}),s.jsxs(b,{variant:"caption",color:"text.secondary",children:[f.students," students"]})]}),s.jsx(re,{label:`${f.score}%`,size:"small",sx:{bgcolor:((j=e.palette.board)==null?void 0:j[f.board])||e.palette.primary.main,color:"white",fontWeight:600}})]})},f.board)})})]})})]})})})})]}),s.jsx(y,{sx:{mt:4},children:s.jsxs(T,{container:!0,spacing:3,children:[s.jsx(T,{item:!0,xs:12,md:4,children:s.jsx(_.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6,duration:.6},children:s.jsxs(K,{sx:{p:3,textAlign:"center",background:`linear-gradient(135deg, ${e.palette.primary.main} 0%, ${e.palette.primary.dark} 100%)`,color:"white",cursor:"pointer",transition:"all 0.3s ease","&:hover":{transform:"translateY(-4px)",boxShadow:"0px 20px 40px rgba(46, 91, 168, 0.3)"}},children:[s.jsx(He,{sx:{fontSize:48,mb:2}}),s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:1},children:"Generate SWOT Analysis"}),s.jsx(b,{variant:"body2",sx:{opacity:.9},children:"Create comprehensive student assessments"})]})})}),s.jsx(T,{item:!0,xs:12,md:4,children:s.jsx(_.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7,duration:.6},children:s.jsxs(K,{sx:{p:3,textAlign:"center",background:`linear-gradient(135deg, ${e.palette.secondary.main} 0%, ${e.palette.secondary.dark} 100%)`,color:"white",cursor:"pointer",transition:"all 0.3s ease","&:hover":{transform:"translateY(-4px)",boxShadow:"0px 20px 40px rgba(255, 153, 51, 0.3)"}},children:[s.jsx(Fr,{sx:{fontSize:48,mb:2}}),s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:1},children:"View Analytics"}),s.jsx(b,{variant:"body2",sx:{opacity:.9},children:"Detailed performance insights"})]})})}),s.jsx(T,{item:!0,xs:12,md:4,children:s.jsx(_.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8,duration:.6},children:s.jsxs(K,{sx:{p:3,textAlign:"center",background:`linear-gradient(135deg, ${e.palette.success.main} 0%, ${e.palette.success.dark} 100%)`,color:"white",cursor:"pointer",transition:"all 0.3s ease","&:hover":{transform:"translateY(-4px)",boxShadow:"0px 20px 40px rgba(0, 200, 83, 0.3)"}},children:[s.jsx(yl,{sx:{fontSize:48,mb:2}}),s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:1},children:"Manage Students"}),s.jsx(b,{variant:"body2",sx:{opacity:.9},children:"Student profiles and records"})]})})})]})})]}),s.jsx(cg,{})]})},dg=()=>{const t=q();return s.jsx(y,{sx:{position:"absolute",top:0,left:0,right:0,bottom:0,background:`linear-gradient(135deg, ${t.palette.primary.main}15 0%, ${t.palette.secondary.main}10 100%)`,overflow:"hidden",zIndex:-1},children:[...Array(6)].map((e,n)=>s.jsx(_.div,{initial:{opacity:0,scale:0},animate:{opacity:[.1,.3,.1],scale:[1,1.2,1],x:[0,100,0],y:[0,-50,0]},transition:{duration:8+n*2,repeat:1/0,delay:n*1.5},style:{position:"absolute",top:`${20+n*15}%`,left:`${10+n*15}%`,width:60+n*20,height:60+n*20,borderRadius:"50%",background:n%2===0?`linear-gradient(135deg, ${t.palette.primary.main}20, ${t.palette.primary.light}10)`:`linear-gradient(135deg, ${t.palette.secondary.main}20, ${t.palette.secondary.light}10)`,backdropFilter:"blur(10px)"}},n))})},tr=({icon:t,title:e,description:n,delay:r=0})=>{const i=q();return s.jsx(_.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:r,duration:.6},children:s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:2,p:2,borderRadius:2,background:F(i.palette.background.paper,.1),backdropFilter:"blur(10px)",border:`1px solid ${F(i.palette.primary.main,.1)}`,transition:"all 0.3s ease","&:hover":{transform:"translateX(8px)",background:F(i.palette.background.paper,.2)}},children:[s.jsx(se,{sx:{bgcolor:i.palette.primary.main,width:48,height:48},children:s.jsx(t,{})}),s.jsxs(y,{children:[s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:.5},children:e}),s.jsx(b,{variant:"body2",color:"text.secondary",children:n})]})]})})},hg=({onSubmit:t,loading:e})=>{const[n,r]=m.useState(!1),[i,a]=m.useState({email:"",password:""}),o=c=>u=>{a(d=>({...d,[c]:u.target.value}))},l=c=>{c.preventDefault(),t(i)};return s.jsx(y,{component:"form",onSubmit:l,sx:{width:"100%"},children:s.jsxs(pe,{spacing:3,children:[s.jsx(he,{fullWidth:!0,label:"Email Address",type:"email",value:i.email,onChange:o("email"),InputProps:{startAdornment:s.jsx(De,{position:"start",children:s.jsx(Kt,{color:"primary"})})},sx:{"& .MuiOutlinedInput-root":{borderRadius:2,backdropFilter:"blur(10px)",background:F("#fff",.1)}}}),s.jsx(he,{fullWidth:!0,label:"Password",type:n?"text":"password",value:i.password,onChange:o("password"),InputProps:{startAdornment:s.jsx(De,{position:"start",children:s.jsx(na,{color:"primary"})}),endAdornment:s.jsx(De,{position:"end",children:s.jsx(le,{onClick:()=>r(!n),edge:"end",children:n?s.jsx(ta,{}):s.jsx(cn,{})})})},sx:{"& .MuiOutlinedInput-root":{borderRadius:2,backdropFilter:"blur(10px)",background:F("#fff",.1)}}}),s.jsx(W,{type:"submit",fullWidth:!0,variant:"contained",size:"large",disabled:e,endIcon:s.jsx(Mr,{}),sx:{py:1.5,borderRadius:2,background:"linear-gradient(135deg, #2E5BA8 0%, #4A90E2 100%)",boxShadow:"0px 8px 24px rgba(46, 91, 168, 0.3)","&:hover":{background:"linear-gradient(135deg, #1E4A97 0%, #2E5BA8 100%)",boxShadow:"0px 12px 32px rgba(46, 91, 168, 0.4)",transform:"translateY(-2px)"}},children:e?"Signing In...":"Sign In"})]})})},fg=({onSubmit:t,loading:e})=>{const[n,r]=m.useState(!1),[i,a]=m.useState({name:"",email:"",password:"",role:"teacher",board:"CBSE"}),o=c=>u=>{a(d=>({...d,[c]:u.target.value}))},l=c=>{c.preventDefault(),t(i)};return s.jsx(y,{component:"form",onSubmit:l,sx:{width:"100%"},children:s.jsxs(pe,{spacing:3,children:[s.jsx(he,{fullWidth:!0,label:"Full Name",value:i.name,onChange:o("name"),InputProps:{startAdornment:s.jsx(De,{position:"start",children:s.jsx(_t,{color:"primary"})})},sx:{"& .MuiOutlinedInput-root":{borderRadius:2,backdropFilter:"blur(10px)",background:F("#fff",.1)}}}),s.jsx(he,{fullWidth:!0,label:"Email Address",type:"email",value:i.email,onChange:o("email"),InputProps:{startAdornment:s.jsx(De,{position:"start",children:s.jsx(Kt,{color:"primary"})})},sx:{"& .MuiOutlinedInput-root":{borderRadius:2,backdropFilter:"blur(10px)",background:F("#fff",.1)}}}),s.jsx(he,{fullWidth:!0,label:"Password",type:n?"text":"password",value:i.password,onChange:o("password"),InputProps:{startAdornment:s.jsx(De,{position:"start",children:s.jsx(na,{color:"primary"})}),endAdornment:s.jsx(De,{position:"end",children:s.jsx(le,{onClick:()=>r(!n),edge:"end",children:n?s.jsx(ta,{}):s.jsx(cn,{})})})},sx:{"& .MuiOutlinedInput-root":{borderRadius:2,backdropFilter:"blur(10px)",background:F("#fff",.1)}}}),s.jsxs(y,{children:[s.jsx(b,{variant:"body2",sx:{mb:1,fontWeight:500},children:"Education Board"}),s.jsx(pe,{direction:"row",spacing:1,flexWrap:"wrap",children:["CBSE","ICSE","State Board","IB"].map(c=>s.jsx(re,{label:c,clickable:!0,color:i.board===c?"primary":"default",onClick:()=>a(u=>({...u,board:c})),sx:{borderRadius:2,fontWeight:500}},c))})]}),s.jsx(W,{type:"submit",fullWidth:!0,variant:"contained",size:"large",disabled:e,endIcon:s.jsx(Mr,{}),sx:{py:1.5,borderRadius:2,background:"linear-gradient(135deg, #FF9933 0%, #FFB366 100%)",boxShadow:"0px 8px 24px rgba(255, 153, 51, 0.3)","&:hover":{background:"linear-gradient(135deg, #FF8F00 0%, #FF9933 100%)",boxShadow:"0px 12px 32px rgba(255, 153, 51, 0.4)",transform:"translateY(-2px)"}},children:e?"Creating Account...":"Create Account"})]})})},pg=({onLogin:t,onSignup:e})=>{const{t:n}=ye(["auth","common"]),r=q(),[i,a]=m.useState(0),[o,l]=m.useState(!1),c=(h,f)=>{a(f)},u=async h=>{l(!0);try{t?await t(h):(await new Promise(f=>setTimeout(f,2e3)),console.log("Login:",h))}catch(f){console.error("Login error:",f)}finally{l(!1)}},d=async h=>{l(!0);try{e?await e(h):(await new Promise(f=>setTimeout(f,2e3)),console.log("Signup:",h))}catch(f){console.error("Signup error:",f)}finally{l(!1)}};return s.jsxs(y,{sx:{minHeight:"100vh",display:"flex",position:"relative",background:`linear-gradient(135deg, ${r.palette.background.default} 0%, ${r.palette.background.surface} 100%)`},children:[s.jsx(dg,{}),s.jsx(y,{sx:{flex:1,display:{xs:"none",md:"flex"},flexDirection:"column",justifyContent:"center",p:6,position:"relative"},children:s.jsxs(_.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},children:[s.jsxs(y,{sx:{mb:6},children:[s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:2,mb:3},children:[s.jsx(se,{sx:{bgcolor:r.palette.primary.main,width:64,height:64},children:s.jsx(je,{sx:{fontSize:32}})}),s.jsx(b,{variant:"h3",sx:{fontWeight:600,color:r.palette.mode==="dark"?"#FFFFFF":"#1E293B",textShadow:r.palette.mode==="dark"?"0 2px 8px rgba(0,0,0,0.5)":"0 2px 4px rgba(0,0,0,0.1)"},children:"VidyaMitra"})]}),s.jsx(b,{variant:"h5",sx:{fontWeight:600,color:r.palette.text.primary,mb:2},children:"Empowering Indian Education with AI-Driven SWOT Analysis"}),s.jsx(b,{variant:"body1",sx:{color:r.palette.text.secondary,fontSize:"1.1rem",lineHeight:1.6},children:"Transform student assessment and development with our comprehensive platform designed specifically for Indian educational institutions."})]}),s.jsxs(pe,{spacing:3,children:[s.jsx(tr,{icon:Se,title:"AI-Powered Analysis",description:"Advanced SWOT analysis using machine learning algorithms",delay:.2}),s.jsx(tr,{icon:je,title:"Board-Specific Features",description:"Tailored for CBSE, ICSE, and State Board curricula",delay:.4}),s.jsx(tr,{icon:et,title:"Comprehensive Tracking",description:"Academic, behavioral, and extracurricular monitoring",delay:.6})]})]})}),s.jsx(y,{sx:{flex:{xs:1,md:.6},display:"flex",alignItems:"center",justifyContent:"center",p:3},children:s.jsx(_.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},style:{width:"100%",maxWidth:480},children:s.jsx(K,{sx:{p:4,borderRadius:4,background:F(r.palette.background.paper,.9),backdropFilter:"blur(20px)",border:`1px solid ${F(r.palette.primary.main,.1)}`,boxShadow:"0px 20px 40px rgba(46, 91, 168, 0.15)"},children:s.jsxs(ie,{sx:{p:0},children:[s.jsxs(y,{sx:{textAlign:"center",mb:4},children:[s.jsx(b,{variant:"h4",sx:{fontWeight:500,mb:1,color:r.palette.text.primary},children:"Welcome Back"}),s.jsx(b,{variant:"body1",color:"text.secondary",children:"Access your educational dashboard"})]}),s.jsxs(Qs,{value:i,onChange:c,centered:!0,sx:{mb:4,"& .MuiTab-root":{textTransform:"none",fontWeight:600,fontSize:"1rem",minWidth:120},"& .MuiTabs-indicator":{borderRadius:2,height:3}},children:[s.jsx(dt,{label:"Sign In"}),s.jsx(dt,{label:"Sign Up"})]}),s.jsx(Hm,{mode:"wait",children:i===0?s.jsx(_.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3},children:s.jsx(hg,{onSubmit:u,loading:o})},"login"):s.jsx(_.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:s.jsx(fg,{onSubmit:d,loading:o})},"signup")}),s.jsxs(y,{sx:{mt:4},children:[s.jsx(Or,{sx:{mb:3},children:s.jsx(b,{variant:"body2",color:"text.secondary",children:"Or continue with"})}),s.jsxs(pe,{direction:"row",spacing:2,justifyContent:"center",children:[s.jsx(le,{sx:{border:`1px solid ${F(r.palette.divider,.5)}`,borderRadius:2,p:1.5,"&:hover":{background:F(r.palette.primary.main,.1),transform:"translateY(-2px)"}},children:s.jsx(Pl,{})}),s.jsx(le,{sx:{border:`1px solid ${F(r.palette.divider,.5)}`,borderRadius:2,p:1.5,"&:hover":{background:F(r.palette.primary.main,.1),transform:"translateY(-2px)"}},children:s.jsx(Al,{})}),s.jsx(le,{sx:{border:`1px solid ${F(r.palette.divider,.5)}`,borderRadius:2,p:1.5,"&:hover":{background:F(r.palette.primary.main,.1),transform:"translateY(-2px)"}},children:s.jsx(ea,{})})]})]})]})})})})]})};ua.register(ac,oc,da,ha,lc,cc,fa,pa,uc);const mg=()=>{const t=q(),[e,n]=m.useState(0),[r,i]=m.useState("year"),[a,o]=m.useState("all"),[l,c]=m.useState([]),[u,d]=m.useState(null);m.useEffect(()=>{const C=al();c(C),d(ol(C))},[]);const h=(C,v)=>{n(v)};if(!u)return s.jsx(y,{children:"Loading..."});const f={data:{labels:u.performanceTrends.map(C=>C.month),datasets:[{label:"Overall Average",data:u.performanceTrends.map(C=>C.average),borderColor:t.palette.primary.main,backgroundColor:F(t.palette.primary.main,.1),tension:.4},{label:"CBSE",data:u.performanceTrends.map(C=>C.cbse),borderColor:t.palette.secondary.main,backgroundColor:F(t.palette.secondary.main,.1),tension:.4},{label:"ICSE",data:u.performanceTrends.map(C=>C.icse),borderColor:t.palette.success.main,backgroundColor:F(t.palette.success.main,.1),tension:.4},{label:"State Board",data:u.performanceTrends.map(C=>C.state),borderColor:t.palette.warning.main,backgroundColor:F(t.palette.warning.main,.1),tension:.4}]},options:{responsive:!0,plugins:{legend:{position:"top"},title:{display:!0,text:"Academic Performance Trends"}},scales:{y:{beginAtZero:!1,min:60,max:100}}}},p={data:{labels:u.subjectAnalysis.map(C=>C.subject),datasets:[{label:"Average Score",data:u.subjectAnalysis.map(C=>C.averageScore),backgroundColor:[F(t.palette.primary.main,.8),F(t.palette.secondary.main,.8),F(t.palette.success.main,.8),F(t.palette.warning.main,.8),F(t.palette.error.main,.8),F(t.palette.info.main,.8)],borderColor:[t.palette.primary.main,t.palette.secondary.main,t.palette.success.main,t.palette.warning.main,t.palette.error.main,t.palette.info.main],borderWidth:2}]},options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Subject-wise Performance Analysis"}},scales:{y:{beginAtZero:!1,min:50,max:100}}}},g={data:{labels:["CBSE","ICSE","State Board","IB"],datasets:[{data:[u.boardDistribution.cbse,u.boardDistribution.icse,u.boardDistribution.state,u.boardDistribution.ib],backgroundColor:[F(t.palette.primary.main,.8),F(t.palette.secondary.main,.8),F(t.palette.success.main,.8),F(t.palette.warning.main,.8)],borderColor:[t.palette.primary.main,t.palette.secondary.main,t.palette.success.main,t.palette.warning.main],borderWidth:2}]},options:{responsive:!0,plugins:{legend:{position:"bottom"},title:{display:!0,text:"Student Distribution by Board"}}}},x={data:{labels:u.attendancePatterns.map(C=>C.month),datasets:[{label:"Attendance Percentage",data:u.attendancePatterns.map(C=>C.averageAttendance),backgroundColor:F(t.palette.success.main,.6),borderColor:t.palette.success.main,borderWidth:2}]},options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Monthly Attendance Patterns"}},scales:{y:{beginAtZero:!1,min:70,max:100}}}},j=[{title:"Total Students",value:u.totalStudents,icon:Cn,color:"primary",trend:"+5.2%",description:"Active students enrolled"},{title:"Average Performance",value:`${u.averagePerformance}%`,icon:Se,color:"success",trend:"+2.1%",description:"Overall academic performance"},{title:"Average Attendance",value:`${u.averageAttendance}%`,icon:je,color:"info",trend:"+1.8%",description:"Monthly attendance rate"},{title:"Top Performers",value:u.topPerformers,icon:gt,color:"warning",trend:"+3.5%",description:"Students scoring 90%+"}];return s.jsx(Q,{maxWidth:"xl",sx:{py:3},children:s.jsxs(_.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[s.jsxs(y,{sx:{mb:4},children:[s.jsx(b,{variant:"h4",sx:{fontWeight:600,mb:1,color:t.palette.text.primary},children:"Analytics Dashboard"}),s.jsx(b,{variant:"body1",color:"text.secondary",children:"Comprehensive insights into student performance and institutional metrics"})]}),s.jsxs(y,{sx:{mb:4,display:"flex",gap:2,flexWrap:"wrap"},children:[s.jsxs(nr,{size:"small",sx:{minWidth:120},children:[s.jsx(rr,{children:"Time Range"}),s.jsxs(ir,{value:r,label:"Time Range",onChange:C=>i(C.target.value),children:[s.jsx(U,{value:"month",children:"This Month"}),s.jsx(U,{value:"quarter",children:"This Quarter"}),s.jsx(U,{value:"year",children:"This Year"}),s.jsx(U,{value:"all",children:"All Time"})]})]}),s.jsxs(nr,{size:"small",sx:{minWidth:120},children:[s.jsx(rr,{children:"Board"}),s.jsxs(ir,{value:a,label:"Board",onChange:C=>o(C.target.value),children:[s.jsx(U,{value:"all",children:"All Boards"}),s.jsx(U,{value:"cbse",children:"CBSE"}),s.jsx(U,{value:"icse",children:"ICSE"}),s.jsx(U,{value:"state",children:"State Board"}),s.jsx(U,{value:"ib",children:"IB"})]})]})]}),s.jsx(T,{container:!0,spacing:3,sx:{mb:4},children:j.map((C,v)=>s.jsx(T,{item:!0,xs:12,sm:6,md:3,children:s.jsx(_.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:v*.1},children:s.jsx(sl,{...C})})},C.title))}),s.jsxs(K,{sx:{background:F(t.palette.background.paper,.9),backdropFilter:"blur(20px)",border:`1px solid ${F(t.palette.divider,.1)}`},children:[s.jsxs(Qs,{value:e,onChange:h,sx:{borderBottom:`1px solid ${F(t.palette.divider,.1)}`,px:2},children:[s.jsx(dt,{label:"Performance Trends",icon:s.jsx(Zs,{})}),s.jsx(dt,{label:"Subject Analysis",icon:s.jsx(Fr,{})}),s.jsx(dt,{label:"Board Distribution",icon:s.jsx(je,{})}),s.jsx(dt,{label:"Attendance Patterns",icon:s.jsx(Se,{})})]}),s.jsxs(ie,{sx:{p:3},children:[e===0&&s.jsx(y,{sx:{height:400},children:s.jsx(dc,{...f})}),e===1&&s.jsx(y,{sx:{height:400},children:s.jsx(hi,{...p})}),e===2&&s.jsx(y,{sx:{height:400,display:"flex",justifyContent:"center"},children:s.jsx(y,{sx:{width:400},children:s.jsx(hc,{...g})})}),e===3&&s.jsx(y,{sx:{height:400},children:s.jsx(hi,{...x})})]})]})]})})},gg=({student:t,onView:e,onEdit:n,onGenerateSWOT:r})=>{const i=q(),[a,o]=m.useState(null),[l,c]=m.useState(!1),u=g=>{g.stopPropagation(),o(g.currentTarget)},d=()=>{o(null)},h=g=>g>=80?i.palette.success.main:g>=60?i.palette.warning.main:i.palette.error.main,f=g=>g>0?{icon:Se,color:i.palette.success.main}:g<0?{icon:ra,color:i.palette.error.main}:{icon:null,color:i.palette.text.secondary},p=f(t.performanceTrend).icon;return s.jsx(fe,{in:!0,timeout:300,children:s.jsxs(K,{sx:{height:"100%",cursor:"pointer",transition:"all 0.3s ease-in-out",transform:l?"translateY(-4px)":"translateY(0)",boxShadow:l?i.shadows[8]:i.shadows[1],border:`1px solid ${F(i.palette.primary.main,.1)}`,"&:hover":{borderColor:i.palette.primary.main}},onMouseEnter:()=>c(!0),onMouseLeave:()=>c(!1),onClick:()=>e(t),children:[s.jsxs(ie,{sx:{p:3},children:[s.jsxs(y,{sx:{display:"flex",alignItems:"flex-start",justifyContent:"space-between",mb:2},children:[s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:2,flex:1},children:[s.jsx(se,{sx:{width:56,height:56,bgcolor:i.palette.primary.main,fontSize:"1.25rem",fontWeight:600},children:t.name.split(" ").map(g=>g[0]).join("").toUpperCase()}),s.jsxs(y,{sx:{flex:1,minWidth:0},children:[s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:.5},children:t.name}),s.jsxs(b,{variant:"body2",color:"text.secondary",children:[t.class," • Roll No: ",t.rollNumber]}),s.jsxs(b,{variant:"caption",color:"text.secondary",children:["ID: ",t.admissionNumber]})]})]}),s.jsx(le,{size:"small",onClick:u,sx:{opacity:l?1:.7,transition:"opacity 0.2s ease-in-out"},children:s.jsx(Js,{})})]}),s.jsxs(y,{sx:{mb:2},children:[s.jsxs(y,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:1},children:[s.jsx(b,{variant:"body2",color:"text.secondary",children:"Overall Performance"}),s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:.5},children:[p&&s.jsx(p,{sx:{fontSize:16,color:f(t.performanceTrend).color}}),s.jsxs(b,{variant:"body2",sx:{fontWeight:600,color:h(t.overallScore)},children:[t.overallScore,"%"]})]})]}),s.jsx(y,{sx:{height:6,borderRadius:3,bgcolor:F(h(t.overallScore),.2),overflow:"hidden"},children:s.jsx(y,{sx:{height:"100%",width:`${t.overallScore}%`,bgcolor:h(t.overallScore),borderRadius:3,transition:"width 0.5s ease-in-out"}})})]}),s.jsxs(y,{sx:{display:"flex",gap:1,flexWrap:"wrap",mb:2},children:[s.jsx(re,{label:`${t.attendance}% Attendance`,size:"small",color:t.attendance>=90?"success":t.attendance>=75?"warning":"error",variant:"outlined"}),t.hasActiveSWOT&&s.jsx(re,{label:"SWOT Active",size:"small",color:"primary",variant:"filled"})]}),s.jsxs(y,{sx:{display:"flex",gap:1},children:[s.jsx(W,{size:"small",variant:"outlined",startIcon:s.jsx(cn,{}),onClick:g=>{g.stopPropagation(),e(t)},sx:{flex:1},children:"View"}),s.jsx(W,{size:"small",variant:"contained",startIcon:s.jsx(gt,{}),onClick:g=>{g.stopPropagation(),r(t)},sx:{flex:1},children:"SWOT"})]})]}),s.jsxs(El,{anchorEl:a,open:!!a,onClose:d,onClick:g=>g.stopPropagation(),children:[s.jsxs(U,{onClick:()=>{e(t),d()},children:[s.jsx(cn,{sx:{mr:1}}),"View Details"]}),s.jsxs(U,{onClick:()=>{n(t),d()},children:[s.jsx(Fl,{sx:{mr:1}}),"Edit Student"]}),s.jsxs(U,{onClick:()=>{r(t),d()},children:[s.jsx(gt,{sx:{mr:1}}),"Generate SWOT"]})]})]})})},xg=({open:t,onClose:e,filters:n,onFiltersChange:r})=>{const{t:i}=ye(["common"]);return s.jsxs(kl,{open:t,onClose:e,maxWidth:"sm",fullWidth:!0,children:[s.jsx(Rl,{children:s.jsxs(y,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:["Filter Students",s.jsx(le,{onClick:e,size:"small",children:s.jsx(Ol,{})})]})}),s.jsx(Ml,{children:s.jsxs(T,{container:!0,spacing:2,sx:{mt:1},children:[s.jsx(T,{item:!0,xs:12,sm:6,children:s.jsxs(he,{fullWidth:!0,label:"Class",select:!0,value:n.class||"",onChange:a=>r({...n,class:a.target.value}),children:[s.jsx(U,{value:"",children:"All Classes"}),s.jsx(U,{value:"10-A",children:"Class 10-A"}),s.jsx(U,{value:"10-B",children:"Class 10-B"}),s.jsx(U,{value:"11-Science",children:"Class 11-Science"}),s.jsx(U,{value:"12-Commerce",children:"Class 12-Commerce"})]})}),s.jsx(T,{item:!0,xs:12,sm:6,children:s.jsxs(he,{fullWidth:!0,label:"Performance",select:!0,value:n.performance||"",onChange:a=>r({...n,performance:a.target.value}),children:[s.jsx(U,{value:"",children:"All Performance"}),s.jsx(U,{value:"excellent",children:"Excellent (80%+)"}),s.jsx(U,{value:"good",children:"Good (60-79%)"}),s.jsx(U,{value:"needs-improvement",children:"Needs Improvement (<60%)"})]})}),s.jsx(T,{item:!0,xs:12,sm:6,children:s.jsxs(he,{fullWidth:!0,label:"Attendance",select:!0,value:n.attendance||"",onChange:a=>r({...n,attendance:a.target.value}),children:[s.jsx(U,{value:"",children:"All Attendance"}),s.jsx(U,{value:"excellent",children:"Excellent (90%+)"}),s.jsx(U,{value:"good",children:"Good (75-89%)"}),s.jsx(U,{value:"poor",children:"Poor (<75%)"})]})}),s.jsx(T,{item:!0,xs:12,sm:6,children:s.jsxs(he,{fullWidth:!0,label:"SWOT Status",select:!0,value:n.swotStatus||"",onChange:a=>r({...n,swotStatus:a.target.value}),children:[s.jsx(U,{value:"",children:"All Students"}),s.jsx(U,{value:"active",children:"Has Active SWOT"}),s.jsx(U,{value:"pending",children:"SWOT Pending"})]})})]})}),s.jsxs(Ll,{sx:{p:3},children:[s.jsx(W,{onClick:()=>r({}),children:"Clear All"}),s.jsx(W,{variant:"contained",onClick:e,children:"Apply Filters"})]})]})},yg=()=>{const{t}=ye(["common"]),e=q(),n=Lr(e.breakpoints.down("md")),[r,i]=m.useState([]),[a,o]=m.useState(!0),[l,c]=m.useState(""),[u,d]=m.useState({}),[h,f]=m.useState(!1);m.useEffect(()=>{const v=[{id:1,name:"Aarav Sharma",class:"10-A",rollNumber:"001",admissionNumber:"VM2024001",overallScore:85,attendance:92,performanceTrend:5,hasActiveSWOT:!0},{id:2,name:"Priya Patel",class:"10-A",rollNumber:"002",admissionNumber:"VM2024002",overallScore:78,attendance:88,performanceTrend:-2,hasActiveSWOT:!1},{id:3,name:"Arjun Kumar",class:"10-B",rollNumber:"003",admissionNumber:"VM2024003",overallScore:92,attendance:95,performanceTrend:8,hasActiveSWOT:!0},{id:4,name:"Ananya Singh",class:"11-Science",rollNumber:"004",admissionNumber:"VM2024004",overallScore:67,attendance:82,performanceTrend:3,hasActiveSWOT:!1}];setTimeout(()=>{i(v),o(!1)},1e3)},[]);const p=m.useMemo(()=>r.filter(v=>{const w=v.name.toLowerCase().includes(l.toLowerCase())||v.admissionNumber.toLowerCase().includes(l.toLowerCase())||v.class.toLowerCase().includes(l.toLowerCase()),P=!u.class||v.class===u.class,E=!u.performance||u.performance==="excellent"&&v.overallScore>=80||u.performance==="good"&&v.overallScore>=60&&v.overallScore<80||u.performance==="needs-improvement"&&v.overallScore<60,k=!u.attendance||u.attendance==="excellent"&&v.attendance>=90||u.attendance==="good"&&v.attendance>=75&&v.attendance<90||u.attendance==="poor"&&v.attendance<75,R=!u.swotStatus||u.swotStatus==="active"&&v.hasActiveSWOT||u.swotStatus==="pending"&&!v.hasActiveSWOT;return w&&P&&E&&k&&R}),[r,l,u]),g=v=>{console.log("View student:",v)},x=v=>{console.log("Edit student:",v)},j=v=>{console.log("Generate SWOT for:",v)},C=()=>{console.log("Add new student")};return s.jsxs(y,{sx:{p:{xs:2,md:3}},children:[s.jsxs(y,{sx:{mb:4},children:[s.jsx(b,{variant:"h4",component:"h1",sx:{fontWeight:700,mb:1},children:"Student Management"}),s.jsx(b,{variant:"body1",color:"text.secondary",children:"Manage student profiles, track performance, and generate SWOT analyses"})]}),s.jsx(y,{sx:{mb:3},children:s.jsxs(T,{container:!0,spacing:2,alignItems:"center",children:[s.jsx(T,{item:!0,xs:12,md:8,children:s.jsx(he,{fullWidth:!0,placeholder:"Search students by name, ID, or class...",value:l,onChange:v=>c(v.target.value),InputProps:{startAdornment:s.jsx(De,{position:"start",children:s.jsx(Rr,{color:"action"})})},sx:{"& .MuiOutlinedInput-root":{borderRadius:2}}})}),s.jsx(T,{item:!0,xs:12,md:4,children:s.jsxs(y,{sx:{display:"flex",gap:1,justifyContent:"flex-end"},children:[s.jsx(W,{variant:"outlined",startIcon:s.jsx(Xs,{}),onClick:()=>f(!0),sx:{borderRadius:2},children:"Filter"}),s.jsx(W,{variant:"contained",startIcon:s.jsx(Vn,{}),onClick:C,sx:{borderRadius:2},children:"Add Student"})]})})]})}),s.jsx(y,{sx:{mb:3},children:s.jsxs(b,{variant:"body2",color:"text.secondary",children:["Showing ",p.length," of ",r.length," students"]})}),s.jsx(T,{container:!0,spacing:3,children:p.map(v=>s.jsx(T,{item:!0,xs:12,sm:6,lg:4,children:s.jsx(gg,{student:v,onView:g,onEdit:x,onGenerateSWOT:j})},v.id))}),p.length===0&&!a&&s.jsxs(y,{sx:{textAlign:"center",py:8},children:[s.jsx(_t,{sx:{fontSize:64,color:"text.disabled",mb:2}}),s.jsx(b,{variant:"h6",color:"text.secondary",gutterBottom:!0,children:"No students found"}),s.jsx(b,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Try adjusting your search criteria or add a new student"}),s.jsx(W,{variant:"contained",startIcon:s.jsx(Vn,{}),onClick:C,children:"Add First Student"})]}),n&&s.jsx(Tl,{color:"primary","aria-label":"add student",onClick:C,sx:{position:"fixed",bottom:24,right:24,zIndex:e.zIndex.fab},children:s.jsx(Vn,{})}),s.jsx(xg,{open:h,onClose:()=>f(!1),filters:u,onFiltersChange:d})]})},bg=({type:t,title:e,items:n,color:r,icon:i,loading:a=!1,culturalPattern:o="lotus"})=>{const l=q(),c=u=>{switch(u){case"lotus":return`radial-gradient(circle at 20% 80%, ${r}15 0%, transparent 50%), 
                radial-gradient(circle at 80% 20%, ${r}10 0%, transparent 50%)`;case"mandala":return`conic-gradient(from 0deg at 50% 50%, ${r}05, ${r}15, ${r}05)`;case"paisley":return`linear-gradient(45deg, ${r}08 25%, transparent 25%), 
                linear-gradient(-45deg, ${r}08 25%, transparent 25%)`;default:return`linear-gradient(135deg, ${r}10 0%, ${r}05 100%)`}};return a?s.jsx(K,{sx:{height:"100%",minHeight:300},children:s.jsxs(ie,{children:[s.jsx(Pe,{variant:"circular",width:48,height:48}),s.jsx(Pe,{variant:"text",width:"60%",sx:{mt:2}}),[...Array(4)].map((u,d)=>s.jsx(Pe,{variant:"text",width:"90%",sx:{mt:1}},d))]})}):s.jsx(fe,{in:!0,timeout:300,children:s.jsx(K,{sx:{height:"100%",minHeight:300,background:c(o),border:`2px solid ${r}20`,transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:l.shadows[8],border:`2px solid ${r}40`}},children:s.jsxs(ie,{sx:{p:3},children:[s.jsxs(y,{sx:{display:"flex",alignItems:"center",mb:3},children:[s.jsx(se,{sx:{bgcolor:r,width:48,height:48,mr:2},children:s.jsx(i,{})}),s.jsxs(y,{children:[s.jsx(b,{variant:"h6",sx:{fontWeight:700,color:r},children:e}),s.jsxs(b,{variant:"body2",color:"text.secondary",children:[n.length," identified areas"]})]})]}),s.jsx(y,{children:n.map((u,d)=>s.jsx(fe,{in:!0,timeout:300+d*100,children:s.jsxs(y,{sx:{p:2,mb:2,borderRadius:2,bgcolor:"background.paper",border:`1px solid ${r}20`,transition:"all 0.2s ease-in-out","&:hover":{bgcolor:`${r}05`,border:`1px solid ${r}40`}},children:[s.jsxs(y,{sx:{display:"flex",alignItems:"center",mb:1},children:[s.jsx(y,{sx:{width:8,height:8,borderRadius:"50%",bgcolor:r,mr:1}}),s.jsx(b,{variant:"body2",sx:{fontWeight:600},children:u.title}),u.priority&&s.jsx(re,{label:u.priority,size:"small",sx:{ml:"auto",fontSize:"0.75rem"},color:u.priority==="High"?"error":u.priority==="Medium"?"warning":"default"})]}),s.jsx(b,{variant:"caption",color:"text.secondary",children:u.description}),u.culturalContext&&s.jsxs(b,{variant:"caption",sx:{display:"block",mt:.5,fontStyle:"italic",color:r},children:["Cultural Context: ",u.culturalContext]})]})},d))})]})})})},vg=({studentId:t,boardType:e="CBSE"})=>{const n=q();Lr(n.breakpoints.down("md"));const[r,i]=m.useState(!0),[a,o]=m.useState({}),[l,c]=m.useState(null);m.useEffect(()=>{(async()=>{i(!0),await new Promise(f=>setTimeout(f,1500)),o({strengths:[{title:"Strong Mathematical Foundation",description:"Excellent performance in algebra and geometry",priority:"High",culturalContext:"Vedic mathematics influence"},{title:"Hindi Language Proficiency",description:"Native speaker with excellent writing skills",priority:"High",culturalContext:"Mother tongue advantage"},{title:"Cultural Values Integration",description:"Strong moral and ethical foundation",priority:"Medium",culturalContext:"Family and community values"},{title:"Collaborative Learning",description:"Works well in group projects and team activities",priority:"Medium",culturalContext:"Community-oriented upbringing"}],weaknesses:[{title:"English Communication",description:"Needs improvement in spoken English confidence",priority:"High",culturalContext:"Regional language dominance"},{title:"Technology Adaptation",description:"Slower adoption of digital learning tools",priority:"Medium",culturalContext:"Traditional learning methods preference"},{title:"Time Management",description:"Struggles with deadline management",priority:"Medium",culturalContext:"Flexible time concept in culture"}],opportunities:[{title:"Competitive Exam Preparation",description:"Strong foundation for JEE/NEET preparation",priority:"High",culturalContext:"Engineering/Medical career aspirations"},{title:"Multilingual Advantage",description:"Can leverage multiple language skills",priority:"High",culturalContext:"Diverse linguistic environment"},{title:"Cultural Leadership",description:"Can lead cultural and festival activities",priority:"Medium",culturalContext:"Rich cultural heritage knowledge"}],threats:[{title:"Urban Competition",description:"Intense competition from metro city students",priority:"High",culturalContext:"Resource disparity between regions"},{title:"Digital Divide",description:"Limited access to advanced technology",priority:"Medium",culturalContext:"Infrastructure limitations"},{title:"Career Pressure",description:"Family expectations for traditional careers",priority:"Medium",culturalContext:"Societal career preferences"}]}),c({name:"Arjun Sharma",class:"Class X-A",board:e,rollNumber:"CB2024001"}),i(!1)})()},[t,e]);const u=[{type:"strengths",title:"Strengths (शक्तियाँ)",color:n.palette.success.main,icon:He,pattern:"lotus",items:a.strengths||[]},{type:"weaknesses",title:"Weaknesses (कमजोरियाँ)",color:n.palette.error.main,icon:ia,pattern:"mandala",items:a.weaknesses||[]},{type:"opportunities",title:"Opportunities (अवसर)",color:n.palette.primary.main,icon:Se,pattern:"paisley",items:a.opportunities||[]},{type:"threats",title:"Threats (चुनौतियाँ)",color:n.palette.warning.main,icon:sa,pattern:"lotus",items:a.threats||[]}];return s.jsxs(y,{sx:{p:{xs:2,md:3}},children:[s.jsxs(y,{sx:{mb:4},children:[s.jsx(b,{variant:"h4",component:"h1",sx:{fontWeight:700,mb:1},children:"Cultural SWOT Analysis"}),l&&s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:2,mb:2},children:[s.jsx(se,{sx:{bgcolor:"primary.main"},children:l.name.charAt(0)}),s.jsxs(y,{children:[s.jsx(b,{variant:"h6",sx:{fontWeight:600},children:l.name}),s.jsxs(b,{variant:"body2",color:"text.secondary",children:[l.class," • ",l.board," Board • Roll: ",l.rollNumber]})]})]}),s.jsx(b,{variant:"body1",color:"text.secondary",children:"Comprehensive analysis with Indian educational and cultural context"})]}),s.jsx(T,{container:!0,spacing:3,children:u.map(d=>s.jsx(T,{item:!0,xs:12,md:6,children:s.jsx(bg,{type:d.type,title:d.title,items:d.items,color:d.color,icon:d.icon,culturalPattern:d.pattern,loading:r})},d.type))})]})};ua.register(fc,da,ha,pc,fa,pa);const jg=({performanceData:t,loading:e})=>{if(e)return s.jsx(y,{sx:{height:300,display:"flex",alignItems:"center",justifyContent:"center"},children:s.jsx(jt,{sx:{width:"80%"}})});const n={labels:["Math","English","Science","Geography","History","Art","PE"],datasets:[{label:"Student Performance",data:[87,92,95,75,78,96,72],backgroundColor:"rgba(54, 162, 235, 0.2)",borderColor:"rgba(54, 162, 235, 1)",borderWidth:2,pointBackgroundColor:"rgba(54, 162, 235, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(54, 162, 235, 1)"},{label:"Class Average",data:[75,78,80,72,74,82,76],backgroundColor:"rgba(255, 99, 132, 0.2)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:2,pointBackgroundColor:"rgba(255, 99, 132, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(255, 99, 132, 1)"}]},r={responsive:!0,maintainAspectRatio:!1,scales:{r:{angleLines:{display:!0},suggestedMin:0,suggestedMax:100,ticks:{stepSize:20}}},plugins:{legend:{position:"bottom"}}};return s.jsx(y,{sx:{height:300},children:s.jsx(mc,{data:n,options:r})})},an=({title:t,items:e,color:n,icon:r})=>(q(),s.jsx(K,{sx:{height:"100%",border:`2px solid ${n}`},children:s.jsxs(ie,{children:[s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:1,mb:2},children:[s.jsx(r,{sx:{color:n}}),s.jsx(b,{variant:"h6",sx:{fontWeight:600,color:n},children:t})]}),s.jsx(xt,{dense:!0,children:e.map((i,a)=>s.jsx(Ce,{sx:{px:0},children:s.jsx(Fe,{primary:s.jsxs(b,{variant:"body2",sx:{fontWeight:500},children:["• ",i]})})},a))})]})})),Sg=({attendanceData:t,loading:e})=>{if(e)return s.jsx(y,{sx:{height:200,display:"flex",alignItems:"center",justifyContent:"center"},children:s.jsx(jt,{sx:{width:"80%"}})});const n=Array.from({length:30},(i,a)=>({day:a+1,status:Math.random()>.05?"present":Math.random()>.5?"absent":"late"})),r=i=>{switch(i){case"present":return"#4CAF50";case"absent":return"#F44336";case"late":return"#FF9800";default:return"#E0E0E0"}};return s.jsxs(y,{children:[s.jsx(b,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600},children:"Attendance"}),s.jsx(y,{sx:{display:"grid",gridTemplateColumns:"repeat(7, 1fr)",gap:1,mb:2},children:n.map(i=>s.jsx(zt,{title:`Day ${i.day}: ${i.status}`,children:s.jsx(y,{sx:{width:32,height:32,borderRadius:1,backgroundColor:r(i.status),display:"flex",alignItems:"center",justifyContent:"center",fontSize:"0.75rem",color:"white",fontWeight:500,cursor:"pointer"},children:i.day})},i.day))}),s.jsxs(y,{sx:{display:"flex",flexDirection:"column",gap:1},children:[s.jsxs(b,{variant:"body2",children:[s.jsx("strong",{children:"Present:"})," 43 days (95.6%)"]}),s.jsxs(b,{variant:"body2",children:[s.jsx("strong",{children:"Absent:"})," 2 days"]}),s.jsxs(b,{variant:"body2",children:[s.jsx("strong",{children:"Tardy:"})," 1 day"]})]})]})},wg=({behaviorData:t,loading:e})=>{if(e)return s.jsx(y,{sx:{height:200,display:"flex",alignItems:"center",justifyContent:"center"},children:s.jsx(jt,{sx:{width:"80%"}})});const n=[{date:5,type:"positive",description:"Helped new student"},{date:10,type:"positive",description:"Excellent project presentation"},{date:15,type:"negative",description:"Late to class"},{date:20,type:"positive",description:"Volunteered for cleanup"}];return s.jsxs(y,{children:[s.jsx(b,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600},children:"Behavior"}),s.jsxs(y,{sx:{position:"relative",height:60,mb:2},children:[s.jsx(y,{sx:{position:"absolute",top:"50%",left:0,right:0,height:2,backgroundColor:"#E0E0E0",transform:"translateY(-50%)"}}),n.map((r,i)=>s.jsx(zt,{title:r.description,children:s.jsx(y,{sx:{position:"absolute",left:`${r.date/30*100}%`,top:"50%",transform:"translate(-50%, -50%)",width:16,height:16,borderRadius:"50%",backgroundColor:r.type==="positive"?"#4CAF50":"#F44336",cursor:"pointer","&:hover":{transform:"translate(-50%, -50%) scale(1.2)"},transition:"transform 0.2s ease-in-out"}})},i)),s.jsxs(y,{sx:{display:"flex",justifyContent:"space-between",mt:3},children:[s.jsx(b,{variant:"caption",children:"1"}),s.jsx(b,{variant:"caption",children:"10"}),s.jsx(b,{variant:"caption",children:"20"}),s.jsx(b,{variant:"caption",children:"30"})]})]}),s.jsxs(y,{sx:{display:"flex",flexDirection:"column",gap:1},children:[s.jsxs(b,{variant:"body2",children:[s.jsx("strong",{children:"Positive Incidents:"})," 3"]}),s.jsxs(b,{variant:"body2",children:[s.jsx("strong",{children:"Negative Incidents:"})," 1"]}),s.jsxs(b,{variant:"body2",children:[s.jsx("strong",{children:"Trend:"})," ",s.jsx("span",{style:{color:"#4CAF50"},children:"Improving"})]})]})]})},Cg=({activitiesData:t,loading:e})=>{if(e)return s.jsx(y,{sx:{height:150,display:"flex",alignItems:"center",justifyContent:"center"},children:s.jsx(jt,{sx:{width:"80%"}})});const n=[{name:"Chess Club",hours:2,attendance:100},{name:"School Newspaper",hours:3,attendance:92}];return s.jsxs(y,{children:[s.jsx(b,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600},children:"Extracurricular Activities"}),s.jsx(pe,{spacing:2,children:n.map((r,i)=>s.jsx(K,{variant:"outlined",children:s.jsxs(ie,{sx:{py:2},children:[s.jsx(b,{variant:"body1",sx:{fontWeight:500},children:r.name}),s.jsxs(b,{variant:"body2",color:"text.secondary",children:[r.hours," hrs/week, ",r.attendance,"% attendance"]})]})},i))})]})},Pg=({recommendations:t,loading:e})=>{if(e)return s.jsx(y,{sx:{height:150,display:"flex",alignItems:"center",justifyContent:"center"},children:s.jsx(jt,{sx:{width:"80%"}})});const n=["Consider Math Olympiad to strengthen skills","Monitor History performance - offer additional resources","Encourage consistent PE participation"];return s.jsx(K,{children:s.jsxs(ie,{children:[s.jsx(b,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600},children:"Recommendations"}),s.jsx(xt,{children:n.map((r,i)=>s.jsx(Ce,{sx:{px:0},children:s.jsx(Fe,{primary:s.jsxs(b,{variant:"body2",children:["• ",r]})})},i))})]})})},Ag=()=>{const{t}=ye(["swot","common"]);q();const e=ge(),{studentId:n}=Jc(),[r,i]=m.useState(!0),[a,o]=m.useState({name:"Jane Doe",grade:9,id:"STU12345",quarter:"Q1 2024-2025",gpa:3.7,classRank:8,totalStudents:28,subjects:[{name:"Math",grade:"B+",score:87},{name:"English",grade:"A-",score:92},{name:"Science",grade:"A",score:95},{name:"Geography",grade:"C",score:75},{name:"History",grade:"C+",score:78},{name:"Art",grade:"A",score:96},{name:"PE",grade:"C-",score:72}],swotData:{strengths:["Math","Science","Art"],weaknesses:["Geography","History","PE"],opportunities:["Join Science Club","Math tutoring"],threats:["Attendance pattern in History class","Declining PE scores"]}});m.useEffect(()=>{(async()=>{i(!0),await new Promise(u=>setTimeout(u,1500)),i(!1)})()},[n]);const l=()=>{e("/dashboard/students")};return s.jsxs(y,{sx:{p:3},children:[s.jsxs(y,{sx:{mb:3},children:[s.jsxs(y,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:2},children:[s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:2},children:[s.jsx(le,{onClick:l,children:s.jsx(Pn,{})}),s.jsxs(b,{variant:"h4",sx:{fontWeight:700},children:["Student: ",a.name]})]}),s.jsx(y,{sx:{display:"flex",gap:1,alignItems:"center"},children:s.jsx(le,{children:s.jsx(Rr,{})})})]}),s.jsxs(y,{sx:{display:"flex",gap:2,alignItems:"center",mb:2},children:[s.jsx(re,{label:`Grade: ${a.grade}`,color:"primary"}),s.jsx(re,{label:`ID: ${a.id}`,variant:"outlined"}),s.jsx(re,{label:a.quarter,variant:"outlined"})]})]}),s.jsxs(T,{container:!0,spacing:3,children:[s.jsx(T,{item:!0,xs:12,md:6,children:s.jsx(K,{sx:{mb:3},children:s.jsxs(ie,{children:[s.jsx(b,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600},children:"Academic Performance"}),s.jsx(jg,{loading:r}),s.jsxs(y,{sx:{mt:3},children:[a.subjects.map((c,u)=>s.jsxs(y,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[s.jsxs(b,{variant:"body2",children:[c.name,":"]}),s.jsxs(b,{variant:"body2",sx:{fontWeight:500},children:[c.grade," (",c.score,"%)"]})]},u)),s.jsx(Or,{sx:{my:2}}),s.jsxs(y,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[s.jsx(b,{variant:"body1",sx:{fontWeight:600},children:"GPA:"}),s.jsx(b,{variant:"body1",sx:{fontWeight:600},children:a.gpa})]}),s.jsxs(y,{sx:{display:"flex",justifyContent:"space-between"},children:[s.jsx(b,{variant:"body1",sx:{fontWeight:600},children:"Class Rank:"}),s.jsxs(b,{variant:"body1",sx:{fontWeight:600},children:[a.classRank,"/",a.totalStudents]})]})]})]})})}),s.jsx(T,{item:!0,xs:12,md:6,children:s.jsx(K,{sx:{mb:3},children:s.jsxs(ie,{children:[s.jsx(b,{variant:"h6",gutterBottom:!0,sx:{fontWeight:600},children:"SWOT Analysis"}),s.jsxs(T,{container:!0,spacing:2,children:[s.jsx(T,{item:!0,xs:6,children:s.jsx(an,{title:"Strengths",items:a.swotData.strengths,color:"#4CAF50",icon:et})}),s.jsx(T,{item:!0,xs:6,children:s.jsx(an,{title:"Weaknesses",items:a.swotData.weaknesses,color:"#FF5722",icon:ia})}),s.jsx(T,{item:!0,xs:6,children:s.jsx(an,{title:"Opportunities",items:a.swotData.opportunities,color:"#2196F3",icon:Se})}),s.jsx(T,{item:!0,xs:6,children:s.jsx(an,{title:"Threats",items:a.swotData.threats,color:"#FF9800",icon:ra})})]})]})})}),s.jsx(T,{item:!0,xs:12,md:6,children:s.jsx(K,{sx:{height:"100%"},children:s.jsx(ie,{children:s.jsx(Sg,{loading:r})})})}),s.jsx(T,{item:!0,xs:12,md:6,children:s.jsx(K,{sx:{height:"100%"},children:s.jsx(ie,{children:s.jsx(wg,{loading:r})})})}),s.jsx(T,{item:!0,xs:12,md:6,children:s.jsx(K,{children:s.jsx(ie,{children:s.jsx(Cg,{loading:r})})})}),s.jsx(T,{item:!0,xs:12,md:6,children:s.jsx(Pg,{loading:r})})]}),s.jsxs(y,{sx:{mt:3,display:"flex",gap:2,justifyContent:"flex-end"},children:[s.jsx(W,{variant:"outlined",startIcon:s.jsx(Il,{}),sx:{textTransform:"none"},children:"Download Report"}),s.jsx(W,{variant:"outlined",startIcon:s.jsx(Dl,{}),sx:{textTransform:"none"},children:"Share"}),s.jsx(W,{variant:"outlined",startIcon:s.jsx(Bl,{}),sx:{textTransform:"none"},children:"Print"}),s.jsx(W,{variant:"contained",startIcon:s.jsx(He,{}),sx:{textTransform:"none"},children:"Generate New Analysis"})]})]})},Dn=({threshold:t=.1,root:e=null,rootMargin:n="0px",once:r=!0,enabled:i=!0})=>{const[a,o]=m.useState(!1),[l,c]=m.useState(!1),u=m.useRef(null),d=m.useRef(null);return m.useEffect(()=>{if(!i)return;const h=u.current;if(!h)return;if(!window.IntersectionObserver){o(!0),c(!0);return}const f=p=>{const[g]=p,x=g.isIntersecting;o(x),x&&!l&&(c(!0),r&&d.current&&d.current.disconnect())};return d.current=new IntersectionObserver(f,{threshold:Array.isArray(t)?t:[t],root:e,rootMargin:n}),d.current.observe(h),()=>{d.current&&d.current.disconnect()}},[t,e,n,r,i,l]),[u,r?l:a,l]},Tg=()=>{const t=ge(),e=q(),n=Lr(e.breakpoints.down("md")),[r,i]=m.useState(!1),a=()=>{i(!r)},o=[{label:"Home",path:"/"},{label:"About",path:"/about"},{label:"Features",path:"/features"},{label:"Contact",path:"/contact"}],l=s.jsxs(y,{onClick:a,sx:{textAlign:"center"},children:[s.jsx(b,{variant:"h6",sx:{my:2,fontWeight:700},children:"VidyaMitra"}),s.jsxs(xt,{children:[o.map(c=>s.jsx(Ce,{disablePadding:!0,children:s.jsx(W,{fullWidth:!0,onClick:()=>t(c.path),sx:{textAlign:"center",py:1},children:s.jsx(Fe,{primary:c.label})})},c.label)),s.jsx(Ce,{disablePadding:!0,children:s.jsx(W,{fullWidth:!0,variant:"contained",onClick:()=>t("/login"),sx:{m:2},children:"Login"})})]})]});return s.jsxs(s.Fragment,{children:[s.jsx(kr,{position:"fixed",sx:{bgcolor:"rgba(255, 255, 255, 0.95)",backdropFilter:"blur(10px)",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",color:"text.primary"},children:s.jsxs(Ht,{children:[s.jsx(b,{variant:"h6",component:"div",sx:{flexGrow:1,fontWeight:700,color:e.palette.primary.main,cursor:"pointer"},onClick:()=>t("/"),children:"VidyaMitra"}),n?s.jsx(le,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:a,children:s.jsx(Vl,{})}):s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:2},children:[o.map(c=>s.jsx(W,{onClick:()=>t(c.path),sx:{color:"text.primary",fontWeight:500,"&:hover":{bgcolor:"rgba(46, 91, 168, 0.1)"}},children:c.label},c.label)),s.jsx(W,{variant:"contained",onClick:()=>t("/login"),sx:{ml:2,borderRadius:2,px:3},children:"Login"})]})]})}),s.jsx(aa,{variant:"temporary",open:r,onClose:a,ModalProps:{keepMounted:!0},sx:{display:{xs:"block",md:"none"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:240}},children:l})]})},Eg=()=>{const t=ge(),e=q(),[n,r]=Dn({threshold:.1});return s.jsx(y,{ref:n,sx:{minHeight:"100vh",background:`linear-gradient(135deg, ${e.palette.primary.main} 0%, ${e.palette.secondary.main} 100%)`,display:"flex",alignItems:"center",position:"relative",overflow:"hidden","&::before":{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,background:'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")'}},children:s.jsx(Q,{maxWidth:"lg",sx:{position:"relative",zIndex:1},children:s.jsxs(T,{container:!0,spacing:4,alignItems:"center",children:[s.jsx(T,{item:!0,xs:12,md:6,children:s.jsx(fe,{in:r,timeout:1e3,children:s.jsxs(y,{children:[s.jsx(b,{variant:"h1",sx:{color:"white",fontWeight:800,mb:3,textShadow:"0 2px 4px rgba(0,0,0,0.3)",fontSize:{xs:"2.5rem",md:"3.5rem",lg:"4rem"}},children:"VidyaMitra"}),s.jsx(b,{variant:"h4",sx:{color:"rgba(255,255,255,0.9)",mb:3,fontWeight:400,fontSize:{xs:"1.2rem",md:"1.5rem"}},children:"Student SWOT Analysis Platform"}),s.jsx(b,{variant:"h6",sx:{color:"rgba(255,255,255,0.8)",mb:4,lineHeight:1.6,fontSize:{xs:"1rem",md:"1.1rem"}},children:"Empowering Indian education through intelligent student analysis. Supporting CBSE, ICSE, and State boards with AI-powered insights."}),s.jsxs(y,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:[s.jsx(W,{variant:"contained",size:"large",onClick:()=>t("/login"),sx:{bgcolor:"white",color:e.palette.primary.main,px:4,py:1.5,fontSize:"1.1rem",fontWeight:600,borderRadius:3,boxShadow:"0 4px 12px rgba(0,0,0,0.2)","&:hover":{bgcolor:"rgba(255,255,255,0.9)",transform:"translateY(-2px)",boxShadow:"0 6px 16px rgba(0,0,0,0.3)"}},endIcon:s.jsx(Mr,{}),children:"Get Started"}),s.jsx(W,{variant:"outlined",size:"large",onClick:()=>t("/features"),sx:{color:"white",borderColor:"white",px:4,py:1.5,fontSize:"1.1rem",fontWeight:600,borderRadius:3,"&:hover":{bgcolor:"rgba(255,255,255,0.1)",borderColor:"white",transform:"translateY(-2px)"}},children:"Learn More"})]})]})})}),s.jsx(T,{item:!0,xs:12,md:6,children:s.jsx(oa,{direction:"left",in:r,timeout:1200,children:s.jsx(y,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:{xs:300,md:400}},children:s.jsx(Be,{elevation:8,sx:{p:4,borderRadius:4,bgcolor:"rgba(255,255,255,0.95)",backdropFilter:"blur(10px)",maxWidth:400,width:"100%"},children:s.jsxs(y,{sx:{textAlign:"center"},children:[s.jsx(se,{sx:{width:80,height:80,bgcolor:e.palette.secondary.main,mx:"auto",mb:2},children:s.jsx(je,{sx:{fontSize:40}})}),s.jsx(b,{variant:"h6",sx:{mb:2,fontWeight:600},children:"Comprehensive Student Analysis"}),s.jsx(b,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"AI-powered SWOT analysis tailored for Indian educational boards"}),s.jsxs(y,{sx:{display:"flex",justifyContent:"space-around"},children:[s.jsxs(y,{sx:{textAlign:"center"},children:[s.jsx(b,{variant:"h4",color:"primary",sx:{fontWeight:700},children:"1000+"}),s.jsx(b,{variant:"caption",children:"Students"})]}),s.jsxs(y,{sx:{textAlign:"center"},children:[s.jsx(b,{variant:"h4",color:"secondary",sx:{fontWeight:700},children:"50+"}),s.jsx(b,{variant:"caption",children:"Schools"})]}),s.jsxs(y,{sx:{textAlign:"center"},children:[s.jsx(b,{variant:"h4",color:"success.main",sx:{fontWeight:700},children:"95%"}),s.jsx(b,{variant:"caption",children:"Satisfaction"})]})]})]})})})})})]})})})},Fg=()=>{const t=q(),[e,n]=Dn({threshold:.1}),r=[{icon:gt,title:"AI-Powered SWOT Analysis",description:"Comprehensive analysis of student strengths, weaknesses, opportunities, and threats using advanced AI algorithms.",color:t.palette.primary.main},{icon:je,title:"Multi-Board Support",description:"Full support for CBSE, ICSE, and all major State boards with curriculum-specific insights.",color:t.palette.secondary.main},{icon:Se,title:"Performance Tracking",description:"Real-time monitoring of student progress with detailed analytics and trend analysis.",color:t.palette.success.main},{icon:Cn,title:"Multi-Stakeholder Access",description:"Dedicated interfaces for teachers, parents, administrators, and students with role-based permissions.",color:t.palette.warning.main}];return s.jsx(y,{ref:e,sx:{py:8,bgcolor:"background.default"},children:s.jsxs(Q,{maxWidth:"lg",children:[s.jsx(fe,{in:n,timeout:800,children:s.jsxs(y,{sx:{textAlign:"center",mb:6},children:[s.jsx(b,{variant:"h3",sx:{fontWeight:700,mb:2,color:"text.primary"},children:"Powerful Features for Indian Education"}),s.jsx(b,{variant:"h6",color:"text.secondary",sx:{maxWidth:600,mx:"auto"},children:"Designed specifically for the Indian education system with cultural sensitivity and local requirements in mind."})]})}),s.jsx(T,{container:!0,spacing:4,children:r.map((i,a)=>s.jsx(T,{item:!0,xs:12,sm:6,md:3,children:s.jsx(la,{in:n,timeout:800+a*200,children:s.jsxs(K,{sx:{height:"100%",textAlign:"center",p:3,transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-8px)",boxShadow:t.shadows[8]}},children:[s.jsx(se,{sx:{width:64,height:64,bgcolor:i.color,mx:"auto",mb:2},children:s.jsx(i.icon,{sx:{fontSize:32}})}),s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:2},children:i.title}),s.jsx(b,{variant:"body2",color:"text.secondary",children:i.description})]})})},a))})]})})},kg=()=>{const[t,e]=Dn({threshold:.1});return s.jsx(y,{ref:t,sx:{py:8,bgcolor:"primary.main",color:"white"},children:s.jsx(Q,{maxWidth:"lg",children:s.jsxs(T,{container:!0,spacing:6,alignItems:"center",children:[s.jsx(T,{item:!0,xs:12,md:6,children:s.jsx(fe,{in:e,timeout:800,children:s.jsxs(y,{children:[s.jsx(b,{variant:"h3",sx:{fontWeight:700,mb:3},children:"Our Vision"}),s.jsx(b,{variant:"h6",sx:{mb:4,lineHeight:1.6,opacity:.9},children:"To revolutionize Indian education by providing intelligent, data-driven insights that help every student reach their full potential while respecting cultural values and educational traditions."}),s.jsxs(y,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:[s.jsx(re,{label:"AI-Powered",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}}),s.jsx(re,{label:"Culturally Sensitive",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}}),s.jsx(re,{label:"Student-Centric",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}})]})]})})}),s.jsx(T,{item:!0,xs:12,md:6,children:s.jsx(oa,{direction:"left",in:e,timeout:1e3,children:s.jsxs(y,{children:[s.jsx(b,{variant:"h3",sx:{fontWeight:700,mb:3},children:"Our Mission"}),s.jsx(b,{variant:"h6",sx:{mb:4,lineHeight:1.6,opacity:.9},children:"Empowering teachers, parents, and administrators with comprehensive SWOT analysis tools that provide actionable insights for student development across all Indian educational boards."}),s.jsxs(y,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:[s.jsx(re,{label:"Multi-Board Support",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}}),s.jsx(re,{label:"Real-time Analytics",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}}),s.jsx(re,{label:"Collaborative Platform",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}})]})]})})})]})})})},Rg=()=>{const t=q(),[e,n]=Dn({threshold:.1}),r=[{title:"For Teachers",description:"Generate comprehensive SWOT reports, track student progress, and identify areas for improvement with AI-powered insights.",features:["Automated SWOT Generation","Progress Tracking","Parent Communication","Class Analytics"],color:t.palette.primary.main,icon:"👩‍🏫"},{title:"For Parents",description:"Stay informed about your child's academic journey with detailed reports and actionable recommendations.",features:["Student Reports","Performance Trends","Improvement Suggestions","Meeting Scheduling"],color:t.palette.secondary.main,icon:"👨‍👩‍👧‍👦"},{title:"For Administrators",description:"Manage school-wide analytics, monitor teacher effectiveness, and make data-driven decisions.",features:["School Analytics","Teacher Performance","Resource Planning","Compliance Reports"],color:t.palette.success.main,icon:"🏫"}];return s.jsx(y,{ref:e,sx:{py:8,bgcolor:"background.paper"},children:s.jsxs(Q,{maxWidth:"lg",children:[s.jsx(fe,{in:n,timeout:800,children:s.jsxs(y,{sx:{textAlign:"center",mb:6},children:[s.jsx(b,{variant:"h3",sx:{fontWeight:700,mb:2,color:"text.primary"},children:"Designed for Every Stakeholder"}),s.jsx(b,{variant:"h6",color:"text.secondary",sx:{maxWidth:600,mx:"auto"},children:"VidyaMitra serves the entire educational ecosystem with tailored solutions for each user type."})]})}),s.jsx(T,{container:!0,spacing:4,children:r.map((i,a)=>s.jsx(T,{item:!0,xs:12,md:4,children:s.jsx(la,{in:n,timeout:800+a*200,children:s.jsxs(K,{sx:{height:"100%",p:4,transition:"all 0.3s ease-in-out",border:"2px solid transparent","&:hover":{transform:"translateY(-8px)",boxShadow:t.shadows[12],borderColor:i.color}},children:[s.jsxs(y,{sx:{textAlign:"center",mb:3},children:[s.jsx(b,{variant:"h2",sx:{mb:1},children:i.icon}),s.jsx(b,{variant:"h5",sx:{fontWeight:600,color:i.color},children:i.title})]}),s.jsx(b,{variant:"body1",sx:{mb:3,lineHeight:1.6},children:i.description}),s.jsx(y,{children:i.features.map((o,l)=>s.jsxs(y,{sx:{display:"flex",alignItems:"center",mb:1},children:[s.jsx(Wl,{sx:{fontSize:16,color:i.color,mr:1}}),s.jsx(b,{variant:"body2",children:o})]},l))})]})})},a))})]})})},Og=()=>{const t=ge();return s.jsx(y,{sx:{bgcolor:"grey.900",color:"white",py:6},children:s.jsxs(Q,{maxWidth:"lg",children:[s.jsxs(T,{container:!0,spacing:4,children:[s.jsxs(T,{item:!0,xs:12,md:4,children:[s.jsx(b,{variant:"h6",sx:{fontWeight:700,mb:2},children:"VidyaMitra"}),s.jsx(b,{variant:"body2",sx:{mb:2,opacity:.8},children:"Empowering Indian education through intelligent student analysis. Supporting CBSE, ICSE, and State boards with AI-powered insights."}),s.jsxs(y,{sx:{display:"flex",gap:1},children:[s.jsx(re,{label:"AI-Powered",size:"small",sx:{bgcolor:"rgba(255,255,255,0.1)",color:"white"}}),s.jsx(re,{label:"Multi-Board",size:"small",sx:{bgcolor:"rgba(255,255,255,0.1)",color:"white"}})]})]}),s.jsxs(T,{item:!0,xs:12,sm:6,md:2,children:[s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Platform"}),s.jsxs(y,{sx:{display:"flex",flexDirection:"column",gap:1},children:[s.jsx(W,{color:"inherit",sx:{justifyContent:"flex-start",p:0},onClick:()=>t("/features"),children:"Features"}),s.jsx(W,{color:"inherit",sx:{justifyContent:"flex-start",p:0},onClick:()=>t("/about"),children:"About"}),s.jsx(W,{color:"inherit",sx:{justifyContent:"flex-start",p:0},onClick:()=>t("/login"),children:"Login"})]})]}),s.jsxs(T,{item:!0,xs:12,sm:6,md:3,children:[s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Support"}),s.jsxs(y,{sx:{display:"flex",flexDirection:"column",gap:1},children:[s.jsx(W,{color:"inherit",sx:{justifyContent:"flex-start",p:0},onClick:()=>t("/contact"),children:"Contact Us"}),s.jsx(W,{color:"inherit",sx:{justifyContent:"flex-start",p:0},children:"Help Center"}),s.jsx(W,{color:"inherit",sx:{justifyContent:"flex-start",p:0},children:"Documentation"})]})]}),s.jsxs(T,{item:!0,xs:12,md:3,children:[s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Contact Info"}),s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:1,mb:1},children:[s.jsx(Kt,{sx:{fontSize:16}}),s.jsx(b,{variant:"body2",children:"<EMAIL>"})]}),s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:1,mb:1},children:[s.jsx(Ir,{sx:{fontSize:16}}),s.jsx(b,{variant:"body2",children:"+91 98765 43210"})]}),s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:1},children:[s.jsx(Dr,{sx:{fontSize:16}}),s.jsx(b,{variant:"body2",children:"Mumbai, India"})]})]})]}),s.jsx(y,{sx:{borderTop:"1px solid rgba(255,255,255,0.1)",mt:4,pt:3,textAlign:"center"},children:s.jsx(b,{variant:"body2",sx:{opacity:.7},children:"© 2024 VidyaMitra. All rights reserved. | Privacy Policy | Terms of Service"})})]})})};function Mg(){return s.jsxs(y,{children:[s.jsx(Tg,{}),s.jsxs(y,{sx:{pt:8},children:[" ",s.jsx(Eg,{}),s.jsx(Fg,{}),s.jsx(kg,{}),s.jsx(Rg,{})]}),s.jsx(Og,{})]})}const Lg=()=>{const{t}=ye("common"),e=ge(),n=q(),r=[{icon:je,title:"Educational Excellence",description:"Committed to enhancing the quality of education through data-driven insights and personalized learning approaches.",color:n.palette.primary.main},{icon:He,title:"AI-Powered Innovation",description:"Leveraging cutting-edge artificial intelligence to provide meaningful and actionable student analysis.",color:n.palette.secondary.main},{icon:Nl,title:"Collaborative Approach",description:"Bringing together teachers, parents, and administrators in a unified platform for student success.",color:n.palette.success.main},{icon:Se,title:"Continuous Improvement",description:"Constantly evolving our platform based on user feedback and educational best practices.",color:n.palette.warning.main}],i=[{number:"1000+",label:"Students Analyzed"},{number:"50+",label:"Partner Schools"},{number:"95%",label:"User Satisfaction"},{number:"3",label:"Educational Boards"}];return s.jsxs(y,{children:[s.jsx(y,{sx:{bgcolor:"primary.main",color:"white",py:8},children:s.jsxs(Q,{maxWidth:"lg",children:[s.jsx(W,{startIcon:s.jsx(Pn,{}),onClick:()=>e("/"),sx:{color:"white",mb:4},children:"Back to Home"}),s.jsx(b,{variant:"h2",sx:{fontWeight:700,mb:3},children:"About VidyaMitra"}),s.jsx(b,{variant:"h5",sx:{opacity:.9,maxWidth:800},children:"Revolutionizing Indian education through intelligent student analysis and comprehensive SWOT insights tailored for CBSE, ICSE, and State boards."})]})}),s.jsx(y,{sx:{py:8,bgcolor:"background.default"},children:s.jsx(Q,{maxWidth:"lg",children:s.jsxs(T,{container:!0,spacing:6,children:[s.jsx(T,{item:!0,xs:12,md:6,children:s.jsx(fe,{in:!0,timeout:800,children:s.jsxs(Be,{elevation:2,sx:{p:4,height:"100%"},children:[s.jsx(b,{variant:"h4",sx:{fontWeight:600,mb:3,color:"primary.main"},children:"Our Mission"}),s.jsx(b,{variant:"body1",sx:{lineHeight:1.8,mb:3},children:"To empower educators, parents, and administrators with comprehensive SWOT analysis tools that provide actionable insights for student development across all Indian educational boards."}),s.jsx(b,{variant:"body1",sx:{lineHeight:1.8},children:"We believe every student has unique strengths and potential. Our platform helps identify these strengths while addressing weaknesses through personalized recommendations and data-driven strategies."})]})})}),s.jsx(T,{item:!0,xs:12,md:6,children:s.jsx(fe,{in:!0,timeout:1e3,children:s.jsxs(Be,{elevation:2,sx:{p:4,height:"100%"},children:[s.jsx(b,{variant:"h4",sx:{fontWeight:600,mb:3,color:"secondary.main"},children:"Our Vision"}),s.jsx(b,{variant:"body1",sx:{lineHeight:1.8,mb:3},children:"To become the leading platform for student analysis in India, helping create a generation of well-rounded individuals who are prepared for the challenges of the 21st century."}),s.jsx(b,{variant:"body1",sx:{lineHeight:1.8},children:"We envision a future where every student receives personalized attention and guidance, leading to improved academic outcomes and holistic development."})]})})})]})})}),s.jsx(y,{sx:{py:8,bgcolor:"background.paper"},children:s.jsxs(Q,{maxWidth:"lg",children:[s.jsx(b,{variant:"h3",sx:{textAlign:"center",fontWeight:700,mb:6},children:"Our Core Values"}),s.jsx(T,{container:!0,spacing:4,children:r.map((a,o)=>s.jsx(T,{item:!0,xs:12,sm:6,md:3,children:s.jsx(fe,{in:!0,timeout:800+o*200,children:s.jsxs(K,{sx:{height:"100%",textAlign:"center",p:3,transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-8px)",boxShadow:n.shadows[8]}},children:[s.jsx(se,{sx:{width:64,height:64,bgcolor:a.color,mx:"auto",mb:2},children:s.jsx(a.icon,{sx:{fontSize:32}})}),s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:2},children:a.title}),s.jsx(b,{variant:"body2",color:"text.secondary",children:a.description})]})})},o))})]})}),s.jsx(y,{sx:{py:8,bgcolor:"primary.main",color:"white"},children:s.jsxs(Q,{maxWidth:"lg",children:[s.jsx(b,{variant:"h3",sx:{textAlign:"center",fontWeight:700,mb:6},children:"Our Impact"}),s.jsx(T,{container:!0,spacing:4,children:i.map((a,o)=>s.jsx(T,{item:!0,xs:6,md:3,children:s.jsx(fe,{in:!0,timeout:800+o*200,children:s.jsxs(y,{sx:{textAlign:"center"},children:[s.jsx(b,{variant:"h2",sx:{fontWeight:800,mb:1},children:a.number}),s.jsx(b,{variant:"h6",sx:{opacity:.9},children:a.label})]})})},o))})]})}),s.jsx(y,{sx:{py:8,bgcolor:"background.default",textAlign:"center"},children:s.jsxs(Q,{maxWidth:"md",children:[s.jsx(b,{variant:"h3",sx:{fontWeight:700,mb:3},children:"Ready to Transform Education?"}),s.jsx(b,{variant:"h6",color:"text.secondary",sx:{mb:4},children:"Join thousands of educators who are already using VidyaMitra to enhance student outcomes."}),s.jsxs(y,{sx:{display:"flex",gap:2,justifyContent:"center",flexWrap:"wrap"},children:[s.jsx(W,{variant:"contained",size:"large",onClick:()=>e("/login"),sx:{px:4,py:1.5},children:"Get Started Today"}),s.jsx(W,{variant:"outlined",size:"large",onClick:()=>e("/contact"),sx:{px:4,py:1.5},children:"Contact Us"})]})]})})]})},Ig=()=>{const{t}=ye("common"),e=ge(),n=q(),r=[{icon:gt,title:"AI-Powered SWOT Analysis",description:"Advanced artificial intelligence algorithms analyze student data to generate comprehensive SWOT reports.",features:["Automated strength identification","Weakness pattern recognition","Opportunity mapping","Threat assessment","Personalized recommendations"],color:n.palette.primary.main},{icon:je,title:"Multi-Board Support",description:"Complete support for CBSE, ICSE, and all major State educational boards across India.",features:["CBSE curriculum alignment","ICSE standards compliance","State board customization","Regional language support","Board-specific analytics"],color:n.palette.secondary.main},{icon:Se,title:"Performance Tracking",description:"Real-time monitoring and analysis of student academic and behavioral performance.",features:["Grade tracking","Attendance monitoring","Behavioral analysis","Progress visualization","Trend identification"],color:n.palette.success.main},{icon:Cn,title:"Multi-Stakeholder Platform",description:"Dedicated interfaces for teachers, parents, administrators, and students.",features:["Teacher dashboard","Parent portal","Admin analytics","Student interface","Role-based permissions"],color:n.palette.warning.main}],i=[{icon:$l,title:"Multi-Language Support",description:"Interface available in English, Hindi, and major regional languages."},{icon:sa,title:"Data Security",description:"Enterprise-grade security with FERPA compliance and data encryption."},{icon:Ul,title:"Cloud Integration",description:"Seamless cloud synchronization with offline capabilities."},{icon:zl,title:"Advanced Analytics",description:"Comprehensive reporting with exportable insights and visualizations."},{icon:He,title:"Behavioral Insights",description:"AI-driven analysis of student behavior patterns and social interactions."},{icon:ca,title:"Intuitive Dashboards",description:"User-friendly interfaces designed specifically for Indian educational context."}];return s.jsxs(y,{children:[s.jsx(y,{sx:{bgcolor:"primary.main",color:"white",py:8},children:s.jsxs(Q,{maxWidth:"lg",children:[s.jsx(W,{startIcon:s.jsx(Pn,{}),onClick:()=>e("/"),sx:{color:"white",mb:4},children:"Back to Home"}),s.jsx(b,{variant:"h2",sx:{fontWeight:700,mb:3},children:"Platform Features"}),s.jsx(b,{variant:"h5",sx:{opacity:.9,maxWidth:800},children:"Discover the comprehensive suite of features designed specifically for Indian educational institutions and stakeholders."})]})}),s.jsx(y,{sx:{py:8,bgcolor:"background.default"},children:s.jsxs(Q,{maxWidth:"lg",children:[s.jsx(b,{variant:"h3",sx:{textAlign:"center",fontWeight:700,mb:6},children:"Core Features"}),s.jsx(T,{container:!0,spacing:4,children:r.map((a,o)=>s.jsx(T,{item:!0,xs:12,md:6,children:s.jsx(fe,{in:!0,timeout:800+o*200,children:s.jsxs(K,{sx:{height:"100%",p:3,transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:n.shadows[8]}},children:[s.jsxs(y,{sx:{display:"flex",alignItems:"flex-start",mb:3},children:[s.jsx(se,{sx:{width:56,height:56,bgcolor:a.color,mr:2},children:s.jsx(a.icon,{sx:{fontSize:28}})}),s.jsxs(y,{children:[s.jsx(b,{variant:"h5",sx:{fontWeight:600,mb:1},children:a.title}),s.jsx(b,{variant:"body2",color:"text.secondary",children:a.description})]})]}),s.jsx(xt,{dense:!0,children:a.features.map((l,c)=>s.jsxs(Ce,{sx:{px:0},children:[s.jsx(ut,{sx:{minWidth:32},children:s.jsx(et,{sx:{fontSize:20,color:a.color}})}),s.jsx(Fe,{primary:l,primaryTypographyProps:{variant:"body2"}})]},c))})]})})},o))})]})}),s.jsx(y,{sx:{py:8,bgcolor:"background.paper"},children:s.jsxs(Q,{maxWidth:"lg",children:[s.jsx(b,{variant:"h3",sx:{textAlign:"center",fontWeight:700,mb:6},children:"Additional Capabilities"}),s.jsx(T,{container:!0,spacing:4,children:i.map((a,o)=>s.jsx(T,{item:!0,xs:12,sm:6,md:4,children:s.jsx(fe,{in:!0,timeout:800+o*150,children:s.jsxs(K,{sx:{height:"100%",textAlign:"center",p:3,transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:n.shadows[6]}},children:[s.jsx(se,{sx:{width:64,height:64,bgcolor:"primary.main",mx:"auto",mb:2},children:s.jsx(a.icon,{sx:{fontSize:32}})}),s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:2},children:a.title}),s.jsx(b,{variant:"body2",color:"text.secondary",children:a.description})]})})},o))})]})}),s.jsx(y,{sx:{py:8,bgcolor:"secondary.main",color:"white"},children:s.jsx(Q,{maxWidth:"lg",children:s.jsxs(T,{container:!0,spacing:6,alignItems:"center",children:[s.jsxs(T,{item:!0,xs:12,md:6,children:[s.jsx(b,{variant:"h3",sx:{fontWeight:700,mb:3},children:"Built for Indian Education"}),s.jsx(b,{variant:"h6",sx:{mb:4,opacity:.9},children:"Every feature is designed with the Indian educational context in mind, ensuring cultural sensitivity and local relevance."}),s.jsxs(y,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:[s.jsx(re,{label:"CBSE Aligned",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}}),s.jsx(re,{label:"ICSE Compatible",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}}),s.jsx(re,{label:"State Board Ready",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}}),s.jsx(re,{label:"Multi-Language",sx:{bgcolor:"rgba(255,255,255,0.2)",color:"white"}})]})]}),s.jsx(T,{item:!0,xs:12,md:6,children:s.jsxs(Be,{elevation:4,sx:{p:4},children:[s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Supported Educational Boards"}),s.jsxs(xt,{children:[s.jsxs(Ce,{children:[s.jsx(ut,{children:s.jsx(et,{color:"primary"})}),s.jsx(Fe,{primary:"Central Board of Secondary Education (CBSE)"})]}),s.jsxs(Ce,{children:[s.jsx(ut,{children:s.jsx(et,{color:"primary"})}),s.jsx(Fe,{primary:"Indian Certificate of Secondary Education (ICSE)"})]}),s.jsxs(Ce,{children:[s.jsx(ut,{children:s.jsx(et,{color:"primary"})}),s.jsx(Fe,{primary:"State Education Boards (All Major States)"})]}),s.jsxs(Ce,{children:[s.jsx(ut,{children:s.jsx(et,{color:"primary"})}),s.jsx(Fe,{primary:"International Baccalaureate (IB)"})]})]})]})})]})})}),s.jsx(y,{sx:{py:8,bgcolor:"background.default",textAlign:"center"},children:s.jsxs(Q,{maxWidth:"md",children:[s.jsx(b,{variant:"h3",sx:{fontWeight:700,mb:3},children:"Experience the Power of VidyaMitra"}),s.jsx(b,{variant:"h6",color:"text.secondary",sx:{mb:4},children:"Start your journey towards data-driven education today."}),s.jsxs(y,{sx:{display:"flex",gap:2,justifyContent:"center",flexWrap:"wrap"},children:[s.jsx(W,{variant:"contained",size:"large",onClick:()=>e("/login"),sx:{px:4,py:1.5},children:"Try VidyaMitra"}),s.jsx(W,{variant:"outlined",size:"large",onClick:()=>e("/contact"),sx:{px:4,py:1.5},children:"Request Demo"})]})]})})]})},Dg=()=>{const{t}=ye("common"),e=ge(),n=q(),[r,i]=m.useState({name:"",email:"",organization:"",subject:"",message:""}),[a,o]=m.useState(!1),l=h=>{i({...r,[h.target.name]:h.target.value})},c=h=>{h.preventDefault(),console.log("Form submitted:",r),o(!0),i({name:"",email:"",organization:"",subject:"",message:""})},u=[{icon:Kt,title:"Email Us",primary:"<EMAIL>",secondary:"<EMAIL>",color:n.palette.primary.main},{icon:Ir,title:"Call Us",primary:"+91 98765 43210",secondary:"+91 98765 43211",color:n.palette.secondary.main},{icon:Dr,title:"Visit Us",primary:"Mumbai, Maharashtra",secondary:"India - 400001",color:n.palette.success.main},{icon:Hl,title:"Business Hours",primary:"Mon - Fri: 9:00 AM - 6:00 PM",secondary:"Sat: 10:00 AM - 4:00 PM",color:n.palette.warning.main}],d=[{icon:Kl,title:"Technical Support",description:"Get help with platform usage, troubleshooting, and technical issues.",action:"Get Support"},{icon:Gl,title:"Sales Inquiry",description:"Learn about pricing, features, and how VidyaMitra can benefit your institution.",action:"Contact Sales"},{icon:je,title:"Educational Partnership",description:"Explore partnership opportunities and institutional collaborations.",action:"Partner With Us"}];return s.jsxs(y,{children:[s.jsx(y,{sx:{bgcolor:"primary.main",color:"white",py:8},children:s.jsxs(Q,{maxWidth:"lg",children:[s.jsx(W,{startIcon:s.jsx(Pn,{}),onClick:()=>e("/"),sx:{color:"white",mb:4},children:"Back to Home"}),s.jsx(b,{variant:"h2",sx:{fontWeight:700,mb:3},children:"Contact Us"}),s.jsx(b,{variant:"h5",sx:{opacity:.9,maxWidth:800},children:"We're here to help you transform education. Get in touch with our team for support, sales inquiries, or partnership opportunities."})]})}),s.jsx(y,{sx:{py:8,bgcolor:"background.default"},children:s.jsxs(Q,{maxWidth:"lg",children:[s.jsx(b,{variant:"h3",sx:{textAlign:"center",fontWeight:700,mb:6},children:"Get In Touch"}),s.jsx(T,{container:!0,spacing:4,children:u.map((h,f)=>s.jsx(T,{item:!0,xs:12,sm:6,md:3,children:s.jsx(fe,{in:!0,timeout:800+f*200,children:s.jsxs(K,{sx:{height:"100%",textAlign:"center",p:3,transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-4px)",boxShadow:n.shadows[8]}},children:[s.jsx(se,{sx:{width:64,height:64,bgcolor:h.color,mx:"auto",mb:2},children:s.jsx(h.icon,{sx:{fontSize:32}})}),s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:2},children:h.title}),s.jsx(b,{variant:"body2",sx:{mb:1},children:h.primary}),s.jsx(b,{variant:"body2",color:"text.secondary",children:h.secondary})]})})},f))})]})}),s.jsx(y,{sx:{py:8,bgcolor:"background.paper"},children:s.jsx(Q,{maxWidth:"lg",children:s.jsxs(T,{container:!0,spacing:6,children:[s.jsx(T,{item:!0,xs:12,md:8,children:s.jsxs(Be,{elevation:2,sx:{p:4},children:[s.jsx(b,{variant:"h4",sx:{fontWeight:600,mb:3},children:"Send us a Message"}),s.jsx(y,{component:"form",onSubmit:c,children:s.jsxs(T,{container:!0,spacing:3,children:[s.jsx(T,{item:!0,xs:12,sm:6,children:s.jsx(he,{fullWidth:!0,label:"Full Name",name:"name",value:r.name,onChange:l,required:!0})}),s.jsx(T,{item:!0,xs:12,sm:6,children:s.jsx(he,{fullWidth:!0,label:"Email Address",name:"email",type:"email",value:r.email,onChange:l,required:!0})}),s.jsx(T,{item:!0,xs:12,children:s.jsx(he,{fullWidth:!0,label:"Organization/School",name:"organization",value:r.organization,onChange:l})}),s.jsx(T,{item:!0,xs:12,children:s.jsx(he,{fullWidth:!0,label:"Subject",name:"subject",value:r.subject,onChange:l,required:!0})}),s.jsx(T,{item:!0,xs:12,children:s.jsx(he,{fullWidth:!0,label:"Message",name:"message",multiline:!0,rows:4,value:r.message,onChange:l,required:!0})}),s.jsx(T,{item:!0,xs:12,children:s.jsx(W,{type:"submit",variant:"contained",size:"large",endIcon:s.jsx(_l,{}),sx:{px:4,py:1.5},children:"Send Message"})})]})})]})}),s.jsxs(T,{item:!0,xs:12,md:4,children:[s.jsx(b,{variant:"h5",sx:{fontWeight:600,mb:3},children:"How Can We Help?"}),d.map((h,f)=>s.jsx(fe,{in:!0,timeout:800+f*200,children:s.jsx(K,{sx:{mb:3,p:3},children:s.jsxs(y,{sx:{display:"flex",alignItems:"flex-start",mb:2},children:[s.jsx(se,{sx:{width:48,height:48,bgcolor:"primary.main",mr:2},children:s.jsx(h.icon,{sx:{fontSize:24}})}),s.jsxs(y,{children:[s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:1},children:h.title}),s.jsx(b,{variant:"body2",color:"text.secondary",sx:{mb:2},children:h.description}),s.jsx(W,{variant:"outlined",size:"small",sx:{textTransform:"none"},children:h.action})]})]})})},f))]})]})})}),s.jsx(y,{sx:{py:8,bgcolor:"background.default"},children:s.jsxs(Q,{maxWidth:"md",children:[s.jsx(b,{variant:"h3",sx:{textAlign:"center",fontWeight:700,mb:6},children:"Frequently Asked Questions"}),s.jsxs(T,{container:!0,spacing:3,children:[s.jsx(T,{item:!0,xs:12,children:s.jsxs(Be,{elevation:1,sx:{p:3},children:[s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:2},children:"How does VidyaMitra ensure data privacy?"}),s.jsx(b,{variant:"body2",color:"text.secondary",children:"We implement enterprise-grade security measures including data encryption, FERPA compliance, and strict access controls to protect student information."})]})}),s.jsx(T,{item:!0,xs:12,children:s.jsxs(Be,{elevation:1,sx:{p:3},children:[s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Which educational boards are supported?"}),s.jsx(b,{variant:"body2",color:"text.secondary",children:"VidyaMitra supports CBSE, ICSE, and all major State educational boards across India, with customizable features for each curriculum."})]})}),s.jsx(T,{item:!0,xs:12,children:s.jsxs(Be,{elevation:1,sx:{p:3},children:[s.jsx(b,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Is training provided for teachers and staff?"}),s.jsx(b,{variant:"body2",color:"text.secondary",children:"Yes, we provide comprehensive training sessions, documentation, and ongoing support to ensure successful platform adoption."})]})})]})]})}),s.jsx(ql,{open:a,autoHideDuration:6e3,onClose:()=>o(!1),anchorOrigin:{vertical:"bottom",horizontal:"center"},children:s.jsx(Yl,{onClose:()=>o(!1),severity:"success",sx:{width:"100%"},children:"Thank you for your message! We'll get back to you within 24 hours."})})]})},Bg=()=>{const t=q(),e=new Date().getFullYear(),n=[{title:"Platform",links:[{label:"Dashboard",href:"/dashboard"},{label:"Student Management",href:"/dashboard/students"},{label:"SWOT Analysis",href:"/dashboard/swot"},{label:"Reports",href:"/dashboard/reports"}]},{title:"Educational Boards",links:[{label:"CBSE Integration",href:"/features#cbse"},{label:"ICSE Support",href:"/features#icse"},{label:"State Boards",href:"/features#state"},{label:"International Boards",href:"/features#international"}]},{title:"Resources",links:[{label:"Documentation",href:"/docs"},{label:"API Reference",href:"/api"},{label:"Support Center",href:"/support"},{label:"Training Materials",href:"/training"}]},{title:"Company",links:[{label:"About Us",href:"/about"},{label:"Privacy Policy",href:"/privacy"},{label:"Terms of Service",href:"/terms"},{label:"Contact",href:"/contact"}]}],r=[{icon:ea,href:"#",label:"Facebook"},{icon:Xl,href:"#",label:"Twitter"},{icon:Zl,href:"#",label:"LinkedIn"},{icon:Ql,href:"#",label:"Instagram"},{icon:ec,href:"#",label:"YouTube"}];return s.jsx(y,{component:"footer",sx:{background:t.palette.mode==="dark"?`linear-gradient(135deg, ${F("#0F172A",.95)} 0%, ${F("#1E293B",.95)} 100%)`:`linear-gradient(135deg, ${F("#F8FAFC",.95)} 0%, ${F("#E2E8F0",.95)} 100%)`,backdropFilter:"blur(20px)",borderTop:`1px solid ${F(t.palette.divider,.1)}`,mt:"auto",py:6},children:s.jsxs(Q,{maxWidth:"lg",children:[s.jsxs(T,{container:!0,spacing:4,children:[s.jsx(T,{item:!0,xs:12,md:4,children:s.jsx(_.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:s.jsxs(y,{sx:{mb:3},children:[s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:2,mb:2},children:[s.jsx(je,{sx:{fontSize:32,color:t.palette.primary.main}}),s.jsx(b,{variant:"h5",sx:{fontWeight:600,color:t.palette.text.primary},children:"VidyaMitra"})]}),s.jsx(b,{variant:"body2",sx:{color:t.palette.text.secondary,lineHeight:1.6,mb:3},children:"Empowering Indian education through intelligent student analysis. Supporting CBSE, ICSE, and State boards with AI-powered insights."}),s.jsxs(pe,{spacing:1.5,children:[s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:1.5},children:[s.jsx(Dr,{sx:{fontSize:18,color:t.palette.primary.main,opacity:.8}}),s.jsx(b,{variant:"body2",sx:{color:t.palette.mode==="dark"?t.palette.text.primary:t.palette.text.secondary,fontWeight:500,opacity:t.palette.mode==="dark"?.9:.8},children:"Hyderabad, India"})]}),s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:1.5},children:[s.jsx(Ir,{sx:{fontSize:18,color:t.palette.primary.main,opacity:.8}}),s.jsx(b,{variant:"body2",sx:{color:t.palette.mode==="dark"?t.palette.text.primary:t.palette.text.secondary,fontWeight:500,opacity:t.palette.mode==="dark"?.9:.8},children:"+91 9392233989"})]}),s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:1.5},children:[s.jsx(Kt,{sx:{fontSize:18,color:t.palette.primary.main,opacity:.8}}),s.jsx(b,{variant:"body2",sx:{color:t.palette.mode==="dark"?t.palette.text.primary:t.palette.text.secondary,fontWeight:500,opacity:t.palette.mode==="dark"?.9:.8},children:"<EMAIL>"})]})]})]})})}),n.map((i,a)=>s.jsx(T,{item:!0,xs:6,md:2,children:s.jsxs(_.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:a*.1},viewport:{once:!0},children:[s.jsx(b,{variant:"h6",sx:{fontWeight:500,mb:2,color:t.palette.text.primary},children:i.title}),s.jsx(pe,{spacing:1,children:i.links.map(o=>s.jsx(Jl,{href:o.href,sx:{color:t.palette.text.secondary,textDecoration:"none",fontSize:"0.875rem",transition:"all 0.3s ease","&:hover":{color:t.palette.primary.main,transform:"translateX(4px)"}},children:o.label},o.label))})]})},i.title))]}),s.jsx(Or,{sx:{my:4,opacity:.3}}),s.jsxs(y,{sx:{display:"flex",flexDirection:{xs:"column",md:"row"},justifyContent:"space-between",alignItems:"center",gap:2},children:[s.jsxs(b,{variant:"body2",sx:{color:t.palette.mode==="dark"?t.palette.text.primary:t.palette.text.secondary,textAlign:{xs:"center",md:"left"},fontWeight:500,opacity:t.palette.mode==="dark"?.9:.8},children:["© ",e," VidyaMitra. All rights reserved. Empowering Indian education with AI-driven insights."]}),s.jsx(pe,{direction:"row",spacing:1,children:r.map(i=>s.jsx(le,{href:i.href,"aria-label":i.label,sx:{color:t.palette.text.secondary,border:`1px solid ${F(t.palette.divider,.3)}`,borderRadius:2,p:1,transition:"all 0.3s ease","&:hover":{color:t.palette.primary.main,borderColor:t.palette.primary.main,transform:"translateY(-2px)",background:F(t.palette.primary.main,.1)}},children:s.jsx(i.icon,{sx:{fontSize:18}})},i.label))})]})]})})},cl=m.createContext(),Vg=({children:t})=>{const[e,n]=m.useState(localStorage.getItem("isAuthenticated")==="true"),[r,i]=m.useState(JSON.parse(localStorage.getItem("user"))),a=l=>new Promise(c=>{setTimeout(()=>{const u={username:l.username,role:"teacher",school_id:"school123",name:"Demo User"};localStorage.setItem("isAuthenticated","true"),localStorage.setItem("user",JSON.stringify(u)),n(!0),i(u),c(u)},500)}),o=()=>{localStorage.removeItem("isAuthenticated"),localStorage.removeItem("user"),n(!1),i(null)};return s.jsx(cl.Provider,{value:{isAuthenticated:e,user:r,login:a,logout:o},children:t})},ci=()=>m.useContext(cl),ul=m.createContext(),Wg=({children:t})=>{const[e,n]=m.useState(localStorage.getItem("appLanguage")||"en");m.useEffect(()=>{ae.changeLanguage(e),localStorage.setItem("appLanguage",e)},[e]);const r=i=>{n(i)};return s.jsx(ul.Provider,{value:{language:e,changeLanguage:r},children:t})},Ng=()=>m.useContext(ul);ae.use(Ia).use(ld).init({supportedLngs:["en","hi"],fallbackLng:"en",debug:!1,interpolation:{escapeValue:!1},backend:{loadPath:"/locales/{{lng}}/{{ns}}.json"},ns:["common","dashboard","login"],defaultNS:"common"});const $g=we.lazy(()=>it(()=>import("./StudentRegistration-B1REbrCi.js"),__vite__mapDeps([0,1,2,3]))),Ug=we.lazy(()=>it(()=>import("./StudentProfile-BDX8WOY5.js"),__vite__mapDeps([4,1,2,3]))),zg=we.lazy(()=>it(()=>import("./AttendanceManagement-4vnBIZnH.js"),__vite__mapDeps([5,1,2,3]))),Hg=we.lazy(()=>it(()=>import("./GradeEntry-CesnzI9J.js"),__vite__mapDeps([6,1,2,3]))),_g=we.lazy(()=>it(()=>import("./TeacherDashboard-1-qBGxVt.js"),__vite__mapDeps([7,1,2,3]))),Kg=we.lazy(()=>it(()=>import("./SWOTWizard-KQTt7XPx.js"),__vite__mapDeps([8,1,2,3]))),Gg=we.lazy(()=>it(()=>import("./ReportGeneration-CrX_JsK4.js"),__vite__mapDeps([9,1,2,3]))),Er=240,qg=()=>{const{t}=ye("common"),{logout:e,user:n}=ci(),{language:r,changeLanguage:i}=Ng(),a=ge(),o=()=>{e(),a("/login")},l=c=>{i(c.target.value)};return s.jsx(kr,{position:"fixed",sx:{zIndex:c=>c.zIndex.drawer+1},children:s.jsxs(Ht,{sx:{px:{xs:2,md:3}},children:[s.jsx(b,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1,fontWeight:600},children:t("platformTitle")}),s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:2},children:[s.jsxs(nr,{size:"small",sx:{minWidth:120},children:[s.jsx(rr,{id:"language-select-label",children:t("language")}),s.jsxs(ir,{labelId:"language-select-label",id:"language-select",value:r,label:t("language"),onChange:l,sx:{"& .MuiOutlinedInput-notchedOutline":{borderColor:"rgba(255, 255, 255, 0.3)"},"&:hover .MuiOutlinedInput-notchedOutline":{borderColor:"rgba(255, 255, 255, 0.5)"}},children:[s.jsx(U,{value:"en",children:"🇬🇧 English"}),s.jsx(U,{value:"hi",children:"🇮🇳 हिन्दी"})]})]}),n&&s.jsxs(y,{sx:{display:"flex",alignItems:"center",gap:1},children:[s.jsx(se,{sx:{width:32,height:32,bgcolor:"secondary.main",fontSize:"0.875rem",fontWeight:600},children:(n.name||n.username).charAt(0).toUpperCase()}),s.jsx(b,{sx:{display:{xs:"none",sm:"block"}},children:n.name||n.username})]}),s.jsx(W,{color:"inherit",onClick:o,startIcon:s.jsx(tc,{}),sx:{borderRadius:2,"&:hover":{bgcolor:"rgba(255, 255, 255, 0.1)"}},children:t("logout")})]})]})})},Yg=()=>{const{t}=ye("common"),e=[{text:t("dashboard"),icon:s.jsx(ca,{}),path:"/dashboard"},{text:t("students"),icon:s.jsx(Cn,{}),path:"/students"},{text:t("reports"),icon:s.jsx(gt,{}),path:"/reports"},{text:t("settings"),icon:s.jsx(nc,{}),path:"/settings"}];return s.jsxs(aa,{variant:"permanent",sx:{width:Er,flexShrink:0,"& .MuiDrawer-paper":{width:Er,boxSizing:"border-box",backgroundColor:"#2E5BA8",color:"white"}},children:[s.jsx(Ht,{})," ",s.jsx(y,{sx:{overflow:"auto"},children:s.jsx(xt,{children:e.map(n=>s.jsxs(Ce,{button:!0,component:Pa,to:n.path,sx:{"&:hover":{backgroundColor:"rgba(255, 255, 255, 0.1)"},"& .MuiListItemIcon-root":{color:"white"}},children:[s.jsx(ut,{children:n.icon}),s.jsx(Fe,{primary:n.text})]},n.text))})})]})},Jg=()=>s.jsxs(y,{sx:{display:"flex",flexDirection:"column",minHeight:"100vh"},children:[s.jsxs(y,{sx:{display:"flex",flex:1},children:[s.jsx(Ys,{}),s.jsx(qg,{}),s.jsx(Yg,{}),s.jsxs(y,{component:"main",sx:{flexGrow:1,bgcolor:"background.default",p:3,width:`calc(100% - ${Er}px)`,display:"flex",flexDirection:"column"},children:[s.jsx(Ht,{})," ",s.jsx(y,{sx:{flex:1},children:s.jsxs(m.Suspense,{fallback:s.jsx(dl,{}),children:[s.jsx(hu,{})," "]})})]})]}),s.jsx(Bg,{})]}),Xg=()=>{const{login:t}=ci(),e=ge(),n=async i=>{try{await t({username:i.email,password:i.password}),e("/dashboard")}catch(a){console.error("Login failed:",a)}},r=async i=>{try{console.log("Signup data:",i),await t({username:i.email,password:i.password}),e("/dashboard")}catch(a){console.error("Signup failed:",a)}};return s.jsx(pg,{onLogin:n,onSignup:r})},Zg=()=>s.jsx(ug,{}),Qg=()=>s.jsx(yg,{}),ex=()=>s.jsx(m.Suspense,{fallback:s.jsx(ot,{}),children:s.jsx($g,{})}),tx=()=>s.jsx(m.Suspense,{fallback:s.jsx(ot,{}),children:s.jsx(Ug,{})}),nx=()=>s.jsx(m.Suspense,{fallback:s.jsx(ot,{}),children:s.jsx(_g,{})}),rx=()=>s.jsx(m.Suspense,{fallback:s.jsx(ot,{}),children:s.jsx(zg,{})}),ix=()=>s.jsx(m.Suspense,{fallback:s.jsx(ot,{}),children:s.jsx(Hg,{})}),sx=()=>s.jsx(m.Suspense,{fallback:s.jsx(ot,{}),children:s.jsx(Kg,{})}),ax=()=>s.jsx(m.Suspense,{fallback:s.jsx(ot,{}),children:s.jsx(Gg,{})}),ox=()=>s.jsx(vg,{}),lx=()=>s.jsx(Ag,{}),cx=()=>s.jsx(mg,{}),ux=()=>{const{t}=ye("common");return s.jsxs(y,{sx:{p:3},children:[s.jsx(b,{variant:"h4",sx:{fontWeight:700,mb:2},children:t("reports")}),s.jsx(b,{variant:"body1",color:"text.secondary",children:"Reports functionality coming soon..."})]})},dx=()=>{const{t}=ye("common");return s.jsxs(y,{sx:{p:3},children:[s.jsx(b,{variant:"h4",sx:{fontWeight:700,mb:2},children:t("settings")}),s.jsx(b,{variant:"body1",color:"text.secondary",children:"Settings functionality coming soon..."})]})},hx=()=>{const{t}=ye("common");return s.jsxs(y,{sx:{textAlign:"center",mt:5},children:[s.jsx(b,{variant:"h3",children:t("notFoundTitle")}),s.jsx(b,{children:t("notFoundMessage")}),s.jsx(W,{component:Pa,to:"/dashboard",variant:"contained",sx:{mt:2},children:t("goHome")})]})},dl=()=>s.jsx(y,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"80vh"},children:s.jsx(It,{})}),ot=()=>s.jsxs(y,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"60vh"},children:[s.jsx(It,{size:40}),s.jsx(b,{sx:{ml:2},children:"Loading component..."})]}),fx=({children:t})=>{const{isAuthenticated:e}=ci();return e?t:s.jsx(du,{to:"/login",replace:!0})};function px(){return s.jsx(Tu,{children:s.jsx(Vg,{children:s.jsx(Wg,{children:s.jsx(m.Suspense,{fallback:s.jsx(dl,{}),children:s.jsx(ju,{children:s.jsxs(pu,{children:[s.jsx(X,{path:"/",element:s.jsx(Mg,{})}),s.jsx(X,{path:"/about",element:s.jsx(Lg,{})}),s.jsx(X,{path:"/features",element:s.jsx(Ig,{})}),s.jsx(X,{path:"/contact",element:s.jsx(Dg,{})}),s.jsx(X,{path:"/login",element:s.jsx(Xg,{})}),s.jsxs(X,{path:"/dashboard",element:s.jsx(fx,{children:s.jsx(Jg,{})}),children:[s.jsx(X,{index:!0,element:s.jsx(Zg,{})}),s.jsx(X,{path:"students",element:s.jsx(Qg,{})}),s.jsx(X,{path:"students/register",element:s.jsx(ex,{})}),s.jsx(X,{path:"students/:studentId",element:s.jsx(tx,{})}),s.jsx(X,{path:"students/:studentId/swot",element:s.jsx(lx,{})}),s.jsx(X,{path:"teacher",element:s.jsx(nx,{})}),s.jsx(X,{path:"attendance",element:s.jsx(rx,{})}),s.jsx(X,{path:"grades",element:s.jsx(ix,{})}),s.jsx(X,{path:"swot/wizard",element:s.jsx(sx,{})}),s.jsx(X,{path:"analytics",element:s.jsx(cx,{})}),s.jsx(X,{path:"swot",element:s.jsx(ox,{})}),s.jsx(X,{path:"reports",element:s.jsx(ux,{})}),s.jsx(X,{path:"reports/generate",element:s.jsx(ax,{})}),s.jsx(X,{path:"settings",element:s.jsx(dx,{})})]}),s.jsx(X,{path:"*",element:s.jsx(hx,{})})]})})})})})})}class mx extends we.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,n){console.error("Application Error:",e,n)}render(){return this.state.hasError?s.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh",fontFamily:"Arial, sans-serif"},children:[s.jsx("h1",{children:"Something went wrong."}),s.jsx("p",{children:"Please refresh the page or contact support if the problem persists."}),s.jsx("button",{onClick:()=>window.location.reload(),style:{padding:"10px 20px",backgroundColor:"#2E5BA8",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},children:"Refresh Page"})]}):this.props.children}}sr.createRoot(document.getElementById("root")).render(s.jsx(we.StrictMode,{children:s.jsx(mx,{children:s.jsx(px,{})})}));export{Hm as A,ye as a,Jc as b,_ as m,ge as u};
//# sourceMappingURL=index-_PFqhbKW.js.map
