import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  IconButton,
  Button,
  LinearProgress,
  Fade,
  Skeleton,
  useTheme,
  useMediaQuery,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tabs,
  Tab,
  Badge,
  Divider,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  School as SchoolIcon,
  Assessment as AssessmentIcon,
  Notifications as NotificationsIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  CalendarToday as CalendarIcon,
  EmojiEvents as TrophyIcon,
  Psychology as PsychologyIcon,
  Groups as GroupsIcon,
  MenuBook as BookIcon,
  Star as StarIcon,
  Timeline as TimelineIcon,
  Flag as FlagIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

// Import new components
import BoardSpecificMetrics from './BoardSpecificMetrics';
import IndianAcademicCalendar from './IndianAcademicCalendar';

// Board Selector Component for Indian Education System
const BoardSelector = ({ selectedBoard, onBoardChange, loading = false }) => {
  const boards = [
    { value: 'CBSE', label: 'CBSE', color: '#2E5BA8', description: 'Central Board of Secondary Education' },
    { value: 'ICSE', label: 'ICSE', color: '#FF9933', description: 'Indian Certificate of Secondary Education' },
    { value: 'STATE', label: 'State Board', color: '#138808', description: 'State Education Board' },
    { value: 'IB', label: 'IB', color: '#D81159', description: 'International Baccalaureate' },
  ];

  return (
    <FormControl size="small" sx={{ minWidth: 150 }} disabled={loading}>
      <InputLabel>Education Board</InputLabel>
      <Select
        value={selectedBoard}
        label="Education Board"
        onChange={(e) => onBoardChange(e.target.value)}
        sx={{
          '& .MuiSelect-select': {
            display: 'flex',
            alignItems: 'center',
            gap: 1,
          },
        }}
      >
        {boards.map((board) => (
          <MenuItem key={board.value} value={board.value}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  bgcolor: board.color,
                }}
              />
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  {board.label}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {board.description}
                </Typography>
              </Box>
            </Box>
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

// Academic Term Tabs Component
const AcademicTermTabs = ({ selectedTerm, onTermChange, loading = false }) => {
  const terms = [
    { value: 'current', label: 'Current Term', icon: <CalendarIcon /> },
    { value: 'term1', label: 'Term 1', icon: <BookIcon /> },
    { value: 'term2', label: 'Term 2', icon: <AssessmentIcon /> },
    { value: 'annual', label: 'Annual', icon: <TrophyIcon /> },
  ];

  return (
    <Tabs
      value={selectedTerm}
      onChange={(e, newValue) => onTermChange(newValue)}
      variant="scrollable"
      scrollButtons="auto"
      sx={{
        '& .MuiTab-root': {
          minHeight: 48,
          textTransform: 'none',
          fontWeight: 500,
        },
      }}
      disabled={loading}
    >
      {terms.map((term) => (
        <Tab
          key={term.value}
          value={term.value}
          label={term.label}
          icon={term.icon}
          iconPosition="start"
        />
      ))}
    </Tabs>
  );
};

// Enhanced metric card with animations and better visual hierarchy
const MetricCard = ({ 
  title, 
  value, 
  change, 
  trend, 
  icon: Icon, 
  color = 'primary',
  loading = false,
  onClick 
}) => {
  const theme = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  if (loading) {
    return (
      <Card sx={{ height: '100%', minHeight: 140 }}>
        <CardContent>
          <Skeleton variant="circular" width={48} height={48} />
          <Skeleton variant="text" width="60%" sx={{ mt: 2 }} />
          <Skeleton variant="text" width="40%" />
          <Skeleton variant="text" width="80%" />
        </CardContent>
      </Card>
    );
  }

  const trendColor = trend === 'up' ? theme.palette.success.main : 
                    trend === 'down' ? theme.palette.error.main : 
                    theme.palette.text.secondary;

  const TrendIcon = trend === 'up' ? TrendingUpIcon : 
                   trend === 'down' ? TrendingDownIcon : null;

  return (
    <Fade in timeout={300}>
      <Card
        sx={{
          height: '100%',
          minHeight: 140,
          cursor: onClick ? 'pointer' : 'default',
          transition: 'all 0.3s ease-in-out',
          transform: isHovered ? 'translateY(-4px)' : 'translateY(0)',
          boxShadow: isHovered ? theme.shadows[8] : theme.shadows[1],
          '&:hover': {
            '& .metric-icon': {
              transform: 'scale(1.1)',
            },
          },
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={onClick}
      >
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
            <Box sx={{ flex: 1 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {title}
              </Typography>
              <Typography variant="h4" component="div" sx={{ fontWeight: 700, mb: 1 }}>
                {value}
              </Typography>
              {change && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {TrendIcon && (
                    <TrendIcon sx={{ fontSize: 16, color: trendColor }} />
                  )}
                  <Typography variant="body2" sx={{ color: trendColor, fontWeight: 500 }}>
                    {change}
                  </Typography>
                </Box>
              )}
            </Box>
            <Avatar
              className="metric-icon"
              sx={{
                bgcolor: `${color}.main`,
                width: 48,
                height: 48,
                transition: 'transform 0.2s ease-in-out',
              }}
            >
              <Icon />
            </Avatar>
          </Box>
        </CardContent>
      </Card>
    </Fade>
  );
};

// Quick action button with enhanced styling
const QuickActionButton = ({ icon: Icon, label, onClick, color = 'primary' }) => {
  const theme = useTheme();
  
  return (
    <Button
      variant="contained"
      startIcon={<Icon />}
      onClick={onClick}
      sx={{
        borderRadius: 2,
        py: 1.5,
        px: 3,
        textTransform: 'none',
        fontWeight: 600,
        boxShadow: theme.shadows[2],
        '&:hover': {
          boxShadow: theme.shadows[4],
          transform: 'translateY(-1px)',
        },
        transition: 'all 0.2s ease-in-out',
      }}
      color={color}
    >
      {label}
    </Button>
  );
};

// Recent activity item with better visual design
const ActivityItem = ({ activity, index }) => {
  const theme = useTheme();
  
  const getActivityColor = (type) => {
    switch (type) {
      case 'success': return theme.palette.success.main;
      case 'warning': return theme.palette.warning.main;
      case 'error': return theme.palette.error.main;
      default: return theme.palette.info.main;
    }
  };

  return (
    <Fade in timeout={300 + index * 100}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          p: 2,
          borderRadius: 2,
          transition: 'background-color 0.2s ease-in-out',
          '&:hover': {
            bgcolor: 'action.hover',
          },
        }}
      >
        <Box
          sx={{
            width: 8,
            height: 8,
            borderRadius: '50%',
            bgcolor: getActivityColor(activity.type),
            flexShrink: 0,
          }}
        />
        <Box sx={{ flex: 1, minWidth: 0 }}>
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            {activity.title}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {activity.time}
          </Typography>
        </Box>
        <Chip
          label={activity.category}
          size="small"
          variant="outlined"
          sx={{ fontSize: '0.75rem' }}
        />
      </Box>
    </Fade>
  );
};

// Main Enhanced Dashboard Component
const EnhancedDashboard = () => {
  const { t } = useTranslation(['dashboard', 'common']);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [loading, setLoading] = useState(true);
  const [metrics, setMetrics] = useState({});
  const [recentActivities, setRecentActivities] = useState([]);
  const [selectedBoard, setSelectedBoard] = useState('CBSE');
  const [selectedTerm, setSelectedTerm] = useState('current');
  const [competitiveExamData, setCompetitiveExamData] = useState([]);
  const [culturalActivities, setCulturalActivities] = useState([]);

  // Simulate data loading with Indian educational context
  useEffect(() => {
    const loadDashboardData = async () => {
      setLoading(true);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Board-specific metrics
      const boardSpecificMetrics = {
        CBSE: {
          totalStudents: { value: '1,247', change: '+12 this month', trend: 'up' },
          activeClasses: { value: '24', change: '+2 new classes', trend: 'up' },
          boardExamPrep: { value: '156', change: 'Class X & XII students', trend: 'up' },
          avgPerformance: { value: '78.5%', change: '+2.3% improvement', trend: 'up' },
          competitivePrep: { value: '89', change: 'JEE/NEET aspirants', trend: 'up' },
          practicalScores: { value: '85.2%', change: 'Lab assessments', trend: 'up' },
          projectWork: { value: '92%', change: 'Completion rate', trend: 'up' },
          skillAssessment: { value: '76%', change: 'CCE evaluation', trend: 'neutral' },
        },
        ICSE: {
          totalStudents: { value: '892', change: '+8 this month', trend: 'up' },
          activeClasses: { value: '18', change: '+1 new class', trend: 'up' },
          boardExamPrep: { value: '124', change: 'Class X & XII students', trend: 'up' },
          avgPerformance: { value: '82.1%', change: '+1.8% improvement', trend: 'up' },
          competitivePrep: { value: '67', change: 'JEE/NEET aspirants', trend: 'up' },
          practicalScores: { value: '88.7%', change: 'Lab assessments', trend: 'up' },
          projectWork: { value: '94%', change: 'Completion rate', trend: 'up' },
          skillAssessment: { value: '81%', change: 'Comprehensive evaluation', trend: 'up' },
        },
        STATE: {
          totalStudents: { value: '2,156', change: '+18 this month', trend: 'up' },
          activeClasses: { value: '32', change: '+3 new classes', trend: 'up' },
          boardExamPrep: { value: '234', change: 'Class X & XII students', trend: 'up' },
          avgPerformance: { value: '74.8%', change: '+3.1% improvement', trend: 'up' },
          competitivePrep: { value: '45', change: 'JEE/NEET aspirants', trend: 'neutral' },
          practicalScores: { value: '79.3%', change: 'Lab assessments', trend: 'up' },
          projectWork: { value: '87%', change: 'Completion rate', trend: 'up' },
          skillAssessment: { value: '72%', change: 'State evaluation', trend: 'up' },
        },
      };

      setMetrics({
        ...boardSpecificMetrics[selectedBoard],
        pendingReports: { value: '8', change: '-3 from last week', trend: 'down' },
        parentMeetings: { value: '32', change: 'Scheduled this week', trend: 'up' },
        attendanceRate: { value: '94.2%', change: '+1.8% this month', trend: 'up' },
        swotCompleted: { value: '892', change: '71% completion rate', trend: 'up' },
        culturalParticipation: { value: '78%', change: 'Festival celebrations', trend: 'up' },
        languageScores: { value: '83.5%', change: 'Regional languages', trend: 'up' },
        moralEducation: { value: '91%', change: 'Value-based learning', trend: 'up' },
        communityService: { value: '65%', change: 'Social responsibility', trend: 'up' },
      });

      setRecentActivities([
        {
          id: 1,
          title: `${selectedBoard} Board exam registration reminder sent`,
          time: '2 minutes ago',
          type: 'success',
          category: 'Board Exam'
        },
        {
          id: 2,
          title: 'Diwali celebration planning meeting scheduled',
          time: '15 minutes ago',
          type: 'info',
          category: 'Cultural'
        },
        {
          id: 3,
          title: 'JEE Main mock test results published',
          time: '1 hour ago',
          type: 'success',
          category: 'Competitive'
        },
        {
          id: 4,
          title: 'Parent-teacher conference for Term 1 scheduled',
          time: '2 hours ago',
          type: 'info',
          category: 'Meeting'
        },
        {
          id: 5,
          title: 'Hindi language assessment completed',
          time: '3 hours ago',
          type: 'success',
          category: 'Language'
        },
        {
          id: 6,
          title: 'Community service project submissions due',
          time: '4 hours ago',
          type: 'warning',
          category: 'Service'
        },
      ]);

      // Competitive exam preparation data
      setCompetitiveExamData([
        { exam: 'JEE Main', students: 45, avgScore: 78, trend: 'up', color: 'primary' },
        { exam: 'NEET', students: 32, avgScore: 82, trend: 'up', color: 'success' },
        { exam: 'KVPY', students: 12, avgScore: 75, trend: 'neutral', color: 'warning' },
        { exam: 'NTSE', students: 28, avgScore: 85, trend: 'up', color: 'secondary' },
        { exam: 'Olympiads', students: 67, avgScore: 73, trend: 'up', color: 'info' },
      ]);

      // Cultural and co-curricular activities
      setCulturalActivities([
        { activity: 'Diwali Celebration', participation: 95, date: 'Nov 12, 2024', type: 'festival' },
        { activity: 'Science Exhibition', participation: 78, date: 'Nov 20, 2024', type: 'academic' },
        { activity: 'Hindi Diwas', participation: 88, date: 'Sep 14, 2024', type: 'language' },
        { activity: 'Sports Day', participation: 92, date: 'Dec 5, 2024', type: 'sports' },
        { activity: 'Cultural Program', participation: 85, date: 'Dec 15, 2024', type: 'cultural' },
      ]);
      
      setLoading(false);
    };

    loadDashboardData();
  }, [selectedBoard, selectedTerm]);

  const handleRefresh = () => {
    setLoading(true);
    // Simulate refresh
    setTimeout(() => setLoading(false), 1000);
  };

  const handleBoardChange = (board) => {
    setSelectedBoard(board);
  };

  const handleTermChange = (term) => {
    setSelectedTerm(term);
  };

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 700, mb: 1 }}>
              {t('dashboard:welcomeMessage')} - {selectedBoard} Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Monitor student progress and manage your classes with Indian educational insights
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <BoardSelector
              selectedBoard={selectedBoard}
              onBoardChange={handleBoardChange}
              loading={loading}
            />
            <IconButton onClick={handleRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
            <Badge badgeContent={6} color="error">
              <IconButton>
                <NotificationsIcon />
              </IconButton>
            </Badge>
            <IconButton>
              <MoreVertIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Academic Term Tabs */}
        <Box sx={{ mb: 3 }}>
          <AcademicTermTabs
            selectedTerm={selectedTerm}
            onTermChange={handleTermChange}
            loading={loading}
          />
        </Box>

        {/* Quick Actions */}
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 3 }}>
          <QuickActionButton
            icon={AddIcon}
            label={t('dashboard:addStudent')}
            onClick={() => console.log('Add student')}
          />
          <QuickActionButton
            icon={AssessmentIcon}
            label={t('dashboard:generateReport')}
            onClick={() => console.log('Generate report')}
            color="secondary"
          />
          <QuickActionButton
            icon={SchoolIcon}
            label={t('dashboard:manageClasses')}
            onClick={() => console.log('Manage classes')}
            color="success"
          />
        </Box>
      </Box>

      {/* Primary Metrics Grid */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title={t('dashboard:totalStudents')}
            value={metrics.totalStudents?.value}
            change={metrics.totalStudents?.change}
            trend={metrics.totalStudents?.trend}
            icon={PeopleIcon}
            color="primary"
            loading={loading}
            onClick={() => console.log('View students')}
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title={t('dashboard:activeClasses')}
            value={metrics.activeClasses?.value}
            change={metrics.activeClasses?.change}
            trend={metrics.activeClasses?.trend}
            icon={SchoolIcon}
            color="secondary"
            loading={loading}
            onClick={() => console.log('View classes')}
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title="Attendance Rate"
            value={metrics.attendanceRate?.value}
            change={metrics.attendanceRate?.change}
            trend={metrics.attendanceRate?.trend}
            icon={TrendingUpIcon}
            color="success"
            loading={loading}
            onClick={() => console.log('View attendance')}
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title="SWOT Completed"
            value={metrics.swotCompleted?.value}
            change={metrics.swotCompleted?.change}
            trend={metrics.swotCompleted?.trend}
            icon={AssessmentIcon}
            color="info"
            loading={loading}
            onClick={() => console.log('View SWOT reports')}
          />
        </Grid>
      </Grid>

      {/* Indian Education Specific Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title="Competitive Exam Prep"
            value={metrics.competitivePrep?.value}
            change={metrics.competitivePrep?.change}
            trend={metrics.competitivePrep?.trend}
            icon={TrophyIcon}
            color="warning"
            loading={loading}
            onClick={() => console.log('View competitive exam preparation')}
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title="Cultural Participation"
            value={metrics.culturalParticipation?.value}
            change={metrics.culturalParticipation?.change}
            trend={metrics.culturalParticipation?.trend}
            icon={GroupsIcon}
            color="secondary"
            loading={loading}
            onClick={() => console.log('View cultural activities')}
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title="Language Scores"
            value={metrics.languageScores?.value}
            change={metrics.languageScores?.change}
            trend={metrics.languageScores?.trend}
            icon={BookIcon}
            color="info"
            loading={loading}
            onClick={() => console.log('View language performance')}
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title="Moral Education"
            value={metrics.moralEducation?.value}
            change={metrics.moralEducation?.change}
            trend={metrics.moralEducation?.trend}
            icon={PsychologyIcon}
            color="success"
            loading={loading}
            onClick={() => console.log('View moral education')}
          />
        </Grid>
      </Grid>

      {/* Board-Specific Assessment Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title="Practical Scores"
            value={metrics.practicalScores?.value}
            change={metrics.practicalScores?.change}
            trend={metrics.practicalScores?.trend}
            icon={AssessmentIcon}
            color="primary"
            loading={loading}
            onClick={() => console.log('View practical assessments')}
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title="Project Work"
            value={metrics.projectWork?.value}
            change={metrics.projectWork?.change}
            trend={metrics.projectWork?.trend}
            icon={StarIcon}
            color="success"
            loading={loading}
            onClick={() => console.log('View project work')}
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title="Skill Assessment"
            value={metrics.skillAssessment?.value}
            change={metrics.skillAssessment?.change}
            trend={metrics.skillAssessment?.trend}
            icon={TimelineIcon}
            color="warning"
            loading={loading}
            onClick={() => console.log('View skill assessments')}
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title="Community Service"
            value={metrics.communityService?.value}
            change={metrics.communityService?.change}
            trend={metrics.communityService?.trend}
            icon={FlagIcon}
            color="secondary"
            loading={loading}
            onClick={() => console.log('View community service')}
          />
        </Grid>
      </Grid>

      {/* Content Grid */}
      <Grid container spacing={3}>
        {/* Recent Activity */}
        <Grid item xs={12} lg={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                {t('dashboard:recentActivity')}
              </Typography>
              {loading ? (
                <Box>
                  {[...Array(4)].map((_, index) => (
                    <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Skeleton variant="circular" width={8} height={8} />
                      <Box sx={{ flex: 1 }}>
                        <Skeleton variant="text" width="80%" />
                        <Skeleton variant="text" width="40%" />
                      </Box>
                      <Skeleton variant="rectangular" width={60} height={24} />
                    </Box>
                  ))}
                </Box>
              ) : (
                <Box>
                  {recentActivities.map((activity, index) => (
                    <ActivityItem key={activity.id} activity={activity} index={index} />
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Performance Overview */}
        <Grid item xs={12} lg={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                {t('dashboard:performanceOverview')}
              </Typography>
              {loading ? (
                <Box>
                  {[...Array(3)].map((_, index) => (
                    <Box key={index} sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Skeleton variant="text" width="40%" />
                        <Skeleton variant="text" width="20%" />
                      </Box>
                      <Skeleton variant="rectangular" height={8} />
                    </Box>
                  ))}
                </Box>
              ) : (
                <Box>
                  {[
                    { subject: 'Mathematics', score: 85, color: 'primary', board: 'CBSE' },
                    { subject: 'Science (Physics)', score: 78, color: 'secondary', board: 'CBSE' },
                    { subject: 'English', score: 92, color: 'success', board: 'CBSE' },
                    { subject: 'Hindi', score: 88, color: 'warning', board: 'CBSE' },
                    { subject: 'Social Science', score: 82, color: 'info', board: 'CBSE' },
                    { subject: 'Computer Science', score: 90, color: 'primary', board: 'CBSE' },
                  ].map((item, index) => (
                    <Fade in timeout={300 + index * 100} key={item.subject}>
                      <Box sx={{ mb: 3 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {item.subject}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {item.score}%
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={item.score}
                          color={item.color}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            bgcolor: 'action.hover',
                          }}
                        />
                      </Box>
                    </Fade>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Indian Academic Calendar & Board Exam Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Academic Calendar */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                  <SchoolIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Academic Calendar
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Upcoming events and important dates
                  </Typography>
                </Box>
              </Box>

              {loading ? (
                <Box>
                  {[1, 2, 3].map((item) => (
                    <Box key={item} sx={{ mb: 2 }}>
                      <Skeleton variant="text" width="80%" />
                      <Skeleton variant="text" width="60%" />
                    </Box>
                  ))}
                </Box>
              ) : (
                <Box>
                  {[
                    { event: 'Term 1 Exams', date: 'Nov 15-30, 2024', type: 'exam', urgent: true },
                    { event: 'Parent-Teacher Meeting', date: 'Dec 5, 2024', type: 'meeting', urgent: false },
                    { event: 'Board Exam Registration', date: 'Dec 10, 2024', type: 'registration', urgent: true },
                    { event: 'Winter Break', date: 'Dec 25 - Jan 5', type: 'holiday', urgent: false },
                    { event: 'Term 2 Begins', date: 'Jan 8, 2025', type: 'academic', urgent: false },
                  ].map((item, index) => (
                    <Fade in timeout={300 + index * 100} key={index}>
                      <Box
                        sx={{
                          p: 2,
                          mb: 2,
                          borderRadius: 2,
                          bgcolor: item.urgent ? 'error.light' : 'action.hover',
                          border: item.urgent ? '1px solid' : 'none',
                          borderColor: item.urgent ? 'error.main' : 'transparent',
                        }}
                      >
                        <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
                          {item.event}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {item.date}
                        </Typography>
                      </Box>
                    </Fade>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Board Exam Preparation */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ bgcolor: 'error.main', mr: 2 }}>
                  <AssessmentIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Board Exam Preparation
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Class X & XII preparation status
                  </Typography>
                </Box>
              </Box>

              {loading ? (
                <Box>
                  {[1, 2, 3].map((item) => (
                    <Box key={item} sx={{ mb: 3 }}>
                      <Skeleton variant="text" width="70%" />
                      <Skeleton variant="rectangular" height={8} sx={{ mt: 1 }} />
                    </Box>
                  ))}
                </Box>
              ) : (
                <Box>
                  {[
                    { class: 'Class X (CBSE)', students: 78, completed: 85, color: 'success' },
                    { class: 'Class XII Science', students: 45, completed: 78, color: 'warning' },
                    { class: 'Class XII Commerce', students: 33, completed: 92, color: 'success' },
                  ].map((item, index) => (
                    <Fade in timeout={300 + index * 100} key={item.class}>
                      <Box sx={{ mb: 3 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {item.class}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {item.students} students • {item.completed}% ready
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={item.completed}
                          color={item.color}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            bgcolor: 'action.hover',
                          }}
                        />
                      </Box>
                    </Fade>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Competitive Exam Tracking & Cultural Activities */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Competitive Exam Preparation */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <TrophyIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Competitive Exam Preparation
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    JEE, NEET, and other competitive exams
                  </Typography>
                </Box>
              </Box>

              {loading ? (
                <Box>
                  {[1, 2, 3].map((item) => (
                    <Box key={item} sx={{ mb: 3 }}>
                      <Skeleton variant="text" width="70%" />
                      <Skeleton variant="rectangular" height={8} sx={{ mt: 1 }} />
                    </Box>
                  ))}
                </Box>
              ) : (
                <Box>
                  {competitiveExamData.map((exam, index) => (
                    <Fade in timeout={300 + index * 100} key={exam.exam}>
                      <Box sx={{ mb: 3 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {exam.exam}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {exam.students} students • {exam.avgScore}% avg
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={exam.avgScore}
                          color={exam.color}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            bgcolor: 'action.hover',
                          }}
                        />
                      </Box>
                    </Fade>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Cultural Activities */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                  <GroupsIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Cultural & Co-curricular Activities
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Festivals, events, and participation rates
                  </Typography>
                </Box>
              </Box>

              {loading ? (
                <Box>
                  {[1, 2, 3].map((item) => (
                    <Box key={item} sx={{ mb: 2 }}>
                      <Skeleton variant="text" width="80%" />
                      <Skeleton variant="text" width="60%" />
                    </Box>
                  ))}
                </Box>
              ) : (
                <Box>
                  {culturalActivities.map((activity, index) => (
                    <Fade in timeout={300 + index * 100} key={index}>
                      <Box
                        sx={{
                          p: 2,
                          mb: 2,
                          borderRadius: 2,
                          bgcolor: 'action.hover',
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}
                      >
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
                            {activity.activity}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {activity.date}
                          </Typography>
                        </Box>
                        <Chip
                          label={`${activity.participation}%`}
                          size="small"
                          color={activity.participation > 85 ? 'success' : activity.participation > 70 ? 'warning' : 'default'}
                          variant="outlined"
                        />
                      </Box>
                    </Fade>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Board-Specific Metrics Section */}
      <Box sx={{ mb: 4 }}>
        <BoardSpecificMetrics selectedBoard={selectedBoard} loading={loading} />
      </Box>

      {/* Indian Academic Calendar Section */}
      <Box sx={{ mb: 4 }}>
        <IndianAcademicCalendar
          selectedBoard={selectedBoard}
          selectedTerm={selectedTerm}
          loading={loading}
        />
      </Box>
    </Box>
  );
};

export default EnhancedDashboard;
