/**
 * VidyaMitra Platform - Collapsible Sidebar Navigation
 * 
 * Modern sidebar with smooth animations, responsive design, and accessibility features
 * Supports both expanded and collapsed states with glassmorphism design
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  Typography,
  Tooltip,
  Divider,
  alpha,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
  Settings as SettingsIcon,
  School as SchoolIcon,
  MenuOpen as MenuOpenIcon,
  Menu as MenuIcon,
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation, useNavigate } from 'react-router-dom';

const DRAWER_WIDTH_EXPANDED = 280;
const DRAWER_WIDTH_COLLAPSED = 72;

const CollapsibleSidebar = ({ open, onToggle }) => {
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [isExpanded, setIsExpanded] = useState(!isMobile);

  // Auto-collapse on mobile
  useEffect(() => {
    if (isMobile) {
      setIsExpanded(false);
    }
  }, [isMobile]);

  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: DashboardIcon,
      path: '/dashboard',
      description: 'Overview and metrics',
    },
    {
      id: 'students',
      label: 'Students',
      icon: PeopleIcon,
      path: '/dashboard/students',
      description: 'Student management',
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: AnalyticsIcon,
      path: '/dashboard/analytics',
      description: 'Performance analytics',
    },
    {
      id: 'swot',
      label: 'SWOT Analysis',
      icon: AssessmentIcon,
      path: '/dashboard/swot',
      description: 'Strength & weakness analysis',
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: AssignmentIcon,
      path: '/dashboard/reports',
      description: 'Generate reports',
    },
    {
      id: 'trends',
      label: 'Trends',
      icon: TrendingUpIcon,
      path: '/dashboard/trends',
      description: 'Performance trends',
    },
  ];

  const bottomMenuItems = [
    {
      id: 'settings',
      label: 'Settings',
      icon: SettingsIcon,
      path: '/dashboard/settings',
      description: 'Application settings',
    },
  ];

  const handleToggle = () => {
    if (isMobile) {
      onToggle();
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  const handleNavigation = (path) => {
    navigate(path);
    if (isMobile) {
      onToggle(); // Close sidebar on mobile after navigation
    }
  };

  const isActive = (path) => {
    if (path === '/dashboard') {
      return location.pathname === '/dashboard';
    }
    return location.pathname.startsWith(path);
  };

  const drawerWidth = isExpanded ? DRAWER_WIDTH_EXPANDED : DRAWER_WIDTH_COLLAPSED;

  const sidebarContent = (
    <Box
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        background: theme.palette.mode === 'dark'
          ? `linear-gradient(180deg, ${alpha('#0F172A', 0.95)} 0%, ${alpha('#1E293B', 0.95)} 100%)`
          : `linear-gradient(180deg, ${alpha('#F8FAFC', 0.95)} 0%, ${alpha('#E2E8F0', 0.95)} 100%)`,
        backdropFilter: 'blur(20px)',
        borderRight: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: isExpanded ? 'space-between' : 'center',
          minHeight: 64,
        }}
      >
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              style={{ display: 'flex', alignItems: 'center', gap: 12 }}
            >
              <SchoolIcon sx={{ color: theme.palette.primary.main, fontSize: 28 }} />
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: theme.palette.text.primary,
                }}
              >
                VidyaMitra
              </Typography>
            </motion.div>
          )}
        </AnimatePresence>

        <IconButton
          onClick={handleToggle}
          sx={{
            color: theme.palette.text.secondary,
            '&:hover': {
              background: alpha(theme.palette.primary.main, 0.1),
              color: theme.palette.primary.main,
            },
          }}
        >
          {isExpanded ? <MenuOpenIcon /> : <MenuIcon />}
        </IconButton>
      </Box>

      <Divider sx={{ opacity: 0.3 }} />

      {/* Main Navigation */}
      <Box sx={{ flex: 1, py: 1 }}>
        <List sx={{ px: 1 }}>
          {menuItems.map((item) => {
            const active = isActive(item.path);
            const IconComponent = item.icon;

            return (
              <ListItem key={item.id} disablePadding sx={{ mb: 0.5 }}>
                <Tooltip
                  title={isExpanded ? '' : item.label}
                  placement="right"
                  arrow
                >
                  <ListItemButton
                    onClick={() => handleNavigation(item.path)}
                    sx={{
                      borderRadius: 2,
                      minHeight: 48,
                      px: isExpanded ? 2 : 1.5,
                      background: active
                        ? alpha(theme.palette.primary.main, 0.15)
                        : 'transparent',
                      color: active
                        ? theme.palette.primary.main
                        : theme.palette.text.secondary,
                      '&:hover': {
                        background: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main,
                        transform: 'translateX(4px)',
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    <ListItemIcon
                      sx={{
                        minWidth: isExpanded ? 40 : 'auto',
                        color: 'inherit',
                        justifyContent: 'center',
                      }}
                    >
                      <IconComponent sx={{ fontSize: 22 }} />
                    </ListItemIcon>
                    <AnimatePresence>
                      {isExpanded && (
                        <motion.div
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -10 }}
                          transition={{ duration: 0.2 }}
                          style={{ width: '100%' }}
                        >
                          <ListItemText
                            primary={item.label}
                            secondary={item.description}
                            primaryTypographyProps={{
                              fontSize: '0.875rem',
                              fontWeight: active ? 600 : 500,
                            }}
                            secondaryTypographyProps={{
                              fontSize: '0.75rem',
                              opacity: 0.7,
                            }}
                          />
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </ListItemButton>
                </Tooltip>
              </ListItem>
            );
          })}
        </List>
      </Box>

      <Divider sx={{ opacity: 0.3 }} />

      {/* Bottom Navigation */}
      <Box sx={{ py: 1 }}>
        <List sx={{ px: 1 }}>
          {bottomMenuItems.map((item) => {
            const active = isActive(item.path);
            const IconComponent = item.icon;

            return (
              <ListItem key={item.id} disablePadding>
                <Tooltip
                  title={isExpanded ? '' : item.label}
                  placement="right"
                  arrow
                >
                  <ListItemButton
                    onClick={() => handleNavigation(item.path)}
                    sx={{
                      borderRadius: 2,
                      minHeight: 48,
                      px: isExpanded ? 2 : 1.5,
                      background: active
                        ? alpha(theme.palette.primary.main, 0.15)
                        : 'transparent',
                      color: active
                        ? theme.palette.primary.main
                        : theme.palette.text.secondary,
                      '&:hover': {
                        background: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main,
                        transform: 'translateX(4px)',
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    <ListItemIcon
                      sx={{
                        minWidth: isExpanded ? 40 : 'auto',
                        color: 'inherit',
                        justifyContent: 'center',
                      }}
                    >
                      <IconComponent sx={{ fontSize: 22 }} />
                    </ListItemIcon>
                    <AnimatePresence>
                      {isExpanded && (
                        <motion.div
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -10 }}
                          transition={{ duration: 0.2 }}
                          style={{ width: '100%' }}
                        >
                          <ListItemText
                            primary={item.label}
                            primaryTypographyProps={{
                              fontSize: '0.875rem',
                              fontWeight: active ? 600 : 500,
                            }}
                          />
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </ListItemButton>
                </Tooltip>
              </ListItem>
            );
          })}
        </List>
      </Box>
    </Box>
  );

  if (isMobile) {
    return (
      <Drawer
        variant="temporary"
        open={open}
        onClose={onToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile
        }}
        sx={{
          '& .MuiDrawer-paper': {
            width: DRAWER_WIDTH_EXPANDED,
            boxSizing: 'border-box',
          },
        }}
      >
        {sidebarContent}
      </Drawer>
    );
  }

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          transition: theme.transitions.create('width', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.enteringScreen,
          }),
          overflowX: 'hidden',
        },
      }}
    >
      {sidebarContent}
    </Drawer>
  );
};

export default CollapsibleSidebar;
