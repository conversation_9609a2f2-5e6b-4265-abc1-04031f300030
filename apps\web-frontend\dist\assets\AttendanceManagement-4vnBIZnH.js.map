{"version": 3, "file": "AttendanceManagement-4vnBIZnH.js", "sources": ["../../src/components/Attendance/AttendanceManagement.jsx"], "sourcesContent": ["/**\n * VidyaMitra Platform - Attendance Management Component\n * \n * Daily attendance marking interface for teachers with Indian educational context\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Grid,\n  Button,\n  Avatar,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  IconButton,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Stack,\n  Alert,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  CheckCircle,\n  Cancel,\n  Schedule,\n  CalendarToday,\n  Save,\n  Print,\n  Download,\n  Refresh,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\n\n// Sample attendance data with Indian student names\nconst attendanceData = [\n  { id: 1, name: '<PERSON><PERSON>', rollNumber: 1, status: 'present', lastAttendance: '95%' },\n  { id: 2, name: '<PERSON><PERSON><PERSON><PERSON>', rollNumber: 2, status: 'present', lastAttendance: '92%' },\n  { id: 3, name: '<PERSON><PERSON><PERSON>', rollNumber: 3, status: 'absent', lastAttendance: '88%' },\n  { id: 4, name: '<PERSON>', rollNumber: 4, status: 'present', lastA<PERSON>dance: '94%' },\n  { id: 5, name: 'Ankitha Patel', rollNumber: 5, status: 'late', lastAttendance: '90%' },\n  { id: 6, name: 'Sirisha Nair', rollNumber: 6, status: 'present', lastAttendance: '96%' },\n  { id: 7, name: 'Priya Agarwal', rollNumber: 7, status: 'present', lastAttendance: '85%' },\n];\n\nconst AttendanceManagement = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const [selectedClass, setSelectedClass] = useState('10-A');\n  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);\n  const [attendance, setAttendance] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    // Initialize attendance state\n    const initialAttendance = {};\n    attendanceData.forEach(student => {\n      initialAttendance[student.id] = student.status;\n    });\n    setAttendance(initialAttendance);\n  }, []);\n\n  const handleAttendanceChange = (studentId, status) => {\n    setAttendance(prev => ({\n      ...prev,\n      [studentId]: status\n    }));\n  };\n\n  const handleSaveAttendance = async () => {\n    setLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      // Show success message\n      alert('Attendance saved successfully!');\n    } catch (error) {\n      console.error('Error saving attendance:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'present':\n        return 'success';\n      case 'absent':\n        return 'error';\n      case 'late':\n        return 'warning';\n      default:\n        return 'default';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'present':\n        return <CheckCircle />;\n      case 'absent':\n        return <Cancel />;\n      case 'late':\n        return <Schedule />;\n      default:\n        return null;\n    }\n  };\n\n  const attendanceSummary = {\n    present: Object.values(attendance).filter(status => status === 'present').length,\n    absent: Object.values(attendance).filter(status => status === 'absent').length,\n    late: Object.values(attendance).filter(status => status === 'late').length,\n  };\n\n  return (\n    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Box sx={{ mb: 4 }}>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              fontWeight: 600,\n              mb: 1,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n            }}\n          >\n            Attendance Management\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Mark daily attendance for your students\n          </Typography>\n        </Box>\n      </motion.div>\n\n      {/* Controls */}\n      <Card sx={{ mb: 4 }}>\n        <CardContent>\n          <Grid container spacing={3} alignItems=\"center\">\n            <Grid item xs={12} md={3}>\n              <FormControl fullWidth>\n                <InputLabel>Class</InputLabel>\n                <Select\n                  value={selectedClass}\n                  onChange={(e) => setSelectedClass(e.target.value)}\n                  label=\"Class\"\n                >\n                  <MenuItem value=\"9-A\">Class 9-A</MenuItem>\n                  <MenuItem value=\"9-B\">Class 9-B</MenuItem>\n                  <MenuItem value=\"10-A\">Class 10-A</MenuItem>\n                  <MenuItem value=\"10-B\">Class 10-B</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <FormControl fullWidth>\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                  Date\n                </Typography>\n                <input\n                  type=\"date\"\n                  value={selectedDate}\n                  onChange={(e) => setSelectedDate(e.target.value)}\n                  style={{\n                    width: '100%',\n                    padding: '12px',\n                    border: `1px solid ${theme.palette.divider}`,\n                    borderRadius: '4px',\n                    fontSize: '16px',\n                  }}\n                />\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Stack direction=\"row\" spacing={2}>\n                <Button\n                  variant=\"contained\"\n                  startIcon={<Save />}\n                  onClick={handleSaveAttendance}\n                  loading={loading}\n                  sx={{\n                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n                  }}\n                >\n                  Save Attendance\n                </Button>\n                <Button variant=\"outlined\" startIcon={<Print />}>\n                  Print Report\n                </Button>\n                <Button variant=\"outlined\" startIcon={<Download />}>\n                  Export\n                </Button>\n              </Stack>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      {/* Attendance Summary */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {attendanceSummary.present}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Present\n                  </Typography>\n                </Box>\n                <CheckCircle sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.error.main} 0%, ${theme.palette.error.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {attendanceSummary.absent}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Absent\n                  </Typography>\n                </Box>\n                <Cancel sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {attendanceSummary.late}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Late\n                  </Typography>\n                </Box>\n                <Schedule sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Card\n            sx={{\n              background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,\n              color: 'white',\n            }}\n          >\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600 }}>\n                    {attendanceData.length}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Total Students\n                  </Typography>\n                </Box>\n                <CalendarToday sx={{ fontSize: 40, opacity: 0.8 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Attendance Table */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>\n            Student Attendance - {selectedClass} ({selectedDate})\n          </Typography>\n\n          <TableContainer component={Paper} variant=\"outlined\">\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Roll No.</TableCell>\n                  <TableCell>Student Name</TableCell>\n                  <TableCell>Previous Attendance</TableCell>\n                  <TableCell>Status</TableCell>\n                  <TableCell>Actions</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {attendanceData.map((student) => (\n                  <TableRow key={student.id} hover>\n                    <TableCell>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                        {student.rollNumber}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                        <Avatar sx={{ width: 32, height: 32 }}>\n                          {student.name.charAt(0)}\n                        </Avatar>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                          {student.name}\n                        </Typography>\n                      </Box>\n                    </TableCell>\n                    <TableCell>\n                      <Typography variant=\"body2\">{student.lastAttendance}</Typography>\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        icon={getStatusIcon(attendance[student.id])}\n                        label={attendance[student.id]?.charAt(0).toUpperCase() + attendance[student.id]?.slice(1)}\n                        color={getStatusColor(attendance[student.id])}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Stack direction=\"row\" spacing={1}>\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleAttendanceChange(student.id, 'present')}\n                          color={attendance[student.id] === 'present' ? 'success' : 'default'}\n                        >\n                          <CheckCircle />\n                        </IconButton>\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleAttendanceChange(student.id, 'absent')}\n                          color={attendance[student.id] === 'absent' ? 'error' : 'default'}\n                        >\n                          <Cancel />\n                        </IconButton>\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleAttendanceChange(student.id, 'late')}\n                          color={attendance[student.id] === 'late' ? 'warning' : 'default'}\n                        >\n                          <Schedule />\n                        </IconButton>\n                      </Stack>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default AttendanceManagement;\n"], "names": ["attendanceData", "AttendanceManagement", "theme", "useTheme", "useNavigate", "selectedClass", "setSelectedClass", "useState", "selectedDate", "setSelectedDate", "attendance", "setAttendance", "loading", "setLoading", "useEffect", "initialAttendance", "student", "handleAttendanceChange", "studentId", "status", "prev", "handleSaveAttendance", "resolve", "error", "getStatusColor", "getStatusIcon", "CheckCircle", "Cancel", "Schedule", "attendanceSummary", "jsxs", "Box", "jsx", "motion", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "e", "MenuItem", "<PERSON><PERSON>", "<PERSON><PERSON>", "Save", "Print", "Download", "CalendarToday", "TableContainer", "Paper", "Table", "TableHead", "TableRow", "TableCell", "TableBody", "Avatar", "Chip", "_a", "_b", "IconButton"], "mappings": "sXA+CA,MAAMA,EAAiB,CACrB,CAAE,GAAI,EAAG,KAAM,oBAAqB,WAAY,EAAG,OAAQ,UAAW,eAAgB,KAAM,EAC5F,CAAE,GAAI,EAAG,KAAM,oBAAqB,WAAY,EAAG,OAAQ,UAAW,eAAgB,KAAM,EAC5F,CAAE,GAAI,EAAG,KAAM,eAAgB,WAAY,EAAG,OAAQ,SAAU,eAAgB,KAAM,EACtF,CAAE,GAAI,EAAG,KAAM,mBAAoB,WAAY,EAAG,OAAQ,UAAW,eAAgB,KAAM,EAC3F,CAAE,GAAI,EAAG,KAAM,gBAAiB,WAAY,EAAG,OAAQ,OAAQ,eAAgB,KAAM,EACrF,CAAE,GAAI,EAAG,KAAM,eAAgB,WAAY,EAAG,OAAQ,UAAW,eAAgB,KAAM,EACvF,CAAE,GAAI,EAAG,KAAM,gBAAiB,WAAY,EAAG,OAAQ,UAAW,eAAgB,KAAM,CAC1F,EAEMC,GAAuB,IAAM,CACjC,MAAMC,EAAQC,EAAS,EACNC,EAAY,EAC7B,KAAM,CAACC,EAAeC,CAAgB,EAAIC,EAAAA,SAAS,MAAM,EACnD,CAACC,EAAcC,CAAe,EAAIF,EAAAA,SAAa,IAAA,OAAO,YAAc,EAAA,MAAM,GAAG,EAAE,CAAC,CAAC,EACjF,CAACG,EAAYC,CAAa,EAAIJ,EAAAA,SAAS,CAAA,CAAE,EACzC,CAACK,EAASC,CAAU,EAAIN,EAAAA,SAAS,EAAK,EAE5CO,EAAAA,UAAU,IAAM,CAEd,MAAMC,EAAoB,CAAC,EAC3Bf,EAAe,QAAmBgB,GAAA,CACdD,EAAAC,EAAQ,EAAE,EAAIA,EAAQ,MAAA,CACzC,EACDL,EAAcI,CAAiB,CACjC,EAAG,EAAE,EAEC,MAAAE,EAAyB,CAACC,EAAWC,IAAW,CACpDR,EAAuBS,IAAA,CACrB,GAAGA,EACH,CAACF,CAAS,EAAGC,CAAA,EACb,CACJ,EAEME,EAAuB,SAAY,CACvCR,EAAW,EAAI,EACX,GAAA,CAEF,MAAM,IAAI,QAAQS,GAAW,WAAWA,EAAS,GAAI,CAAC,EAEtD,MAAM,gCAAgC,QAC/BC,EAAO,CACN,QAAA,MAAM,2BAA4BA,CAAK,CAAA,QAC/C,CACAV,EAAW,EAAK,CAAA,CAEpB,EAEMW,EAAkBL,GAAW,CACjC,OAAQA,EAAQ,CACd,IAAK,UACI,MAAA,UACT,IAAK,SACI,MAAA,QACT,IAAK,OACI,MAAA,UACT,QACS,MAAA,SAAA,CAEb,EAEMM,EAAiBN,GAAW,CAChC,OAAQA,EAAQ,CACd,IAAK,UACH,aAAQO,EAAY,EAAA,EACtB,IAAK,SACH,aAAQC,EAAO,EAAA,EACjB,IAAK,OACH,aAAQC,EAAS,EAAA,EACnB,QACS,OAAA,IAAA,CAEb,EAEMC,EAAoB,CACxB,QAAS,OAAO,OAAOnB,CAAU,EAAE,OAAOS,GAAUA,IAAW,SAAS,EAAE,OAC1E,OAAQ,OAAO,OAAOT,CAAU,EAAE,OAAOS,GAAUA,IAAW,QAAQ,EAAE,OACxE,KAAM,OAAO,OAAOT,CAAU,EAAE,OAAOS,GAAUA,IAAW,MAAM,EAAE,MACtE,EAGE,OAAAW,OAACC,EAAI,CAAA,GAAI,CAAE,SAAU,KAAM,GAAI,OAAQ,EAAG,CAAA,EAExC,SAAA,CAAAC,EAAA,IAACC,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,GAAI,EAC9B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,EAAI,EAE5B,gBAACF,EAAI,CAAA,GAAI,CAAE,GAAI,CACb,EAAA,SAAA,CAAAC,EAAA,IAACE,EAAA,CACC,QAAQ,KACR,GAAI,CACF,WAAY,IACZ,GAAI,EACJ,WAAY,2BAA2BhC,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,SACrG,qBAAsB,OACtB,oBAAqB,cACrB,eAAgB,MAClB,EACD,SAAA,uBAAA,CAED,QACCgC,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAEnD,yCAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,QAGCC,EAAK,CAAA,GAAI,CAAE,GAAI,GACd,SAAAH,EAAA,IAACI,EACC,CAAA,SAAAN,OAACO,GAAK,UAAS,GAAC,QAAS,EAAG,WAAW,SACrC,SAAA,CAACL,EAAA,IAAAK,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAP,EAAAA,KAACQ,EAAY,CAAA,UAAS,GACpB,SAAA,CAAAN,EAAAA,IAACO,GAAW,SAAK,OAAA,CAAA,EACjBT,EAAA,KAACU,EAAA,CACC,MAAOnC,EACP,SAAWoC,GAAMnC,EAAiBmC,EAAE,OAAO,KAAK,EAChD,MAAM,QAEN,SAAA,CAACT,EAAA,IAAAU,EAAA,CAAS,MAAM,MAAM,SAAS,YAAA,EAC9BV,EAAA,IAAAU,EAAA,CAAS,MAAM,MAAM,SAAS,YAAA,EAC9BV,EAAA,IAAAU,EAAA,CAAS,MAAM,OAAO,SAAU,aAAA,EAChCV,EAAA,IAAAU,EAAA,CAAS,MAAM,OAAO,SAAU,YAAA,CAAA,CAAA,CAAA,CAAA,CACnC,CAAA,CACF,CACF,CAAA,EACAV,EAAA,IAACK,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAP,EAAAA,KAACQ,EAAY,CAAA,UAAS,GACpB,SAAA,CAACN,EAAAA,IAAAE,EAAA,CAAW,QAAQ,QAAQ,MAAM,iBAAiB,GAAI,CAAE,GAAI,CAAE,EAAG,SAElE,MAAA,CAAA,EACAF,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOxB,EACP,SAAWiC,GAAMhC,EAAgBgC,EAAE,OAAO,KAAK,EAC/C,MAAO,CACL,MAAO,OACP,QAAS,OACT,OAAQ,aAAavC,EAAM,QAAQ,OAAO,GAC1C,aAAc,MACd,SAAU,MAAA,CACZ,CAAA,CACF,CAAA,CACF,CACF,CAAA,EACC8B,EAAA,IAAAK,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAACP,EAAAA,KAAAa,EAAA,CAAM,UAAU,MAAM,QAAS,EAC9B,SAAA,CAAAX,EAAA,IAACY,EAAA,CACC,QAAQ,YACR,gBAAYC,EAAK,EAAA,EACjB,QAASxB,EACT,QAAAT,EACA,GAAI,CACF,WAAY,2BAA2BV,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,QACvG,EACD,SAAA,iBAAA,CAED,EACA8B,EAAAA,IAACY,GAAO,QAAQ,WAAW,UAAYZ,MAAAc,EAAA,CAAM,CAAA,EAAI,SAEjD,cAAA,CAAA,EACAd,EAAAA,IAACY,GAAO,QAAQ,WAAW,UAAYZ,EAAA,IAAAe,EAAA,CAAA,CAAS,EAAI,SAEpD,QAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGAjB,EAAAA,KAACO,EAAK,CAAA,UAAS,GAAC,QAAS,EAAG,GAAI,CAAE,GAAI,CAAA,EACpC,SAAA,CAAAL,MAACK,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAL,EAAA,IAACG,EAAA,CACC,GAAI,CACF,WAAY,2BAA2BjC,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,QAAQ,IAAI,SACnG,MAAO,OACT,EAEA,SAAC8B,EAAA,IAAAI,EAAA,CACC,SAACN,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,eAChE,EAAA,SAAA,CAAAD,OAACC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GAAA,EACxC,SAAAL,EAAkB,OACrB,CAAA,EACAG,EAAAA,IAACE,GAAW,QAAQ,QAAQ,GAAI,CAAE,QAAS,EAAI,EAAG,SAElD,SAAA,CAAA,CAAA,EACF,EACAF,MAACN,GAAY,GAAI,CAAE,SAAU,GAAI,QAAS,GAAO,CAAA,CAAA,CAAA,CACnD,CACF,CAAA,CAAA,CAAA,EAEJ,QACCW,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAL,EAAA,IAACG,EAAA,CACC,GAAI,CACF,WAAY,2BAA2BjC,EAAM,QAAQ,MAAM,IAAI,QAAQA,EAAM,QAAQ,MAAM,IAAI,SAC/F,MAAO,OACT,EAEA,SAAC8B,EAAA,IAAAI,EAAA,CACC,SAACN,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,eAChE,EAAA,SAAA,CAAAD,OAACC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GAAA,EACxC,SAAAL,EAAkB,MACrB,CAAA,EACAG,EAAAA,IAACE,GAAW,QAAQ,QAAQ,GAAI,CAAE,QAAS,EAAI,EAAG,SAElD,QAAA,CAAA,CAAA,EACF,EACAF,MAACL,GAAO,GAAI,CAAE,SAAU,GAAI,QAAS,GAAO,CAAA,CAAA,CAAA,CAC9C,CACF,CAAA,CAAA,CAAA,EAEJ,QACCU,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAL,EAAA,IAACG,EAAA,CACC,GAAI,CACF,WAAY,2BAA2BjC,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,QAAQ,IAAI,SACnG,MAAO,OACT,EAEA,SAAC8B,EAAA,IAAAI,EAAA,CACC,SAACN,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,eAChE,EAAA,SAAA,CAAAD,OAACC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GAAA,EACxC,SAAAL,EAAkB,IACrB,CAAA,EACAG,EAAAA,IAACE,GAAW,QAAQ,QAAQ,GAAI,CAAE,QAAS,EAAI,EAAG,SAElD,MAAA,CAAA,CAAA,EACF,EACAF,MAACJ,GAAS,GAAI,CAAE,SAAU,GAAI,QAAS,GAAO,CAAA,CAAA,CAAA,CAChD,CACF,CAAA,CAAA,CAAA,EAEJ,QACCS,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAL,EAAA,IAACG,EAAA,CACC,GAAI,CACF,WAAY,2BAA2BjC,EAAM,QAAQ,KAAK,IAAI,QAAQA,EAAM,QAAQ,KAAK,IAAI,SAC7F,MAAO,OACT,EAEA,SAAC8B,EAAA,IAAAI,EAAA,CACC,SAACN,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,eAChE,EAAA,SAAA,CAAAD,OAACC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,WAAY,GAAA,EACxC,SAAAlC,EAAe,MAClB,CAAA,EACAgC,EAAAA,IAACE,GAAW,QAAQ,QAAQ,GAAI,CAAE,QAAS,EAAI,EAAG,SAElD,gBAAA,CAAA,CAAA,EACF,EACAF,MAACgB,GAAc,GAAI,CAAE,SAAU,GAAI,QAAS,GAAO,CAAA,CAAA,CAAA,CACrD,CACF,CAAA,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,EAGAhB,EAAA,IAACG,EACC,CAAA,SAAAL,EAAAA,KAACM,EACC,CAAA,SAAA,CAACN,EAAAA,KAAAI,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,IAAK,MAAO,cAAA,EAAkB,SAAA,CAAA,wBACxD7B,EAAc,KAAGG,EAAa,GAAA,EACtD,QAECyC,EAAe,CAAA,UAAWC,EAAO,QAAQ,WACxC,gBAACC,EACC,CAAA,SAAA,CAACnB,EAAA,IAAAoB,EAAA,CACC,gBAACC,EACC,CAAA,SAAA,CAAArB,EAAAA,IAACsB,GAAU,SAAQ,UAAA,CAAA,EACnBtB,EAAAA,IAACsB,GAAU,SAAY,cAAA,CAAA,EACvBtB,EAAAA,IAACsB,GAAU,SAAmB,qBAAA,CAAA,EAC9BtB,EAAAA,IAACsB,GAAU,SAAM,QAAA,CAAA,EACjBtB,EAAAA,IAACsB,GAAU,SAAO,SAAA,CAAA,CAAA,CAAA,CACpB,CACF,CAAA,EACAtB,EAAAA,IAACuB,GACE,SAAevD,EAAA,IAAKgB,GACnBc,SAAAA,OAAAA,EAAAA,KAACuB,EAA0B,CAAA,MAAK,GAC9B,SAAA,CAAArB,EAAA,IAACsB,EACC,CAAA,SAAAtB,EAAA,IAACE,EAAW,CAAA,QAAQ,QAAQ,GAAI,CAAE,WAAY,KAC3C,SAAQlB,EAAA,UACX,CAAA,EACF,EACCgB,EAAA,IAAAsB,EAAA,CACC,SAACxB,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,CACrD,EAAA,SAAA,CAAAC,EAAA,IAACwB,EAAO,CAAA,GAAI,CAAE,MAAO,GAAI,OAAQ,EAAG,EACjC,SAAQxC,EAAA,KAAK,OAAO,CAAC,CACxB,CAAA,EACAgB,EAAAA,IAACE,EAAW,CAAA,QAAQ,QAAQ,GAAI,CAAE,WAAY,GAC3C,EAAA,SAAAlB,EAAQ,IACX,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACAgB,EAAAA,IAACsB,GACC,SAACtB,EAAAA,IAAAE,EAAA,CAAW,QAAQ,QAAS,SAAAlB,EAAQ,eAAe,CACtD,CAAA,QACCsC,EACC,CAAA,SAAAtB,EAAA,IAACyB,EAAA,CACC,KAAMhC,EAAcf,EAAWM,EAAQ,EAAE,CAAC,EAC1C,QAAO0C,EAAAhD,EAAWM,EAAQ,EAAE,IAArB,YAAA0C,EAAwB,OAAO,GAAG,iBAAgBC,EAAAjD,EAAWM,EAAQ,EAAE,IAArB,YAAA2C,EAAwB,MAAM,IACvF,MAAOnC,EAAed,EAAWM,EAAQ,EAAE,CAAC,EAC5C,KAAK,OAAA,CAAA,EAET,QACCsC,EACC,CAAA,SAAAxB,OAACa,GAAM,UAAU,MAAM,QAAS,EAC9B,SAAA,CAAAX,EAAA,IAAC4B,EAAA,CACC,KAAK,QACL,QAAS,IAAM3C,EAAuBD,EAAQ,GAAI,SAAS,EAC3D,MAAON,EAAWM,EAAQ,EAAE,IAAM,UAAY,UAAY,UAE1D,eAACU,EAAY,CAAA,CAAA,CAAA,CACf,EACAM,EAAA,IAAC4B,EAAA,CACC,KAAK,QACL,QAAS,IAAM3C,EAAuBD,EAAQ,GAAI,QAAQ,EAC1D,MAAON,EAAWM,EAAQ,EAAE,IAAM,SAAW,QAAU,UAEvD,eAACW,EAAO,CAAA,CAAA,CAAA,CACV,EACAK,EAAA,IAAC4B,EAAA,CACC,KAAK,QACL,QAAS,IAAM3C,EAAuBD,EAAQ,GAAI,MAAM,EACxD,MAAON,EAAWM,EAAQ,EAAE,IAAM,OAAS,UAAY,UAEvD,eAACY,EAAS,CAAA,CAAA,CAAA,CAAA,CACZ,CAAA,CACF,CACF,CAAA,CAAA,GAnDaZ,EAAQ,EAoDvB,EACD,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ"}