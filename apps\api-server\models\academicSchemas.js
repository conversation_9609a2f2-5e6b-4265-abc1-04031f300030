/**
 * VidyaMitra Platform - Academic Performance Schemas
 * 
 * This file defines the academic performance, attendance, and behavioral schemas
 * for the VidyaMitra Platform based on the technical documentation.
 * 
 * Based on: docs/technical/03-data-model.md
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Academic Performance Schema
 * Stores quarterly academic performance records
 */
const AcademicPerformanceSchema = new Schema({
  performance_id: {
    type: String,
    required: true,
    unique: true,
    match: /^PERF\d{8}$/
  },
  student_id: {
    type: Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  quarter: {
    type: Number,
    required: true,
    min: 1,
    max: 4
  },
  academic_year: {
    type: String,
    required: true,
    match: /^\d{4}-\d{4}$/
  },
  overall_gpa: {
    type: Number,
    required: true,
    min: 0.0,
    max: 4.0
  },
  overall_percentage: {
    type: Number,
    min: 0,
    max: 100
  },
  class_rank: Number,
  total_students: Number,
  // Indian education specific fields
  board_exam_preparation: {
    type: Boolean,
    default: false
  },
  competitive_exam_scores: [{
    exam_name: {
      type: String,
      enum: ['JEE Main', 'JEE Advanced', 'NEET', 'CLAT', 'CAT', 'State CET', 'Other']
    },
    score: Number,
    percentile: Number,
    rank: Number,
    date: Date
  }],
  term_type: {
    type: String,
    enum: ['Unit Test', 'Mid Term', 'Final Term', 'Annual', 'Board Exam'],
    default: 'Final Term'
  }
}, {
  timestamps: true
});

// Compound index for uniqueness
AcademicPerformanceSchema.index({ student_id: 1, quarter: 1, academic_year: 1 }, { unique: true });

/**
 * Subject Performance Schema
 * Stores subject-specific performance information
 */
const SubjectSchema = new Schema({
  subject_id: {
    type: String,
    required: true,
    unique: true,
    match: /^SUB\d{8}$/
  },
  performance_id: {
    type: Schema.Types.ObjectId,
    ref: 'AcademicPerformance',
    required: true
  },
  subject_name: {
    type: String,
    required: true,
    maxlength: 50
  },
  subject_code: String,
  score: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  grade: {
    type: String,
    required: true,
    enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'F']
  },
  grade_points: {
    type: Number,
    min: 0,
    max: 10
  },
  teacher_name: String,
  teacher_comments: String,
  // Indian education specific fields
  practical_marks: Number,
  theory_marks: Number,
  internal_assessment: Number,
  project_marks: Number,
  subject_category: {
    type: String,
    enum: ['Core', 'Elective', 'Additional', 'Language', 'Skill Enhancement']
  },
  difficulty_level: {
    type: String,
    enum: ['Basic', 'Standard', 'Advanced'],
    default: 'Standard'
  }
}, {
  timestamps: true
});

SubjectSchema.index({ performance_id: 1 });

/**
 * Attendance Schema
 * Stores daily attendance records
 */
const AttendanceSchema = new Schema({
  attendance_id: {
    type: String,
    required: true,
    unique: true,
    match: /^ATT\d{8}$/
  },
  student_id: {
    type: Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  date: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    required: true,
    enum: ['present', 'absent', 'late', 'excused', 'medical_leave', 'authorized_absence']
  },
  tardiness: {
    type: Number,
    default: 0,
    min: 0
  },
  excused: {
    type: Boolean,
    default: false
  },
  reason: String,
  period_wise_attendance: [{
    period: Number,
    subject: String,
    status: {
      type: String,
      enum: ['present', 'absent', 'late']
    }
  }],
  // Indian education specific fields
  session: {
    type: String,
    enum: ['Morning', 'Afternoon', 'Full Day'],
    default: 'Full Day'
  },
  transport_used: {
    type: String,
    enum: ['School Bus', 'Private Vehicle', 'Public Transport', 'Walking', 'Other']
  }
}, {
  timestamps: true
});

// Compound index for uniqueness
AttendanceSchema.index({ student_id: 1, date: 1 }, { unique: true });

/**
 * Quarterly Attendance Summary Schema
 * Stores quarterly attendance summaries
 */
const QuarterlyAttendanceSchema = new Schema({
  summary_id: {
    type: String,
    required: true,
    unique: true,
    match: /^QATT\d{8}$/
  },
  student_id: {
    type: Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  quarter: {
    type: Number,
    required: true,
    min: 1,
    max: 4
  },
  academic_year: {
    type: String,
    required: true,
    match: /^\d{4}-\d{4}$/
  },
  present_days: {
    type: Number,
    required: true,
    min: 0
  },
  absent_days: {
    type: Number,
    required: true,
    min: 0
  },
  tardy_days: {
    type: Number,
    required: true,
    min: 0
  },
  attendance_rate: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  total_working_days: Number,
  medical_leaves: {
    type: Number,
    default: 0
  },
  authorized_absences: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Compound index for uniqueness
QuarterlyAttendanceSchema.index({ student_id: 1, quarter: 1, academic_year: 1 }, { unique: true });

// Create and export models
const academicModels = {
  AcademicPerformance: mongoose.model('AcademicPerformance', AcademicPerformanceSchema),
  Subject: mongoose.model('Subject', SubjectSchema),
  Attendance: mongoose.model('Attendance', AttendanceSchema),
  QuarterlyAttendance: mongoose.model('QuarterlyAttendance', QuarterlyAttendanceSchema)
};

module.exports = academicModels;
