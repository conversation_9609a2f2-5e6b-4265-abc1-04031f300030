import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  IconButton,
  Button,
  LinearProgress,
  Fade,
  Skeleton,
  useTheme,
  useMediaQuery,
  Paper,
  Divider,
} from '@mui/material';
import {
  Psychology as StrengthIcon,
  Warning as WeaknessIcon,
  TrendingUp as OpportunityIcon,
  Security as ThreatIcon,
  Star as StarIcon,
  EmojiEvents as TrophyIcon,
  School as SchoolIcon,
  Groups as GroupsIcon,
} from '@mui/icons-material';

// Cultural SWOT Card with Indian design patterns
const CulturalSWOTCard = ({ 
  type, 
  title, 
  items, 
  color, 
  icon: Icon, 
  loading = false,
  culturalPattern = 'lotus' 
}) => {
  const theme = useTheme();
  
  const getPatternBackground = (pattern) => {
    switch (pattern) {
      case 'lotus':
        return `radial-gradient(circle at 20% 80%, ${color}15 0%, transparent 50%), 
                radial-gradient(circle at 80% 20%, ${color}10 0%, transparent 50%)`;
      case 'mandala':
        return `conic-gradient(from 0deg at 50% 50%, ${color}05, ${color}15, ${color}05)`;
      case 'paisley':
        return `linear-gradient(45deg, ${color}08 25%, transparent 25%), 
                linear-gradient(-45deg, ${color}08 25%, transparent 25%)`;
      default:
        return `linear-gradient(135deg, ${color}10 0%, ${color}05 100%)`;
    }
  };

  if (loading) {
    return (
      <Card sx={{ height: '100%', minHeight: 300 }}>
        <CardContent>
          <Skeleton variant="circular" width={48} height={48} />
          <Skeleton variant="text" width="60%" sx={{ mt: 2 }} />
          {[...Array(4)].map((_, index) => (
            <Skeleton key={index} variant="text" width="90%" sx={{ mt: 1 }} />
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <Fade in timeout={300}>
      <Card
        sx={{
          height: '100%',
          minHeight: 300,
          background: getPatternBackground(culturalPattern),
          border: `2px solid ${color}20`,
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: theme.shadows[8],
            border: `2px solid ${color}40`,
          },
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Avatar
              sx={{
                bgcolor: color,
                width: 48,
                height: 48,
                mr: 2,
              }}
            >
              <Icon />
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 700, color: color }}>
                {title}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {items.length} identified areas
              </Typography>
            </Box>
          </Box>

          <Box>
            {items.map((item, index) => (
              <Fade in timeout={300 + index * 100} key={index}>
                <Box
                  sx={{
                    p: 2,
                    mb: 2,
                    borderRadius: 2,
                    bgcolor: 'background.paper',
                    border: `1px solid ${color}20`,
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      bgcolor: `${color}05`,
                      border: `1px solid ${color}40`,
                    },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Box
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        bgcolor: color,
                        mr: 1,
                      }}
                    />
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {item.title}
                    </Typography>
                    {item.priority && (
                      <Chip
                        label={item.priority}
                        size="small"
                        sx={{ ml: 'auto', fontSize: '0.75rem' }}
                        color={item.priority === 'High' ? 'error' : item.priority === 'Medium' ? 'warning' : 'default'}
                      />
                    )}
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    {item.description}
                  </Typography>
                  {item.culturalContext && (
                    <Typography variant="caption" sx={{ 
                      display: 'block', 
                      mt: 0.5, 
                      fontStyle: 'italic',
                      color: color 
                    }}>
                      Cultural Context: {item.culturalContext}
                    </Typography>
                  )}
                </Box>
              </Fade>
            ))}
          </Box>
        </CardContent>
      </Card>
    </Fade>
  );
};

// Main Cultural SWOT Visualization Component
const CulturalSWOTVisualization = ({ studentId, boardType = 'CBSE' }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [loading, setLoading] = useState(true);
  const [swotData, setSWOTData] = useState({});
  const [selectedStudent, setSelectedStudent] = useState(null);

  useEffect(() => {
    const loadSWOTData = async () => {
      setLoading(true);
      
      // Simulate API call with Indian educational context
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const mockSWOTData = {
        strengths: [
          {
            title: 'Strong Mathematical Foundation',
            description: 'Excellent performance in algebra and geometry',
            priority: 'High',
            culturalContext: 'Vedic mathematics influence'
          },
          {
            title: 'Hindi Language Proficiency',
            description: 'Native speaker with excellent writing skills',
            priority: 'High',
            culturalContext: 'Mother tongue advantage'
          },
          {
            title: 'Cultural Values Integration',
            description: 'Strong moral and ethical foundation',
            priority: 'Medium',
            culturalContext: 'Family and community values'
          },
          {
            title: 'Collaborative Learning',
            description: 'Works well in group projects and team activities',
            priority: 'Medium',
            culturalContext: 'Community-oriented upbringing'
          },
        ],
        weaknesses: [
          {
            title: 'English Communication',
            description: 'Needs improvement in spoken English confidence',
            priority: 'High',
            culturalContext: 'Regional language dominance'
          },
          {
            title: 'Technology Adaptation',
            description: 'Slower adoption of digital learning tools',
            priority: 'Medium',
            culturalContext: 'Traditional learning methods preference'
          },
          {
            title: 'Time Management',
            description: 'Struggles with deadline management',
            priority: 'Medium',
            culturalContext: 'Flexible time concept in culture'
          },
        ],
        opportunities: [
          {
            title: 'Competitive Exam Preparation',
            description: 'Strong foundation for JEE/NEET preparation',
            priority: 'High',
            culturalContext: 'Engineering/Medical career aspirations'
          },
          {
            title: 'Multilingual Advantage',
            description: 'Can leverage multiple language skills',
            priority: 'High',
            culturalContext: 'Diverse linguistic environment'
          },
          {
            title: 'Cultural Leadership',
            description: 'Can lead cultural and festival activities',
            priority: 'Medium',
            culturalContext: 'Rich cultural heritage knowledge'
          },
        ],
        threats: [
          {
            title: 'Urban Competition',
            description: 'Intense competition from metro city students',
            priority: 'High',
            culturalContext: 'Resource disparity between regions'
          },
          {
            title: 'Digital Divide',
            description: 'Limited access to advanced technology',
            priority: 'Medium',
            culturalContext: 'Infrastructure limitations'
          },
          {
            title: 'Career Pressure',
            description: 'Family expectations for traditional careers',
            priority: 'Medium',
            culturalContext: 'Societal career preferences'
          },
        ],
      };
      
      setSWOTData(mockSWOTData);
      setSelectedStudent({
        name: 'Arjun Sharma',
        class: 'Class X-A',
        board: boardType,
        rollNumber: 'CB2024001'
      });
      setLoading(false);
    };

    loadSWOTData();
  }, [studentId, boardType]);

  const swotConfig = [
    {
      type: 'strengths',
      title: 'Strengths (शक्तियाँ)',
      color: theme.palette.success.main,
      icon: StrengthIcon,
      pattern: 'lotus',
      items: swotData.strengths || []
    },
    {
      type: 'weaknesses',
      title: 'Weaknesses (कमजोरियाँ)',
      color: theme.palette.error.main,
      icon: WeaknessIcon,
      pattern: 'mandala',
      items: swotData.weaknesses || []
    },
    {
      type: 'opportunities',
      title: 'Opportunities (अवसर)',
      color: theme.palette.primary.main,
      icon: OpportunityIcon,
      pattern: 'paisley',
      items: swotData.opportunities || []
    },
    {
      type: 'threats',
      title: 'Threats (चुनौतियाँ)',
      color: theme.palette.warning.main,
      icon: ThreatIcon,
      pattern: 'lotus',
      items: swotData.threats || []
    },
  ];

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 700, mb: 1 }}>
          Cultural SWOT Analysis
        </Typography>
        {selectedStudent && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Avatar sx={{ bgcolor: 'primary.main' }}>
              {selectedStudent.name.charAt(0)}
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {selectedStudent.name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {selectedStudent.class} • {selectedStudent.board} Board • Roll: {selectedStudent.rollNumber}
              </Typography>
            </Box>
          </Box>
        )}
        <Typography variant="body1" color="text.secondary">
          Comprehensive analysis with Indian educational and cultural context
        </Typography>
      </Box>

      {/* SWOT Grid */}
      <Grid container spacing={3}>
        {swotConfig.map((config) => (
          <Grid item xs={12} md={6} key={config.type}>
            <CulturalSWOTCard
              type={config.type}
              title={config.title}
              items={config.items}
              color={config.color}
              icon={config.icon}
              culturalPattern={config.pattern}
              loading={loading}
            />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default CulturalSWOTVisualization;
