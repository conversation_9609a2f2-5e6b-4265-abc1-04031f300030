/**
 * VidyaMitra Platform - Authentication Context
 * 
 * Role-based authentication with Indian educational hierarchy
 */

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authAPI } from '../services/api';
import websocketService from '../services/websocket';

// Indian Educational Hierarchy Roles
export const ROLES = {
  SUPER_ADMIN: 'super_admin',
  PRINCIPAL: 'principal',
  VICE_PRINCIPAL: 'vice_principal',
  HEAD_TEACHER: 'head_teacher',
  TEACHER: 'teacher',
  ASSISTANT_TEACHER: 'assistant_teacher',
  COUNSELOR: 'counselor',
  LIBRARIAN: 'librarian',
  ADMIN_STAFF: 'admin_staff',
  PARENT: 'parent',
  STUDENT: 'student',
};

// Role hierarchy for permission checking
const ROLE_HIERARCHY = {
  [ROLES.SUPER_ADMIN]: 10,
  [ROLES.PRINCIPAL]: 9,
  [ROLES.VICE_PRINCIPAL]: 8,
  [ROLES.HEAD_TEACHER]: 7,
  [ROLES.TEACHER]: 6,
  [ROLES.ASSISTANT_TEACHER]: 5,
  [ROLES.COUNSELOR]: 4,
  [ROLES.LIBRARIAN]: 3,
  [ROLES.ADMIN_STAFF]: 2,
  [ROLES.PARENT]: 1,
  [ROLES.STUDENT]: 0,
};

// Permissions for different actions
export const PERMISSIONS = {
  // Student Management
  VIEW_ALL_STUDENTS: 'view_all_students',
  CREATE_STUDENT: 'create_student',
  EDIT_STUDENT: 'edit_student',
  DELETE_STUDENT: 'delete_student',
  
  // Grade Management
  VIEW_ALL_GRADES: 'view_all_grades',
  ENTER_GRADES: 'enter_grades',
  MODIFY_GRADES: 'modify_grades',
  APPROVE_GRADES: 'approve_grades',
  
  // Attendance Management
  VIEW_ALL_ATTENDANCE: 'view_all_attendance',
  MARK_ATTENDANCE: 'mark_attendance',
  MODIFY_ATTENDANCE: 'modify_attendance',
  
  // SWOT Analysis
  VIEW_ALL_SWOT: 'view_all_swot',
  CREATE_SWOT: 'create_swot',
  EDIT_SWOT: 'edit_swot',
  APPROVE_SWOT: 'approve_swot',
  
  // Reports
  GENERATE_REPORTS: 'generate_reports',
  VIEW_ANALYTICS: 'view_analytics',
  EXPORT_DATA: 'export_data',
  
  // System Administration
  MANAGE_USERS: 'manage_users',
  SYSTEM_SETTINGS: 'system_settings',
  BACKUP_DATA: 'backup_data',
};

// Role-based permissions mapping
const ROLE_PERMISSIONS = {
  [ROLES.SUPER_ADMIN]: Object.values(PERMISSIONS),
  [ROLES.PRINCIPAL]: [
    PERMISSIONS.VIEW_ALL_STUDENTS,
    PERMISSIONS.CREATE_STUDENT,
    PERMISSIONS.EDIT_STUDENT,
    PERMISSIONS.VIEW_ALL_GRADES,
    PERMISSIONS.APPROVE_GRADES,
    PERMISSIONS.VIEW_ALL_ATTENDANCE,
    PERMISSIONS.MODIFY_ATTENDANCE,
    PERMISSIONS.VIEW_ALL_SWOT,
    PERMISSIONS.APPROVE_SWOT,
    PERMISSIONS.GENERATE_REPORTS,
    PERMISSIONS.VIEW_ANALYTICS,
    PERMISSIONS.EXPORT_DATA,
    PERMISSIONS.MANAGE_USERS,
  ],
  [ROLES.VICE_PRINCIPAL]: [
    PERMISSIONS.VIEW_ALL_STUDENTS,
    PERMISSIONS.CREATE_STUDENT,
    PERMISSIONS.EDIT_STUDENT,
    PERMISSIONS.VIEW_ALL_GRADES,
    PERMISSIONS.MODIFY_GRADES,
    PERMISSIONS.VIEW_ALL_ATTENDANCE,
    PERMISSIONS.MODIFY_ATTENDANCE,
    PERMISSIONS.VIEW_ALL_SWOT,
    PERMISSIONS.EDIT_SWOT,
    PERMISSIONS.GENERATE_REPORTS,
    PERMISSIONS.VIEW_ANALYTICS,
  ],
  [ROLES.HEAD_TEACHER]: [
    PERMISSIONS.VIEW_ALL_STUDENTS,
    PERMISSIONS.EDIT_STUDENT,
    PERMISSIONS.VIEW_ALL_GRADES,
    PERMISSIONS.ENTER_GRADES,
    PERMISSIONS.VIEW_ALL_ATTENDANCE,
    PERMISSIONS.MARK_ATTENDANCE,
    PERMISSIONS.VIEW_ALL_SWOT,
    PERMISSIONS.CREATE_SWOT,
    PERMISSIONS.GENERATE_REPORTS,
  ],
  [ROLES.TEACHER]: [
    PERMISSIONS.VIEW_ALL_STUDENTS,
    PERMISSIONS.ENTER_GRADES,
    PERMISSIONS.MARK_ATTENDANCE,
    PERMISSIONS.CREATE_SWOT,
    PERMISSIONS.GENERATE_REPORTS,
  ],
  [ROLES.ASSISTANT_TEACHER]: [
    PERMISSIONS.VIEW_ALL_STUDENTS,
    PERMISSIONS.MARK_ATTENDANCE,
    PERMISSIONS.CREATE_SWOT,
  ],
  [ROLES.COUNSELOR]: [
    PERMISSIONS.VIEW_ALL_STUDENTS,
    PERMISSIONS.VIEW_ALL_SWOT,
    PERMISSIONS.CREATE_SWOT,
    PERMISSIONS.EDIT_SWOT,
  ],
  [ROLES.PARENT]: [],
  [ROLES.STUDENT]: [],
};

// Auth reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, loading: true, error: null };
    
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        loading: false,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
        error: null,
      };
    
    case 'LOGIN_FAILURE':
      return {
        ...state,
        loading: false,
        isAuthenticated: false,
        user: null,
        token: null,
        error: action.payload,
      };
    
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        error: null,
      };
    
    case 'UPDATE_USER':
      return {
        ...state,
        user: { ...state.user, ...action.payload },
      };
    
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    
    default:
      return state;
  }
};

// Initial state
const initialState = {
  isAuthenticated: false,
  user: null,
  token: null,
  loading: false,
  error: null,
};

// Create context
const AuthContext = createContext();

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check for existing token on mount
  useEffect(() => {
    const token = localStorage.getItem('authToken');
    if (token) {
      verifyToken(token);
    }
  }, []);

  // Verify token validity
  const verifyToken = async (token) => {
    try {
      const response = await authAPI.verifyToken();
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          user: response.user,
          token: token,
        },
      });
      
      // Connect to WebSocket with authenticated token
      websocketService.connect(token);
    } catch (error) {
      localStorage.removeItem('authToken');
      dispatch({ type: 'LOGOUT' });
    }
  };

  // Login function
  const login = async (credentials) => {
    dispatch({ type: 'LOGIN_START' });
    
    try {
      const response = await authAPI.login(credentials);
      
      localStorage.setItem('authToken', response.token);
      
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          user: response.user,
          token: response.token,
        },
      });
      
      // Connect to WebSocket
      websocketService.connect(response.token);
      
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Login failed';
      dispatch({
        type: 'LOGIN_FAILURE',
        payload: errorMessage,
      });
      return { success: false, error: errorMessage };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('authToken');
      websocketService.disconnect();
      dispatch({ type: 'LOGOUT' });
    }
  };

  // Update user profile
  const updateUser = (userData) => {
    dispatch({ type: 'UPDATE_USER', payload: userData });
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Check if user has specific permission
  const hasPermission = (permission) => {
    if (!state.user || !state.user.role) return false;
    
    const userPermissions = ROLE_PERMISSIONS[state.user.role] || [];
    return userPermissions.includes(permission);
  };

  // Check if user has role or higher
  const hasRole = (requiredRole) => {
    if (!state.user || !state.user.role) return false;
    
    const userRoleLevel = ROLE_HIERARCHY[state.user.role] || 0;
    const requiredRoleLevel = ROLE_HIERARCHY[requiredRole] || 0;
    
    return userRoleLevel >= requiredRoleLevel;
  };

  // Check if user can access specific student data
  const canAccessStudent = (studentId) => {
    if (!state.user) return false;
    
    // Admins and teachers can access all students
    if (hasRole(ROLES.TEACHER)) return true;
    
    // Parents can only access their children
    if (state.user.role === ROLES.PARENT) {
      return state.user.children?.includes(studentId);
    }
    
    // Students can only access their own data
    if (state.user.role === ROLES.STUDENT) {
      return state.user.studentId === studentId;
    }
    
    return false;
  };

  // Check if user can access specific class data
  const canAccessClass = (classId) => {
    if (!state.user) return false;
    
    // Admins can access all classes
    if (hasRole(ROLES.VICE_PRINCIPAL)) return true;
    
    // Teachers can access their assigned classes
    if (hasRole(ROLES.TEACHER)) {
      return state.user.assignedClasses?.includes(classId);
    }
    
    return false;
  };

  const value = {
    ...state,
    login,
    logout,
    updateUser,
    clearError,
    hasPermission,
    hasRole,
    canAccessStudent,
    canAccessClass,
    ROLES,
    PERMISSIONS,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// HOC for protected routes
export const withAuth = (Component, requiredRole = null, requiredPermission = null) => {
  return function AuthenticatedComponent(props) {
    const { isAuthenticated, hasRole, hasPermission } = useAuth();
    
    if (!isAuthenticated) {
      return <Navigate to="/login" replace />;
    }
    
    if (requiredRole && !hasRole(requiredRole)) {
      return <Navigate to="/unauthorized" replace />;
    }
    
    if (requiredPermission && !hasPermission(requiredPermission)) {
      return <Navigate to="/unauthorized" replace />;
    }
    
    return <Component {...props} />;
  };
};

// Component for role-based rendering
export const RoleGuard = ({ 
  children, 
  role = null, 
  permission = null, 
  fallback = null 
}) => {
  const { hasRole, hasPermission } = useAuth();
  
  const hasAccess = (role ? hasRole(role) : true) && 
                   (permission ? hasPermission(permission) : true);
  
  return hasAccess ? children : fallback;
};

export default AuthContext;
