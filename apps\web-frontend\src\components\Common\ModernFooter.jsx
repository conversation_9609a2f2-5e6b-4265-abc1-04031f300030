/**
 * VidyaMitra Platform - Modern Footer Component
 * 
 * Contemporary footer with glassmorphism design, contact information, and dynamic year
 * Maintains Indian educational context while providing comprehensive platform information
 */

import React from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Link,
  IconButton,
  Divider,
  Stack,
  alpha,
  useTheme,
} from '@mui/material';
import {
  School,
  Email,
  Phone,
  LocationOn,
  Facebook,
  Twitter,
  LinkedIn,
  Instagram,
  YouTube,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

const ModernFooter = () => {
  const theme = useTheme();
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: 'Platform',
      links: [
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Student Management', href: '/dashboard/students' },
        { label: 'SWOT Analysis', href: '/dashboard/swot' },
        { label: 'Reports', href: '/dashboard/reports' },
      ],
    },
    {
      title: 'Educational Boards',
      links: [
        { label: 'CBSE Integration', href: '/features#cbse' },
        { label: 'ICSE Support', href: '/features#icse' },
        { label: 'State Boards', href: '/features#state' },
        { label: 'International Boards', href: '/features#international' },
      ],
    },
    {
      title: 'Resources',
      links: [
        { label: 'Documentation', href: '/docs' },
        { label: 'API Reference', href: '/api' },
        { label: 'Support Center', href: '/support' },
        { label: 'Training Materials', href: '/training' },
      ],
    },
    {
      title: 'Company',
      links: [
        { label: 'About Us', href: '/about' },
        { label: 'Privacy Policy', href: '/privacy' },
        { label: 'Terms of Service', href: '/terms' },
        { label: 'Contact', href: '/contact' },
      ],
    },
  ];

  const socialLinks = [
    { icon: Facebook, href: '#', label: 'Facebook' },
    { icon: Twitter, href: '#', label: 'Twitter' },
    { icon: LinkedIn, href: '#', label: 'LinkedIn' },
    { icon: Instagram, href: '#', label: 'Instagram' },
    { icon: YouTube, href: '#', label: 'YouTube' },
  ];

  return (
    <Box
      component="footer"
      sx={{
        background: theme.palette.mode === 'dark'
          ? `linear-gradient(135deg, ${alpha('#0F172A', 0.95)} 0%, ${alpha('#1E293B', 0.95)} 100%)`
          : `linear-gradient(135deg, ${alpha('#F8FAFC', 0.95)} 0%, ${alpha('#E2E8F0', 0.95)} 100%)`,
        backdropFilter: 'blur(20px)',
        borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        mt: 'auto',
        py: 6,
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* Brand Section */}
          <Grid item xs={12} md={4}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <School 
                    sx={{ 
                      fontSize: 32, 
                      color: theme.palette.primary.main 
                    }} 
                  />
                  <Typography
                    variant="h5"
                    sx={{
                      fontWeight: 600,
                      color: theme.palette.text.primary,
                    }}
                  >
                    VidyaMitra
                  </Typography>
                </Box>
                <Typography
                  variant="body2"
                  sx={{
                    color: theme.palette.text.secondary,
                    lineHeight: 1.6,
                    mb: 3,
                  }}
                >
                  Empowering Indian education through intelligent student analysis.
                  Supporting CBSE, ICSE, and State boards with AI-powered insights.
                </Typography>

                {/* Contact Information */}
                <Stack spacing={1.5}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                    <LocationOn sx={{ fontSize: 18, color: theme.palette.text.secondary }} />
                    <Typography variant="body2" color="text.secondary">
                      Hyderabad, India
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                    <Phone sx={{ fontSize: 18, color: theme.palette.text.secondary }} />
                    <Typography variant="body2" color="text.secondary">
                      +91 9392233989
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                    <Email sx={{ fontSize: 18, color: theme.palette.text.secondary }} />
                    <Typography variant="body2" color="text.secondary">
                      <EMAIL>
                    </Typography>
                  </Box>
                </Stack>
              </Box>
            </motion.div>
          </Grid>

          {/* Links Sections */}
          {footerSections.map((section, index) => (
            <Grid item xs={6} md={2} key={section.title}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 500,
                    mb: 2,
                    color: theme.palette.text.primary,
                  }}
                >
                  {section.title}
                </Typography>
                <Stack spacing={1}>
                  {section.links.map((link) => (
                    <Link
                      key={link.label}
                      href={link.href}
                      sx={{
                        color: theme.palette.text.secondary,
                        textDecoration: 'none',
                        fontSize: '0.875rem',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          color: theme.palette.primary.main,
                          transform: 'translateX(4px)',
                        },
                      }}
                    >
                      {link.label}
                    </Link>
                  ))}
                </Stack>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        <Divider sx={{ my: 4, opacity: 0.3 }} />

        {/* Bottom Section */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            justifyContent: 'space-between',
            alignItems: 'center',
            gap: 2,
          }}
        >
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.text.secondary,
              textAlign: { xs: 'center', md: 'left' },
            }}
          >
            © {currentYear} VidyaMitra. All rights reserved. Empowering Indian education with AI-driven insights.
          </Typography>

          {/* Social Links */}
          <Stack direction="row" spacing={1}>
            {socialLinks.map((social) => (
              <IconButton
                key={social.label}
                href={social.href}
                aria-label={social.label}
                sx={{
                  color: theme.palette.text.secondary,
                  border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
                  borderRadius: 2,
                  p: 1,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    color: theme.palette.primary.main,
                    borderColor: theme.palette.primary.main,
                    transform: 'translateY(-2px)',
                    background: alpha(theme.palette.primary.main, 0.1),
                  },
                }}
              >
                <social.icon sx={{ fontSize: 18 }} />
              </IconButton>
            ))}
          </Stack>
        </Box>
      </Container>
    </Box>
  );
};

export default ModernFooter;
