/**
 * VidyaMitra Platform - WebSocket Service
 * 
 * Real-time communication service for live updates and notifications
 */

class WebSocketService {
  constructor() {
    this.socket = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 1000; // Start with 1 second
    this.listeners = new Map();
    this.isConnected = false;
    this.url = process.env.REACT_APP_WS_URL || 'ws://localhost:3001';
  }

  // Connect to WebSocket server
  connect(token = null) {
    try {
      const wsUrl = token ? `${this.url}?token=${token}` : this.url;
      this.socket = new WebSocket(wsUrl);

      this.socket.onopen = this.handleOpen.bind(this);
      this.socket.onmessage = this.handleMessage.bind(this);
      this.socket.onclose = this.handleClose.bind(this);
      this.socket.onerror = this.handleError.bind(this);

      console.log('WebSocket: Attempting to connect...');
    } catch (error) {
      console.error('WebSocket: Connection failed', error);
    }
  }

  // Handle connection open
  handleOpen(event) {
    console.log('WebSocket: Connected successfully');
    this.isConnected = true;
    this.reconnectAttempts = 0;
    this.reconnectInterval = 1000;

    // Emit connection event
    this.emit('connected', { timestamp: new Date() });

    // Send authentication if token is available
    const token = localStorage.getItem('authToken');
    if (token) {
      this.send('authenticate', { token });
    }
  }

  // Handle incoming messages
  handleMessage(event) {
    try {
      const data = JSON.parse(event.data);
      console.log('WebSocket: Message received', data);

      // Emit the specific event
      this.emit(data.type, data.payload);

      // Also emit a general message event
      this.emit('message', data);
    } catch (error) {
      console.error('WebSocket: Failed to parse message', error);
    }
  }

  // Handle connection close
  handleClose(event) {
    console.log('WebSocket: Connection closed', event.code, event.reason);
    this.isConnected = false;

    // Emit disconnection event
    this.emit('disconnected', { 
      code: event.code, 
      reason: event.reason,
      timestamp: new Date()
    });

    // Attempt to reconnect if not a clean close
    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.attemptReconnect();
    }
  }

  // Handle connection error
  handleError(error) {
    console.error('WebSocket: Error occurred', error);
    this.emit('error', { error, timestamp: new Date() });
  }

  // Attempt to reconnect
  attemptReconnect() {
    this.reconnectAttempts++;
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`WebSocket: Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);

    setTimeout(() => {
      if (!this.isConnected) {
        this.connect();
      }
    }, delay);
  }

  // Send message to server
  send(type, payload = {}) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      const message = JSON.stringify({ type, payload });
      this.socket.send(message);
      console.log('WebSocket: Message sent', { type, payload });
    } else {
      console.warn('WebSocket: Cannot send message - not connected');
    }
  }

  // Subscribe to events
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // Unsubscribe from events
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Emit events to listeners
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`WebSocket: Error in event listener for ${event}`, error);
        }
      });
    }
  }

  // Disconnect from server
  disconnect() {
    if (this.socket) {
      this.socket.close(1000, 'Client disconnecting');
      this.socket = null;
      this.isConnected = false;
    }
  }

  // Get connection status
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      readyState: this.socket ? this.socket.readyState : WebSocket.CLOSED,
      reconnectAttempts: this.reconnectAttempts,
    };
  }

  // Join a room for targeted updates
  joinRoom(roomId) {
    this.send('join_room', { roomId });
  }

  // Leave a room
  leaveRoom(roomId) {
    this.send('leave_room', { roomId });
  }

  // Subscribe to student updates
  subscribeToStudent(studentId) {
    this.send('subscribe_student', { studentId });
  }

  // Subscribe to class updates
  subscribeToClass(classId) {
    this.send('subscribe_class', { classId });
  }

  // Subscribe to teacher updates
  subscribeToTeacher(teacherId) {
    this.send('subscribe_teacher', { teacherId });
  }

  // Send real-time attendance update
  sendAttendanceUpdate(attendanceData) {
    this.send('attendance_update', attendanceData);
  }

  // Send real-time grade update
  sendGradeUpdate(gradeData) {
    this.send('grade_update', gradeData);
  }

  // Send SWOT analysis update
  sendSWOTUpdate(swotData) {
    this.send('swot_update', swotData);
  }

  // Send notification
  sendNotification(notification) {
    this.send('notification', notification);
  }
}

// Create singleton instance
const websocketService = new WebSocketService();

// React hook for WebSocket
export const useWebSocket = () => {
  const [isConnected, setIsConnected] = React.useState(websocketService.isConnected);
  const [lastMessage, setLastMessage] = React.useState(null);

  React.useEffect(() => {
    const handleConnected = () => setIsConnected(true);
    const handleDisconnected = () => setIsConnected(false);
    const handleMessage = (data) => setLastMessage(data);

    websocketService.on('connected', handleConnected);
    websocketService.on('disconnected', handleDisconnected);
    websocketService.on('message', handleMessage);

    // Connect if not already connected
    if (!websocketService.isConnected) {
      const token = localStorage.getItem('authToken');
      websocketService.connect(token);
    }

    return () => {
      websocketService.off('connected', handleConnected);
      websocketService.off('disconnected', handleDisconnected);
      websocketService.off('message', handleMessage);
    };
  }, []);

  return {
    isConnected,
    lastMessage,
    send: websocketService.send.bind(websocketService),
    subscribe: websocketService.on.bind(websocketService),
    unsubscribe: websocketService.off.bind(websocketService),
    joinRoom: websocketService.joinRoom.bind(websocketService),
    leaveRoom: websocketService.leaveRoom.bind(websocketService),
  };
};

// React hook for real-time notifications
export const useNotifications = () => {
  const [notifications, setNotifications] = React.useState([]);

  React.useEffect(() => {
    const handleNotification = (notification) => {
      setNotifications(prev => [notification, ...prev].slice(0, 50)); // Keep last 50
    };

    websocketService.on('notification', handleNotification);

    return () => {
      websocketService.off('notification', handleNotification);
    };
  }, []);

  const markAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === notificationId 
          ? { ...notif, read: true }
          : notif
      )
    );
    websocketService.send('mark_notification_read', { notificationId });
  };

  const clearAll = () => {
    setNotifications([]);
    websocketService.send('clear_notifications');
  };

  return {
    notifications,
    unreadCount: notifications.filter(n => !n.read).length,
    markAsRead,
    clearAll,
  };
};

// React hook for real-time attendance updates
export const useRealTimeAttendance = (classId) => {
  const [attendanceUpdates, setAttendanceUpdates] = React.useState([]);

  React.useEffect(() => {
    if (!classId) return;

    const handleAttendanceUpdate = (data) => {
      if (data.classId === classId) {
        setAttendanceUpdates(prev => [data, ...prev].slice(0, 100));
      }
    };

    websocketService.on('attendance_update', handleAttendanceUpdate);
    websocketService.subscribeToClass(classId);

    return () => {
      websocketService.off('attendance_update', handleAttendanceUpdate);
      websocketService.leaveRoom(`class_${classId}`);
    };
  }, [classId]);

  return attendanceUpdates;
};

// React hook for real-time grade updates
export const useRealTimeGrades = (classId, subject) => {
  const [gradeUpdates, setGradeUpdates] = React.useState([]);

  React.useEffect(() => {
    if (!classId || !subject) return;

    const handleGradeUpdate = (data) => {
      if (data.classId === classId && data.subject === subject) {
        setGradeUpdates(prev => [data, ...prev].slice(0, 100));
      }
    };

    websocketService.on('grade_update', handleGradeUpdate);
    websocketService.joinRoom(`class_${classId}_${subject}`);

    return () => {
      websocketService.off('grade_update', handleGradeUpdate);
      websocketService.leaveRoom(`class_${classId}_${subject}`);
    };
  }, [classId, subject]);

  return gradeUpdates;
};

export default websocketService;
