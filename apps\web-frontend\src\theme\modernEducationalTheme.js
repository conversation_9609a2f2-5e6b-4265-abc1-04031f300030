import { createTheme } from '@mui/material/styles';

// Modern VidyaMitra Educational Theme with Contemporary UI/UX
// Supports dark/light mode with glassmorphism and modern design elements

const createModernEducationalTheme = (mode = 'light') => {
  const isLight = mode === 'light';
  
  return createTheme({
    palette: {
      mode,
      primary: {
        50: isLight ? '#E8F4FD' : '#0F172A',
        100: isLight ? '#C5E4FA' : '#1E293B',
        200: isLight ? '#9FD3F7' : '#334155',
        300: isLight ? '#79C2F4' : '#475569',
        400: isLight ? '#5CB5F1' : '#64748B',
        500: isLight ? '#2E5BA8' : '#4A90E2',
        600: isLight ? '#1E4A97' : '#6BA3E8',
        700: isLight ? '#1A4086' : '#8BB8ED',
        800: isLight ? '#163675' : '#ABCDF2',
        900: isLight ? '#0F2654' : '#CBE2F7',
        main: isLight ? '#2E5BA8' : '#4A90E2',
        light: isLight ? '#5CB5F1' : '#6BA3E8',
        dark: isLight ? '#1A4086' : '#2E5BA8',
        contrastText: '#FFFFFF',
      },
      secondary: {
        50: isLight ? '#FFF8E1' : '#1A0F00',
        100: isLight ? '#FFECB3' : '#331F00',
        200: isLight ? '#FFE082' : '#4D2E00',
        300: isLight ? '#FFD54F' : '#663D00',
        400: isLight ? '#FFCA28' : '#804D00',
        500: isLight ? '#FF9933' : '#FFB366',
        600: isLight ? '#FFB300' : '#FFCC80',
        700: isLight ? '#FF8F00' : '#FFD699',
        800: isLight ? '#FF6F00' : '#FFE0B3',
        900: isLight ? '#E65100' : '#FFEBCC',
        main: isLight ? '#FF9933' : '#FFB366',
        light: isLight ? '#FFCA28' : '#FFCC80',
        dark: isLight ? '#FF8F00' : '#FF9933',
        contrastText: isLight ? '#000000' : '#FFFFFF',
      },
      success: {
        main: isLight ? '#00C853' : '#4CAF50',
        light: isLight ? '#4CAF50' : '#81C784',
        dark: isLight ? '#00A046' : '#388E3C',
        contrastText: '#FFFFFF',
      },
      warning: {
        main: isLight ? '#FF9800' : '#FFB74D',
        light: isLight ? '#FFB74D' : '#FFCC80',
        dark: isLight ? '#F57C00' : '#FF9800',
        contrastText: isLight ? '#000000' : '#FFFFFF',
      },
      error: {
        main: isLight ? '#F44336' : '#EF5350',
        light: isLight ? '#EF5350' : '#E57373',
        dark: isLight ? '#D32F2F' : '#C62828',
        contrastText: '#FFFFFF',
      },
      info: {
        main: isLight ? '#2196F3' : '#42A5F5',
        light: isLight ? '#42A5F5' : '#64B5F6',
        dark: isLight ? '#1976D2' : '#1565C0',
        contrastText: '#FFFFFF',
      },
      background: {
        default: isLight ? '#F8FAFC' : '#0F172A',
        paper: isLight ? '#FFFFFF' : '#1E293B',
        surface: isLight ? '#F1F5F9' : '#334155',
        elevated: isLight ? '#FFFFFF' : '#475569',
      },
      text: {
        primary: isLight ? '#1E293B' : '#F1F5F9',
        secondary: isLight ? '#64748B' : '#94A3B8',
        disabled: isLight ? '#CBD5E1' : '#475569',
      },
      divider: isLight ? '#E2E8F0' : '#334155',
      
      // Modern glassmorphism colors
      glass: {
        primary: isLight 
          ? 'rgba(255, 255, 255, 0.25)' 
          : 'rgba(255, 255, 255, 0.05)',
        secondary: isLight 
          ? 'rgba(46, 91, 168, 0.1)' 
          : 'rgba(74, 144, 226, 0.1)',
        backdrop: isLight 
          ? 'rgba(255, 255, 255, 0.8)' 
          : 'rgba(15, 23, 42, 0.8)',
        surface: isLight
          ? 'rgba(248, 250, 252, 0.8)'
          : 'rgba(30, 41, 59, 0.8)',
      },
      
      // Modern gradient colors
      gradients: {
        primary: isLight
          ? 'linear-gradient(135deg, #2E5BA8 0%, #4A90E2 100%)'
          : 'linear-gradient(135deg, #4A90E2 0%, #6BA3E8 100%)',
        secondary: isLight
          ? 'linear-gradient(135deg, #FF9933 0%, #FFB366 100%)'
          : 'linear-gradient(135deg, #FFB366 0%, #FFCC80 100%)',
        success: isLight
          ? 'linear-gradient(135deg, #00C853 0%, #4CAF50 100%)'
          : 'linear-gradient(135deg, #4CAF50 0%, #81C784 100%)',
        surface: isLight
          ? 'linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%)'
          : 'linear-gradient(135deg, #1E293B 0%, #334155 100%)',
        glass: isLight
          ? 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)'
          : 'linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%)',
      },
      
      // Board-specific modern colors
      board: {
        CBSE: isLight ? '#2E5BA8' : '#4A90E2',
        ICSE: isLight ? '#FF9933' : '#FFB366',
        STATE: isLight ? '#00C853' : '#4CAF50',
        IB: isLight ? '#9C27B0' : '#BA68C8',
      },
    },

    typography: {
      fontFamily: [
        'Inter',
        'Roboto',
        'Noto Sans',
        '-apple-system',
        'BlinkMacSystemFont',
        '"Segoe UI"',
        'Arial',
        'sans-serif',
      ].join(','),
      
      // Modern heading styles with improved readability
      h1: {
        fontSize: 'clamp(2.5rem, 2rem + 2vw, 4rem)',
        fontWeight: 600, // Reduced from 800
        lineHeight: 1.1,
        letterSpacing: '-0.025em',
        color: isLight ? '#1E293B' : '#F1F5F9', // Solid color instead of gradient for better visibility
      },
      h2: {
        fontSize: 'clamp(2rem, 1.7rem + 1.5vw, 3rem)',
        fontWeight: 500, // Reduced from 700
        lineHeight: 1.2,
        letterSpacing: '-0.025em',
        color: isLight ? '#1E293B' : '#F1F5F9',
      },
      h3: {
        fontSize: 'clamp(1.75rem, 1.5rem + 1.25vw, 2.5rem)',
        fontWeight: 500, // Reduced from 600
        lineHeight: 1.25,
        color: isLight ? '#1E293B' : '#F1F5F9',
      },
      h4: {
        fontSize: 'clamp(1.5rem, 1.3rem + 1vw, 2rem)',
        fontWeight: 500, // Reduced from 600
        lineHeight: 1.3,
        color: isLight ? '#1E293B' : '#F1F5F9',
      },
      h5: {
        fontSize: 'clamp(1.25rem, 1.1rem + 0.75vw, 1.75rem)',
        fontWeight: 500, // Reduced from 600
        lineHeight: 1.35,
        color: isLight ? '#1E293B' : '#F1F5F9',
      },
      h6: {
        fontSize: 'clamp(1.125rem, 1rem + 0.625vw, 1.5rem)',
        fontWeight: 500, // Reduced from 600
        lineHeight: 1.4,
        color: isLight ? '#1E293B' : '#F1F5F9',
      },
      
      body1: {
        fontSize: '1rem',
        lineHeight: 1.6,
        fontWeight: 400,
        color: isLight ? '#334155' : '#CBD5E1', // Better contrast
      },
      body2: {
        fontSize: '0.875rem',
        lineHeight: 1.5,
        fontWeight: 400,
        color: isLight ? '#475569' : '#94A3B8', // Better contrast
      },

      // Modern button typography
      button: {
        fontSize: '0.875rem',
        fontWeight: 500, // Reduced from 600
        textTransform: 'none',
        letterSpacing: '0.025em',
      },
    },

    spacing: 4,

    shape: {
      borderRadius: 16,
      modernRadius: 20,
      glassRadius: 24,
    },

    // Modern shadows with glassmorphism
    shadows: [
      'none',
      isLight 
        ? '0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.08)'
        : '0px 1px 3px rgba(0, 0, 0, 0.24), 0px 1px 2px rgba(0, 0, 0, 0.16)',
      isLight
        ? '0px 3px 6px rgba(0, 0, 0, 0.16), 0px 2px 4px rgba(0, 0, 0, 0.08)'
        : '0px 3px 6px rgba(0, 0, 0, 0.32), 0px 2px 4px rgba(0, 0, 0, 0.16)',
      isLight
        ? '0px 6px 12px rgba(0, 0, 0, 0.16), 0px 4px 8px rgba(0, 0, 0, 0.08)'
        : '0px 6px 12px rgba(0, 0, 0, 0.32), 0px 4px 8px rgba(0, 0, 0, 0.16)',
      isLight
        ? '0px 8px 16px rgba(0, 0, 0, 0.16), 0px 6px 12px rgba(0, 0, 0, 0.08)'
        : '0px 8px 16px rgba(0, 0, 0, 0.32), 0px 6px 12px rgba(0, 0, 0, 0.16)',
      isLight
        ? '0px 12px 24px rgba(0, 0, 0, 0.16), 0px 8px 16px rgba(0, 0, 0, 0.08)'
        : '0px 12px 24px rgba(0, 0, 0, 0.32), 0px 8px 16px rgba(0, 0, 0, 0.16)',
      // Glassmorphism shadow
      isLight
        ? '0px 8px 32px rgba(46, 91, 168, 0.15), 0px 1px 0px rgba(255, 255, 255, 0.05) inset'
        : '0px 8px 32px rgba(0, 0, 0, 0.4), 0px 1px 0px rgba(255, 255, 255, 0.1) inset',
    ],

    transitions: {
      easing: {
        easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
        easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
        easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
        sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
        modern: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
      },
      duration: {
        shortest: 150,
        shorter: 200,
        short: 250,
        standard: 300,
        complex: 375,
        modern: 400,
        enteringScreen: 225,
        leavingScreen: 195,
      },
    },

    // Modern component overrides
    components: {
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: 20,
            border: isLight ? '1px solid rgba(226, 232, 240, 0.8)' : '1px solid rgba(51, 65, 85, 0.8)',
            backdropFilter: 'blur(20px)',
            background: isLight 
              ? 'rgba(255, 255, 255, 0.8)'
              : 'rgba(30, 41, 59, 0.8)',
            transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            '&:hover': {
              transform: 'translateY(-4px)',
              boxShadow: isLight
                ? '0px 20px 40px rgba(46, 91, 168, 0.15)'
                : '0px 20px 40px rgba(0, 0, 0, 0.4)',
            },
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 12,
            textTransform: 'none',
            fontWeight: 600,
            padding: '12px 24px',
            transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            '&:hover': {
              transform: 'translateY(-2px)',
            },
          },
          contained: {
            boxShadow: '0px 4px 12px rgba(46, 91, 168, 0.3)',
            '&:hover': {
              boxShadow: '0px 8px 24px rgba(46, 91, 168, 0.4)',
            },
          },
        },
      },
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 12,
            fontWeight: 500,
            backdropFilter: 'blur(10px)',
          },
        },
      },
    },
  });
};

export default createModernEducationalTheme;
