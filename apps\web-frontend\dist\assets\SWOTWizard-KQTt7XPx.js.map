{"version": 3, "file": "SWOTWizard-KQTt7XPx.js", "sources": ["../../src/components/SWOT/SWOTWizard.jsx"], "sourcesContent": ["/**\n * VidyaMitra Platform - SWOT Analysis Wizard Component\n * \n * Interactive SWOT generation interface with AI-powered suggestions\n * and Indian educational context\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Stepper,\n  Step,\n  StepLabel,\n  Button,\n  TextField,\n  Grid,\n  Chip,\n  Avatar,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  IconButton,\n  Alert,\n  Stack,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Psychology,\n  TrendingUp,\n  Warning,\n  Lightbulb,\n  Add,\n  Delete,\n  Save,\n  ArrowBack,\n  ArrowForward,\n  CheckCircle,\n  AutoAwesome,\n} from '@mui/icons-material';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useNavigate } from 'react-router-dom';\n\n// Sample AI suggestions for Indian educational context\nconst aiSuggestions = {\n  strengths: [\n    'Strong mathematical reasoning skills',\n    'Excellent memory and retention',\n    'Good leadership qualities in group activities',\n    'Consistent academic performance',\n    'Active participation in cultural events',\n    'Strong family support system',\n    'Multilingual communication abilities',\n  ],\n  weaknesses: [\n    'Needs improvement in English speaking confidence',\n    'Time management during examinations',\n    'Hesitant to ask questions in class',\n    'Difficulty with practical applications',\n    'Limited exposure to technology',\n    'Peer pressure sensitivity',\n  ],\n  opportunities: [\n    'Science Olympiad participation',\n    'Student council leadership roles',\n    'Inter-school cultural competitions',\n    'STEM career exploration programs',\n    'Scholarship opportunities for higher education',\n    'Skill development workshops',\n    'Community service projects',\n  ],\n  threats: [\n    'Increased academic competition',\n    'Board examination pressure',\n    'Limited career guidance resources',\n    'Technology adaptation challenges',\n    'Economic constraints for higher education',\n    'Social media distractions',\n  ],\n};\n\nconst SWOTWizard = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const [activeStep, setActiveStep] = useState(0);\n  const [selectedStudent, setSelectedStudent] = useState(null);\n  const [swotData, setSWOTData] = useState({\n    strengths: [],\n    weaknesses: [],\n    opportunities: [],\n    threats: [],\n  });\n  const [currentInput, setCurrentInput] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  const steps = [\n    {\n      label: 'Select Student',\n      icon: Psychology,\n      description: 'Choose student for SWOT analysis',\n    },\n    {\n      label: 'Strengths',\n      icon: TrendingUp,\n      description: 'Identify student strengths',\n    },\n    {\n      label: 'Weaknesses',\n      icon: Warning,\n      description: 'Areas for improvement',\n    },\n    {\n      label: 'Opportunities',\n      icon: Lightbulb,\n      description: 'Growth opportunities',\n    },\n    {\n      label: 'Threats',\n      icon: Warning,\n      description: 'Potential challenges',\n    },\n    {\n      label: 'Review & Save',\n      icon: CheckCircle,\n      description: 'Finalize SWOT analysis',\n    },\n  ];\n\n  // Sample students\n  const students = [\n    { id: 1, name: 'Sanju Kumar Reddy', class: '10-A', avatar: 'S' },\n    { id: 2, name: 'Niraimathi Selvam', class: '10-A', avatar: 'N' },\n    { id: 3, name: 'Mahesh Reddy', class: '10-B', avatar: 'M' },\n    { id: 4, name: 'Ravi Teja Sharma', class: '9-A', avatar: 'R' },\n    { id: 5, name: 'Ankitha Patel', class: '10-A', avatar: 'A' },\n  ];\n\n  const getCurrentCategory = () => {\n    const categories = ['', 'strengths', 'weaknesses', 'opportunities', 'threats'];\n    return categories[activeStep];\n  };\n\n  const handleAddItem = () => {\n    if (currentInput.trim() && activeStep >= 1 && activeStep <= 4) {\n      const category = getCurrentCategory();\n      setSWOTData(prev => ({\n        ...prev,\n        [category]: [...prev[category], currentInput.trim()],\n      }));\n      setCurrentInput('');\n    }\n  };\n\n  const handleRemoveItem = (category, index) => {\n    setSWOTData(prev => ({\n      ...prev,\n      [category]: prev[category].filter((_, i) => i !== index),\n    }));\n  };\n\n  const handleAddSuggestion = (category, suggestion) => {\n    if (!swotData[category].includes(suggestion)) {\n      setSWOTData(prev => ({\n        ...prev,\n        [category]: [...prev[category], suggestion],\n      }));\n    }\n  };\n\n  const handleNext = () => {\n    if (activeStep < steps.length - 1) {\n      setActiveStep(prev => prev + 1);\n    }\n  };\n\n  const handleBack = () => {\n    if (activeStep > 0) {\n      setActiveStep(prev => prev - 1);\n    }\n  };\n\n  const handleSave = async () => {\n    setLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      alert('SWOT Analysis saved successfully!');\n      navigate('/dashboard/students');\n    } catch (error) {\n      console.error('Error saving SWOT analysis:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getCategoryColor = (category) => {\n    switch (category) {\n      case 'strengths':\n        return theme.palette.success.main;\n      case 'weaknesses':\n        return theme.palette.error.main;\n      case 'opportunities':\n        return theme.palette.info.main;\n      case 'threats':\n        return theme.palette.warning.main;\n      default:\n        return theme.palette.primary.main;\n    }\n  };\n\n  const getCategoryIcon = (category) => {\n    switch (category) {\n      case 'strengths':\n        return '💪';\n      case 'weaknesses':\n        return '⚠️';\n      case 'opportunities':\n        return '🚀';\n      case 'threats':\n        return '⚡';\n      default:\n        return '📝';\n    }\n  };\n\n  return (\n    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Box sx={{ mb: 4 }}>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              fontWeight: 600,\n              mb: 1,\n              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n            }}\n          >\n            SWOT Analysis Wizard\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Create comprehensive SWOT analysis with AI-powered suggestions\n          </Typography>\n        </Box>\n      </motion.div>\n\n      {/* Stepper */}\n      <Card sx={{ mb: 4, overflow: 'visible' }}>\n        <CardContent>\n          <Stepper activeStep={activeStep} alternativeLabel>\n            {steps.map((step, index) => (\n              <Step key={step.label}>\n                <StepLabel\n                  StepIconComponent={({ active, completed }) => (\n                    <Box\n                      sx={{\n                        width: 48,\n                        height: 48,\n                        borderRadius: '50%',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        background: completed\n                          ? `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`\n                          : active\n                          ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`\n                          : alpha(theme.palette.action.disabled, 0.12),\n                        color: completed || active ? 'white' : theme.palette.action.disabled,\n                        transition: 'all 0.3s ease',\n                      }}\n                    >\n                      <step.icon sx={{ fontSize: 24 }} />\n                    </Box>\n                  )}\n                >\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 500 }}>\n                    {step.label}\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    {step.description}\n                  </Typography>\n                </StepLabel>\n              </Step>\n            ))}\n          </Stepper>\n        </CardContent>\n      </Card>\n\n      {/* Step Content */}\n      <Card>\n        <CardContent sx={{ p: 4 }}>\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={activeStep}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -20 }}\n              transition={{ duration: 0.3 }}\n            >\n              {/* Student Selection */}\n              {activeStep === 0 && (\n                <Box>\n                  <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\n                    Select Student for SWOT Analysis\n                  </Typography>\n                  <Grid container spacing={2}>\n                    {students.map((student) => (\n                      <Grid item xs={12} sm={6} md={4} key={student.id}>\n                        <Card\n                          sx={{\n                            cursor: 'pointer',\n                            border: selectedStudent?.id === student.id ? `2px solid ${theme.palette.primary.main}` : '1px solid',\n                            borderColor: selectedStudent?.id === student.id ? 'primary.main' : 'divider',\n                            transition: 'all 0.2s ease',\n                            '&:hover': {\n                              transform: 'translateY(-2px)',\n                              boxShadow: theme.shadows[4],\n                            },\n                          }}\n                          onClick={() => setSelectedStudent(student)}\n                        >\n                          <CardContent>\n                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                              <Avatar sx={{ bgcolor: 'primary.main' }}>\n                                {student.avatar}\n                              </Avatar>\n                              <Box>\n                                <Typography variant=\"subtitle1\" sx={{ fontWeight: 600 }}>\n                                  {student.name}\n                                </Typography>\n                                <Typography variant=\"body2\" color=\"text.secondary\">\n                                  Class {student.class}\n                                </Typography>\n                              </Box>\n                            </Box>\n                          </CardContent>\n                        </Card>\n                      </Grid>\n                    ))}\n                  </Grid>\n                </Box>\n              )}\n\n              {/* SWOT Categories */}\n              {activeStep >= 1 && activeStep <= 4 && (\n                <Box>\n                  <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\n                    {getCategoryIcon(getCurrentCategory())} {steps[activeStep].label}\n                  </Typography>\n                  \n                  {/* Input Section */}\n                  <Box sx={{ mb: 4 }}>\n                    <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n                      <TextField\n                        fullWidth\n                        label={`Add ${steps[activeStep].label.toLowerCase()}`}\n                        value={currentInput}\n                        onChange={(e) => setCurrentInput(e.target.value)}\n                        onKeyPress={(e) => e.key === 'Enter' && handleAddItem()}\n                        placeholder={`Enter student ${steps[activeStep].label.toLowerCase()}...`}\n                      />\n                      <Button\n                        variant=\"contained\"\n                        startIcon={<Add />}\n                        onClick={handleAddItem}\n                        disabled={!currentInput.trim()}\n                      >\n                        Add\n                      </Button>\n                    </Stack>\n                  </Box>\n\n                  <Grid container spacing={3}>\n                    {/* Current Items */}\n                    <Grid item xs={12} md={6}>\n                      <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 600 }}>\n                        Current {steps[activeStep].label}\n                      </Typography>\n                      <List>\n                        {swotData[getCurrentCategory()]?.map((item, index) => (\n                          <ListItem\n                            key={index}\n                            sx={{\n                              border: `1px solid ${alpha(getCategoryColor(getCurrentCategory()), 0.2)}`,\n                              borderRadius: 1,\n                              mb: 1,\n                              background: alpha(getCategoryColor(getCurrentCategory()), 0.05),\n                            }}\n                          >\n                            <ListItemText primary={item} />\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleRemoveItem(getCurrentCategory(), index)}\n                              color=\"error\"\n                            >\n                              <Delete />\n                            </IconButton>\n                          </ListItem>\n                        ))}\n                      </List>\n                    </Grid>\n\n                    {/* AI Suggestions */}\n                    <Grid item xs={12} md={6}>\n                      <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 600 }}>\n                        <AutoAwesome sx={{ mr: 1, verticalAlign: 'middle' }} />\n                        AI Suggestions\n                      </Typography>\n                      <List>\n                        {aiSuggestions[getCurrentCategory()]?.map((suggestion, index) => (\n                          <ListItem\n                            key={index}\n                            sx={{\n                              border: '1px solid',\n                              borderColor: 'divider',\n                              borderRadius: 1,\n                              mb: 1,\n                              cursor: 'pointer',\n                              '&:hover': {\n                                background: alpha(theme.palette.primary.main, 0.05),\n                              },\n                            }}\n                            onClick={() => handleAddSuggestion(getCurrentCategory(), suggestion)}\n                          >\n                            <ListItemIcon>\n                              <Add color=\"primary\" />\n                            </ListItemIcon>\n                            <ListItemText primary={suggestion} />\n                          </ListItem>\n                        ))}\n                      </List>\n                    </Grid>\n                  </Grid>\n                </Box>\n              )}\n\n              {/* Review Step */}\n              {activeStep === 5 && (\n                <Box>\n                  <Typography variant=\"h6\" sx={{ mb: 3, fontWeight: 600 }}>\n                    Review SWOT Analysis\n                  </Typography>\n                  \n                  {selectedStudent && (\n                    <Alert severity=\"info\" sx={{ mb: 3 }}>\n                      SWOT Analysis for <strong>{selectedStudent.name}</strong> - Class {selectedStudent.class}\n                    </Alert>\n                  )}\n\n                  <Grid container spacing={3}>\n                    {Object.entries(swotData).map(([category, items]) => (\n                      <Grid item xs={12} md={6} key={category}>\n                        <Card\n                          sx={{\n                            border: `2px solid ${alpha(getCategoryColor(category), 0.2)}`,\n                            background: `linear-gradient(135deg, ${alpha(getCategoryColor(category), 0.05)} 0%, ${alpha(getCategoryColor(category), 0.02)} 100%)`,\n                          }}\n                        >\n                          <CardContent>\n                            <Typography\n                              variant=\"h6\"\n                              sx={{\n                                fontWeight: 600,\n                                color: getCategoryColor(category),\n                                mb: 2,\n                              }}\n                            >\n                              {getCategoryIcon(category)} {category.charAt(0).toUpperCase() + category.slice(1)}\n                            </Typography>\n                            <List dense>\n                              {items.map((item, index) => (\n                                <ListItem key={index}>\n                                  <ListItemText primary={`• ${item}`} />\n                                </ListItem>\n                              ))}\n                            </List>\n                          </CardContent>\n                        </Card>\n                      </Grid>\n                    ))}\n                  </Grid>\n                </Box>\n              )}\n            </motion.div>\n          </AnimatePresence>\n\n          {/* Navigation Buttons */}\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\n            <Button\n              onClick={handleBack}\n              disabled={activeStep === 0}\n              startIcon={<ArrowBack />}\n              variant=\"outlined\"\n            >\n              Back\n            </Button>\n            \n            <Button\n              onClick={activeStep === steps.length - 1 ? handleSave : handleNext}\n              endIcon={activeStep === steps.length - 1 ? <Save /> : <ArrowForward />}\n              variant=\"contained\"\n              disabled={activeStep === 0 && !selectedStudent}\n              loading={loading}\n              sx={{\n                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              }}\n            >\n              {activeStep === steps.length - 1 ? 'Save SWOT Analysis' : 'Next'}\n            </Button>\n          </Box>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default SWOTWizard;\n"], "names": ["aiSuggestions", "SWOTWizard", "theme", "useTheme", "navigate", "useNavigate", "activeStep", "setActiveStep", "useState", "selectedStudent", "setSelectedStudent", "swotData", "setSWOTData", "currentInput", "setCurrentInput", "loading", "setLoading", "steps", "Psychology", "TrendingUp", "Warning", "Lightbulb", "CheckCircle", "students", "getCurrentCategory", "handleAddItem", "category", "prev", "handleRemoveItem", "index", "_", "i", "handleAddSuggestion", "suggestion", "handleNext", "handleBack", "handleSave", "resolve", "error", "getCategoryColor", "getCategoryIcon", "jsxs", "Box", "jsx", "motion", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Stepper", "step", "Step", "<PERSON><PERSON><PERSON><PERSON>", "active", "completed", "alpha", "AnimatePresence", "Grid", "student", "Avatar", "<PERSON><PERSON>", "TextField", "e", "<PERSON><PERSON>", "Add", "List", "_a", "item", "ListItem", "ListItemText", "IconButton", "Delete", "AutoAwesome", "_b", "ListItemIcon", "<PERSON><PERSON>", "items", "ArrowBack", "Save", "ArrowForward"], "mappings": "uZAgDA,MAAMA,GAAgB,CACpB,UAAW,CACT,uCACA,iCACA,gDACA,kCACA,0CACA,+BACA,sCACF,EACA,WAAY,CACV,mDACA,sCACA,qCACA,yCACA,iCACA,2BACF,EACA,cAAe,CACb,iCACA,mCACA,qCACA,mCACA,iDACA,8BACA,4BACF,EACA,QAAS,CACP,iCACA,6BACA,oCACA,mCACA,4CACA,2BAAA,CAEJ,EAEMC,GAAa,IAAM,SACvB,MAAMC,EAAQC,EAAS,EACjBC,EAAWC,GAAY,EACvB,CAACC,EAAYC,CAAa,EAAIC,EAAAA,SAAS,CAAC,EACxC,CAACC,EAAiBC,CAAkB,EAAIF,EAAAA,SAAS,IAAI,EACrD,CAACG,EAAUC,CAAW,EAAIJ,WAAS,CACvC,UAAW,CAAC,EACZ,WAAY,CAAC,EACb,cAAe,CAAC,EAChB,QAAS,CAAA,CAAC,CACX,EACK,CAACK,EAAcC,CAAe,EAAIN,EAAAA,SAAS,EAAE,EAC7C,CAACO,EAASC,CAAU,EAAIR,EAAAA,SAAS,EAAK,EAEtCS,EAAQ,CACZ,CACE,MAAO,iBACP,KAAMC,EACN,YAAa,kCACf,EACA,CACE,MAAO,YACP,KAAMC,EACN,YAAa,4BACf,EACA,CACE,MAAO,aACP,KAAMC,EACN,YAAa,uBACf,EACA,CACE,MAAO,gBACP,KAAMC,EACN,YAAa,sBACf,EACA,CACE,MAAO,UACP,KAAMD,EACN,YAAa,sBACf,EACA,CACE,MAAO,gBACP,KAAME,EACN,YAAa,wBAAA,CAEjB,EAGMC,EAAW,CACf,CAAE,GAAI,EAAG,KAAM,oBAAqB,MAAO,OAAQ,OAAQ,GAAI,EAC/D,CAAE,GAAI,EAAG,KAAM,oBAAqB,MAAO,OAAQ,OAAQ,GAAI,EAC/D,CAAE,GAAI,EAAG,KAAM,eAAgB,MAAO,OAAQ,OAAQ,GAAI,EAC1D,CAAE,GAAI,EAAG,KAAM,mBAAoB,MAAO,MAAO,OAAQ,GAAI,EAC7D,CAAE,GAAI,EAAG,KAAM,gBAAiB,MAAO,OAAQ,OAAQ,GAAI,CAC7D,EAEMC,EAAqB,IACN,CAAC,GAAI,YAAa,aAAc,gBAAiB,SAAS,EAC3DlB,CAAU,EAGxBmB,EAAgB,IAAM,CAC1B,GAAIZ,EAAa,KAAK,GAAKP,GAAc,GAAKA,GAAc,EAAG,CAC7D,MAAMoB,EAAWF,EAAmB,EACpCZ,EAAqBe,IAAA,CACnB,GAAGA,EACH,CAACD,CAAQ,EAAG,CAAC,GAAGC,EAAKD,CAAQ,EAAGb,EAAa,KAAM,CAAA,CAAA,EACnD,EACFC,EAAgB,EAAE,CAAA,CAEtB,EAEMc,EAAmB,CAACF,EAAUG,IAAU,CAC5CjB,EAAqBe,IAAA,CACnB,GAAGA,EACH,CAACD,CAAQ,EAAGC,EAAKD,CAAQ,EAAE,OAAO,CAACI,EAAGC,IAAMA,IAAMF,CAAK,CAAA,EACvD,CACJ,EAEMG,EAAsB,CAACN,EAAUO,IAAe,CAC/CtB,EAASe,CAAQ,EAAE,SAASO,CAAU,GACzCrB,EAAqBe,IAAA,CACnB,GAAGA,EACH,CAACD,CAAQ,EAAG,CAAC,GAAGC,EAAKD,CAAQ,EAAGO,CAAU,CAAA,EAC1C,CAEN,EAEMC,EAAa,IAAM,CACnB5B,EAAaW,EAAM,OAAS,GAChBV,EAAAoB,GAAQA,EAAO,CAAC,CAElC,EAEMQ,EAAa,IAAM,CACnB7B,EAAa,GACDC,EAAAoB,GAAQA,EAAO,CAAC,CAElC,EAEMS,EAAa,SAAY,CAC7BpB,EAAW,EAAI,EACX,GAAA,CAEF,MAAM,IAAI,QAAQqB,GAAW,WAAWA,EAAS,GAAI,CAAC,EACtD,MAAM,mCAAmC,EACzCjC,EAAS,qBAAqB,QACvBkC,EAAO,CACN,QAAA,MAAM,8BAA+BA,CAAK,CAAA,QAClD,CACAtB,EAAW,EAAK,CAAA,CAEpB,EAEMuB,EAAoBb,GAAa,CACrC,OAAQA,EAAU,CAChB,IAAK,YACI,OAAAxB,EAAM,QAAQ,QAAQ,KAC/B,IAAK,aACI,OAAAA,EAAM,QAAQ,MAAM,KAC7B,IAAK,gBACI,OAAAA,EAAM,QAAQ,KAAK,KAC5B,IAAK,UACI,OAAAA,EAAM,QAAQ,QAAQ,KAC/B,QACS,OAAAA,EAAM,QAAQ,QAAQ,IAAA,CAEnC,EAEMsC,EAAmBd,GAAa,CACpC,OAAQA,EAAU,CAChB,IAAK,YACI,MAAA,KACT,IAAK,aACI,MAAA,KACT,IAAK,gBACI,MAAA,KACT,IAAK,UACI,MAAA,IACT,QACS,MAAA,IAAA,CAEb,EAGE,OAAAe,OAACC,EAAI,CAAA,GAAI,CAAE,SAAU,KAAM,GAAI,OAAQ,EAAG,CAAA,EAExC,SAAA,CAAAC,EAAA,IAACC,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,GAAI,EAC9B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,EAAI,EAE5B,gBAACF,EAAI,CAAA,GAAI,CAAE,GAAI,CACb,EAAA,SAAA,CAAAC,EAAA,IAACE,EAAA,CACC,QAAQ,KACR,GAAI,CACF,WAAY,IACZ,GAAI,EACJ,WAAY,2BAA2B3C,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,SACrG,qBAAsB,OACtB,oBAAqB,cACrB,eAAgB,MAClB,EACD,SAAA,sBAAA,CAED,QACC2C,EAAW,CAAA,QAAQ,QAAQ,MAAM,iBAAiB,SAEnD,gEAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAGAF,EAAAA,IAACG,GAAK,GAAI,CAAE,GAAI,EAAG,SAAU,SAAU,EACrC,SAACH,EAAA,IAAAI,EAAA,CACC,eAACC,EAAQ,CAAA,WAAA1C,EAAwB,iBAAgB,GAC9C,SAAAW,EAAM,IAAI,CAACgC,EAAMpB,IAChBc,EAAAA,IAACO,EACC,CAAA,SAAAT,EAAA,KAACU,EAAA,CACC,kBAAmB,CAAC,CAAE,OAAAC,EAAQ,UAAAC,CAC5B,IAAAV,EAAA,IAACD,EAAA,CACC,GAAI,CACF,MAAO,GACP,OAAQ,GACR,aAAc,MACd,QAAS,OACT,WAAY,SACZ,eAAgB,SAChB,WAAYW,EACR,2BAA2BnD,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,QAAQ,IAAI,SACvFkD,EACA,2BAA2BlD,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,SACzFoD,EAAMpD,EAAM,QAAQ,OAAO,SAAU,GAAI,EAC7C,MAAOmD,GAAaD,EAAS,QAAUlD,EAAM,QAAQ,OAAO,SAC5D,WAAY,eACd,EAEA,SAAAyC,EAAAA,IAACM,EAAK,KAAL,CAAU,GAAI,CAAE,SAAU,GAAM,CAAA,CAAA,CACnC,EAGF,SAAA,CAACN,EAAAA,IAAAE,EAAA,CAAW,QAAQ,YAAY,GAAI,CAAE,WAAY,GAAA,EAC/C,SAAAI,EAAK,KACR,CAAA,QACCJ,EAAW,CAAA,QAAQ,UAAU,MAAM,iBACjC,WAAK,WACR,CAAA,CAAA,CAAA,CAAA,GA7BOI,EAAK,KA+BhB,CACD,CAAA,CACH,EACF,CACF,CAAA,EAGAN,EAAAA,IAACG,GACC,SAACL,OAAAM,EAAA,CAAY,GAAI,CAAE,EAAG,GACpB,SAAA,CAACJ,EAAAA,IAAAY,GAAA,CAAgB,KAAK,OACpB,SAAAd,EAAA,KAACG,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,KAAM,CAAE,QAAS,EAAG,EAAG,GAAI,EAC3B,WAAY,CAAE,SAAU,EAAI,EAG3B,SAAA,CAAetC,IAAA,UACboC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,GAAI,EAAG,SAEzD,kCAAA,CAAA,QACCW,EAAK,CAAA,UAAS,GAAC,QAAS,EACtB,WAAS,IAAKC,GACZd,MAAAa,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EAAG,GAAI,EAC5B,SAAAb,EAAA,IAACG,EAAA,CACC,GAAI,CACF,OAAQ,UACR,QAAQrC,GAAA,YAAAA,EAAiB,MAAOgD,EAAQ,GAAK,aAAavD,EAAM,QAAQ,QAAQ,IAAI,GAAK,YACzF,aAAaO,GAAA,YAAAA,EAAiB,MAAOgD,EAAQ,GAAK,eAAiB,UACnE,WAAY,gBACZ,UAAW,CACT,UAAW,mBACX,UAAWvD,EAAM,QAAQ,CAAC,CAAA,CAE9B,EACA,QAAS,IAAMQ,EAAmB+C,CAAO,EAEzC,SAACd,EAAA,IAAAI,EAAA,CACC,SAACN,EAAAA,KAAAC,EAAA,CAAI,GAAI,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,CACrD,EAAA,SAAA,CAAAC,MAACe,GAAO,GAAI,CAAE,QAAS,cAAe,EACnC,WAAQ,OACX,SACChB,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,YAAY,GAAI,CAAE,WAAY,GAAA,EAC/C,SAAAY,EAAQ,IACX,CAAA,EACChB,EAAA,KAAAI,EAAA,CAAW,QAAQ,QAAQ,MAAM,iBAAiB,SAAA,CAAA,SAC1CY,EAAQ,KAAA,CACjB,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,GA5BkCA,EAAQ,EA8B9C,CACD,CACH,CAAA,CAAA,EACF,EAIDnD,GAAc,GAAKA,GAAc,UAC/BoC,EACC,CAAA,SAAA,CAACD,EAAAA,KAAAI,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,GAAA,EAC/C,SAAA,CAAAL,EAAgBhB,GAAoB,EAAE,IAAEP,EAAMX,CAAU,EAAE,KAAA,EAC7D,EAGCqC,EAAA,IAAAD,EAAA,CAAI,GAAI,CAAE,GAAI,CAAE,EACf,SAACD,EAAAA,KAAAkB,GAAA,CAAM,UAAU,MAAM,QAAS,EAAG,WAAW,SAC5C,SAAA,CAAAhB,EAAA,IAACiB,GAAA,CACC,UAAS,GACT,MAAO,OAAO3C,EAAMX,CAAU,EAAE,MAAM,aAAa,GACnD,MAAOO,EACP,SAAWgD,GAAM/C,EAAgB+C,EAAE,OAAO,KAAK,EAC/C,WAAaA,GAAMA,EAAE,MAAQ,SAAWpC,EAAc,EACtD,YAAa,iBAAiBR,EAAMX,CAAU,EAAE,MAAM,aAAa,KAAA,CACrE,EACAqC,EAAA,IAACmB,EAAA,CACC,QAAQ,YACR,gBAAYC,EAAI,EAAA,EAChB,QAAStC,EACT,SAAU,CAACZ,EAAa,KAAK,EAC9B,SAAA,KAAA,CAAA,CAED,CAAA,CACF,CACF,CAAA,EAEC4B,EAAA,KAAAe,EAAA,CAAK,UAAS,GAAC,QAAS,EAEvB,SAAA,CAAAf,OAACe,GAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAA,CAACf,EAAAA,KAAAI,EAAA,CAAW,QAAQ,YAAY,GAAI,CAAE,GAAI,EAAG,WAAY,GAAA,EAAO,SAAA,CAAA,WACrD5B,EAAMX,CAAU,EAAE,KAAA,EAC7B,EACAqC,EAAAA,IAACqB,GACE,UAASC,EAAAtD,EAAAa,GAAoB,IAApB,YAAAyC,EAAuB,IAAI,CAACC,EAAMrC,IAC1CY,EAAA,KAAC0B,EAAA,CAEC,GAAI,CACF,OAAQ,aAAab,EAAMf,EAAiBf,GAAoB,EAAG,EAAG,CAAC,GACvE,aAAc,EACd,GAAI,EACJ,WAAY8B,EAAMf,EAAiBf,EAAmB,CAAC,EAAG,GAAI,CAChE,EAEA,SAAA,CAACmB,EAAAA,IAAAyB,EAAA,CAAa,QAASF,CAAM,CAAA,EAC7BvB,EAAA,IAAC0B,GAAA,CACC,KAAK,QACL,QAAS,IAAMzC,EAAiBJ,EAAA,EAAsBK,CAAK,EAC3D,MAAM,QAEN,eAACyC,GAAO,CAAA,CAAA,CAAA,CAAA,CACV,CAAA,EAfKzC,CAAA,EAkBX,CAAA,CAAA,EACF,SAGC2B,EAAK,CAAA,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAA,CAACf,EAAAA,KAAAI,EAAA,CAAW,QAAQ,YAAY,GAAI,CAAE,GAAI,EAAG,WAAY,GAAA,EACvD,SAAA,CAAAF,MAAC4B,IAAY,GAAI,CAAE,GAAI,EAAG,cAAe,UAAY,EAAE,gBAAA,EAEzD,EACA5B,EAAAA,IAACqB,GACE,UAAcQ,EAAAxE,GAAAwB,GAAoB,IAApB,YAAAgD,EAAuB,IAAI,CAACvC,EAAYJ,IACrDY,EAAA,KAAC0B,EAAA,CAEC,GAAI,CACF,OAAQ,YACR,YAAa,UACb,aAAc,EACd,GAAI,EACJ,OAAQ,UACR,UAAW,CACT,WAAYb,EAAMpD,EAAM,QAAQ,QAAQ,KAAM,GAAI,CAAA,CAEtD,EACA,QAAS,IAAM8B,EAAoBR,EAAA,EAAsBS,CAAU,EAEnE,SAAA,CAAAU,MAAC8B,GACC,CAAA,SAAA9B,EAAA,IAACoB,EAAI,CAAA,MAAM,SAAU,CAAA,EACvB,EACApB,EAAAA,IAACyB,EAAa,CAAA,QAASnC,CAAY,CAAA,CAAA,CAAA,EAhB9BJ,CAAA,EAmBX,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAIDvB,IAAe,GACdmC,EAAAA,KAACC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAW,QAAQ,KAAK,GAAI,CAAE,GAAI,EAAG,WAAY,GAAI,EAAG,SAEzD,sBAAA,CAAA,EAECpC,UACEiE,GAAM,CAAA,SAAS,OAAO,GAAI,CAAE,GAAI,CAAK,EAAA,SAAA,CAAA,qBAClB/B,EAAAA,IAAC,SAAQ,CAAA,SAAAlC,EAAgB,IAAK,CAAA,EAAS,YAAUA,EAAgB,KAAA,EACrF,EAGFkC,EAAAA,IAACa,GAAK,UAAS,GAAC,QAAS,EACtB,SAAA,OAAO,QAAQ7C,CAAQ,EAAE,IAAI,CAAC,CAACe,EAAUiD,CAAK,IAC5ChC,MAAAa,EAAA,CAAK,KAAI,GAAC,GAAI,GAAI,GAAI,EACrB,SAAAb,EAAA,IAACG,EAAA,CACC,GAAI,CACF,OAAQ,aAAaQ,EAAMf,EAAiBb,CAAQ,EAAG,EAAG,CAAC,GAC3D,WAAY,2BAA2B4B,EAAMf,EAAiBb,CAAQ,EAAG,GAAI,CAAC,QAAQ4B,EAAMf,EAAiBb,CAAQ,EAAG,GAAI,CAAC,QAC/H,EAEA,gBAACqB,EACC,CAAA,SAAA,CAAAN,EAAA,KAACI,EAAA,CACC,QAAQ,KACR,GAAI,CACF,WAAY,IACZ,MAAON,EAAiBb,CAAQ,EAChC,GAAI,CACN,EAEC,SAAA,CAAAc,EAAgBd,CAAQ,EAAE,IAAEA,EAAS,OAAO,CAAC,EAAE,cAAgBA,EAAS,MAAM,CAAC,CAAA,CAAA,CAClF,EACAiB,EAAAA,IAACqB,GAAK,MAAK,GACR,WAAM,IAAI,CAACE,EAAMrC,IAChBc,EAAAA,IAACwB,GACC,SAACxB,EAAA,IAAAyB,EAAA,CAAa,QAAS,KAAKF,CAAI,EAAI,CAAA,GADvBrC,CAEf,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EA1B6BH,CA2B/B,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,EA5LGpB,CAAA,EA+LT,EAGAmC,EAAAA,KAACC,EAAI,CAAA,GAAI,CAAE,QAAS,OAAQ,eAAgB,gBAAiB,GAAI,CAAA,EAC/D,SAAA,CAAAC,EAAA,IAACmB,EAAA,CACC,QAAS3B,EACT,SAAU7B,IAAe,EACzB,gBAAYsE,GAAU,EAAA,EACtB,QAAQ,WACT,SAAA,MAAA,CAED,EAEAjC,EAAA,IAACmB,EAAA,CACC,QAASxD,IAAeW,EAAM,OAAS,EAAImB,EAAaF,EACxD,QAAS5B,IAAeW,EAAM,OAAS,EAAK0B,EAAAA,IAAAkC,GAAA,EAAK,EAAKlC,MAACmC,GAAa,CAAA,CAAA,EACpE,QAAQ,YACR,SAAUxE,IAAe,GAAK,CAACG,EAC/B,QAAAM,EACA,GAAI,CACF,WAAY,2BAA2Bb,EAAM,QAAQ,QAAQ,IAAI,QAAQA,EAAM,QAAQ,UAAU,IAAI,QACvG,EAEC,SAAeI,IAAAW,EAAM,OAAS,EAAI,qBAAuB,MAAA,CAAA,CAC5D,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ"}