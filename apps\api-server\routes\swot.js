/**
 * VidyaMitra Platform - SWOT Analysis Routes
 * 
 * This file defines all API routes related to SWOT analysis
 * including generation, retrieval, and management of SWOT analyses.
 */

const express = require('express');
const router = express.Router();
const axios = require('axios');
const {
  Student, AcademicPerformance, Subject, QuarterlyAttendance,
  BehavioralSummary, ExtracurricularActivity, SWOTAnalysis, AuditLog
} = require('../models/index');

/**
 * @route   GET /api/v1/swot/student/:studentId
 * @desc    Get SWOT analysis for a student
 * @access  Private (Teacher, Admin, Parent)
 */
router.get('/student/:studentId', async (req, res) => {
  try {
    const { studentId } = req.params;
    const { academic_year, quarter } = req.query;

    // Build filter
    const filter = { student_id: studentId };
    if (academic_year) filter.academic_year = academic_year;
    if (quarter) filter.quarter = parseInt(quarter);

    // Get SWOT analyses
    const swotAnalyses = await SWOTAnalysis.find(filter)
      .populate('student_id', 'name student_id grade_level board stream')
      .sort({ academic_year: -1, quarter: -1 })
      .lean();

    if (swotAnalyses.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No SWOT analysis found for this student'
      });
    }

    res.json({
      success: true,
      data: swotAnalyses
    });

  } catch (error) {
    console.error('Error fetching SWOT analysis:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch SWOT analysis'
    });
  }
});

/**
 * @route   POST /api/v1/swot/generate/:studentId
 * @desc    Generate SWOT analysis for a student
 * @access  Private (Teacher, Admin)
 */
router.post('/generate/:studentId', async (req, res) => {
  try {
    const { studentId } = req.params;
    const { academic_year, quarter } = req.body;

    // Validate required fields
    if (!academic_year || !quarter) {
      return res.status(400).json({
        success: false,
        error: 'Academic year and quarter are required'
      });
    }

    // Check if student exists
    const student = await Student.findById(studentId);
    if (!student) {
      return res.status(404).json({
        success: false,
        error: 'Student not found'
      });
    }

    // Check if SWOT analysis already exists for this period
    const existingSWOT = await SWOTAnalysis.findOne({
      student_id: studentId,
      academic_year,
      quarter
    });

    if (existingSWOT) {
      return res.status(409).json({
        success: false,
        error: 'SWOT analysis already exists for this period',
        data: existingSWOT
      });
    }

    // Gather student data for analysis
    const studentData = await gatherStudentData(studentId, academic_year, quarter);

    // Generate SWOT analysis using AI service
    const swotResult = await generateSWOTAnalysis(studentData);

    // Create SWOT analysis record
    const swotAnalysis = new SWOTAnalysis({
      analysis_id: `SWOT${Date.now()}${Math.random().toString(36).substr(2, 4)}`,
      student_id: studentId,
      quarter,
      academic_year,
      analysis_date: new Date(),
      generated_by: 'AI',
      analysis_data: swotResult,
      strengths: swotResult.strengths || [],
      weaknesses: swotResult.weaknesses || [],
      opportunities: swotResult.opportunities || [],
      threats: swotResult.threats || [],
      recommendations: swotResult.recommendations || [],
      overall_score: swotResult.overall_score || 0,
      confidence_level: swotResult.confidence_level || 0.8
    });

    await swotAnalysis.save();

    // Log the action
    await AuditLog.create({
      log_id: `LOG${Date.now()}${Math.random().toString(36).substr(2, 4)}`,
      user_id: req.user.id,
      action: 'SWOT_GENERATED',
      resource_type: 'SWOTAnalysis',
      resource_id: swotAnalysis._id.toString(),
      details: {
        student_id: student.student_id,
        academic_year,
        quarter,
        generated_by: 'AI'
      }
    });

    res.status(201).json({
      success: true,
      data: swotAnalysis,
      message: 'SWOT analysis generated successfully'
    });

  } catch (error) {
    console.error('Error generating SWOT analysis:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate SWOT analysis'
    });
  }
});

/**
 * @route   PUT /api/v1/swot/:analysisId
 * @desc    Update SWOT analysis
 * @access  Private (Teacher, Admin)
 */
router.put('/:analysisId', async (req, res) => {
  try {
    const { analysisId } = req.params;
    const updateData = req.body;

    const swotAnalysis = await SWOTAnalysis.findByIdAndUpdate(
      analysisId,
      updateData,
      { new: true, runValidators: true }
    );

    if (!swotAnalysis) {
      return res.status(404).json({
        success: false,
        error: 'SWOT analysis not found'
      });
    }

    // Log the action
    await AuditLog.create({
      log_id: `LOG${Date.now()}${Math.random().toString(36).substr(2, 4)}`,
      user_id: req.user.id,
      action: 'UPDATE',
      resource_type: 'SWOTAnalysis',
      resource_id: swotAnalysis._id.toString(),
      details: { updated_fields: Object.keys(updateData) }
    });

    res.json({
      success: true,
      data: swotAnalysis,
      message: 'SWOT analysis updated successfully'
    });

  } catch (error) {
    console.error('Error updating SWOT analysis:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update SWOT analysis'
    });
  }
});

/**
 * @route   GET /api/v1/swot/class/:classId
 * @desc    Get SWOT analysis summary for a class
 * @access  Private (Teacher, Admin)
 */
router.get('/class/:classId', async (req, res) => {
  try {
    const { classId } = req.params;
    const { academic_year, quarter } = req.query;

    // Get all students in the class
    const students = await Student.find({
      homeroom: classId,
      status: 'Active'
    }).select('_id name student_id');

    if (students.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No students found in this class'
      });
    }

    const studentIds = students.map(s => s._id);

    // Build filter for SWOT analyses
    const filter = { student_id: { $in: studentIds } };
    if (academic_year) filter.academic_year = academic_year;
    if (quarter) filter.quarter = parseInt(quarter);

    // Get SWOT analyses for all students
    const swotAnalyses = await SWOTAnalysis.find(filter)
      .populate('student_id', 'name student_id')
      .sort({ overall_score: -1 })
      .lean();

    // Calculate class statistics
    const stats = calculateClassSWOTStats(swotAnalyses);

    res.json({
      success: true,
      data: {
        class_id: classId,
        total_students: students.length,
        analyzed_students: swotAnalyses.length,
        statistics: stats,
        student_analyses: swotAnalyses
      }
    });

  } catch (error) {
    console.error('Error fetching class SWOT summary:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch class SWOT summary'
    });
  }
});

// Helper function to gather student data for SWOT analysis
async function gatherStudentData(studentId, academicYear, quarter) {
  try {
    // Get student basic info
    const student = await Student.findById(studentId).lean();

    // Get academic performance
    const academicPerformance = await AcademicPerformance.findOne({
      student_id: studentId,
      academic_year: academicYear,
      quarter
    }).lean();

    // Get subjects for this performance
    let subjects = [];
    if (academicPerformance) {
      subjects = await Subject.find({
        performance_id: academicPerformance._id
      }).lean();
    }

    // Get attendance
    const attendance = await QuarterlyAttendance.findOne({
      student_id: studentId,
      academic_year: academicYear,
      quarter
    }).lean();

    // Get behavioral summary
    const behavior = await BehavioralSummary.findOne({
      student_id: studentId,
      academic_year: academicYear,
      quarter
    }).lean();

    // Get extracurricular activities
    const activities = await ExtracurricularActivity.find({
      student_id: studentId,
      academic_year: academicYear
    }).lean();

    return {
      student,
      academic_performance: academicPerformance,
      subjects,
      attendance,
      behavior,
      extracurricular_activities: activities
    };

  } catch (error) {
    console.error('Error gathering student data:', error);
    throw error;
  }
}

// Helper function to generate SWOT analysis using AI service
async function generateSWOTAnalysis(studentData) {
  try {
    const aiServiceUrl = process.env.AI_SERVICE_URL || 'http://localhost:8000';
    
    const response = await axios.post(`${aiServiceUrl}/generate-swot`, {
      student_data: studentData
    }, {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    return response.data;

  } catch (error) {
    console.error('Error calling AI service:', error);
    
    // Fallback: Generate basic SWOT analysis
    return generateBasicSWOT(studentData);
  }
}

// Helper function to generate basic SWOT analysis as fallback
function generateBasicSWOT(studentData) {
  const { student, academic_performance, subjects, attendance, behavior, extracurricular_activities } = studentData;
  
  const strengths = [];
  const weaknesses = [];
  const opportunities = [];
  const threats = [];
  const recommendations = [];

  // Analyze academic performance
  if (academic_performance && subjects) {
    const highPerformingSubjects = subjects.filter(s => s.score >= 85);
    const lowPerformingSubjects = subjects.filter(s => s.score < 70);

    highPerformingSubjects.forEach(subject => {
      strengths.push({
        category: 'Academic',
        type: 'Subject Strength',
        description: `Excellence in ${subject.subject_name} (${subject.grade}, ${subject.score}%)`,
        score: subject.score
      });
    });

    lowPerformingSubjects.forEach(subject => {
      weaknesses.push({
        category: 'Academic',
        type: 'Subject Weakness',
        description: `Needs improvement in ${subject.subject_name} (${subject.grade}, ${subject.score}%)`,
        score: subject.score
      });
    });
  }

  // Analyze attendance
  if (attendance) {
    if (attendance.attendance_rate >= 95) {
      strengths.push({
        category: 'Attendance',
        type: 'Consistency',
        description: `Excellent attendance rate (${attendance.attendance_rate}%)`,
        score: attendance.attendance_rate
      });
    } else if (attendance.attendance_rate < 85) {
      threats.push({
        category: 'Attendance',
        type: 'Inconsistency',
        description: `Low attendance rate (${attendance.attendance_rate}%) may impact academic performance`,
        severity: 'Medium'
      });
    }
  }

  return {
    strengths,
    weaknesses,
    opportunities,
    threats,
    recommendations,
    overall_score: calculateOverallScore(strengths, weaknesses),
    confidence_level: 0.7
  };
}

// Helper function to calculate overall SWOT score
function calculateOverallScore(strengths, weaknesses) {
  const strengthScore = strengths.reduce((sum, s) => sum + (s.score || 80), 0) / Math.max(strengths.length, 1);
  const weaknessScore = weaknesses.reduce((sum, w) => sum + (w.score || 60), 0) / Math.max(weaknesses.length, 1);
  
  return Math.round((strengthScore * 0.7) + (weaknessScore * 0.3));
}

// Helper function to calculate class SWOT statistics
function calculateClassSWOTStats(swotAnalyses) {
  if (swotAnalyses.length === 0) return {};

  const totalScore = swotAnalyses.reduce((sum, analysis) => sum + (analysis.overall_score || 0), 0);
  const avgScore = totalScore / swotAnalyses.length;

  const strengthCounts = {};
  const weaknessCounts = {};

  swotAnalyses.forEach(analysis => {
    analysis.strengths?.forEach(strength => {
      strengthCounts[strength.category] = (strengthCounts[strength.category] || 0) + 1;
    });
    
    analysis.weaknesses?.forEach(weakness => {
      weaknessCounts[weakness.category] = (weaknessCounts[weakness.category] || 0) + 1;
    });
  });

  return {
    average_score: Math.round(avgScore),
    total_analyses: swotAnalyses.length,
    common_strengths: strengthCounts,
    common_weaknesses: weaknessCounts
  };
}

module.exports = router;
