/**
 * VidyaMitra Platform - API Hooks
 * 
 * React Query hooks for efficient data fetching and caching
 */

import { useQuery, useMutation, useQueryClient } from 'react-query';
import { 
  studentsAPI, 
  swotAPI, 
  attendanceAPI, 
  gradesAPI, 
  classesAPI, 
  teachersAPI, 
  reportsAPI, 
  analyticsAPI 
} from '../services/api';

// Query Keys
export const QUERY_KEYS = {
  STUDENTS: 'students',
  STUDENT: 'student',
  STUDENT_PERFORMANCE: 'studentPerformance',
  SWOT: 'swot',
  SWOT_TEMPLATES: 'swotTemplates',
  ATTENDANCE: 'attendance',
  ATTENDANCE_STATS: 'attendanceStats',
  GRADES: 'grades',
  GRADE_STATS: 'gradeStats',
  CLASSES: 'classes',
  CLASS: 'class',
  CLASS_STUDENTS: 'classStudents',
  TEACHER_DASHBOARD: 'teacherDashboard',
  TEACHER_CLASSES: 'teacherClasses',
  ANALYTICS: 'analytics',
  REPORTS: 'reports',
};

// Students Hooks
export const useStudents = (params = {}) => {
  return useQuery(
    [QUERY_KEYS.STUDENTS, params],
    () => studentsAPI.getStudents(params),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  );
};

export const useStudent = (studentId) => {
  return useQuery(
    [QUERY_KEYS.STUDENT, studentId],
    () => studentsAPI.getStudent(studentId),
    {
      enabled: !!studentId,
      staleTime: 5 * 60 * 1000,
    }
  );
};

export const useStudentPerformance = (studentId, params = {}) => {
  return useQuery(
    [QUERY_KEYS.STUDENT_PERFORMANCE, studentId, params],
    () => studentsAPI.getStudentPerformance(studentId, params),
    {
      enabled: !!studentId,
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
};

export const useCreateStudent = () => {
  const queryClient = useQueryClient();
  
  return useMutation(studentsAPI.createStudent, {
    onSuccess: () => {
      queryClient.invalidateQueries(QUERY_KEYS.STUDENTS);
    },
  });
};

export const useUpdateStudent = () => {
  const queryClient = useQueryClient();
  
  return useMutation(
    ({ studentId, studentData }) => studentsAPI.updateStudent(studentId, studentData),
    {
      onSuccess: (data, variables) => {
        queryClient.invalidateQueries([QUERY_KEYS.STUDENT, variables.studentId]);
        queryClient.invalidateQueries(QUERY_KEYS.STUDENTS);
      },
    }
  );
};

// SWOT Analysis Hooks
export const useStudentSWOT = (studentId) => {
  return useQuery(
    [QUERY_KEYS.SWOT, studentId],
    () => swotAPI.getStudentSWOT(studentId),
    {
      enabled: !!studentId,
      staleTime: 10 * 60 * 1000, // 10 minutes
    }
  );
};

export const useSWOTTemplates = () => {
  return useQuery(
    QUERY_KEYS.SWOT_TEMPLATES,
    swotAPI.getSWOTTemplates,
    {
      staleTime: 30 * 60 * 1000, // 30 minutes
      cacheTime: 60 * 60 * 1000, // 1 hour
    }
  );
};

export const useCreateSWOTAnalysis = () => {
  const queryClient = useQueryClient();
  
  return useMutation(swotAPI.createSWOTAnalysis, {
    onSuccess: (data) => {
      queryClient.invalidateQueries([QUERY_KEYS.SWOT, data.studentId]);
      queryClient.invalidateQueries(QUERY_KEYS.ANALYTICS);
    },
  });
};

// Attendance Hooks
export const useAttendance = (classId, date, subject = null) => {
  return useQuery(
    [QUERY_KEYS.ATTENDANCE, classId, date, subject],
    () => attendanceAPI.getAttendance(classId, date, subject),
    {
      enabled: !!(classId && date),
      staleTime: 1 * 60 * 1000, // 1 minute
    }
  );
};

export const useStudentAttendance = (studentId, params = {}) => {
  return useQuery(
    [QUERY_KEYS.ATTENDANCE, studentId, params],
    () => attendanceAPI.getStudentAttendance(studentId, params),
    {
      enabled: !!studentId,
      staleTime: 5 * 60 * 1000,
    }
  );
};

export const useAttendanceStats = (classId, params = {}) => {
  return useQuery(
    [QUERY_KEYS.ATTENDANCE_STATS, classId, params],
    () => attendanceAPI.getAttendanceStats(classId, params),
    {
      enabled: !!classId,
      staleTime: 5 * 60 * 1000,
    }
  );
};

export const useMarkAttendance = () => {
  const queryClient = useQueryClient();
  
  return useMutation(attendanceAPI.markAttendance, {
    onSuccess: (data) => {
      queryClient.invalidateQueries(QUERY_KEYS.ATTENDANCE);
      queryClient.invalidateQueries(QUERY_KEYS.ATTENDANCE_STATS);
      queryClient.invalidateQueries(QUERY_KEYS.ANALYTICS);
    },
  });
};

// Grades Hooks
export const useGrades = (classId, subject, assessment = null) => {
  return useQuery(
    [QUERY_KEYS.GRADES, classId, subject, assessment],
    () => gradesAPI.getGrades(classId, subject, assessment),
    {
      enabled: !!(classId && subject),
      staleTime: 2 * 60 * 1000,
    }
  );
};

export const useStudentGrades = (studentId, params = {}) => {
  return useQuery(
    [QUERY_KEYS.GRADES, studentId, params],
    () => gradesAPI.getStudentGrades(studentId, params),
    {
      enabled: !!studentId,
      staleTime: 5 * 60 * 1000,
    }
  );
};

export const useGradeStats = (classId, subject, params = {}) => {
  return useQuery(
    [QUERY_KEYS.GRADE_STATS, classId, subject, params],
    () => gradesAPI.getGradeStats(classId, subject, params),
    {
      enabled: !!(classId && subject),
      staleTime: 5 * 60 * 1000,
    }
  );
};

export const useSubmitGrades = () => {
  const queryClient = useQueryClient();
  
  return useMutation(gradesAPI.submitGrades, {
    onSuccess: () => {
      queryClient.invalidateQueries(QUERY_KEYS.GRADES);
      queryClient.invalidateQueries(QUERY_KEYS.GRADE_STATS);
      queryClient.invalidateQueries(QUERY_KEYS.ANALYTICS);
    },
  });
};

// Classes Hooks
export const useClasses = () => {
  return useQuery(
    QUERY_KEYS.CLASSES,
    classesAPI.getClasses,
    {
      staleTime: 15 * 60 * 1000, // 15 minutes
      cacheTime: 30 * 60 * 1000, // 30 minutes
    }
  );
};

export const useClass = (classId) => {
  return useQuery(
    [QUERY_KEYS.CLASS, classId],
    () => classesAPI.getClass(classId),
    {
      enabled: !!classId,
      staleTime: 10 * 60 * 1000,
    }
  );
};

export const useClassStudents = (classId) => {
  return useQuery(
    [QUERY_KEYS.CLASS_STUDENTS, classId],
    () => classesAPI.getClassStudents(classId),
    {
      enabled: !!classId,
      staleTime: 5 * 60 * 1000,
    }
  );
};

// Teacher Hooks
export const useTeacherDashboard = (teacherId) => {
  return useQuery(
    [QUERY_KEYS.TEACHER_DASHBOARD, teacherId],
    () => teachersAPI.getDashboardData(teacherId),
    {
      enabled: !!teacherId,
      staleTime: 2 * 60 * 1000,
      refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    }
  );
};

export const useTeacherClasses = (teacherId) => {
  return useQuery(
    [QUERY_KEYS.TEACHER_CLASSES, teacherId],
    () => teachersAPI.getTeacherClasses(teacherId),
    {
      enabled: !!teacherId,
      staleTime: 15 * 60 * 1000,
    }
  );
};

// Analytics Hooks
export const useDashboardAnalytics = (params = {}) => {
  return useQuery(
    [QUERY_KEYS.ANALYTICS, 'dashboard', params],
    () => analyticsAPI.getDashboardAnalytics(params),
    {
      staleTime: 5 * 60 * 1000,
      refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
    }
  );
};

export const usePerformanceTrends = (params = {}) => {
  return useQuery(
    [QUERY_KEYS.ANALYTICS, 'performance', params],
    () => analyticsAPI.getPerformanceTrends(params),
    {
      staleTime: 10 * 60 * 1000,
    }
  );
};

export const useAttendanceTrends = (params = {}) => {
  return useQuery(
    [QUERY_KEYS.ANALYTICS, 'attendance', params],
    () => analyticsAPI.getAttendanceTrends(params),
    {
      staleTime: 10 * 60 * 1000,
    }
  );
};

// Reports Hooks
export const useGenerateReport = () => {
  const queryClient = useQueryClient();
  
  return useMutation(
    ({ type, id, reportType, params }) => {
      if (type === 'student') {
        return reportsAPI.generateStudentReport(id, reportType, params);
      } else if (type === 'class') {
        return reportsAPI.generateClassReport(id, reportType, params);
      }
      throw new Error('Invalid report type');
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_KEYS.REPORTS);
      },
    }
  );
};

// Custom hooks for complex operations
export const useStudentData = (studentId) => {
  const studentQuery = useStudent(studentId);
  const performanceQuery = useStudentPerformance(studentId);
  const swotQuery = useStudentSWOT(studentId);
  const attendanceQuery = useStudentAttendance(studentId);
  const gradesQuery = useStudentGrades(studentId);

  return {
    student: studentQuery.data,
    performance: performanceQuery.data,
    swot: swotQuery.data,
    attendance: attendanceQuery.data,
    grades: gradesQuery.data,
    isLoading: studentQuery.isLoading || performanceQuery.isLoading,
    error: studentQuery.error || performanceQuery.error,
  };
};

export const useClassData = (classId) => {
  const classQuery = useClass(classId);
  const studentsQuery = useClassStudents(classId);
  const attendanceStatsQuery = useAttendanceStats(classId);

  return {
    classInfo: classQuery.data,
    students: studentsQuery.data,
    attendanceStats: attendanceStatsQuery.data,
    isLoading: classQuery.isLoading || studentsQuery.isLoading,
    error: classQuery.error || studentsQuery.error,
  };
};
