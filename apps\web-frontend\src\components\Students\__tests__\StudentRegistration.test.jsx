/**
 * VidyaMitra Platform - StudentRegistration Component Tests
 * 
 * Comprehensive unit tests for student registration with Indian educational context
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import StudentRegistration from '../StudentRegistration';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }) => children,
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Test theme
const theme = createTheme();

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

describe('StudentRegistration Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders student registration form', () => {
    render(
      <TestWrapper>
        <StudentRegistration />
      </TestWrapper>
    );

    expect(screen.getByText('Student Registration')).toBeInTheDocument();
    expect(screen.getByText('Register new students with comprehensive information')).toBeInTheDocument();
  });

  test('displays stepper with correct steps', () => {
    render(
      <TestWrapper>
        <StudentRegistration />
      </TestWrapper>
    );

    expect(screen.getByText('Basic Information')).toBeInTheDocument();
    expect(screen.getByText('Academic Details')).toBeInTheDocument();
    expect(screen.getByText('Contact Information')).toBeInTheDocument();
    expect(screen.getByText('Review & Submit')).toBeInTheDocument();
  });

  test('validates required fields in basic information step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentRegistration />
      </TestWrapper>
    );

    // Try to proceed without filling required fields
    const nextButton = screen.getByRole('button', { name: /next/i });
    await user.click(nextButton);

    // Should show validation errors
    await waitFor(() => {
      expect(screen.getByText(/first name is required/i)).toBeInTheDocument();
    });
  });

  test('allows navigation between steps', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentRegistration />
      </TestWrapper>
    );

    // Fill required fields
    await user.type(screen.getByLabelText(/first name/i), 'Sanju');
    await user.type(screen.getByLabelText(/last name/i), 'Kumar Reddy');
    await user.type(screen.getByLabelText(/date of birth/i), '2010-01-15');

    // Navigate to next step
    const nextButton = screen.getByRole('button', { name: /next/i });
    await user.click(nextButton);

    // Should be on academic details step
    await waitFor(() => {
      expect(screen.getByText('Academic Information')).toBeInTheDocument();
    });

    // Navigate back
    const backButton = screen.getByRole('button', { name: /back/i });
    await user.click(backButton);

    // Should be back on basic information step
    await waitFor(() => {
      expect(screen.getByText('Personal Details')).toBeInTheDocument();
    });
  });

  test('supports Indian educational boards selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentRegistration />
      </TestWrapper>
    );

    // Navigate to academic details step
    await user.type(screen.getByLabelText(/first name/i), 'Niraimathi');
    await user.type(screen.getByLabelText(/last name/i), 'Selvam');
    await user.type(screen.getByLabelText(/date of birth/i), '2010-03-20');

    const nextButton = screen.getByRole('button', { name: /next/i });
    await user.click(nextButton);

    // Check for Indian educational boards
    await waitFor(() => {
      expect(screen.getByText(/board/i)).toBeInTheDocument();
    });

    // Open board dropdown
    const boardSelect = screen.getByLabelText(/board/i);
    await user.click(boardSelect);

    // Should show Indian boards
    await waitFor(() => {
      expect(screen.getByText('CBSE')).toBeInTheDocument();
      expect(screen.getByText('ICSE')).toBeInTheDocument();
      expect(screen.getByText('State Board - Andhra Pradesh')).toBeInTheDocument();
      expect(screen.getByText('State Board - Telangana')).toBeInTheDocument();
    });
  });

  test('validates Indian phone number format', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentRegistration />
      </TestWrapper>
    );

    // Navigate to contact information step
    // Fill basic info
    await user.type(screen.getByLabelText(/first name/i), 'Mahesh');
    await user.type(screen.getByLabelText(/last name/i), 'Reddy');
    await user.type(screen.getByLabelText(/date of birth/i), '2010-05-10');
    
    let nextButton = screen.getByRole('button', { name: /next/i });
    await user.click(nextButton);

    // Fill academic info
    await waitFor(() => {
      expect(screen.getByLabelText(/class/i)).toBeInTheDocument();
    });
    
    const classSelect = screen.getByLabelText(/class/i);
    await user.click(classSelect);
    await user.click(screen.getByText('Class 10'));

    nextButton = screen.getByRole('button', { name: /next/i });
    await user.click(nextButton);

    // Now on contact information step
    await waitFor(() => {
      expect(screen.getByText('Contact Details')).toBeInTheDocument();
    });

    // Test invalid phone number
    const phoneInput = screen.getByLabelText(/phone number/i);
    await user.type(phoneInput, '123456');

    // Should show validation error
    await waitFor(() => {
      expect(screen.getByText(/invalid phone number format/i)).toBeInTheDocument();
    });

    // Test valid Indian phone number
    await user.clear(phoneInput);
    await user.type(phoneInput, '+91 9876543210');

    // Error should disappear
    await waitFor(() => {
      expect(screen.queryByText(/invalid phone number format/i)).not.toBeInTheDocument();
    });
  });

  test('displays review information correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentRegistration />
      </TestWrapper>
    );

    // Fill all steps
    // Basic Information
    await user.type(screen.getByLabelText(/first name/i), 'Ravi Teja');
    await user.type(screen.getByLabelText(/last name/i), 'Sharma');
    await user.type(screen.getByLabelText(/date of birth/i), '2010-07-25');

    let nextButton = screen.getByRole('button', { name: /next/i });
    await user.click(nextButton);

    // Academic Details
    await waitFor(() => {
      const classSelect = screen.getByLabelText(/class/i);
      user.click(classSelect);
    });
    await user.click(screen.getByText('Class 9'));

    nextButton = screen.getByRole('button', { name: /next/i });
    await user.click(nextButton);

    // Contact Information
    await waitFor(() => {
      expect(screen.getByLabelText(/phone number/i)).toBeInTheDocument();
    });
    await user.type(screen.getByLabelText(/phone number/i), '+91 9876543210');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');

    nextButton = screen.getByRole('button', { name: /next/i });
    await user.click(nextButton);

    // Review step
    await waitFor(() => {
      expect(screen.getByText('Review Registration Details')).toBeInTheDocument();
      expect(screen.getByText('Ravi Teja Sharma')).toBeInTheDocument();
      expect(screen.getByText('Class 9')).toBeInTheDocument();
      expect(screen.getByText('+91 9876543210')).toBeInTheDocument();
    });
  });

  test('handles form submission', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentRegistration />
      </TestWrapper>
    );

    // Fill and navigate through all steps (abbreviated for test)
    await user.type(screen.getByLabelText(/first name/i), 'Ankitha');
    await user.type(screen.getByLabelText(/last name/i), 'Patel');
    await user.type(screen.getByLabelText(/date of birth/i), '2010-09-15');

    // Navigate through steps quickly
    for (let i = 0; i < 3; i++) {
      const nextButton = screen.getByRole('button', { name: /next/i });
      await user.click(nextButton);
      
      if (i === 0) {
        // Academic step
        await waitFor(() => {
          const classSelect = screen.getByLabelText(/class/i);
          user.click(classSelect);
        });
        await user.click(screen.getByText('Class 10'));
      } else if (i === 1) {
        // Contact step
        await waitFor(() => {
          expect(screen.getByLabelText(/phone number/i)).toBeInTheDocument();
        });
        await user.type(screen.getByLabelText(/phone number/i), '+91 9876543210');
      }
    }

    // Submit form
    await waitFor(() => {
      const submitButton = screen.getByRole('button', { name: /register student/i });
      expect(submitButton).toBeInTheDocument();
      user.click(submitButton);
    });

    // Should navigate after successful submission
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard/students');
    });
  });

  test('supports accessibility features', () => {
    render(
      <TestWrapper>
        <StudentRegistration />
      </TestWrapper>
    );

    // Check for proper ARIA labels
    expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/last name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/date of birth/i)).toBeInTheDocument();

    // Check for proper heading structure
    expect(screen.getByRole('heading', { name: /student registration/i })).toBeInTheDocument();

    // Check for proper button roles
    expect(screen.getByRole('button', { name: /next/i })).toBeInTheDocument();
  });

  test('handles Indian cultural context', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentRegistration />
      </TestWrapper>
    );

    // Test Indian name patterns
    await user.type(screen.getByLabelText(/first name/i), 'Sai Krishna');
    await user.type(screen.getByLabelText(/last name/i), 'Venkata Ramana');

    // Should accept multi-word Indian names
    expect(screen.getByDisplayValue('Sai Krishna')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Venkata Ramana')).toBeInTheDocument();

    // Navigate to academic step to check for Indian context
    const nextButton = screen.getByRole('button', { name: /next/i });
    await user.click(nextButton);

    await waitFor(() => {
      // Should have Indian language options
      expect(screen.getByText(/mother tongue/i)).toBeInTheDocument();
    });
  });
});

// Integration test for complete workflow
describe('StudentRegistration Integration', () => {
  test('complete registration workflow', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentRegistration />
      </TestWrapper>
    );

    // Complete workflow test
    const studentData = {
      firstName: 'Priya',
      lastName: 'Agarwal',
      dateOfBirth: '2010-12-05',
      class: 'Class 9',
      phone: '+91 9876543210',
      email: '<EMAIL>'
    };

    // Step 1: Basic Information
    await user.type(screen.getByLabelText(/first name/i), studentData.firstName);
    await user.type(screen.getByLabelText(/last name/i), studentData.lastName);
    await user.type(screen.getByLabelText(/date of birth/i), studentData.dateOfBirth);
    
    await user.click(screen.getByRole('button', { name: /next/i }));

    // Step 2: Academic Details
    await waitFor(() => {
      const classSelect = screen.getByLabelText(/class/i);
      user.click(classSelect);
    });
    await user.click(screen.getByText(studentData.class));
    
    await user.click(screen.getByRole('button', { name: /next/i }));

    // Step 3: Contact Information
    await waitFor(() => {
      expect(screen.getByLabelText(/phone number/i)).toBeInTheDocument();
    });
    await user.type(screen.getByLabelText(/phone number/i), studentData.phone);
    await user.type(screen.getByLabelText(/email/i), studentData.email);
    
    await user.click(screen.getByRole('button', { name: /next/i }));

    // Step 4: Review & Submit
    await waitFor(() => {
      expect(screen.getByText('Review Registration Details')).toBeInTheDocument();
      expect(screen.getByText(`${studentData.firstName} ${studentData.lastName}`)).toBeInTheDocument();
    });

    // Submit
    const submitButton = screen.getByRole('button', { name: /register student/i });
    await user.click(submitButton);

    // Verify navigation
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard/students');
    });
  });
});
