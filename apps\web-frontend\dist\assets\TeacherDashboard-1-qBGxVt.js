import{u as $,d as j,j as e,B as n,g as t,G as l,b as d,e as o,a1 as E,b8 as u,b9 as g,y as b,m as y,k as c,a2 as f,a9 as v,b2 as P,aw as I,b3 as z,b4 as D,b5 as C,b6 as i,b7 as M,A as R,n as A,L,I as k,_ as N,aa as F}from"./mui-Cjipzt4F.js";import{r as S}from"./vendor-BfWiUekA.js";import{e as Y,D as _,C as O,a as V,L as H,P as K,b as Q,B as U,A as Z,p as q,c as J,d as X,i as ee}from"./charts-Dx2u7Eir.js";import{u as ae,a as se,m as x}from"./index-_PFqhbKW.js";O.register(V,H,K,Q,U,Z,q,J,X,ee);const h={name:"Mrs. <PERSON><PERSON>",classes:[{grade:10,section:"A",students:35,board:"CBSE"},{grade:10,section:"B",students:32,board:"CBSE"},{grade:9,section:"A",students:38,board:"CBSE"}],totalStudents:105,pendingGrades:12,upcomingTests:3},te=[{id:1,name:"Sanju Kumar Reddy",class:"10-A",subject:"Mathematics",lastGrade:"A1",attendance:95,status:"Excellent"},{id:2,name:"Niraimathi Selvam",class:"10-A",subject:"Mathematics",lastGrade:"A2",attendance:92,status:"Good"},{id:3,name:"Mahesh Reddy",class:"10-B",subject:"Mathematics",lastGrade:"B1",attendance:88,status:"Average"},{id:4,name:"Ravi Teja Sharma",class:"9-A",subject:"Physics",lastGrade:"A1",attendance:94,status:"Excellent"},{id:5,name:"Ankitha Patel",class:"10-A",subject:"Mathematics",lastGrade:"A2",attendance:90,status:"Good"},{id:6,name:"Sirisha Nair",class:"10-B",subject:"Physics",lastGrade:"A1",attendance:96,status:"Excellent"},{id:7,name:"Priya Agarwal",class:"9-A",subject:"Mathematics",lastGrade:"B2",attendance:85,status:"Average"}],me=()=>{const a=$(),r=ae(),{t:ne}=se(["common","dashboard"]),[ie,re]=S.useState(h.classes[0]),[le,de]=S.useState(!1),w={labels:["A1","A2","B1","B2","C1","C2"],datasets:[{label:"Grade Distribution",data:[12,15,8,6,3,1],backgroundColor:[a.palette.success.main,a.palette.success.light,a.palette.info.main,a.palette.info.light,a.palette.warning.main,a.palette.error.main],borderWidth:2,borderColor:"#fff"}]},G={labels:["Mon","Tue","Wed","Thu","Fri","Sat"],datasets:[{label:"Class 10-A",data:[95,92,94,96,93,89],borderColor:a.palette.primary.main,backgroundColor:j(a.palette.primary.main,.1),fill:!0,tension:.4},{label:"Class 10-B",data:[88,90,87,91,89,85],borderColor:a.palette.secondary.main,backgroundColor:j(a.palette.secondary.main,.1),fill:!0,tension:.4}]},m=()=>{r("/dashboard/grades")},p=()=>{r("/dashboard/attendance")},T=s=>{r(`/dashboard/students/${s}`)},W=s=>{r(`/dashboard/students/${s}/swot`)},B=s=>{switch(s){case"Excellent":return"success";case"Good":return"info";case"Average":return"warning";case"Needs Attention":return"error";default:return"default"}};return e.jsxs(n,{sx:{maxWidth:1400,mx:"auto",p:3},children:[e.jsx(x.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:e.jsxs(n,{sx:{mb:4},children:[e.jsx(t,{variant:"h4",sx:{fontWeight:600,mb:1,background:`linear-gradient(135deg, ${a.palette.primary.main} 0%, ${a.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"Teacher Dashboard"}),e.jsxs(t,{variant:"body1",color:"text.secondary",children:["Welcome back, ",h.name,"! Manage your classes and track student progress."]})]})}),e.jsxs(l,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(l,{item:!0,xs:12,sm:6,md:3,children:e.jsx(x.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},children:e.jsx(d,{sx:{background:`linear-gradient(135deg, ${a.palette.primary.main} 0%, ${a.palette.primary.dark} 100%)`,color:"white",cursor:"pointer",transition:"transform 0.2s ease","&:hover":{transform:"translateY(-4px)"}},children:e.jsx(o,{children:e.jsxs(n,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(n,{children:[e.jsx(t,{variant:"h4",sx:{fontWeight:600},children:h.totalStudents}),e.jsx(t,{variant:"body2",sx:{opacity:.9},children:"Total Students"})]}),e.jsx(E,{sx:{fontSize:40,opacity:.8}})]})})})})}),e.jsx(l,{item:!0,xs:12,sm:6,md:3,children:e.jsx(x.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:e.jsx(d,{sx:{background:`linear-gradient(135deg, ${a.palette.success.main} 0%, ${a.palette.success.dark} 100%)`,color:"white",cursor:"pointer",transition:"transform 0.2s ease","&:hover":{transform:"translateY(-4px)"}},onClick:m,children:e.jsx(o,{children:e.jsxs(n,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(n,{children:[e.jsx(t,{variant:"h4",sx:{fontWeight:600},children:h.pendingGrades}),e.jsx(t,{variant:"body2",sx:{opacity:.9},children:"Pending Grades"})]}),e.jsx(u,{sx:{fontSize:40,opacity:.8}})]})})})})}),e.jsx(l,{item:!0,xs:12,sm:6,md:3,children:e.jsx(x.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},children:e.jsx(d,{sx:{background:`linear-gradient(135deg, ${a.palette.warning.main} 0%, ${a.palette.warning.dark} 100%)`,color:"white",cursor:"pointer",transition:"transform 0.2s ease","&:hover":{transform:"translateY(-4px)"}},children:e.jsx(o,{children:e.jsxs(n,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(n,{children:[e.jsx(t,{variant:"h4",sx:{fontWeight:600},children:h.upcomingTests}),e.jsx(t,{variant:"body2",sx:{opacity:.9},children:"Upcoming Tests"})]}),e.jsx(g,{sx:{fontSize:40,opacity:.8}})]})})})})}),e.jsx(l,{item:!0,xs:12,sm:6,md:3,children:e.jsx(x.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},children:e.jsx(d,{sx:{background:`linear-gradient(135deg, ${a.palette.info.main} 0%, ${a.palette.info.dark} 100%)`,color:"white",cursor:"pointer",transition:"transform 0.2s ease","&:hover":{transform:"translateY(-4px)"}},onClick:p,children:e.jsx(o,{children:e.jsxs(n,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(n,{children:[e.jsx(t,{variant:"h4",sx:{fontWeight:600},children:"92%"}),e.jsx(t,{variant:"body2",sx:{opacity:.9},children:"Avg Attendance"})]}),e.jsx(b,{sx:{fontSize:40,opacity:.8}})]})})})})})]}),e.jsx(d,{sx:{mb:4},children:e.jsxs(o,{children:[e.jsx(t,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Quick Actions"}),e.jsxs(y,{direction:"row",spacing:2,flexWrap:"wrap",useFlexGap:!0,children:[e.jsx(c,{variant:"contained",startIcon:e.jsx(u,{}),onClick:m,sx:{background:`linear-gradient(135deg, ${a.palette.primary.main} 0%, ${a.palette.secondary.main} 100%)`},children:"Enter Grades"}),e.jsx(c,{variant:"outlined",startIcon:e.jsx(b,{}),onClick:p,children:"Mark Attendance"}),e.jsx(c,{variant:"outlined",startIcon:e.jsx(f,{}),onClick:()=>r("/dashboard/swot/wizard"),children:"SWOT Analysis"}),e.jsx(c,{variant:"outlined",startIcon:e.jsx(g,{}),onClick:()=>r("/dashboard/reports/generate"),children:"Generate Reports"}),e.jsx(c,{variant:"outlined",startIcon:e.jsx(v,{}),onClick:()=>r("/dashboard/students/register"),children:"Add Student"})]})]})}),e.jsxs(l,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(l,{item:!0,xs:12,md:8,children:e.jsx(d,{children:e.jsxs(o,{children:[e.jsx(t,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Weekly Attendance Trends"}),e.jsx(n,{sx:{height:300},children:e.jsx(Y,{data:G,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!1,min:80,max:100}}}})})]})})}),e.jsx(l,{item:!0,xs:12,md:4,children:e.jsx(d,{children:e.jsxs(o,{children:[e.jsx(t,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Grade Distribution"}),e.jsx(n,{sx:{height:300},children:e.jsx(_,{data:w,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}}})})]})})})]}),e.jsx(d,{children:e.jsxs(o,{children:[e.jsxs(n,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsx(t,{variant:"h6",sx:{fontWeight:600,color:"primary.main"},children:"Recent Student Performance"}),e.jsx(c,{variant:"outlined",size:"small",onClick:()=>r("/dashboard/students"),children:"View All Students"})]}),e.jsx(P,{component:I,variant:"outlined",children:e.jsxs(z,{children:[e.jsx(D,{children:e.jsxs(C,{children:[e.jsx(i,{children:"Student Name"}),e.jsx(i,{children:"Class"}),e.jsx(i,{children:"Subject"}),e.jsx(i,{children:"Last Grade"}),e.jsx(i,{children:"Attendance"}),e.jsx(i,{children:"Status"}),e.jsx(i,{children:"Actions"})]})}),e.jsx(M,{children:te.map(s=>e.jsxs(C,{hover:!0,children:[e.jsx(i,{children:e.jsxs(n,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx(R,{sx:{width:32,height:32},children:s.name.charAt(0)}),e.jsx(t,{variant:"body2",sx:{fontWeight:500},children:s.name})]})}),e.jsx(i,{children:s.class}),e.jsx(i,{children:s.subject}),e.jsx(i,{children:e.jsx(A,{label:s.lastGrade,color:s.lastGrade.startsWith("A")?"success":"warning",size:"small"})}),e.jsx(i,{children:e.jsxs(n,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(L,{variant:"determinate",value:s.attendance,sx:{width:60,height:6,borderRadius:3},color:s.attendance>=90?"success":s.attendance>=75?"warning":"error"}),e.jsxs(t,{variant:"body2",children:[s.attendance,"%"]})]})}),e.jsx(i,{children:e.jsx(A,{label:s.status,color:B(s.status),size:"small",variant:"outlined"})}),e.jsx(i,{children:e.jsxs(y,{direction:"row",spacing:1,children:[e.jsx(k,{size:"small",onClick:()=>T(s.id),color:"primary",children:e.jsx(N,{})}),e.jsx(k,{size:"small",onClick:()=>W(s.id),color:"secondary",children:e.jsx(f,{})})]})})]},s.id))})]})})]})}),e.jsx(F,{color:"primary",sx:{position:"fixed",bottom:24,right:24,background:`linear-gradient(135deg, ${a.palette.primary.main} 0%, ${a.palette.secondary.main} 100%)`},onClick:()=>r("/dashboard/students/register"),children:e.jsx(v,{})})]})};export{me as default};
//# sourceMappingURL=TeacherDashboard-1-qBGxVt.js.map
