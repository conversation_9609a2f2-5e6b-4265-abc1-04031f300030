import { createTheme } from '@mui/material/styles';

// Enhanced Indian Educational Theme with Cultural Design Patterns
// Incorporates traditional Indian design elements and educational context

const indianEducationalTheme = createTheme({
  // Enhanced color palette with Indian cultural significance
  palette: {
    mode: 'light',
    primary: {
      50: '#E8F4FD',
      100: '#C5E4FA',
      200: '#9FD3F7',
      300: '#79C2F4',
      400: '#5CB5F1',
      500: '#2E5BA8', // Knowledge Blue - <PERSON>'s color
      600: '#1E4A97',
      700: '#1A4086',
      800: '#163675',
      900: '#0F2654',
      main: '#2E5BA8',
      light: '#5CB5F1',
      dark: '#1A4086',
      contrastText: '#ffffff',
    },
    secondary: {
      50: '#FFF8E1',
      100: '#FFECB3',
      200: '#FFE082',
      300: '#FFD54F',
      400: '#FFCA28',
      500: '#FF9933', // Saffron - Cultural and spiritual significance
      600: '#FFB300',
      700: '#FF8F00',
      800: '#FF6F00',
      900: '#E65100',
      main: '#FF9933',
      light: '#FFCA28',
      dark: '#FF8F00',
      contrastText: '#000000',
    },
    success: {
      50: '#E8F5E8',
      100: '#C8E6C9',
      200: '#A5D6A7',
      300: '#81C784',
      400: '#66BB6A',
      500: '#138808', // Prosperity Green - Growth and learning
      600: '#43A047',
      700: '#388E3C',
      800: '#2E7D32',
      900: '#1B5E20',
      main: '#138808',
      light: '#66BB6A',
      dark: '#2E7D32',
      contrastText: '#ffffff',
    },
    warning: {
      50: '#FFFBF0',
      100: '#FFF4E0',
      200: '#FFECB3',
      300: '#FFE082',
      400: '#FFD54F',
      500: '#F4C430', // Turmeric Yellow - Auspiciousness and learning
      600: '#FFB300',
      700: '#F9A825',
      800: '#F57F17',
      900: '#FF6F00',
      main: '#F4C430',
      light: '#FFD54F',
      dark: '#F57F17',
      contrastText: '#000000',
    },
    error: {
      50: '#FFEBEE',
      100: '#FFCDD2',
      200: '#EF9A9A',
      300: '#E57373',
      400: '#EF5350',
      500: '#D81159', // Vermilion - Attention and importance
      600: '#E53935',
      700: '#D32F2F',
      800: '#C62828',
      900: '#B71C1C',
      main: '#D81159',
      light: '#EF5350',
      dark: '#C62828',
      contrastText: '#ffffff',
    },
    info: {
      50: '#E3F2FD',
      100: '#BBDEFB',
      200: '#90CAF9',
      300: '#64B5F6',
      400: '#42A5F5',
      500: '#2196F3',
      600: '#1E88E5',
      700: '#1976D2',
      800: '#1565C0',
      900: '#0D47A1',
      main: '#2196F3',
      light: '#64B5F6',
      dark: '#1565C0',
      contrastText: '#ffffff',
    },
    // Cultural accent colors
    cultural: {
      lotus: '#FF69B4', // Lotus pink
      peacock: '#005F73', // Peacock blue-green
      marigold: '#FFA500', // Marigold orange
      sandalwood: '#F4A460', // Sandalwood beige
      henna: '#8B4513', // Henna brown
      jasmine: '#F8F8FF', // Jasmine white
    },
    // Board-specific colors
    board: {
      CBSE: '#2E5BA8',
      ICSE: '#FF9933',
      STATE: '#138808',
      IB: '#D81159',
    },
    background: {
      default: '#FAFAFA',
      paper: '#FFFFFF',
      elevated: '#FFFFFF',
      cultural: '#FFF8E1', // Warm cultural background
    },
    text: {
      primary: '#212121',
      secondary: '#616161',
      disabled: '#9E9E9E',
      cultural: '#8B4513', // Cultural text color
    },
  },

  // Enhanced typography with Indic script support
  typography: {
    fontFamily: [
      'Noto Sans',
      'Roboto',
      'Inter',
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Arial',
      'sans-serif',
    ].join(','),
    
    // Devanagari font stack for Hindi
    fontFamilyDevanagari: [
      'Noto Sans Devanagari',
      'Hind',
      'Mangal',
      'Kruti Dev 010',
      'sans-serif',
    ].join(','),
    
    // Regional language fonts
    fontFamilyTamil: [
      'Noto Sans Tamil',
      'Catamaran',
      'Latha',
      'sans-serif',
    ].join(','),
    
    fontFamilyTelugu: [
      'Noto Sans Telugu',
      'Mallanna',
      'Gautami',
      'sans-serif',
    ].join(','),
    
    fontFamilyBengali: [
      'Noto Sans Bengali',
      'Hind Siliguri',
      'SolaimanLipi',
      'sans-serif',
    ].join(','),
    
    fontFamilyGujarati: [
      'Noto Sans Gujarati',
      'Hind Vadodara',
      'Shruti',
      'sans-serif',
    ].join(','),

    // Enhanced heading styles
    h1: {
      fontSize: 'clamp(2.5rem, 2rem + 2vw, 3.5rem)',
      fontWeight: 700,
      lineHeight: 1.2,
      letterSpacing: '-0.025em',
      color: '#2E5BA8',
    },
    h2: {
      fontSize: 'clamp(2rem, 1.7rem + 1.5vw, 2.75rem)',
      fontWeight: 600,
      lineHeight: 1.25,
      letterSpacing: '-0.025em',
      color: '#2E5BA8',
    },
    h3: {
      fontSize: 'clamp(1.75rem, 1.5rem + 1.25vw, 2.25rem)',
      fontWeight: 600,
      lineHeight: 1.3,
      color: '#2E5BA8',
    },
    h4: {
      fontSize: 'clamp(1.5rem, 1.3rem + 1vw, 2rem)',
      fontWeight: 600,
      lineHeight: 1.35,
      color: '#2E5BA8',
    },
    h5: {
      fontSize: 'clamp(1.25rem, 1.1rem + 0.75vw, 1.75rem)',
      fontWeight: 600,
      lineHeight: 1.4,
      color: '#2E5BA8',
    },
    h6: {
      fontSize: 'clamp(1.125rem, 1rem + 0.625vw, 1.5rem)',
      fontWeight: 600,
      lineHeight: 1.45,
      color: '#2E5BA8',
    },
    
    // Cultural text variants
    culturalHeading: {
      fontFamily: 'Noto Sans Devanagari, serif',
      fontSize: '1.5rem',
      fontWeight: 600,
      color: '#8B4513',
      textAlign: 'center',
    },
    
    sanskritText: {
      fontFamily: 'Noto Sans Devanagari, serif',
      fontSize: '1.1rem',
      fontStyle: 'italic',
      color: '#8B4513',
    },
  },

  // Enhanced spacing system
  spacing: 4,

  // Cultural shape and border radius
  shape: {
    borderRadius: 12,
    culturalRadius: 16, // More rounded for cultural elements
  },

  // Enhanced breakpoints for Indian device usage patterns
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
    },
  },

  // Cultural shadows and elevations
  shadows: [
    'none',
    '0px 1px 3px rgba(46, 91, 168, 0.12), 0px 1px 2px rgba(46, 91, 168, 0.08)',
    '0px 3px 6px rgba(46, 91, 168, 0.16), 0px 2px 4px rgba(46, 91, 168, 0.08)',
    '0px 6px 12px rgba(46, 91, 168, 0.16), 0px 4px 8px rgba(46, 91, 168, 0.08)',
    '0px 8px 16px rgba(46, 91, 168, 0.16), 0px 6px 12px rgba(46, 91, 168, 0.08)',
    '0px 12px 24px rgba(46, 91, 168, 0.16), 0px 8px 16px rgba(46, 91, 168, 0.08)',
    '0px 16px 32px rgba(46, 91, 168, 0.16), 0px 12px 24px rgba(46, 91, 168, 0.08)',
    '0px 20px 40px rgba(46, 91, 168, 0.16), 0px 16px 32px rgba(46, 91, 168, 0.08)',
    '0px 24px 48px rgba(46, 91, 168, 0.16), 0px 20px 40px rgba(46, 91, 168, 0.08)',
  ],

  // Enhanced transitions with cultural smoothness
  transitions: {
    easing: {
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
      cultural: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)', // Smooth cultural transition
    },
    duration: {
      shortest: 150,
      shorter: 200,
      short: 250,
      standard: 300,
      complex: 375,
      cultural: 500, // Longer for cultural animations
      enteringScreen: 225,
      leavingScreen: 195,
    },
  },

  // Custom cultural patterns and gradients
  culturalPatterns: {
    lotus: {
      background: 'radial-gradient(circle at 20% 80%, #FF69B415 0%, transparent 50%)',
      border: '2px solid #FF69B430',
    },
    mandala: {
      background: 'conic-gradient(from 0deg at 50% 50%, #2E5BA805, #2E5BA815, #2E5BA805)',
      border: '2px solid #2E5BA830',
    },
    paisley: {
      background: 'linear-gradient(45deg, #FF993308 25%, transparent 25%)',
      border: '2px solid #FF993330',
    },
    peacock: {
      background: 'linear-gradient(135deg, #005F7310 0%, #005F7305 100%)',
      border: '2px solid #005F7330',
    },
  },
});

export default indianEducationalTheme;
