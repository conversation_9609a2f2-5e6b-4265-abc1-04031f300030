import{u as R,j as e,B as n,g as t,b as d,e as o,G as l,a4 as k,a5 as D,a6 as E,a7 as j,m as I,k as p,aY as M,ap as O,an as H,H as b,b1 as g,aH as y,y as L,b2 as F,aw as G,b3 as K,b4 as U,b5 as T,b6 as i,b7 as Y,A as q,n as J,I as f}from"./mui-Cjipzt4F.js";import{r as x}from"./vendor-BfWiUekA.js";import{u as Q,m as V}from"./index-_PFqhbKW.js";import"./charts-Dx2u7Eir.js";const v=[{id:1,name:"<PERSON><PERSON>",rollNumber:1,status:"present",lastAttendance:"95%"},{id:2,name:"<PERSON><PERSON><PERSON><PERSON>",rollNumber:2,status:"present",lastAttendance:"92%"},{id:3,name:"<PERSON><PERSON><PERSON>",rollNumber:3,status:"absent",lastAttendance:"88%"},{id:4,name:"<PERSON>",rollNumber:4,status:"present",lastAttendance:"94%"},{id:5,name:"Ankitha <PERSON>",rollNumber:5,status:"late",lastAttendance:"90%"},{id:6,name:"Sirisha Nair",rollNumber:6,status:"present",lastAttendance:"96%"},{id:7,name:"Priya Agarwal",rollNumber:7,status:"present",lastAttendance:"85%"}],ae=()=>{const s=R();Q();const[C,N]=x.useState("10-A"),[A,W]=x.useState(new Date().toISOString().split("T")[0]),[r,S]=x.useState({}),[$,w]=x.useState(!1);x.useEffect(()=>{const a={};v.forEach(c=>{a[c.id]=c.status}),S(a)},[]);const u=(a,c)=>{S(h=>({...h,[a]:c}))},B=async()=>{w(!0);try{await new Promise(a=>setTimeout(a,1e3)),alert("Attendance saved successfully!")}catch(a){console.error("Error saving attendance:",a)}finally{w(!1)}},z=a=>{switch(a){case"present":return"success";case"absent":return"error";case"late":return"warning";default:return"default"}},P=a=>{switch(a){case"present":return e.jsx(b,{});case"absent":return e.jsx(g,{});case"late":return e.jsx(y,{});default:return null}},m={present:Object.values(r).filter(a=>a==="present").length,absent:Object.values(r).filter(a=>a==="absent").length,late:Object.values(r).filter(a=>a==="late").length};return e.jsxs(n,{sx:{maxWidth:1200,mx:"auto",p:3},children:[e.jsx(V.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:e.jsxs(n,{sx:{mb:4},children:[e.jsx(t,{variant:"h4",sx:{fontWeight:600,mb:1,background:`linear-gradient(135deg, ${s.palette.primary.main} 0%, ${s.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"Attendance Management"}),e.jsx(t,{variant:"body1",color:"text.secondary",children:"Mark daily attendance for your students"})]})}),e.jsx(d,{sx:{mb:4},children:e.jsx(o,{children:e.jsxs(l,{container:!0,spacing:3,alignItems:"center",children:[e.jsx(l,{item:!0,xs:12,md:3,children:e.jsxs(k,{fullWidth:!0,children:[e.jsx(D,{children:"Class"}),e.jsxs(E,{value:C,onChange:a=>N(a.target.value),label:"Class",children:[e.jsx(j,{value:"9-A",children:"Class 9-A"}),e.jsx(j,{value:"9-B",children:"Class 9-B"}),e.jsx(j,{value:"10-A",children:"Class 10-A"}),e.jsx(j,{value:"10-B",children:"Class 10-B"})]})]})}),e.jsx(l,{item:!0,xs:12,md:3,children:e.jsxs(k,{fullWidth:!0,children:[e.jsx(t,{variant:"body2",color:"text.secondary",sx:{mb:1},children:"Date"}),e.jsx("input",{type:"date",value:A,onChange:a=>W(a.target.value),style:{width:"100%",padding:"12px",border:`1px solid ${s.palette.divider}`,borderRadius:"4px",fontSize:"16px"}})]})}),e.jsx(l,{item:!0,xs:12,md:6,children:e.jsxs(I,{direction:"row",spacing:2,children:[e.jsx(p,{variant:"contained",startIcon:e.jsx(M,{}),onClick:B,loading:$,sx:{background:`linear-gradient(135deg, ${s.palette.primary.main} 0%, ${s.palette.secondary.main} 100%)`},children:"Save Attendance"}),e.jsx(p,{variant:"outlined",startIcon:e.jsx(O,{}),children:"Print Report"}),e.jsx(p,{variant:"outlined",startIcon:e.jsx(H,{}),children:"Export"})]})})]})})}),e.jsxs(l,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(l,{item:!0,xs:12,md:3,children:e.jsx(d,{sx:{background:`linear-gradient(135deg, ${s.palette.success.main} 0%, ${s.palette.success.dark} 100%)`,color:"white"},children:e.jsx(o,{children:e.jsxs(n,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(n,{children:[e.jsx(t,{variant:"h4",sx:{fontWeight:600},children:m.present}),e.jsx(t,{variant:"body2",sx:{opacity:.9},children:"Present"})]}),e.jsx(b,{sx:{fontSize:40,opacity:.8}})]})})})}),e.jsx(l,{item:!0,xs:12,md:3,children:e.jsx(d,{sx:{background:`linear-gradient(135deg, ${s.palette.error.main} 0%, ${s.palette.error.dark} 100%)`,color:"white"},children:e.jsx(o,{children:e.jsxs(n,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(n,{children:[e.jsx(t,{variant:"h4",sx:{fontWeight:600},children:m.absent}),e.jsx(t,{variant:"body2",sx:{opacity:.9},children:"Absent"})]}),e.jsx(g,{sx:{fontSize:40,opacity:.8}})]})})})}),e.jsx(l,{item:!0,xs:12,md:3,children:e.jsx(d,{sx:{background:`linear-gradient(135deg, ${s.palette.warning.main} 0%, ${s.palette.warning.dark} 100%)`,color:"white"},children:e.jsx(o,{children:e.jsxs(n,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(n,{children:[e.jsx(t,{variant:"h4",sx:{fontWeight:600},children:m.late}),e.jsx(t,{variant:"body2",sx:{opacity:.9},children:"Late"})]}),e.jsx(y,{sx:{fontSize:40,opacity:.8}})]})})})}),e.jsx(l,{item:!0,xs:12,md:3,children:e.jsx(d,{sx:{background:`linear-gradient(135deg, ${s.palette.info.main} 0%, ${s.palette.info.dark} 100%)`,color:"white"},children:e.jsx(o,{children:e.jsxs(n,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsxs(n,{children:[e.jsx(t,{variant:"h4",sx:{fontWeight:600},children:v.length}),e.jsx(t,{variant:"body2",sx:{opacity:.9},children:"Total Students"})]}),e.jsx(L,{sx:{fontSize:40,opacity:.8}})]})})})})]}),e.jsx(d,{children:e.jsxs(o,{children:[e.jsxs(t,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:["Student Attendance - ",C," (",A,")"]}),e.jsx(F,{component:G,variant:"outlined",children:e.jsxs(K,{children:[e.jsx(U,{children:e.jsxs(T,{children:[e.jsx(i,{children:"Roll No."}),e.jsx(i,{children:"Student Name"}),e.jsx(i,{children:"Previous Attendance"}),e.jsx(i,{children:"Status"}),e.jsx(i,{children:"Actions"})]})}),e.jsx(Y,{children:v.map(a=>{var c,h;return e.jsxs(T,{hover:!0,children:[e.jsx(i,{children:e.jsx(t,{variant:"body2",sx:{fontWeight:600},children:a.rollNumber})}),e.jsx(i,{children:e.jsxs(n,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx(q,{sx:{width:32,height:32},children:a.name.charAt(0)}),e.jsx(t,{variant:"body2",sx:{fontWeight:500},children:a.name})]})}),e.jsx(i,{children:e.jsx(t,{variant:"body2",children:a.lastAttendance})}),e.jsx(i,{children:e.jsx(J,{icon:P(r[a.id]),label:((c=r[a.id])==null?void 0:c.charAt(0).toUpperCase())+((h=r[a.id])==null?void 0:h.slice(1)),color:z(r[a.id]),size:"small"})}),e.jsx(i,{children:e.jsxs(I,{direction:"row",spacing:1,children:[e.jsx(f,{size:"small",onClick:()=>u(a.id,"present"),color:r[a.id]==="present"?"success":"default",children:e.jsx(b,{})}),e.jsx(f,{size:"small",onClick:()=>u(a.id,"absent"),color:r[a.id]==="absent"?"error":"default",children:e.jsx(g,{})}),e.jsx(f,{size:"small",onClick:()=>u(a.id,"late"),color:r[a.id]==="late"?"warning":"default",children:e.jsx(y,{})})]})})]},a.id)})})]})})]})})]})};export{ae as default};
//# sourceMappingURL=AttendanceManagement-4vnBIZnH.js.map
