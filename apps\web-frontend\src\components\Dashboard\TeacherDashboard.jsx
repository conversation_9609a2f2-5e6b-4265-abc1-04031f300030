/**
 * <PERSON>idyaMitra Platform - Teacher Dashboard
 * 
 * This component implements the teacher dashboard as specified in the visualization mockups
 * with Indian educational context and board-specific features.
 * 
 * Based on: docs/visualization_mockups.md - Teacher Dashboard section
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Avatar,
  LinearProgress,
  IconButton,
  Tooltip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  useTheme,
  alpha,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Stack,
  Badge,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  School,
  Person,
  Assessment,
  Notifications,
  MoreVert,
  Warning,
  CheckCircle,
  Schedule,
  Group,
  CalendarToday,
  BarChart,
  PieChart,
  Timeline,
  Refresh,
  FilterList,
  Search,
  ArrowForward,
  Psychology
} from '@mui/icons-material';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip as ChartTooltip, Legend, ArcElement } from 'chart.js';
import { Bar, Pie } from 'react-chartjs-2';
import { useTranslation } from 'react-i18next';

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, ChartTooltip, Legend, ArcElement);

// Class Overview Card Component
const ClassOverviewCard = ({ classData, loading }) => {
  const theme = useTheme();

  if (loading) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Class Overview</Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <LinearProgress />
            <LinearProgress />
            <LinearProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
          Class Overview
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary">Students:</Typography>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>{classData.totalStudents}</Typography>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary">Avg GPA:</Typography>
            <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
              {classData.avgGPA}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary">Attendance:</Typography>
            <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.success.main }}>
              {classData.attendance}%
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

// Performance Distribution Chart Component
const PerformanceDistributionCard = ({ performanceData, loading }) => {
  if (loading) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Performance Distribution</Typography>
          <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <LinearProgress sx={{ width: '80%' }} />
          </Box>
        </CardContent>
      </Card>
    );
  }

  const chartData = {
    labels: ['A', 'B', 'C', 'D', 'F'],
    datasets: [
      {
        label: 'Students',
        data: [performanceData.A, performanceData.B, performanceData.C, performanceData.D, performanceData.F],
        backgroundColor: [
          '#4CAF50',
          '#2196F3',
          '#FF9800',
          '#FF5722',
          '#F44336'
        ],
        borderWidth: 0,
        borderRadius: 4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          display: false,
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
          Performance Distribution
        </Typography>
        <Box sx={{ height: 200 }}>
          <Bar data={chartData} options={chartOptions} />
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 2 }}>
          {Object.entries(performanceData).map(([grade, count]) => (
            <Box key={grade} sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">{grade}:</Typography>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>{count}</Typography>
            </Box>
          ))}
        </Box>
      </CardContent>
    </Card>
  );
};

// Attendance Calendar Heat Map Component
const AttendanceCalendarCard = ({ attendanceData, loading }) => {
  if (loading) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Attendance</Typography>
          <Box sx={{ height: 150, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <LinearProgress sx={{ width: '80%' }} />
          </Box>
        </CardContent>
      </Card>
    );
  }

  // Simplified calendar view for demo
  const calendarDays = Array.from({ length: 30 }, (_, i) => ({
    day: i + 1,
    status: Math.random() > 0.1 ? 'present' : Math.random() > 0.5 ? 'absent' : 'late'
  }));

  const getStatusColor = (status) => {
    switch (status) {
      case 'present': return '#4CAF50';
      case 'absent': return '#F44336';
      case 'late': return '#FF9800';
      default: return '#E0E0E0';
    }
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
          Attendance
        </Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: 1, mb: 2 }}>
          {calendarDays.slice(0, 21).map((day) => (
            <Box
              key={day.day}
              sx={{
                width: 24,
                height: 24,
                borderRadius: 1,
                backgroundColor: getStatusColor(day.status),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '0.75rem',
                color: 'white',
                fontWeight: 500
              }}
            >
              {day.day}
            </Box>
          ))}
        </Box>
        <Box sx={{ display: 'flex', gap: 2, fontSize: '0.75rem' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 12, height: 12, backgroundColor: '#4CAF50', borderRadius: 1 }} />
            <Typography variant="caption">Present</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 12, height: 12, backgroundColor: '#FF9800', borderRadius: 1 }} />
            <Typography variant="caption">Late</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 12, height: 12, backgroundColor: '#F44336', borderRadius: 1 }} />
            <Typography variant="caption">Absent</Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

// Behavior Incidents Card Component
const BehaviorIncidentsCard = ({ behaviorData, loading }) => {
  if (loading) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Behavior Incidents</Typography>
          <LinearProgress />
        </CardContent>
      </Card>
    );
  }

  const chartData = {
    labels: ['Positive', 'Negative'],
    datasets: [
      {
        data: [behaviorData.positive, behaviorData.negative],
        backgroundColor: ['#4CAF50', '#FF5722'],
        borderWidth: 0,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
          Behavior Incidents
        </Typography>
        <Box sx={{ height: 150 }}>
          <Pie data={chartData} options={chartOptions} />
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 2 }}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">Positive:</Typography>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#4CAF50' }}>
              {behaviorData.positive}
            </Typography>
          </Box>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">Negative:</Typography>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#FF5722' }}>
              {behaviorData.negative}
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

// Students Requiring Attention Component
const StudentsRequiringAttention = ({ studentsData, loading }) => {
  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>Students Requiring Attention</Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <LinearProgress />
            <LinearProgress />
            <LinearProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
          Students Requiring Attention
        </Typography>
        <List>
          {studentsData.map((student, index) => (
            <React.Fragment key={student.id}>
              <ListItem
                sx={{
                  px: 0,
                  '&:hover': {
                    backgroundColor: alpha('#000', 0.04),
                    borderRadius: 1,
                  },
                }}
              >
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: student.severity === 'high' ? 'error.main' : 'warning.main' }}>
                    {student.severity === 'high' ? <Warning /> : <Schedule />}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {student.name}
                    </Typography>
                  }
                  secondary={student.reason}
                />
                <Button
                  size="small"
                  endIcon={<ArrowForward />}
                  sx={{ textTransform: 'none' }}
                >
                  View
                </Button>
              </ListItem>
              {index < studentsData.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

// Main Teacher Dashboard Component
const TeacherDashboard = () => {
  const { t } = useTranslation(['dashboard', 'common']);
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [selectedClass, setSelectedClass] = useState('9A');
  const [selectedQuarter, setSelectedQuarter] = useState('Q1 2024-2025');

  // Mock data - in real app, this would come from API
  const [dashboardData, setDashboardData] = useState({
    classOverview: {
      totalStudents: 28,
      avgGPA: 3.2,
      attendance: 94
    },
    performanceDistribution: {
      A: 5,
      B: 12,
      C: 8,
      D: 2,
      F: 1
    },
    behaviorData: {
      positive: 42,
      negative: 15
    },
    studentsRequiringAttention: [
      {
        id: 1,
        name: 'John Smith',
        reason: '3 consecutive absences',
        severity: 'high'
      },
      {
        id: 2,
        name: 'Lisa Johnson',
        reason: 'GPA dropped 0.8 points',
        severity: 'medium'
      },
      {
        id: 3,
        name: 'Miguel Sanchez',
        reason: '2 behavioral incidents',
        severity: 'medium'
      }
    ]
  });

  useEffect(() => {
    // Simulate data loading
    const loadData = async () => {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1500));
      setLoading(false);
    };

    loadData();
  }, [selectedClass, selectedQuarter]);

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ bgcolor: 'primary.main' }}>
              <School />
            </Avatar>
            <Typography variant="h4" sx={{ fontWeight: 700 }}>
              Teacher Dashboard
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <IconButton>
              <Search />
            </IconButton>
            <Badge badgeContent={3} color="error">
              <IconButton>
                <Notifications />
              </IconButton>
            </Badge>
          </Box>
        </Box>

        {/* Class and Quarter Selection */}
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Class</InputLabel>
            <Select
              value={selectedClass}
              label="Class"
              onChange={(e) => setSelectedClass(e.target.value)}
            >
              <MenuItem value="9A">Class: 9A</MenuItem>
              <MenuItem value="9B">Class: 9B</MenuItem>
              <MenuItem value="10A">Class: 10A</MenuItem>
              <MenuItem value="10B">Class: 10B</MenuItem>
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Quarter</InputLabel>
            <Select
              value={selectedQuarter}
              label="Quarter"
              onChange={(e) => setSelectedQuarter(e.target.value)}
            >
              <MenuItem value="Q1 2024-2025">Q1 2024-2025</MenuItem>
              <MenuItem value="Q2 2024-2025">Q2 2024-2025</MenuItem>
              <MenuItem value="Q3 2024-2025">Q3 2024-2025</MenuItem>
              <MenuItem value="Q4 2024-2025">Q4 2024-2025</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>

      {/* Main Dashboard Grid */}
      <Grid container spacing={3}>
        {/* Top Row - Overview Cards */}
        <Grid item xs={12} md={3}>
          <ClassOverviewCard classData={dashboardData.classOverview} loading={loading} />
        </Grid>
        <Grid item xs={12} md={3}>
          <PerformanceDistributionCard performanceData={dashboardData.performanceDistribution} loading={loading} />
        </Grid>
        <Grid item xs={12} md={3}>
          <AttendanceCalendarCard loading={loading} />
        </Grid>
        <Grid item xs={12} md={3}>
          <BehaviorIncidentsCard behaviorData={dashboardData.behaviorData} loading={loading} />
        </Grid>

        {/* Bottom Row - Students Requiring Attention */}
        <Grid item xs={12}>
          <StudentsRequiringAttention studentsData={dashboardData.studentsRequiringAttention} loading={loading} />
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Box sx={{ mt: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Quick Actions
        </Typography>
        <Stack direction="row" spacing={2} flexWrap="wrap">
          <Button
            variant="contained"
            startIcon={<Psychology />}
            sx={{ textTransform: 'none' }}
          >
            Generate SWOT Analysis
          </Button>
          <Button
            variant="outlined"
            startIcon={<Assessment />}
            sx={{ textTransform: 'none' }}
          >
            View Reports
          </Button>
          <Button
            variant="outlined"
            startIcon={<Group />}
            sx={{ textTransform: 'none' }}
          >
            Manage Students
          </Button>
          <Button
            variant="outlined"
            startIcon={<CalendarToday />}
            sx={{ textTransform: 'none' }}
          >
            Schedule Meeting
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};

export default TeacherDashboard;
