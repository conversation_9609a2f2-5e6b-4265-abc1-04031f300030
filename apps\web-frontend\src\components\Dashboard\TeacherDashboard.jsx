/**
 * VidyaMitra Platform - Teacher Dashboard Component
 *
 * Comprehensive teacher dashboard with grade entry, student progress tracking,
 * and class management features for Indian educational context
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Stack,
  Alert,
  Fab,
  useTheme,
  alpha,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Badge,
} from '@mui/material';
import {
  School,
  People,
  Assessment,
  TrendingUp,
  Add,
  Edit,
  Visibility,
  NotificationImportant,
  CalendarToday,
  Assignment,
  Grade,
  EmojiEvents,
  Warning,
  Notifications,
  Search,
  Schedule,
  ArrowForward,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, Doughnut, Bar, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler,
} from 'chart.js';
import { useTranslation } from 'react-i18next';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

// Sample teacher data with Indian context
const teacherData = {
  name: 'Mrs. Priya Sharma',
  employeeId: 'TCH001',
  subjects: ['Mathematics', 'Physics'],
  classes: [
    { grade: 10, section: 'A', students: 35, board: 'CBSE' },
    { grade: 10, section: 'B', students: 32, board: 'CBSE' },
    { grade: 9, section: 'A', students: 38, board: 'CBSE' },
  ],
  totalStudents: 105,
  pendingGrades: 12,
  upcomingTests: 3,
  recentActivity: [
    { type: 'grade', message: 'Graded Mathematics test for Class 10-A', time: '2 hours ago' },
    { type: 'attendance', message: 'Marked attendance for Class 9-A', time: '4 hours ago' },
    { type: 'swot', message: 'Updated SWOT analysis for Sanju Reddy', time: '1 day ago' },
  ],
};

// Sample student performance data with Indian names
const studentPerformanceData = [
  { id: 1, name: 'Sanju Kumar Reddy', class: '10-A', subject: 'Mathematics', lastGrade: 'A1', attendance: 95, status: 'Excellent' },
  { id: 2, name: 'Niraimathi Selvam', class: '10-A', subject: 'Mathematics', lastGrade: 'A2', attendance: 92, status: 'Good' },
  { id: 3, name: 'Mahesh Reddy', class: '10-B', subject: 'Mathematics', lastGrade: 'B1', attendance: 88, status: 'Average' },
  { id: 4, name: 'Ravi Teja Sharma', class: '9-A', subject: 'Physics', lastGrade: 'A1', attendance: 94, status: 'Excellent' },
  { id: 5, name: 'Ankitha Patel', class: '10-A', subject: 'Mathematics', lastGrade: 'A2', attendance: 90, status: 'Good' },
  { id: 6, name: 'Sirisha Nair', class: '10-B', subject: 'Physics', lastGrade: 'A1', attendance: 96, status: 'Excellent' },
  { id: 7, name: 'Priya Agarwal', class: '9-A', subject: 'Mathematics', lastGrade: 'B2', attendance: 85, status: 'Average' },
];

// Class Overview Card Component
const ClassOverviewCard = ({ classData, loading }) => {
  const theme = useTheme();

  if (loading) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Class Overview</Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <LinearProgress />
            <LinearProgress />
            <LinearProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
          Class Overview
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary">Students:</Typography>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>{classData.totalStudents}</Typography>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary">Avg GPA:</Typography>
            <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
              {classData.avgGPA}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary">Attendance:</Typography>
            <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.success.main }}>
              {classData.attendance}%
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

// Performance Distribution Chart Component
const PerformanceDistributionCard = ({ performanceData, loading }) => {
  if (loading) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Performance Distribution</Typography>
          <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <LinearProgress sx={{ width: '80%' }} />
          </Box>
        </CardContent>
      </Card>
    );
  }

  const chartData = {
    labels: ['A', 'B', 'C', 'D', 'F'],
    datasets: [
      {
        label: 'Students',
        data: [performanceData.A, performanceData.B, performanceData.C, performanceData.D, performanceData.F],
        backgroundColor: [
          '#4CAF50',
          '#2196F3',
          '#FF9800',
          '#FF5722',
          '#F44336'
        ],
        borderWidth: 0,
        borderRadius: 4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          display: false,
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
          Performance Distribution
        </Typography>
        <Box sx={{ height: 200 }}>
          <Bar data={chartData} options={chartOptions} />
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 2 }}>
          {Object.entries(performanceData).map(([grade, count]) => (
            <Box key={grade} sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">{grade}:</Typography>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>{count}</Typography>
            </Box>
          ))}
        </Box>
      </CardContent>
    </Card>
  );
};

// Attendance Calendar Heat Map Component
const AttendanceCalendarCard = ({ attendanceData, loading }) => {
  if (loading) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Attendance</Typography>
          <Box sx={{ height: 150, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <LinearProgress sx={{ width: '80%' }} />
          </Box>
        </CardContent>
      </Card>
    );
  }

  // Simplified calendar view for demo
  const calendarDays = Array.from({ length: 30 }, (_, i) => ({
    day: i + 1,
    status: Math.random() > 0.1 ? 'present' : Math.random() > 0.5 ? 'absent' : 'late'
  }));

  const getStatusColor = (status) => {
    switch (status) {
      case 'present': return '#4CAF50';
      case 'absent': return '#F44336';
      case 'late': return '#FF9800';
      default: return '#E0E0E0';
    }
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
          Attendance
        </Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: 1, mb: 2 }}>
          {calendarDays.slice(0, 21).map((day) => (
            <Box
              key={day.day}
              sx={{
                width: 24,
                height: 24,
                borderRadius: 1,
                backgroundColor: getStatusColor(day.status),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '0.75rem',
                color: 'white',
                fontWeight: 500
              }}
            >
              {day.day}
            </Box>
          ))}
        </Box>
        <Box sx={{ display: 'flex', gap: 2, fontSize: '0.75rem' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 12, height: 12, backgroundColor: '#4CAF50', borderRadius: 1 }} />
            <Typography variant="caption">Present</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 12, height: 12, backgroundColor: '#FF9800', borderRadius: 1 }} />
            <Typography variant="caption">Late</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 12, height: 12, backgroundColor: '#F44336', borderRadius: 1 }} />
            <Typography variant="caption">Absent</Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

// Behavior Incidents Card Component
const BehaviorIncidentsCard = ({ behaviorData, loading }) => {
  if (loading) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Behavior Incidents</Typography>
          <LinearProgress />
        </CardContent>
      </Card>
    );
  }

  const chartData = {
    labels: ['Positive', 'Negative'],
    datasets: [
      {
        data: [behaviorData.positive, behaviorData.negative],
        backgroundColor: ['#4CAF50', '#FF5722'],
        borderWidth: 0,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
          Behavior Incidents
        </Typography>
        <Box sx={{ height: 150 }}>
          <Pie data={chartData} options={chartOptions} />
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 2 }}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">Positive:</Typography>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#4CAF50' }}>
              {behaviorData.positive}
            </Typography>
          </Box>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">Negative:</Typography>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#FF5722' }}>
              {behaviorData.negative}
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

// Students Requiring Attention Component
const StudentsRequiringAttention = ({ studentsData, loading }) => {
  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>Students Requiring Attention</Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <LinearProgress />
            <LinearProgress />
            <LinearProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
          Students Requiring Attention
        </Typography>
        <List>
          {studentsData.map((student, index) => (
            <React.Fragment key={student.id}>
              <ListItem
                sx={{
                  px: 0,
                  '&:hover': {
                    backgroundColor: alpha('#000', 0.04),
                    borderRadius: 1,
                  },
                }}
              >
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: student.severity === 'high' ? 'error.main' : 'warning.main' }}>
                    {student.severity === 'high' ? <Warning /> : <Schedule />}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {student.name}
                    </Typography>
                  }
                  secondary={student.reason}
                />
                <Button
                  size="small"
                  endIcon={<ArrowForward />}
                  sx={{ textTransform: 'none' }}
                >
                  View
                </Button>
              </ListItem>
              {index < studentsData.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

const TeacherDashboard = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { t } = useTranslation(['common', 'dashboard']);
  const [selectedClass, setSelectedClass] = useState(teacherData.classes[0]);
  const [loading, setLoading] = useState(false);

  // Chart data for class performance
  const classPerformanceData = {
    labels: ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'],
    datasets: [
      {
        label: 'Grade Distribution',
        data: [12, 15, 8, 6, 3, 1],
        backgroundColor: [
          theme.palette.success.main,
          theme.palette.success.light,
          theme.palette.info.main,
          theme.palette.info.light,
          theme.palette.warning.main,
          theme.palette.error.main,
        ],
        borderWidth: 2,
        borderColor: '#fff',
      },
    ],
  };

  // Attendance trend data
  const attendanceTrendData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    datasets: [
      {
        label: 'Class 10-A',
        data: [95, 92, 94, 96, 93, 89],
        borderColor: theme.palette.primary.main,
        backgroundColor: alpha(theme.palette.primary.main, 0.1),
        fill: true,
        tension: 0.4,
      },
      {
        label: 'Class 10-B',
        data: [88, 90, 87, 91, 89, 85],
        borderColor: theme.palette.secondary.main,
        backgroundColor: alpha(theme.palette.secondary.main, 0.1),
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const handleGradeEntry = () => {
    navigate('/dashboard/grades');
  };

  const handleAttendance = () => {
    navigate('/dashboard/attendance');
  };

  const handleStudentProfile = (studentId) => {
    navigate(`/dashboard/students/${studentId}`);
  };

  const handleSWOTAnalysis = (studentId) => {
    navigate(`/dashboard/students/${studentId}/swot`);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Excellent':
        return 'success';
      case 'Good':
        return 'info';
      case 'Average':
        return 'warning';
      case 'Needs Attention':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ maxWidth: 1400, mx: 'auto', p: 3 }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 600,
              mb: 1,
              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
            }}
          >
            Teacher Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Welcome back, {teacherData.name}! Manage your classes and track student progress.
          </Typography>
        </Box>
      </motion.div>

      {/* Quick Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                color: 'white',
                cursor: 'pointer',
                transition: 'transform 0.2s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                },
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {teacherData.totalStudents}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Total Students
                    </Typography>
                  </Box>
                  <People sx={{ fontSize: 40, opacity: 0.8 }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
                color: 'white',
                cursor: 'pointer',
                transition: 'transform 0.2s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                },
              }}
              onClick={handleGradeEntry}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {teacherData.pendingGrades}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Pending Grades
                    </Typography>
                  </Box>
                  <Grade sx={{ fontSize: 40, opacity: 0.8 }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,
                color: 'white',
                cursor: 'pointer',
                transition: 'transform 0.2s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                },
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {teacherData.upcomingTests}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Upcoming Tests
                    </Typography>
                  </Box>
                  <Assignment sx={{ fontSize: 40, opacity: 0.8 }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,
                color: 'white',
                cursor: 'pointer',
                transition: 'transform 0.2s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                },
              }}
              onClick={handleAttendance}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      92%
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Avg Attendance
                    </Typography>
                  </Box>
                  <CalendarToday sx={{ fontSize: 40, opacity: 0.8 }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
            Quick Actions
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap" useFlexGap>
            <Button
              variant="contained"
              startIcon={<Grade />}
              onClick={handleGradeEntry}
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
              }}
            >
              Enter Grades
            </Button>
            <Button
              variant="outlined"
              startIcon={<CalendarToday />}
              onClick={handleAttendance}
            >
              Mark Attendance
            </Button>
            <Button
              variant="outlined"
              startIcon={<Assessment />}
              onClick={() => navigate('/dashboard/swot/wizard')}
            >
              SWOT Analysis
            </Button>
            <Button
              variant="outlined"
              startIcon={<Assignment />}
              onClick={() => navigate('/dashboard/reports/generate')}
            >
              Generate Reports
            </Button>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={() => navigate('/dashboard/students/register')}
            >
              Add Student
            </Button>
          </Stack>
        </CardContent>
      </Card>

      {/* Charts and Analytics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                Weekly Attendance Trends
              </Typography>
              <Box sx={{ height: 300 }}>
                <Line
                  data={attendanceTrendData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'top',
                      },
                    },
                    scales: {
                      y: {
                        beginAtZero: false,
                        min: 80,
                        max: 100,
                      },
                    },
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                Grade Distribution
              </Typography>
              <Box sx={{ height: 300 }}>
                <Doughnut
                  data={classPerformanceData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'bottom',
                      },
                    },
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Student Performance Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.main' }}>
              Recent Student Performance
            </Typography>
            <Button
              variant="outlined"
              size="small"
              onClick={() => navigate('/dashboard/students')}
            >
              View All Students
            </Button>
          </Box>

          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Student Name</TableCell>
                  <TableCell>Class</TableCell>
                  <TableCell>Subject</TableCell>
                  <TableCell>Last Grade</TableCell>
                  <TableCell>Attendance</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {studentPerformanceData.map((student) => (
                  <TableRow key={student.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ width: 32, height: 32 }}>
                          {student.name.charAt(0)}
                        </Avatar>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {student.name}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>{student.class}</TableCell>
                    <TableCell>{student.subject}</TableCell>
                    <TableCell>
                      <Chip
                        label={student.lastGrade}
                        color={student.lastGrade.startsWith('A') ? 'success' : 'warning'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={student.attendance}
                          sx={{ width: 60, height: 6, borderRadius: 3 }}
                          color={student.attendance >= 90 ? 'success' : student.attendance >= 75 ? 'warning' : 'error'}
                        />
                        <Typography variant="body2">{student.attendance}%</Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={student.status}
                        color={getStatusColor(student.status)}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1}>
                        <IconButton
                          size="small"
                          onClick={() => handleStudentProfile(student.id)}
                          color="primary"
                        >
                          <Visibility />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleSWOTAnalysis(student.id)}
                          color="secondary"
                        >
                          <Assessment />
                        </IconButton>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
        }}
        onClick={() => navigate('/dashboard/students/register')}
      >
        <Add />
      </Fab>
    </Box>
  );
};

export default TeacherDashboard;
