/**
 * VidyaMitra Platform - SWOT Analysis Wizard Component
 * 
 * Interactive SWOT generation interface with AI-powered suggestions
 * and Indian educational context
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Button,
  TextField,
  Grid,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Alert,
  Stack,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Psychology,
  TrendingUp,
  Warning,
  Lightbulb,
  Add,
  Delete,
  Save,
  ArrowBack,
  ArrowForward,
  CheckCircle,
  AutoAwesome,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

// Sample AI suggestions for Indian educational context
const aiSuggestions = {
  strengths: [
    'Strong mathematical reasoning skills',
    'Excellent memory and retention',
    'Good leadership qualities in group activities',
    'Consistent academic performance',
    'Active participation in cultural events',
    'Strong family support system',
    'Multilingual communication abilities',
  ],
  weaknesses: [
    'Needs improvement in English speaking confidence',
    'Time management during examinations',
    'Hesitant to ask questions in class',
    'Difficulty with practical applications',
    'Limited exposure to technology',
    'Peer pressure sensitivity',
  ],
  opportunities: [
    'Science Olympiad participation',
    'Student council leadership roles',
    'Inter-school cultural competitions',
    'STEM career exploration programs',
    'Scholarship opportunities for higher education',
    'Skill development workshops',
    'Community service projects',
  ],
  threats: [
    'Increased academic competition',
    'Board examination pressure',
    'Limited career guidance resources',
    'Technology adaptation challenges',
    'Economic constraints for higher education',
    'Social media distractions',
  ],
};

const SWOTWizard = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [swotData, setSWOTData] = useState({
    strengths: [],
    weaknesses: [],
    opportunities: [],
    threats: [],
  });
  const [currentInput, setCurrentInput] = useState('');
  const [loading, setLoading] = useState(false);

  const steps = [
    {
      label: 'Select Student',
      icon: Psychology,
      description: 'Choose student for SWOT analysis',
    },
    {
      label: 'Strengths',
      icon: TrendingUp,
      description: 'Identify student strengths',
    },
    {
      label: 'Weaknesses',
      icon: Warning,
      description: 'Areas for improvement',
    },
    {
      label: 'Opportunities',
      icon: Lightbulb,
      description: 'Growth opportunities',
    },
    {
      label: 'Threats',
      icon: Warning,
      description: 'Potential challenges',
    },
    {
      label: 'Review & Save',
      icon: CheckCircle,
      description: 'Finalize SWOT analysis',
    },
  ];

  // Sample students
  const students = [
    { id: 1, name: 'Sanju Kumar Reddy', class: '10-A', avatar: 'S' },
    { id: 2, name: 'Niraimathi Selvam', class: '10-A', avatar: 'N' },
    { id: 3, name: 'Mahesh Reddy', class: '10-B', avatar: 'M' },
    { id: 4, name: 'Ravi Teja Sharma', class: '9-A', avatar: 'R' },
    { id: 5, name: 'Ankitha Patel', class: '10-A', avatar: 'A' },
  ];

  const getCurrentCategory = () => {
    const categories = ['', 'strengths', 'weaknesses', 'opportunities', 'threats'];
    return categories[activeStep];
  };

  const handleAddItem = () => {
    if (currentInput.trim() && activeStep >= 1 && activeStep <= 4) {
      const category = getCurrentCategory();
      setSWOTData(prev => ({
        ...prev,
        [category]: [...prev[category], currentInput.trim()],
      }));
      setCurrentInput('');
    }
  };

  const handleRemoveItem = (category, index) => {
    setSWOTData(prev => ({
      ...prev,
      [category]: prev[category].filter((_, i) => i !== index),
    }));
  };

  const handleAddSuggestion = (category, suggestion) => {
    if (!swotData[category].includes(suggestion)) {
      setSWOTData(prev => ({
        ...prev,
        [category]: [...prev[category], suggestion],
      }));
    }
  };

  const handleNext = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    if (activeStep > 0) {
      setActiveStep(prev => prev - 1);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('SWOT Analysis saved successfully!');
      navigate('/dashboard/students');
    } catch (error) {
      console.error('Error saving SWOT analysis:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'strengths':
        return theme.palette.success.main;
      case 'weaknesses':
        return theme.palette.error.main;
      case 'opportunities':
        return theme.palette.info.main;
      case 'threats':
        return theme.palette.warning.main;
      default:
        return theme.palette.primary.main;
    }
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'strengths':
        return '💪';
      case 'weaknesses':
        return '⚠️';
      case 'opportunities':
        return '🚀';
      case 'threats':
        return '⚡';
      default:
        return '📝';
    }
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 600,
              mb: 1,
              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
            }}
          >
            SWOT Analysis Wizard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Create comprehensive SWOT analysis with AI-powered suggestions
          </Typography>
        </Box>
      </motion.div>

      {/* Stepper */}
      <Card sx={{ mb: 4, overflow: 'visible' }}>
        <CardContent>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel
                  StepIconComponent={({ active, completed }) => (
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        background: completed
                          ? `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`
                          : active
                          ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
                          : alpha(theme.palette.action.disabled, 0.12),
                        color: completed || active ? 'white' : theme.palette.action.disabled,
                        transition: 'all 0.3s ease',
                      }}
                    >
                      <step.icon sx={{ fontSize: 24 }} />
                    </Box>
                  )}
                >
                  <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>
                    {step.label}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {step.description}
                  </Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>
        </CardContent>
      </Card>

      {/* Step Content */}
      <Card>
        <CardContent sx={{ p: 4 }}>
          <AnimatePresence mode="wait">
            <motion.div
              key={activeStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {/* Student Selection */}
              {activeStep === 0 && (
                <Box>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Select Student for SWOT Analysis
                  </Typography>
                  <Grid container spacing={2}>
                    {students.map((student) => (
                      <Grid item xs={12} sm={6} md={4} key={student.id}>
                        <Card
                          sx={{
                            cursor: 'pointer',
                            border: selectedStudent?.id === student.id ? `2px solid ${theme.palette.primary.main}` : '1px solid',
                            borderColor: selectedStudent?.id === student.id ? 'primary.main' : 'divider',
                            transition: 'all 0.2s ease',
                            '&:hover': {
                              transform: 'translateY(-2px)',
                              boxShadow: theme.shadows[4],
                            },
                          }}
                          onClick={() => setSelectedStudent(student)}
                        >
                          <CardContent>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                              <Avatar sx={{ bgcolor: 'primary.main' }}>
                                {student.avatar}
                              </Avatar>
                              <Box>
                                <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                                  {student.name}
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  Class {student.class}
                                </Typography>
                              </Box>
                            </Box>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}

              {/* SWOT Categories */}
              {activeStep >= 1 && activeStep <= 4 && (
                <Box>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    {getCategoryIcon(getCurrentCategory())} {steps[activeStep].label}
                  </Typography>
                  
                  {/* Input Section */}
                  <Box sx={{ mb: 4 }}>
                    <Stack direction="row" spacing={2} alignItems="center">
                      <TextField
                        fullWidth
                        label={`Add ${steps[activeStep].label.toLowerCase()}`}
                        value={currentInput}
                        onChange={(e) => setCurrentInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleAddItem()}
                        placeholder={`Enter student ${steps[activeStep].label.toLowerCase()}...`}
                      />
                      <Button
                        variant="contained"
                        startIcon={<Add />}
                        onClick={handleAddItem}
                        disabled={!currentInput.trim()}
                      >
                        Add
                      </Button>
                    </Stack>
                  </Box>

                  <Grid container spacing={3}>
                    {/* Current Items */}
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                        Current {steps[activeStep].label}
                      </Typography>
                      <List>
                        {swotData[getCurrentCategory()]?.map((item, index) => (
                          <ListItem
                            key={index}
                            sx={{
                              border: `1px solid ${alpha(getCategoryColor(getCurrentCategory()), 0.2)}`,
                              borderRadius: 1,
                              mb: 1,
                              background: alpha(getCategoryColor(getCurrentCategory()), 0.05),
                            }}
                          >
                            <ListItemText primary={item} />
                            <IconButton
                              size="small"
                              onClick={() => handleRemoveItem(getCurrentCategory(), index)}
                              color="error"
                            >
                              <Delete />
                            </IconButton>
                          </ListItem>
                        ))}
                      </List>
                    </Grid>

                    {/* AI Suggestions */}
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                        <AutoAwesome sx={{ mr: 1, verticalAlign: 'middle' }} />
                        AI Suggestions
                      </Typography>
                      <List>
                        {aiSuggestions[getCurrentCategory()]?.map((suggestion, index) => (
                          <ListItem
                            key={index}
                            sx={{
                              border: '1px solid',
                              borderColor: 'divider',
                              borderRadius: 1,
                              mb: 1,
                              cursor: 'pointer',
                              '&:hover': {
                                background: alpha(theme.palette.primary.main, 0.05),
                              },
                            }}
                            onClick={() => handleAddSuggestion(getCurrentCategory(), suggestion)}
                          >
                            <ListItemIcon>
                              <Add color="primary" />
                            </ListItemIcon>
                            <ListItemText primary={suggestion} />
                          </ListItem>
                        ))}
                      </List>
                    </Grid>
                  </Grid>
                </Box>
              )}

              {/* Review Step */}
              {activeStep === 5 && (
                <Box>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Review SWOT Analysis
                  </Typography>
                  
                  {selectedStudent && (
                    <Alert severity="info" sx={{ mb: 3 }}>
                      SWOT Analysis for <strong>{selectedStudent.name}</strong> - Class {selectedStudent.class}
                    </Alert>
                  )}

                  <Grid container spacing={3}>
                    {Object.entries(swotData).map(([category, items]) => (
                      <Grid item xs={12} md={6} key={category}>
                        <Card
                          sx={{
                            border: `2px solid ${alpha(getCategoryColor(category), 0.2)}`,
                            background: `linear-gradient(135deg, ${alpha(getCategoryColor(category), 0.05)} 0%, ${alpha(getCategoryColor(category), 0.02)} 100%)`,
                          }}
                        >
                          <CardContent>
                            <Typography
                              variant="h6"
                              sx={{
                                fontWeight: 600,
                                color: getCategoryColor(category),
                                mb: 2,
                              }}
                            >
                              {getCategoryIcon(category)} {category.charAt(0).toUpperCase() + category.slice(1)}
                            </Typography>
                            <List dense>
                              {items.map((item, index) => (
                                <ListItem key={index}>
                                  <ListItemText primary={`• ${item}`} />
                                </ListItem>
                              ))}
                            </List>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}
            </motion.div>
          </AnimatePresence>

          {/* Navigation Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
            <Button
              onClick={handleBack}
              disabled={activeStep === 0}
              startIcon={<ArrowBack />}
              variant="outlined"
            >
              Back
            </Button>
            
            <Button
              onClick={activeStep === steps.length - 1 ? handleSave : handleNext}
              endIcon={activeStep === steps.length - 1 ? <Save /> : <ArrowForward />}
              variant="contained"
              disabled={activeStep === 0 && !selectedStudent}
              loading={loading}
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
              }}
            >
              {activeStep === steps.length - 1 ? 'Save SWOT Analysis' : 'Next'}
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default SWOTWizard;
