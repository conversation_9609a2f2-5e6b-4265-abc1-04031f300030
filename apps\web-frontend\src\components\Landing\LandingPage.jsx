import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  AppBar,
  Toolbar,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemText,
  useTheme,
  useMediaQuery,
  Fade,
  Slide,
  Zoom,
  Avatar,
  Chip,
  Paper,
} from '@mui/material';
import {
  Menu as MenuIcon,
  School as SchoolIcon,
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Star as StarIcon,
  ArrowForward as ArrowForwardIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationOnIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useIntersectionObserver } from '../../hooks/useIntersectionObserver';

// Navigation Header Component
const NavigationHeader = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const navigationItems = [
    { label: 'Home', path: '/' },
    { label: 'About', path: '/about' },
    { label: 'Features', path: '/features' },
    { label: 'Contact', path: '/contact' },
  ];

  const drawer = (
    <Box onClick={handleDrawerToggle} sx={{ textAlign: 'center' }}>
      <Typography variant="h6" sx={{ my: 2, fontWeight: 700 }}>
        VidyaMitra
      </Typography>
      <List>
        {navigationItems.map((item) => (
          <ListItem key={item.label} disablePadding>
            <Button
              fullWidth
              onClick={() => navigate(item.path)}
              sx={{ textAlign: 'center', py: 1 }}
            >
              <ListItemText primary={item.label} />
            </Button>
          </ListItem>
        ))}
        <ListItem disablePadding>
          <Button
            fullWidth
            variant="contained"
            onClick={() => navigate('/login')}
            sx={{ m: 2 }}
          >
            Login
          </Button>
        </ListItem>
      </List>
    </Box>
  );

  return (
    <>
      <AppBar
        position="fixed"
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          color: 'text.primary',
        }}
      >
        <Toolbar>
          <Typography
            variant="h6"
            component="div"
            sx={{
              flexGrow: 1,
              fontWeight: 700,
              color: theme.palette.primary.main,
              cursor: 'pointer',
            }}
            onClick={() => navigate('/')}
          >
            VidyaMitra
          </Typography>
          {isMobile ? (
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
            >
              <MenuIcon />
            </IconButton>
          ) : (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              {navigationItems.map((item) => (
                <Button
                  key={item.label}
                  onClick={() => navigate(item.path)}
                  sx={{
                    color: 'text.primary',
                    fontWeight: 500,
                    '&:hover': {
                      bgcolor: 'rgba(46, 91, 168, 0.1)',
                    },
                  }}
                >
                  {item.label}
                </Button>
              ))}
              <Button
                variant="contained"
                onClick={() => navigate('/login')}
                sx={{
                  ml: 2,
                  borderRadius: 2,
                  px: 3,
                }}
              >
                Login
              </Button>
            </Box>
          )}
        </Toolbar>
      </AppBar>
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 240 },
        }}
      >
        {drawer}
      </Drawer>
    </>
  );
};

// Hero Section Component
const HeroSection = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const [heroRef, isHeroVisible] = useIntersectionObserver({ threshold: 0.1 });

  return (
    <Box
      ref={heroRef}
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
        display: 'flex',
        alignItems: 'center',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
        },
      }}
    >
      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
        <Grid container spacing={4} alignItems="center">
          <Grid item xs={12} md={6}>
            <Fade in={isHeroVisible} timeout={1000}>
              <Box>
                <Typography
                  variant="h1"
                  sx={{
                    color: 'white',
                    fontWeight: 800,
                    mb: 3,
                    textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                    fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },
                  }}
                >
                  VidyaMitra
                </Typography>
                <Typography
                  variant="h4"
                  sx={{
                    color: 'rgba(255,255,255,0.9)',
                    mb: 3,
                    fontWeight: 400,
                    fontSize: { xs: '1.2rem', md: '1.5rem' },
                  }}
                >
                  Student SWOT Analysis Platform
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    color: 'rgba(255,255,255,0.8)',
                    mb: 4,
                    lineHeight: 1.6,
                    fontSize: { xs: '1rem', md: '1.1rem' },
                  }}
                >
                  Empowering Indian education through intelligent student analysis.
                  Supporting CBSE, ICSE, and State boards with AI-powered insights.
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    size="large"
                    onClick={() => navigate('/login')}
                    sx={{
                      bgcolor: 'white',
                      color: theme.palette.primary.main,
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      borderRadius: 3,
                      boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                      '&:hover': {
                        bgcolor: 'rgba(255,255,255,0.9)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 6px 16px rgba(0,0,0,0.3)',
                      },
                    }}
                    endIcon={<ArrowForwardIcon />}
                  >
                    Get Started
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    onClick={() => navigate('/features')}
                    sx={{
                      color: 'white',
                      borderColor: 'white',
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      borderRadius: 3,
                      '&:hover': {
                        bgcolor: 'rgba(255,255,255,0.1)',
                        borderColor: 'white',
                        transform: 'translateY(-2px)',
                      },
                    }}
                  >
                    Learn More
                  </Button>
                </Box>
              </Box>
            </Fade>
          </Grid>
          <Grid item xs={12} md={6}>
            <Slide direction="left" in={isHeroVisible} timeout={1200}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: { xs: 300, md: 400 },
                }}
              >
                <Paper
                  elevation={8}
                  sx={{
                    p: 4,
                    borderRadius: 4,
                    bgcolor: 'rgba(255,255,255,0.95)',
                    backdropFilter: 'blur(10px)',
                    maxWidth: 400,
                    width: '100%',
                  }}
                >
                  <Box sx={{ textAlign: 'center' }}>
                    <Avatar
                      sx={{
                        width: 80,
                        height: 80,
                        bgcolor: theme.palette.secondary.main,
                        mx: 'auto',
                        mb: 2,
                      }}
                    >
                      <SchoolIcon sx={{ fontSize: 40 }} />
                    </Avatar>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                      Comprehensive Student Analysis
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      AI-powered SWOT analysis tailored for Indian educational boards
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-around' }}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="primary" sx={{ fontWeight: 700 }}>
                          1000+
                        </Typography>
                        <Typography variant="caption">Students</Typography>
                      </Box>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="secondary" sx={{ fontWeight: 700 }}>
                          50+
                        </Typography>
                        <Typography variant="caption">Schools</Typography>
                      </Box>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="success.main" sx={{ fontWeight: 700 }}>
                          95%
                        </Typography>
                        <Typography variant="caption">Satisfaction</Typography>
                      </Box>
                    </Box>
                  </Box>
                </Paper>
              </Box>
            </Slide>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

// Features Section Component
const FeaturesSection = () => {
  const theme = useTheme();
  const [featuresRef, isFeaturesVisible] = useIntersectionObserver({ threshold: 0.1 });

  const features = [
    {
      icon: AssessmentIcon,
      title: 'AI-Powered SWOT Analysis',
      description: 'Comprehensive analysis of student strengths, weaknesses, opportunities, and threats using advanced AI algorithms.',
      color: theme.palette.primary.main,
    },
    {
      icon: SchoolIcon,
      title: 'Multi-Board Support',
      description: 'Full support for CBSE, ICSE, and all major State boards with curriculum-specific insights.',
      color: theme.palette.secondary.main,
    },
    {
      icon: TrendingUpIcon,
      title: 'Performance Tracking',
      description: 'Real-time monitoring of student progress with detailed analytics and trend analysis.',
      color: theme.palette.success.main,
    },
    {
      icon: PeopleIcon,
      title: 'Multi-Stakeholder Access',
      description: 'Dedicated interfaces for teachers, parents, administrators, and students with role-based permissions.',
      color: theme.palette.warning.main,
    },
  ];

  return (
    <Box ref={featuresRef} sx={{ py: 8, bgcolor: 'background.default' }}>
      <Container maxWidth="lg">
        <Fade in={isFeaturesVisible} timeout={800}>
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography variant="h3" sx={{ fontWeight: 700, mb: 2, color: 'text.primary' }}>
              Powerful Features for Indian Education
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
              Designed specifically for the Indian education system with cultural sensitivity and local requirements in mind.
            </Typography>
          </Box>
        </Fade>
        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Zoom in={isFeaturesVisible} timeout={800 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    textAlign: 'center',
                    p: 3,
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: theme.shadows[8],
                    },
                  }}
                >
                  <Avatar
                    sx={{
                      width: 64,
                      height: 64,
                      bgcolor: feature.color,
                      mx: 'auto',
                      mb: 2,
                    }}
                  >
                    <feature.icon sx={{ fontSize: 32 }} />
                  </Avatar>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {feature.description}
                  </Typography>
                </Card>
              </Zoom>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
};

// Vision & Mission Section Component
const VisionMissionSection = () => {
  const [visionRef, isVisionVisible] = useIntersectionObserver({ threshold: 0.1 });

  return (
    <Box ref={visionRef} sx={{ py: 8, bgcolor: 'primary.main', color: 'white' }}>
      <Container maxWidth="lg">
        <Grid container spacing={6} alignItems="center">
          <Grid item xs={12} md={6}>
            <Fade in={isVisionVisible} timeout={800}>
              <Box>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 3 }}>
                  Our Vision
                </Typography>
                <Typography variant="h6" sx={{ mb: 4, lineHeight: 1.6, opacity: 0.9 }}>
                  To revolutionize Indian education by providing intelligent, data-driven insights
                  that help every student reach their full potential while respecting cultural values
                  and educational traditions.
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Chip
                    label="AI-Powered"
                    sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                  />
                  <Chip
                    label="Culturally Sensitive"
                    sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                  />
                  <Chip
                    label="Student-Centric"
                    sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                  />
                </Box>
              </Box>
            </Fade>
          </Grid>
          <Grid item xs={12} md={6}>
            <Slide direction="left" in={isVisionVisible} timeout={1000}>
              <Box>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 3 }}>
                  Our Mission
                </Typography>
                <Typography variant="h6" sx={{ mb: 4, lineHeight: 1.6, opacity: 0.9 }}>
                  Empowering teachers, parents, and administrators with comprehensive SWOT analysis
                  tools that provide actionable insights for student development across all Indian
                  educational boards.
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Chip
                    label="Multi-Board Support"
                    sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                  />
                  <Chip
                    label="Real-time Analytics"
                    sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                  />
                  <Chip
                    label="Collaborative Platform"
                    sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                  />
                </Box>
              </Box>
            </Slide>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

// Use Cases Section Component
const UseCasesSection = () => {
  const theme = useTheme();
  const [useCasesRef, isUseCasesVisible] = useIntersectionObserver({ threshold: 0.1 });

  const useCases = [
    {
      title: 'For Teachers',
      description: 'Generate comprehensive SWOT reports, track student progress, and identify areas for improvement with AI-powered insights.',
      features: ['Automated SWOT Generation', 'Progress Tracking', 'Parent Communication', 'Class Analytics'],
      color: theme.palette.primary.main,
      icon: '👩‍🏫',
    },
    {
      title: 'For Parents',
      description: 'Stay informed about your child\'s academic journey with detailed reports and actionable recommendations.',
      features: ['Student Reports', 'Performance Trends', 'Improvement Suggestions', 'Meeting Scheduling'],
      color: theme.palette.secondary.main,
      icon: '👨‍👩‍👧‍👦',
    },
    {
      title: 'For Administrators',
      description: 'Manage school-wide analytics, monitor teacher effectiveness, and make data-driven decisions.',
      features: ['School Analytics', 'Teacher Performance', 'Resource Planning', 'Compliance Reports'],
      color: theme.palette.success.main,
      icon: '🏫',
    },
  ];

  return (
    <Box ref={useCasesRef} sx={{ py: 8, bgcolor: 'background.paper' }}>
      <Container maxWidth="lg">
        <Fade in={isUseCasesVisible} timeout={800}>
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography variant="h3" sx={{ fontWeight: 700, mb: 2, color: 'text.primary' }}>
              Designed for Every Stakeholder
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
              VidyaMitra serves the entire educational ecosystem with tailored solutions for each user type.
            </Typography>
          </Box>
        </Fade>
        <Grid container spacing={4}>
          {useCases.map((useCase, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Zoom in={isUseCasesVisible} timeout={800 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    p: 4,
                    transition: 'all 0.3s ease-in-out',
                    border: `2px solid transparent`,
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: theme.shadows[12],
                      borderColor: useCase.color,
                    },
                  }}
                >
                  <Box sx={{ textAlign: 'center', mb: 3 }}>
                    <Typography variant="h2" sx={{ mb: 1 }}>
                      {useCase.icon}
                    </Typography>
                    <Typography variant="h5" sx={{ fontWeight: 600, color: useCase.color }}>
                      {useCase.title}
                    </Typography>
                  </Box>
                  <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.6 }}>
                    {useCase.description}
                  </Typography>
                  <Box>
                    {useCase.features.map((feature, featureIndex) => (
                      <Box key={featureIndex} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <StarIcon sx={{ fontSize: 16, color: useCase.color, mr: 1 }} />
                        <Typography variant="body2">{feature}</Typography>
                      </Box>
                    ))}
                  </Box>
                </Card>
              </Zoom>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
};

// Footer Component
const Footer = () => {
  const navigate = useNavigate();

  return (
    <Box sx={{ bgcolor: 'grey.900', color: 'white', py: 6 }}>
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          <Grid item xs={12} md={4}>
            <Typography variant="h6" sx={{ fontWeight: 700, mb: 2 }}>
              VidyaMitra
            </Typography>
            <Typography variant="body2" sx={{ mb: 2, opacity: 0.8 }}>
              Empowering Indian education through intelligent student analysis.
              Supporting CBSE, ICSE, and State boards with AI-powered insights.
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Chip
                label="AI-Powered"
                size="small"
                sx={{ bgcolor: 'rgba(255,255,255,0.1)', color: 'white' }}
              />
              <Chip
                label="Multi-Board"
                size="small"
                sx={{ bgcolor: 'rgba(255,255,255,0.1)', color: 'white' }}
              />
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Platform
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Button
                color="inherit"
                sx={{ justifyContent: 'flex-start', p: 0 }}
                onClick={() => navigate('/features')}
              >
                Features
              </Button>
              <Button
                color="inherit"
                sx={{ justifyContent: 'flex-start', p: 0 }}
                onClick={() => navigate('/about')}
              >
                About
              </Button>
              <Button
                color="inherit"
                sx={{ justifyContent: 'flex-start', p: 0 }}
                onClick={() => navigate('/login')}
              >
                Login
              </Button>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Support
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Button
                color="inherit"
                sx={{ justifyContent: 'flex-start', p: 0 }}
                onClick={() => navigate('/contact')}
              >
                Contact Us
              </Button>
              <Button
                color="inherit"
                sx={{ justifyContent: 'flex-start', p: 0 }}
              >
                Help Center
              </Button>
              <Button
                color="inherit"
                sx={{ justifyContent: 'flex-start', p: 0 }}
              >
                Documentation
              </Button>
            </Box>
          </Grid>
          <Grid item xs={12} md={3}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Contact Info
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <EmailIcon sx={{ fontSize: 16 }} />
              <Typography variant="body2"><EMAIL></Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <PhoneIcon sx={{ fontSize: 16 }} />
              <Typography variant="body2">+91 98765 43210</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <LocationOnIcon sx={{ fontSize: 16 }} />
              <Typography variant="body2">Mumbai, India</Typography>
            </Box>
          </Grid>
        </Grid>
        <Box
          sx={{
            borderTop: '1px solid rgba(255,255,255,0.1)',
            mt: 4,
            pt: 3,
            textAlign: 'center',
          }}
        >
          <Typography variant="body2" sx={{ opacity: 0.7 }}>
            © 2024 VidyaMitra. All rights reserved. | Privacy Policy | Terms of Service
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default function LandingPage() {
  return (
    <Box>
      <NavigationHeader />
      <Box sx={{ pt: 8 }}> {/* Add padding top to account for fixed header */}
        <HeroSection />
        <FeaturesSection />
        <VisionMissionSection />
        <UseCasesSection />
      </Box>
      <Footer />
    </Box>
  );
}
