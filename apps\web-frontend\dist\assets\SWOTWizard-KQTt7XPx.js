import{u as H,j as e,B as l,g as n,b as j,e as b,aU as K,P as Y,x as X,ak as L,ba as _,H as J,aW as Q,aX as V,d as h,G as m,A as Z,m as ee,W as se,k as S,a9 as R,aq as f,ar as k,as as C,I as ae,bb as te,bc as ie,aC as ne,aM as re,am as oe,aY as le,a0 as ce}from"./mui-Cjipzt4F.js";import{r as u}from"./vendor-BfWiUekA.js";import{u as de,m as B,A as he}from"./index-_PFqhbKW.js";import"./charts-Dx2u7Eir.js";const me={strengths:["Strong mathematical reasoning skills","Excellent memory and retention","Good leadership qualities in group activities","Consistent academic performance","Active participation in cultural events","Strong family support system","Multilingual communication abilities"],weaknesses:["Needs improvement in English speaking confidence","Time management during examinations","Hesitant to ask questions in class","Difficulty with practical applications","Limited exposure to technology","Peer pressure sensitivity"],opportunities:["Science Olympiad participation","Student council leadership roles","Inter-school cultural competitions","STEM career exploration programs","Scholarship opportunities for higher education","Skill development workshops","Community service projects"],threats:["Increased academic competition","Board examination pressure","Limited career guidance resources","Technology adaptation challenges","Economic constraints for higher education","Social media distractions"]},je=()=>{var $,O;const t=H(),E=de(),[a,A]=u.useState(0),[r,P]=u.useState(null),[y,v]=u.useState({strengths:[],weaknesses:[],opportunities:[],threats:[]}),[g,w]=u.useState(""),[N,W]=u.useState(!1),c=[{label:"Select Student",icon:Y,description:"Choose student for SWOT analysis"},{label:"Strengths",icon:X,description:"Identify student strengths"},{label:"Weaknesses",icon:L,description:"Areas for improvement"},{label:"Opportunities",icon:_,description:"Growth opportunities"},{label:"Threats",icon:L,description:"Potential challenges"},{label:"Review & Save",icon:J,description:"Finalize SWOT analysis"}],z=[{id:1,name:"Sanju Kumar Reddy",class:"10-A",avatar:"S"},{id:2,name:"Niraimathi Selvam",class:"10-A",avatar:"N"},{id:3,name:"Mahesh Reddy",class:"10-B",avatar:"M"},{id:4,name:"Ravi Teja Sharma",class:"9-A",avatar:"R"},{id:5,name:"Ankitha Patel",class:"10-A",avatar:"A"}],d=()=>["","strengths","weaknesses","opportunities","threats"][a],T=()=>{if(g.trim()&&a>=1&&a<=4){const s=d();v(i=>({...i,[s]:[...i[s],g.trim()]})),w("")}},M=(s,i)=>{v(o=>({...o,[s]:o[s].filter((p,U)=>U!==i)}))},D=(s,i)=>{y[s].includes(i)||v(o=>({...o,[s]:[...o[s],i]}))},F=()=>{a<c.length-1&&A(s=>s+1)},G=()=>{a>0&&A(s=>s-1)},q=async()=>{W(!0);try{await new Promise(s=>setTimeout(s,2e3)),alert("SWOT Analysis saved successfully!"),E("/dashboard/students")}catch(s){console.error("Error saving SWOT analysis:",s)}finally{W(!1)}},x=s=>{switch(s){case"strengths":return t.palette.success.main;case"weaknesses":return t.palette.error.main;case"opportunities":return t.palette.info.main;case"threats":return t.palette.warning.main;default:return t.palette.primary.main}},I=s=>{switch(s){case"strengths":return"💪";case"weaknesses":return"⚠️";case"opportunities":return"🚀";case"threats":return"⚡";default:return"📝"}};return e.jsxs(l,{sx:{maxWidth:1200,mx:"auto",p:3},children:[e.jsx(B.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:e.jsxs(l,{sx:{mb:4},children:[e.jsx(n,{variant:"h4",sx:{fontWeight:600,mb:1,background:`linear-gradient(135deg, ${t.palette.primary.main} 0%, ${t.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"SWOT Analysis Wizard"}),e.jsx(n,{variant:"body1",color:"text.secondary",children:"Create comprehensive SWOT analysis with AI-powered suggestions"})]})}),e.jsx(j,{sx:{mb:4,overflow:"visible"},children:e.jsx(b,{children:e.jsx(K,{activeStep:a,alternativeLabel:!0,children:c.map((s,i)=>e.jsx(Q,{children:e.jsxs(V,{StepIconComponent:({active:o,completed:p})=>e.jsx(l,{sx:{width:48,height:48,borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",background:p?`linear-gradient(135deg, ${t.palette.success.main} 0%, ${t.palette.success.dark} 100%)`:o?`linear-gradient(135deg, ${t.palette.primary.main} 0%, ${t.palette.secondary.main} 100%)`:h(t.palette.action.disabled,.12),color:p||o?"white":t.palette.action.disabled,transition:"all 0.3s ease"},children:e.jsx(s.icon,{sx:{fontSize:24}})}),children:[e.jsx(n,{variant:"subtitle2",sx:{fontWeight:500},children:s.label}),e.jsx(n,{variant:"caption",color:"text.secondary",children:s.description})]})},s.label))})})}),e.jsx(j,{children:e.jsxs(b,{sx:{p:4},children:[e.jsx(he,{mode:"wait",children:e.jsxs(B.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:[a===0&&e.jsxs(l,{children:[e.jsx(n,{variant:"h6",sx:{mb:3,fontWeight:600},children:"Select Student for SWOT Analysis"}),e.jsx(m,{container:!0,spacing:2,children:z.map(s=>e.jsx(m,{item:!0,xs:12,sm:6,md:4,children:e.jsx(j,{sx:{cursor:"pointer",border:(r==null?void 0:r.id)===s.id?`2px solid ${t.palette.primary.main}`:"1px solid",borderColor:(r==null?void 0:r.id)===s.id?"primary.main":"divider",transition:"all 0.2s ease","&:hover":{transform:"translateY(-2px)",boxShadow:t.shadows[4]}},onClick:()=>P(s),children:e.jsx(b,{children:e.jsxs(l,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx(Z,{sx:{bgcolor:"primary.main"},children:s.avatar}),e.jsxs(l,{children:[e.jsx(n,{variant:"subtitle1",sx:{fontWeight:600},children:s.name}),e.jsxs(n,{variant:"body2",color:"text.secondary",children:["Class ",s.class]})]})]})})})},s.id))})]}),a>=1&&a<=4&&e.jsxs(l,{children:[e.jsxs(n,{variant:"h6",sx:{mb:3,fontWeight:600},children:[I(d())," ",c[a].label]}),e.jsx(l,{sx:{mb:4},children:e.jsxs(ee,{direction:"row",spacing:2,alignItems:"center",children:[e.jsx(se,{fullWidth:!0,label:`Add ${c[a].label.toLowerCase()}`,value:g,onChange:s=>w(s.target.value),onKeyPress:s=>s.key==="Enter"&&T(),placeholder:`Enter student ${c[a].label.toLowerCase()}...`}),e.jsx(S,{variant:"contained",startIcon:e.jsx(R,{}),onClick:T,disabled:!g.trim(),children:"Add"})]})}),e.jsxs(m,{container:!0,spacing:3,children:[e.jsxs(m,{item:!0,xs:12,md:6,children:[e.jsxs(n,{variant:"subtitle1",sx:{mb:2,fontWeight:600},children:["Current ",c[a].label]}),e.jsx(f,{children:($=y[d()])==null?void 0:$.map((s,i)=>e.jsxs(k,{sx:{border:`1px solid ${h(x(d()),.2)}`,borderRadius:1,mb:1,background:h(x(d()),.05)},children:[e.jsx(C,{primary:s}),e.jsx(ae,{size:"small",onClick:()=>M(d(),i),color:"error",children:e.jsx(te,{})})]},i))})]}),e.jsxs(m,{item:!0,xs:12,md:6,children:[e.jsxs(n,{variant:"subtitle1",sx:{mb:2,fontWeight:600},children:[e.jsx(ie,{sx:{mr:1,verticalAlign:"middle"}}),"AI Suggestions"]}),e.jsx(f,{children:(O=me[d()])==null?void 0:O.map((s,i)=>e.jsxs(k,{sx:{border:"1px solid",borderColor:"divider",borderRadius:1,mb:1,cursor:"pointer","&:hover":{background:h(t.palette.primary.main,.05)}},onClick:()=>D(d(),s),children:[e.jsx(ne,{children:e.jsx(R,{color:"primary"})}),e.jsx(C,{primary:s})]},i))})]})]})]}),a===5&&e.jsxs(l,{children:[e.jsx(n,{variant:"h6",sx:{mb:3,fontWeight:600},children:"Review SWOT Analysis"}),r&&e.jsxs(re,{severity:"info",sx:{mb:3},children:["SWOT Analysis for ",e.jsx("strong",{children:r.name})," - Class ",r.class]}),e.jsx(m,{container:!0,spacing:3,children:Object.entries(y).map(([s,i])=>e.jsx(m,{item:!0,xs:12,md:6,children:e.jsx(j,{sx:{border:`2px solid ${h(x(s),.2)}`,background:`linear-gradient(135deg, ${h(x(s),.05)} 0%, ${h(x(s),.02)} 100%)`},children:e.jsxs(b,{children:[e.jsxs(n,{variant:"h6",sx:{fontWeight:600,color:x(s),mb:2},children:[I(s)," ",s.charAt(0).toUpperCase()+s.slice(1)]}),e.jsx(f,{dense:!0,children:i.map((o,p)=>e.jsx(k,{children:e.jsx(C,{primary:`• ${o}`})},p))})]})})},s))})]})]},a)}),e.jsxs(l,{sx:{display:"flex",justifyContent:"space-between",mt:4},children:[e.jsx(S,{onClick:G,disabled:a===0,startIcon:e.jsx(oe,{}),variant:"outlined",children:"Back"}),e.jsx(S,{onClick:a===c.length-1?q:F,endIcon:a===c.length-1?e.jsx(le,{}):e.jsx(ce,{}),variant:"contained",disabled:a===0&&!r,loading:N,sx:{background:`linear-gradient(135deg, ${t.palette.primary.main} 0%, ${t.palette.secondary.main} 100%)`},children:a===c.length-1?"Save SWOT Analysis":"Next"})]})]})})]})};export{je as default};
//# sourceMappingURL=SWOTWizard-KQTt7XPx.js.map
