import React, { useState, createContext, useContext, useEffect, Suspense } from 'react';
import { Browser<PERSON>outer, Routes, Route, Link, Outlet, Navigate, useNavigate } from 'react-router-dom';
import { CssBaseline, AppBar, Toolbar, Typography, Drawer, List, ListItem, ListItemIcon, ListItemText, Box, CircularProgress, Button, Select, MenuItem, FormControl, InputLabel, Avatar } from '@mui/material';
import { ThemeContextProvider } from './contexts/ThemeContext';
import { Dashboard as DashboardIcon, People as PeopleIcon, Assessment as AssessmentIcon, Settings as SettingsIcon, ExitToApp as ExitToAppIcon } from '@mui/icons-material';
import i18n from 'i18next';
import { initReactI18next, useTranslation } from 'react-i18next';
import HttpApi from 'i18next-http-backend';

// --- 1. CONTEXTS (Authentication and Application Settings) ---

// Authentication Context
const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(localStorage.getItem('isAuthenticated') === 'true');
  const [user, setUser] = useState(JSON.parse(localStorage.getItem('user')));
  // In a real app, token would be stored, and user info fetched or stored upon login

  const login = (userData) => {
    // Simulate API call for login
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockUser = { username: userData.username, role: 'teacher', school_id: 'school123', name: 'Demo User' };
        localStorage.setItem('isAuthenticated', 'true');
        localStorage.setItem('user', JSON.stringify(mockUser));
        setIsAuthenticated(true);
        setUser(mockUser);
        resolve(mockUser);
      }, 500);
    });
  };

  const logout = () => {
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('user');
    setIsAuthenticated(false);
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

const useAuth = () => useContext(AuthContext);

// Application Settings Context (for language, theme, etc.)
const AppSettingsContext = createContext();

const AppSettingsProvider = ({ children }) => {
  const [language, setLanguage] = useState(localStorage.getItem('appLanguage') || 'en');

  useEffect(() => {
    i18n.changeLanguage(language);
    localStorage.setItem('appLanguage', language);
  }, [language]);

  const changeLanguage = (lang) => {
    setLanguage(lang);
  };

  return (
    <AppSettingsContext.Provider value={{ language, changeLanguage }}>
      {children}
    </AppSettingsContext.Provider>
  );
};

const useAppSettings = () => useContext(AppSettingsContext);


// --- 2. i18n INTERNATIONALIZATION SETUP ---
i18n
  .use(HttpApi) // Use HttpApi backend to load translations
  .use(initReactI18next) // Passes i18n down to react-i18next
  .init({
    supportedLngs: ['en', 'hi'],
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development', // Enable debug in development
    interpolation: {
      escapeValue: false, // React already safes from xss
    },
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json', // Path to translation files in public folder
    },
    ns: ['common', 'dashboard', 'login'], // Namespaces
    defaultNS: 'common',
  });


// Modern components imports
import ModernDashboard from './components/Dashboard/ModernDashboard';
import ModernLoginPage from './components/Auth/ModernLoginPage';
import AnalyticsDashboard from './components/Analytics/AnalyticsDashboard';
import StudentManagement from './components/Students/StudentManagement';
import SWOTAnalysisVisualization from './components/SWOT/SWOTAnalysisVisualization';
import CulturalSWOTVisualization from './components/SWOT/CulturalSWOTVisualization';
import IndividualStudentSWOT from './components/SWOT/IndividualStudentSWOT';
import LandingPage from './components/Landing/LandingPage';
import AboutPage from './components/Pages/AboutPage';
import FeaturesPage from './components/Pages/FeaturesPage';
import ContactPage from './components/Pages/ContactPage';

// Phase 3 - New Component Imports
import StudentRegistration from './components/Students/StudentRegistration';
import StudentProfile from './components/Students/StudentProfile';
import AttendanceManagement from './components/Attendance/AttendanceManagement';
import GradeEntry from './components/Grades/GradeEntry';
import TeacherDashboard from './components/Dashboard/TeacherDashboard';
import SWOTWizard from './components/SWOT/SWOTWizard';
import ReportGeneration from './components/Reports/ReportGeneration';

// --- 4. LAYOUT COMPONENTS ---
const drawerWidth = 240;

// Enhanced Header Component
const Header = () => {
  const { t } = useTranslation('common');
  const { logout, user } = useAuth();
  const { language, changeLanguage } = useAppSettings();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleLanguageChange = (event) => {
    changeLanguage(event.target.value);
  };

  return (
    <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
      <Toolbar sx={{ px: { xs: 2, md: 3 } }}>
        <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, fontWeight: 600 }}>
          {t('platformTitle')}
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel id="language-select-label">{t('language')}</InputLabel>
            <Select
              labelId="language-select-label"
              id="language-select"
              value={language}
              label={t('language')}
              onChange={handleLanguageChange}
              sx={{
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(255, 255, 255, 0.3)',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(255, 255, 255, 0.5)',
                },
              }}
            >
              <MenuItem value="en">🇬🇧 English</MenuItem>
              <MenuItem value="hi">🇮🇳 हिन्दी</MenuItem>
            </Select>
          </FormControl>

          {user && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Avatar
                sx={{
                  width: 32,
                  height: 32,
                  bgcolor: 'secondary.main',
                  fontSize: '0.875rem',
                  fontWeight: 600,
                }}
              >
                {(user.name || user.username).charAt(0).toUpperCase()}
              </Avatar>
              <Typography sx={{ display: { xs: 'none', sm: 'block' } }}>
                {user.name || user.username}
              </Typography>
            </Box>
          )}

          <Button
            color="inherit"
            onClick={handleLogout}
            startIcon={<ExitToAppIcon />}
            sx={{
              borderRadius: 2,
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            {t('logout')}
          </Button>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

// Sidebar Component
const Sidebar = () => {
  const { t } = useTranslation('common');
  const menuItems = [
    { text: t('dashboard'), icon: <DashboardIcon />, path: '/dashboard' },
    { text: t('students'), icon: <PeopleIcon />, path: '/students' },
    { text: t('reports'), icon: <AssessmentIcon />, path: '/reports' },
    { text: t('settings'), icon: <SettingsIcon />, path: '/settings' },
  ];

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        [`& .MuiDrawer-paper`]: { width: drawerWidth, boxSizing: 'border-box', backgroundColor: '#2E5BA8', color: 'white' },
      }}
    >
      <Toolbar /> {/* For spacing under the AppBar */}
      <Box sx={{ overflow: 'auto' }}>
        <List>
          {menuItems.map((item) => (
            <ListItem button component={Link} to={item.path} key={item.text} sx={{
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              },
              '& .MuiListItemIcon-root': {
                color: 'white',
              }
            }}>
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItem>
          ))}
        </List>
      </Box>
    </Drawer>
  );
};

// Main Layout for Authenticated Routes
const MainLayout = () => {
  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <Header />
      <Sidebar />
      <Box
        component="main"
        sx={{ flexGrow: 1, bgcolor: 'background.default', p: 3, width: `calc(100% - ${drawerWidth}px)` }}
      >
        <Toolbar /> {/* For spacing under the AppBar */}
        <Suspense fallback={<LoadingSpinner />}>
          <Outlet /> {/* Nested routes will render here */}
        </Suspense>
      </Box>
    </Box>
  );
};

// --- 5. PAGE COMPONENTS (Placeholders) ---
// These would typically be in separate files under src/pages/

// Modern Login Page Wrapper
const LoginPageWrapper = () => {
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleLogin = async (formData) => {
    try {
      await login({ username: formData.email, password: formData.password });
      navigate('/dashboard');
    } catch (err) {
      console.error('Login failed:', err);
    }
  };

  const handleSignup = async (formData) => {
    try {
      // In a real app, this would call a signup API
      console.log('Signup data:', formData);
      // For demo, auto-login after signup
      await login({ username: formData.email, password: formData.password });
      navigate('/dashboard');
    } catch (err) {
      console.error('Signup failed:', err);
    }
  };

  return <ModernLoginPage onLogin={handleLogin} onSignup={handleSignup} />;
};

// Modern Page Components
const DashboardPage = () => {
  return <ModernDashboard />;
};

const StudentsPage = () => {
  return <StudentManagement />;
};

// Phase 3 - New Page Components
const StudentRegistrationPage = () => {
  return <StudentRegistration />;
};

const StudentProfilePage = () => {
  return <StudentProfile />;
};

const TeacherDashboardPage = () => {
  return <TeacherDashboard />;
};

const AttendanceManagementPage = () => {
  return <AttendanceManagement />;
};

const GradeEntryPage = () => {
  return <GradeEntry />;
};

const SWOTWizardPage = () => {
  return <SWOTWizard />;
};

const ReportGenerationPage = () => {
  return <ReportGeneration />;
};

const SWOTPage = () => {
  return <CulturalSWOTVisualization />;
};

const IndividualSWOTPage = () => {
  return <IndividualStudentSWOT />;
};

const AnalyticsPage = () => {
  return <AnalyticsDashboard />;
};

const ReportsPage = () => {
  const { t } = useTranslation('common');
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ fontWeight: 700, mb: 2 }}>
        {t('reports')}
      </Typography>
      <Typography variant="body1" color="text.secondary">
        Reports functionality coming soon...
      </Typography>
    </Box>
  );
};

const SettingsPage = () => {
  const { t } = useTranslation('common');
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ fontWeight: 700, mb: 2 }}>
        {t('settings')}
      </Typography>
      <Typography variant="body1" color="text.secondary">
        Settings functionality coming soon...
      </Typography>
    </Box>
  );
};
const NotFoundPage = () => {
  const { t } = useTranslation('common');
  return (
    <Box sx={{ textAlign: 'center', mt: 5 }}>
      <Typography variant="h3">{t('notFoundTitle')}</Typography>
      <Typography>{t('notFoundMessage')}</Typography>
      <Button component={Link} to="/dashboard" variant="contained" sx={{ mt: 2 }}>
        {t('goHome')}
      </Button>
    </Box>
  );
};
const LoadingSpinner = () => (
  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
    <CircularProgress />
  </Box>
);


// --- 6. PROTECTED ROUTE COMPONENT ---
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated } = useAuth();
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  return children;
};


// --- 7. MAIN APP COMPONENT & ROUTER CONFIGURATION ---
function App() {
  return (
    <ThemeContextProvider>
      <AuthProvider>
        <AppSettingsProvider>
          <Suspense fallback={<LoadingSpinner />}>
            <BrowserRouter>
              <Routes>
                {/* Public Routes */}
                <Route path="/" element={<LandingPage />} />
                <Route path="/about" element={<AboutPage />} />
                <Route path="/features" element={<FeaturesPage />} />
                <Route path="/contact" element={<ContactPage />} />
                <Route path="/login" element={<LoginPageWrapper />} />

                {/* Protected Dashboard Routes */}
                <Route
                  path="/dashboard"
                  element={
                    <ProtectedRoute>
                      <MainLayout />
                    </ProtectedRoute>
                  }
                >
                  {/* Nested routes within MainLayout */}
                  <Route index element={<DashboardPage />} />
                  <Route path="students" element={<StudentsPage />} />
                  <Route path="students/register" element={<StudentRegistrationPage />} />
                  <Route path="students/:studentId" element={<StudentProfilePage />} />
                  <Route path="students/:studentId/swot" element={<IndividualSWOTPage />} />
                  <Route path="teacher" element={<TeacherDashboardPage />} />
                  <Route path="attendance" element={<AttendanceManagementPage />} />
                  <Route path="grades" element={<GradeEntryPage />} />
                  <Route path="swot/wizard" element={<SWOTWizardPage />} />
                  <Route path="analytics" element={<AnalyticsPage />} />
                  <Route path="swot" element={<SWOTPage />} />
                  <Route path="reports" element={<ReportsPage />} />
                  <Route path="reports/generate" element={<ReportGenerationPage />} />
                  <Route path="settings" element={<SettingsPage />} />
                </Route>
                <Route path="*" element={<NotFoundPage />} />
              </Routes>
            </BrowserRouter>
          </Suspense>
        </AppSettingsProvider>
      </AuthProvider>
    </ThemeContextProvider>
  );
}

export default App;
