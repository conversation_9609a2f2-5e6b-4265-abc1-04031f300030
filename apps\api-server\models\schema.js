/**
 * VidyaMitra Platform - Complete Database Schema
 *
 * This file defines the comprehensive MongoDB schema using Mongoose for the VidyaMitra Platform
 * specifically adapted for Indian schools. It includes all entity models with proper
 * validation, relationships, and India-specific fields as per the technical documentation.
 *
 * Based on: docs/technical/03-data-model.md
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * School Schema
 * Represents an educational institution in the Indian context
 * Includes fields for board affiliation, medium of instruction, etc.
 */
const SchoolSchema = new Schema({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  code: { 
    type: String, 
    required: true,
    unique: true,
    trim: true,
    // UDISE code in India
    validate: {
      validator: function(v) {
        return /^\d{11}$/.test(v);
      },
      message: props => `${props.value} is not a valid UDISE code!`
    }
  },
  branch: { 
    type: String, 
    trim: true 
  },
  affiliation: {
    board: { 
      type: String, 
      required: true,
      enum: ['CBSE', 'ICSE', 'State_AP', 'State_TS', 'State_KA', 'State_TN', 'State_KL', 
             'State_MH', 'State_GJ', 'State_MP', 'State_UP', 'State_WB', 'State_BR', 
             'State_OR', 'State_PB', 'State_HR', 'State_RJ', 'State_JK', 'State_UK', 
             'State_HP', 'State_AS', 'State_MN', 'State_ML', 'State_TR', 'State_AR', 
             'State_NL', 'State_MZ', 'State_SK', 'State_GA', 'State_DL', 'IB', 'Cambridge', 'Other']
    },
    number: { 
      type: String, 
      trim: true 
    },
    validity: { 
      type: Date 
    }
  },
  address: {
    street: { type: String, trim: true },
    city: { type: String, required: true, trim: true },
    district: { type: String, required: true, trim: true },
    state: { type: String, required: true, trim: true },
    pincode: { 
      type: String, 
      required: true,
      validate: {
        validator: function(v) {
          return /^\d{6}$/.test(v);
        },
        message: props => `${props.value} is not a valid Indian PIN code!`
      }
    },
    zone: { type: String, trim: true } // Educational zone
  },
  contact: {
    email: { 
      type: String, 
      required: true,
      trim: true,
      lowercase: true,
      validate: {
        validator: function(v) {
          return /^\S+@\S+\.\S+$/.test(v);
        },
        message: props => `${props.value} is not a valid email address!`
      }
    },
    phone: { 
      type: String, 
      required: true,
      validate: {
        validator: function(v) {
          return /^[6-9]\d{9}$/.test(v);
        },
        message: props => `${props.value} is not a valid Indian phone number!`
      }
    },
    alternative_phone: { type: String },
    website: { type: String, trim: true }
  },
  administration: {
    principal_name: { type: String, trim: true },
    manager_name: { type: String, trim: true },
    trust_name: { type: String, trim: true }
  },
  classification: {
    type: { 
      type: String, 
      enum: ['Government', 'Private', 'Aided', 'KV', 'JNV', 'Sainik', 'Other'],
      required: true
    },
    category: { 
      type: String, 
      enum: ['Primary', 'Upper Primary', 'Secondary', 'Higher Secondary'],
      required: true
    },
    gender: { 
      type: String, 
      enum: ['Co-Ed', 'Boys', 'Girls'],
      required: true
    },
    medium: [{ 
      type: String,
      enum: ['English', 'Hindi', 'Tamil', 'Telugu', 'Kannada', 'Malayalam', 'Marathi', 
             'Gujarati', 'Bengali', 'Odia', 'Punjabi', 'Assamese', 'Urdu', 'Other']
    }],
    residential: { type: Boolean, default: false },
    religious_affiliation: { type: String, trim: true }
  },
  subscription: {
    tier: { 
      type: String, 
      enum: ['Lite', 'Standard', 'Premium', 'Elite', 'Enterprise'],
      default: 'Lite'
    },
    features_enabled: [String],
    payment_status: { 
      type: String, 
      enum: ['Active', 'Pending', 'Overdue', 'Cancelled'],
      default: 'Active'
    },
    renewal_date: { type: Date }
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Academic Year Schema
 * Represents an academic year in the Indian context
 * Indian schools typically follow April-March academic year
 */
const AcademicYearSchema = new Schema({
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  name: { 
    type: String, 
    required: true,
    trim: true,
    // Format: YYYY-YYYY (e.g., 2024-2025)
    validate: {
      validator: function(v) {
        return /^\d{4}-\d{4}$/.test(v);
      },
      message: props => `${props.value} is not a valid academic year format!`
    }
  },
  start_date: { 
    type: Date, 
    required: true 
  },
  end_date: { 
    type: Date, 
    required: true 
  },
  is_current: { 
    type: Boolean, 
    default: false 
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Academic Term Schema
 * Represents an academic term in the Indian context
 * Indian schools typically have 2-3 terms per academic year instead of quarters
 */
const AcademicTermSchema = new Schema({
  academic_year_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'AcademicYear',
    required: true
  },
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  name: { 
    type: String, 
    required: true,
    trim: true,
    // E.g., "Term 1", "First Term", "Half Yearly", "Annual"
  },
  term_number: { 
    type: Number, 
    required: true,
    min: 1,
    max: 3
  },
  start_date: { 
    type: Date, 
    required: true 
  },
  end_date: { 
    type: Date, 
    required: true 
  },
  examination_dates: {
    start: { type: Date },
    end: { type: Date }
  },
  is_current: { 
    type: Boolean, 
    default: false 
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Class Schema
 * Represents a class/standard in the Indian context
 * Includes sections and stream information
 */
const ClassSchema = new Schema({
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  academic_year_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'AcademicYear',
    required: true
  },
  standard: { 
    type: Number, 
    required: true,
    min: 1,
    max: 12
  },
  section: { 
    type: String, 
    required: true,
    trim: true
  },
  stream: { 
    type: String, 
    enum: ['N/A', 'Science', 'Commerce', 'Arts', 'Vocational'],
    default: 'N/A'
  },
  class_teacher_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'Teacher' 
  },
  room_number: { 
    type: String, 
    trim: true 
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * User Schema
 * Represents a user of the system (teacher, admin, parent)
 */
const UserSchema = new Schema({
  username: { 
    type: String, 
    required: true,
    unique: true,
    trim: true
  },
  email: { 
    type: String, 
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    validate: {
      validator: function(v) {
        return /^\S+@\S+\.\S+$/.test(v);
      },
      message: props => `${props.value} is not a valid email address!`
    }
  },
  password: { 
    type: String, 
    required: true 
    // Will be hashed before saving
  },
  role: { 
    type: String, 
    required: true,
    enum: ['Admin', 'Teacher', 'Parent', 'Staff'],
    default: 'Teacher'
  },
  school_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'School',
    required: true
  },
  profile: {
    name: { type: String, trim: true },
    phone: { type: String, trim: true },
    profile_image: { type: String, trim: true }
  },
  preferences: {
    language: { 
      type: String, 
      enum: ['English', 'Hindi', 'Tamil', 'Telugu', 'Kannada', 'Malayalam', 
             'Marathi', 'Gujarati', 'Bengali', 'Odia', 'Punjabi', 'Assamese', 'Urdu'],
      default: 'English'
    },
    notifications: {
      email: { type: Boolean, default: true },
      sms: { type: Boolean, default: true },
      app: { type: Boolean, default: true }
    },
    theme: { 
      type: String, 
      enum: ['Light', 'Dark', 'System'],
      default: 'System'
    }
  },
  last_login: { 
    type: Date 
  },
  is_active: { 
    type: Boolean, 
    default: true 
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
});

/**
 * Student Schema
 * Represents a student with comprehensive information for SWOT analysis
 */
const StudentSchema = new Schema({
  student_id: {
    type: String,
    required: true,
    unique: true,
    match: /^STU\d{5}$/,
    trim: true
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  grade_level: {
    type: Number,
    required: true,
    min: 1,
    max: 12
  },
  homeroom: {
    type: String,
    required: true,
    trim: true,
    maxlength: 10
  },
  academic_year: {
    type: String,
    required: true,
    match: /^\d{4}-\d{4}$/
  },
  school_id: {
    type: Schema.Types.ObjectId,
    ref: 'School',
    required: true
  },
  // Indian education specific fields
  board: {
    type: String,
    enum: ['CBSE', 'ICSE', 'State Board', 'IB', 'IGCSE'],
    required: true
  },
  stream: {
    type: String,
    enum: ['Science', 'Commerce', 'Arts', 'Vocational', 'N/A'],
    default: 'N/A'
  },
  medium_of_instruction: {
    type: String,
    enum: ['English', 'Hindi', 'Regional Language', 'Bilingual'],
    default: 'English'
  },
  admission_number: {
    type: String,
    required: true,
    unique: true
  },
  date_of_birth: {
    type: Date,
    required: true
  },
  gender: {
    type: String,
    enum: ['Male', 'Female', 'Other'],
    required: true
  },
  category: {
    type: String,
    enum: ['General', 'OBC', 'SC', 'ST', 'EWS'],
    default: 'General'
  },
  address: {
    street: String,
    city: String,
    state: String,
    pincode: {
      type: String,
      match: /^\d{6}$/
    },
    country: {
      type: String,
      default: 'India'
    }
  },
  contact_info: {
    phone: {
      type: String,
      match: /^[6-9]\d{9}$/
    },
    email: {
      type: String,
      lowercase: true,
      validate: {
        validator: function(v) {
          return /^\S+@\S+\.\S+$/.test(v);
        },
        message: 'Invalid email format'
      }
    }
  },
  emergency_contact: {
    name: String,
    relationship: String,
    phone: {
      type: String,
      match: /^[6-9]\d{9}$/
    }
  },
  medical_info: {
    blood_group: {
      type: String,
      enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
    },
    allergies: [String],
    medical_conditions: [String],
    medications: [String]
  },
  enrollment_date: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Transferred', 'Graduated'],
    default: 'Active'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for student's age
StudentSchema.virtual('age').get(function() {
  if (this.date_of_birth) {
    const today = new Date();
    const birthDate = new Date(this.date_of_birth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }
  return null;
});

// Index for efficient queries
StudentSchema.index({ student_id: 1 });
StudentSchema.index({ school_id: 1, grade_level: 1 });
StudentSchema.index({ academic_year: 1, status: 1 });

/**
 * Guardian Schema
 * Represents parents/guardians of students
 */
const GuardianSchema = new Schema({
  guardian_id: {
    type: String,
    required: true,
    unique: true,
    match: /^GRD\d{5}$/
  },
  student_id: {
    type: Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  relationship: {
    type: String,
    required: true,
    enum: ['Father', 'Mother', 'Guardian', 'Grandfather', 'Grandmother', 'Uncle', 'Aunt', 'Other']
  },
  email: {
    type: String,
    lowercase: true,
    validate: {
      validator: function(v) {
        return !v || /^\S+@\S+\.\S+$/.test(v);
      },
      message: 'Invalid email format'
    }
  },
  phone: {
    type: String,
    match: /^[6-9]\d{9}$/
  },
  occupation: String,
  education: {
    type: String,
    enum: ['Below 10th', '10th Pass', '12th Pass', 'Graduate', 'Post Graduate', 'Professional', 'Other']
  },
  annual_income: {
    type: String,
    enum: ['Below 1 Lakh', '1-3 Lakhs', '3-5 Lakhs', '5-10 Lakhs', '10-25 Lakhs', 'Above 25 Lakhs']
  },
  address: {
    street: String,
    city: String,
    state: String,
    pincode: {
      type: String,
      match: /^\d{6}$/
    }
  },
  is_primary_contact: {
    type: Boolean,
    default: false
  },
  communication_preference: {
    type: String,
    enum: ['SMS', 'Email', 'Phone Call', 'WhatsApp', 'App Notification'],
    default: 'App Notification'
  },
  language_preference: {
    type: String,
    enum: ['English', 'Hindi', 'Regional Language'],
    default: 'English'
  }
}, {
  timestamps: true
});

GuardianSchema.index({ student_id: 1 });
GuardianSchema.index({ guardian_id: 1 });

// Create and export models
const models = {
  School: mongoose.model('School', SchoolSchema),
  AcademicYear: mongoose.model('AcademicYear', AcademicYearSchema),
  AcademicTerm: mongoose.model('AcademicTerm', AcademicTermSchema),
  Class: mongoose.model('Class', ClassSchema),
  User: mongoose.model('User', UserSchema),
  Student: mongoose.model('Student', StudentSchema),
  Guardian: mongoose.model('Guardian', GuardianSchema)
};

module.exports = models;
