/**
 * Student SWOT Analysis Platform - API Server
 * 
 * This is the main entry point for the backend API server of the Student SWOT Analysis Platform.
 * It sets up an Express server with routes for all entities, connects to MongoDB,
 * and implements authentication, error handling, and other middleware.
 * 
 * The API is designed specifically for Indian schools with support for various boards,
 * term structures, and other India-specific educational requirements.
 */

// Import required packages
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// Load environment variables
dotenv.config();

// Import comprehensive database models
const {
  // Base models
  School, AcademicYear, AcademicTerm, Class, User, Student, Guardian,
  // Academic models
  AcademicPerformance, Subject, Attendance, QuarterlyAttendance,
  // Behavioral models
  BehavioralIncident, BehavioralSummary, ExtracurricularActivity, SWOTAnalysis,
  // System models
  UserStudentAccess, SystemConfig, AuditLog, Notification,
  // Utility functions
  connectDB, initializeDB
} = require('./models/index');

// For backward compatibility, create models object
const models = {
  School, AcademicYear, AcademicTerm, Class, User, Student, Guardian,
  AcademicPerformance, Subject, Attendance, QuarterlyAttendance,
  BehavioralIncident, BehavioralSummary, ExtracurricularActivity, SWOTAnalysis,
  UserStudentAccess, SystemConfig, AuditLog, Notification
};

// Create Express app
const app = express();

// Define constants
const PORT = process.env.PORT || 4000;
const API_VERSION = 'v1';
const API_BASE = `/api/${API_VERSION}`;
const NODE_ENV = process.env.NODE_ENV || 'development';

// Set up logging
let accessLogStream;
if (NODE_ENV === 'production') {
  // Create log directory if it doesn't exist
  const logDirectory = path.join(__dirname, 'logs');
  if (!fs.existsSync(logDirectory)) {
    fs.mkdirSync(logDirectory);
  }
  accessLogStream = fs.createWriteStream(
    path.join(logDirectory, 'access.log'),
    { flags: 'a' }
  );
}

// Configure middleware
app.use(helmet()); // Security headers
app.use(cors()); // Enable CORS for all routes
app.use(express.json({ limit: '10mb' })); // Parse JSON bodies
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // Parse URL-encoded bodies
app.use(compression()); // Compress responses

// Set up request logging
if (NODE_ENV === 'production') {
  app.use(morgan('combined', { stream: accessLogStream }));
} else {
  app.use(morgan('dev'));
}

// Configure rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many requests from this IP, please try again after 15 minutes'
});

// Apply rate limiting to all API routes
app.use(API_BASE, apiLimiter);

// Database connection
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('Connected to MongoDB');
})
.catch((err) => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

// Authentication middleware
const authenticateJWT = (req, res, next) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: 'Authorization header missing' });
  }

  const token = authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Token missing' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Invalid or expired token' });
  }
};

// Role-based access control middleware
const authorize = (roles = []) => {
  if (typeof roles === 'string') {
    roles = [roles];
  }

  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    if (roles.length && !roles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Forbidden: Insufficient permissions' });
    }

    next();
  };
};

// Health check route
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', version: API_VERSION });
});

// Import route modules
const studentRoutes = require('./routes/students');
const swotRoutes = require('./routes/swot');

// API documentation route
app.get(`${API_BASE}/docs`, (req, res) => {
  res.status(200).json({
    message: 'VidyaMitra Platform API',
    version: API_VERSION,
    description: 'Comprehensive SWOT Analysis Platform for Indian Schools',
    endpoints: [
      { path: '/auth/login', method: 'POST', description: 'Authenticate user' },
      { path: '/auth/register', method: 'POST', description: 'Register new user' },
      { path: '/students', method: 'GET', description: 'Get all students with filtering' },
      { path: '/students/:id', method: 'GET', description: 'Get student profile' },
      { path: '/students', method: 'POST', description: 'Create new student' },
      { path: '/students/:id', method: 'PUT', description: 'Update student' },
      { path: '/swot/student/:studentId', method: 'GET', description: 'Get student SWOT analysis' },
      { path: '/swot/generate/:studentId', method: 'POST', description: 'Generate SWOT analysis' },
      { path: '/swot/class/:classId', method: 'GET', description: 'Get class SWOT summary' }
    ],
    features: [
      'Student Management with Indian education context',
      'AI-powered SWOT Analysis',
      'Board-specific features (CBSE/ICSE/State)',
      'Performance tracking and analytics',
      'Comprehensive audit logging',
      'Role-based access control'
    ]
  });
});

// ===== AUTH ROUTES =====

/**
 * @route   POST /api/v1/auth/login
 * @desc    Authenticate user & get token
 * @access  Public
 */
app.post(`${API_BASE}/auth/login`, async (req, res) => {
  try {
    const { username, password } = req.body;

    // Validate request
    if (!username || !password) {
      return res.status(400).json({ error: 'Please provide username and password' });
    }

    // Find user
    const user = await models.User.findOne({ username });
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check if user is active
    if (!user.is_active) {
      return res.status(401).json({ error: 'Account is disabled. Please contact administrator' });
    }

    // Validate password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Create payload for JWT
    const payload = {
      id: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      school_id: user.school_id
    };

    // Update last login
    user.last_login = Date.now();
    await user.save();

    // Sign token
    jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: '1d' },
      (err, token) => {
        if (err) throw err;
        res.json({
          success: true,
          token: token,
          user: {
            id: user._id,
            username: user.username,
            email: user.email,
            role: user.role,
            name: user.profile.name,
            school_id: user.school_id
          }
        });
      }
    );
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Server error during authentication' });
  }
});

// ===== API ROUTES =====

// Use route modules with authentication middleware
app.use(`${API_BASE}/students`, authenticateJWT, authorize(['Admin', 'Teacher', 'Parent']), studentRoutes);
app.use(`${API_BASE}/swot`, authenticateJWT, authorize(['Admin', 'Teacher', 'Parent']), swotRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT} in ${NODE_ENV} mode`);
  console.log(`API documentation available at http://localhost:${PORT}${API_BASE}/docs`);
});

module.exports = app;
