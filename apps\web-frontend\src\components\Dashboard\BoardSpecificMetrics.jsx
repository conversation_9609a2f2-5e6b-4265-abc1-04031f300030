import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  LinearProgress,
  Fade,
  Skeleton,
  useTheme,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  School as SchoolIcon,
  Assessment as AssessmentIcon,
  EmojiEvents as TrophyIcon,
  Timeline as TimelineIcon,
  Star as StarIcon,
  CheckCircle as CheckIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';

// Board-specific configuration
const boardConfigs = {
  CBSE: {
    name: 'Central Board of Secondary Education',
    color: '#2E5BA8',
    features: [
      'Continuous and Comprehensive Evaluation (CCE)',
      'NCERT Curriculum',
      'All India Secondary School Examination',
      'All India Senior School Certificate Examination',
      'Skill-based Assessment',
      'Co-scholastic Activities',
    ],
    assessmentTypes: [
      { name: 'Formative Assessment', weight: 40, description: 'Continuous evaluation' },
      { name: 'Summative Assessment', weight: 60, description: 'Term-end examinations' },
    ],
    gradingSystem: 'A1 to E2 (9-point grading)',
    subjects: {
      compulsory: ['English', 'Hindi', 'Mathematics', 'Science', 'Social Science'],
      optional: ['Computer Science', 'Physical Education', 'Art Education'],
    },
  },
  ICSE: {
    name: 'Indian Certificate of Secondary Education',
    color: '#FF9933',
    features: [
      'Comprehensive English Language Focus',
      'Detailed Syllabi',
      'Internal Assessment',
      'Practical Examinations',
      'Project Work',
      'Analytical Thinking Development',
    ],
    assessmentTypes: [
      { name: 'Internal Assessment', weight: 20, description: 'School-based evaluation' },
      { name: 'External Examination', weight: 80, description: 'Board examinations' },
    ],
    gradingSystem: 'Percentage-based marking',
    subjects: {
      compulsory: ['English Language', 'English Literature', 'Hindi', 'History & Civics', 'Geography', 'Mathematics', 'Physics', 'Chemistry', 'Biology'],
      optional: ['Computer Applications', 'Commercial Studies', 'Economics'],
    },
  },
  STATE: {
    name: 'State Education Board',
    color: '#138808',
    features: [
      'Regional Language Emphasis',
      'State-specific Curriculum',
      'Local Cultural Integration',
      'Affordable Education',
      'Regional Employment Focus',
      'Community-based Learning',
    ],
    assessmentTypes: [
      { name: 'Unit Tests', weight: 20, description: 'Regular assessments' },
      { name: 'Half-yearly Exam', weight: 30, description: 'Mid-term evaluation' },
      { name: 'Annual Exam', weight: 50, description: 'Final examination' },
    ],
    gradingSystem: 'Percentage and Grade-based',
    subjects: {
      compulsory: ['Regional Language', 'English', 'Mathematics', 'Science', 'Social Studies'],
      optional: ['Vocational Subjects', 'Agriculture', 'Arts & Crafts'],
    },
  },
};

// Board Feature Card Component
const BoardFeatureCard = ({ board, loading = false }) => {
  const theme = useTheme();
  const config = boardConfigs[board];

  if (loading) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Skeleton variant="text" width="60%" height={32} />
          <Skeleton variant="text" width="40%" height={20} sx={{ mb: 2 }} />
          {[...Array(4)].map((_, index) => (
            <Skeleton key={index} variant="text" width="90%" height={16} sx={{ mb: 1 }} />
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <Fade in timeout={300}>
      <Card
        sx={{
          height: '100%',
          border: `2px solid ${config.color}20`,
          '&:hover': {
            border: `2px solid ${config.color}40`,
            transform: 'translateY(-2px)',
            boxShadow: theme.shadows[4],
          },
          transition: 'all 0.3s ease-in-out',
        }}
      >
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Avatar
              sx={{
                bgcolor: config.color,
                width: 48,
                height: 48,
                mr: 2,
              }}
            >
              <SchoolIcon />
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 700, color: config.color }}>
                {board} Board
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {config.name}
              </Typography>
            </Box>
          </Box>

          <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2 }}>
            Key Features:
          </Typography>
          <List dense>
            {config.features.map((feature, index) => (
              <ListItem key={index} sx={{ py: 0.5 }}>
                <ListItemIcon sx={{ minWidth: 32 }}>
                  <CheckIcon sx={{ fontSize: 16, color: config.color }} />
                </ListItemIcon>
                <ListItemText
                  primary={feature}
                  primaryTypographyProps={{ variant: 'body2' }}
                />
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>
    </Fade>
  );
};

// Assessment Structure Card
const AssessmentStructureCard = ({ board, loading = false }) => {
  const config = boardConfigs[board];

  if (loading) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Skeleton variant="text" width="60%" height={32} />
          {[...Array(3)].map((_, index) => (
            <Box key={index} sx={{ mb: 2 }}>
              <Skeleton variant="text" width="70%" height={20} />
              <Skeleton variant="rectangular" height={8} sx={{ mt: 1 }} />
            </Box>
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <Fade in timeout={400}>
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Avatar sx={{ bgcolor: config.color, mr: 2 }}>
              <AssessmentIcon />
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Assessment Structure
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {board} Board Evaluation System
              </Typography>
            </Box>
          </Box>

          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2 }}>
              Assessment Types:
            </Typography>
            {config.assessmentTypes.map((assessment, index) => (
              <Box key={index} sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {assessment.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {assessment.weight}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={assessment.weight}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    bgcolor: 'action.hover',
                    '& .MuiLinearProgress-bar': {
                      bgcolor: config.color,
                    },
                  }}
                />
                <Typography variant="caption" color="text.secondary">
                  {assessment.description}
                </Typography>
              </Box>
            ))}
          </Box>

          <Divider sx={{ my: 2 }} />

          <Box>
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
              Grading System:
            </Typography>
            <Chip
              label={config.gradingSystem}
              variant="outlined"
              sx={{ borderColor: config.color, color: config.color }}
            />
          </Box>
        </CardContent>
      </Card>
    </Fade>
  );
};

// Subject Structure Card
const SubjectStructureCard = ({ board, loading = false }) => {
  const config = boardConfigs[board];

  if (loading) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Skeleton variant="text" width="60%" height={32} />
          <Skeleton variant="text" width="40%" height={20} sx={{ mb: 2 }} />
          {[...Array(6)].map((_, index) => (
            <Skeleton key={index} variant="text" width="80%" height={16} sx={{ mb: 1 }} />
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <Fade in timeout={500}>
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Avatar sx={{ bgcolor: config.color, mr: 2 }}>
              <StarIcon />
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Subject Structure
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {board} Board Curriculum
              </Typography>
            </Box>
          </Box>

          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2, color: config.color }}>
              Compulsory Subjects:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
              {config.subjects.compulsory.map((subject, index) => (
                <Chip
                  key={index}
                  label={subject}
                  size="small"
                  sx={{
                    bgcolor: `${config.color}10`,
                    color: config.color,
                    fontWeight: 500,
                  }}
                />
              ))}
            </Box>
          </Box>

          <Box>
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2, color: 'text.secondary' }}>
              Optional Subjects:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {config.subjects.optional.map((subject, index) => (
                <Chip
                  key={index}
                  label={subject}
                  size="small"
                  variant="outlined"
                  sx={{
                    borderColor: config.color,
                    color: config.color,
                  }}
                />
              ))}
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Fade>
  );
};

// Main Board Specific Metrics Component
const BoardSpecificMetrics = ({ selectedBoard = 'CBSE', loading = false }) => {
  return (
    <Box>
      <Typography variant="h5" sx={{ fontWeight: 700, mb: 3, color: 'primary.main' }}>
        {selectedBoard} Board Specifications
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} lg={4}>
          <BoardFeatureCard board={selectedBoard} loading={loading} />
        </Grid>
        <Grid item xs={12} lg={4}>
          <AssessmentStructureCard board={selectedBoard} loading={loading} />
        </Grid>
        <Grid item xs={12} lg={4}>
          <SubjectStructureCard board={selectedBoard} loading={loading} />
        </Grid>
      </Grid>
    </Box>
  );
};

export default BoardSpecificMetrics;
