{"name": "@vidyamitra/web-frontend", "version": "1.0.0", "description": "VidyaMitra Frontend - React + Vite Application", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx,ts,tsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "@mui/material": "^5.14.0", "@mui/icons-material": "^5.14.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/x-charts": "^6.18.0", "@mui/x-data-grid": "^6.18.0", "@mui/x-date-pickers": "^6.18.0", "i18next": "^23.7.0", "react-i18next": "^13.5.0", "i18next-http-backend": "^2.4.0", "i18next-browser-languagedetector": "^7.2.0", "axios": "^1.6.0", "react-query": "^3.39.0", "react-hook-form": "^7.48.0", "@hookform/resolvers": "^3.3.0", "yup": "^1.4.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "framer-motion": "^10.16.0", "html2canvas": "^1.4.0", "jspdf": "^2.5.0", "react-pdf": "^7.5.0", "react-dropzone": "^14.2.0", "react-hot-toast": "^2.4.0", "zustand": "^4.4.0", "dayjs": "^1.11.0", "lodash": "^4.17.0", "clsx": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/lodash": "^4.14.0", "@vitejs/plugin-react": "^4.1.0", "vite": "^5.0.0", "vitest": "^1.0.0", "@vitest/ui": "^1.0.0", "@vitest/coverage-v8": "^1.0.0", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "prettier": "^3.1.0", "typescript": "^5.3.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/user-event": "^14.5.0", "jsdom": "^23.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}