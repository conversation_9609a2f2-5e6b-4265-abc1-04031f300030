import{u as T,j as e,B as c,g as t,b as y,e as f,aU as J,w as k,s as Q,aV as D,H as ee,aW as ae,aX as te,d as P,k as F,am as ie,aY as re,a0 as le,A as $,I as ne,aZ as se,G as i,W as o,a4 as C,a5 as N,a6 as S,a7 as x,n as U,O as R,aM as G,m as w,a_ as L,a$ as q}from"./mui-Cjipzt4F.js";import{r as W}from"./vendor-BfWiUekA.js";import{u as oe,a as de,m as M,A as ce}from"./index-_PFqhbKW.js";import"./charts-Dx2u7Eir.js";const Y=[{value:"CBSE",label:"Central Board of Secondary Education (CBSE)",color:"#2E5BA8"},{value:"ICSE",label:"Indian Certificate of Secondary Education (ICSE)",color:"#FF9933"},{value:"STATE_AP",label:"Andhra Pradesh State Board",color:"#00C853"},{value:"STATE_TN",label:"Tamil Nadu State Board",color:"#9C27B0"},{value:"STATE_KA",label:"Karnataka State Board",color:"#FF5722"},{value:"STATE_TG",label:"Telangana State Board",color:"#607D8B"},{value:"IB",label:"International Baccalaureate (IB)",color:"#795548"}],_=[{value:1,label:"Class 1"},{value:2,label:"Class 2"},{value:3,label:"Class 3"},{value:4,label:"Class 4"},{value:5,label:"Class 5"},{value:6,label:"Class 6"},{value:7,label:"Class 7"},{value:8,label:"Class 8"},{value:9,label:"Class 9"},{value:10,label:"Class 10"},{value:11,label:"Class 11"},{value:12,label:"Class 12"}],he=["A","B","C","D","E","F"],I=[{value:"en",label:"English"},{value:"hi",label:"हिन्दी (Hindi)"},{value:"te",label:"తెలుగు (Telugu)"},{value:"ta",label:"தமிழ் (Tamil)"},{value:"kn",label:"ಕನ್ನಡ (Kannada)"},{value:"ml",label:"മലയാളം (Malayalam)"}],ye=()=>{const a=T(),r=oe(),{t:l}=de(["common","students"]),[n,j]=W.useState(0),[u,m]=W.useState(!1),[b,p]=W.useState({}),[E,z]=W.useState(null),[d,K]=W.useState({firstName:"",middleName:"",lastName:"",dateOfBirth:"",gender:"",bloodGroup:"",admissionNumber:"",grade:"",section:"",board:"",academicYear:"2024-2025",rollNumber:"",address:"",city:"",state:"",pincode:"",phone:"",email:"",fatherName:"",fatherOccupation:"",fatherPhone:"",motherName:"",motherOccupation:"",motherPhone:"",guardianName:"",guardianRelation:"",guardianPhone:"",emergencyContactName:"",emergencyContactPhone:"",emergencyContactRelation:"",preferredLanguage:"en",specialNeeds:"",medicalConditions:"",previousSchool:"",dataConsent:!1,communicationConsent:!1}),B=[{label:"Basic Information",icon:k,description:"Student personal details"},{label:"Academic Details",icon:Q,description:"Educational information"},{label:"Contact Information",icon:D,description:"Address and contacts"},{label:"Review & Submit",icon:ee,description:"Confirm details"}],A=h=>s=>{const v=s.target.type==="checkbox"?s.target.checked:s.target.value;K(g=>({...g,[h]:v})),b[h]&&p(g=>({...g,[h]:null}))},H=h=>{const s=h.target.files[0];if(s){const v=new FileReader;v.onload=g=>{z(g.target.result)},v.readAsDataURL(s)}},O=h=>{const s={};switch(h){case 0:d.firstName.trim()||(s.firstName="First name is required"),d.lastName.trim()||(s.lastName="Last name is required"),d.dateOfBirth||(s.dateOfBirth="Date of birth is required"),d.gender||(s.gender="Gender is required");break;case 1:d.admissionNumber.trim()||(s.admissionNumber="Admission number is required"),d.grade||(s.grade="Grade is required"),d.section||(s.section="Section is required"),d.board||(s.board="Educational board is required");break;case 2:d.address.trim()||(s.address="Address is required"),d.city.trim()||(s.city="City is required"),d.state.trim()||(s.state="State is required"),d.pincode.trim()||(s.pincode="Pincode is required"),d.fatherName.trim()||(s.fatherName="Father name is required"),d.motherName.trim()||(s.motherName="Mother name is required"),d.emergencyContactName.trim()||(s.emergencyContactName="Emergency contact is required"),d.emergencyContactPhone.trim()||(s.emergencyContactPhone="Emergency contact phone is required");break;case 3:d.dataConsent||(s.dataConsent="Data consent is required");break}return p(s),Object.keys(s).length===0},V=()=>{O(n)&&j(h=>h+1)},X=()=>{j(h=>h-1)},Z=async()=>{if(O(n)){m(!0);try{await new Promise(h=>setTimeout(h,2e3)),r("/dashboard/students")}catch(h){console.error("Registration failed:",h)}finally{m(!1)}}};return e.jsxs(c,{sx:{maxWidth:1200,mx:"auto",p:3},children:[e.jsx(M.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:e.jsxs(c,{sx:{mb:4},children:[e.jsx(t,{variant:"h4",sx:{fontWeight:600,mb:1,background:`linear-gradient(135deg, ${a.palette.primary.main} 0%, ${a.palette.secondary.main} 100%)`,WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"Student Registration"}),e.jsx(t,{variant:"body1",color:"text.secondary",children:"Register a new student with comprehensive information for SWOT analysis"})]})}),e.jsx(y,{sx:{mb:4,overflow:"visible"},children:e.jsx(f,{children:e.jsx(J,{activeStep:n,alternativeLabel:!0,children:B.map((h,s)=>e.jsx(ae,{children:e.jsxs(te,{StepIconComponent:({active:v,completed:g})=>e.jsx(c,{sx:{width:48,height:48,borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",background:g?`linear-gradient(135deg, ${a.palette.success.main} 0%, ${a.palette.success.dark} 100%)`:v?`linear-gradient(135deg, ${a.palette.primary.main} 0%, ${a.palette.secondary.main} 100%)`:P(a.palette.action.disabled,.12),color:g||v?"white":a.palette.action.disabled,transition:"all 0.3s ease"},children:e.jsx(h.icon,{sx:{fontSize:24}})}),children:[e.jsx(t,{variant:"subtitle2",sx:{fontWeight:500},children:h.label}),e.jsx(t,{variant:"caption",color:"text.secondary",children:h.description})]})},h.label))})})}),e.jsx(y,{children:e.jsxs(f,{sx:{p:4},children:[e.jsx(ce,{mode:"wait",children:e.jsxs(M.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:[n===0&&e.jsx(xe,{formData:d,errors:b,handleInputChange:A,profilePhoto:E,handlePhotoUpload:H}),n===1&&e.jsx(ue,{formData:d,errors:b,handleInputChange:A}),n===2&&e.jsx(me,{formData:d,errors:b,handleInputChange:A}),n===3&&e.jsx(je,{formData:d,errors:b,handleInputChange:A,profilePhoto:E})]},n)}),e.jsxs(c,{sx:{display:"flex",justifyContent:"space-between",mt:4},children:[e.jsx(F,{onClick:X,disabled:n===0,startIcon:e.jsx(ie,{}),variant:"outlined",children:"Back"}),e.jsx(F,{onClick:n===B.length-1?Z:V,endIcon:n===B.length-1?e.jsx(re,{}):e.jsx(le,{}),variant:"contained",loading:u,children:n===B.length-1?"Register Student":"Next"})]})]})})]})},xe=({formData:a,errors:r,handleInputChange:l,profilePhoto:n,handlePhotoUpload:j})=>{const u=T();return e.jsxs(c,{children:[e.jsx(t,{variant:"h6",sx:{mb:3,fontWeight:600},children:"Basic Information"}),e.jsx(c,{sx:{display:"flex",justifyContent:"center",mb:4},children:e.jsxs(c,{sx:{position:"relative"},children:[e.jsx($,{src:n,sx:{width:120,height:120,border:`4px solid ${P(u.palette.primary.main,.2)}`,background:`linear-gradient(135deg, ${u.palette.primary.main} 0%, ${u.palette.secondary.main} 100%)`},children:e.jsx(k,{sx:{fontSize:60}})}),e.jsxs(ne,{component:"label",sx:{position:"absolute",bottom:0,right:0,background:u.palette.primary.main,color:"white","&:hover":{background:u.palette.primary.dark}},children:[e.jsx(se,{}),e.jsx("input",{type:"file",hidden:!0,accept:"image/*",onChange:j})]})]})}),e.jsxs(i,{container:!0,spacing:3,children:[e.jsx(i,{item:!0,xs:12,md:4,children:e.jsx(o,{fullWidth:!0,label:"First Name *",value:a.firstName,onChange:l("firstName"),error:!!r.firstName,helperText:r.firstName,placeholder:"e.g., Sanju"})}),e.jsx(i,{item:!0,xs:12,md:4,children:e.jsx(o,{fullWidth:!0,label:"Middle Name",value:a.middleName,onChange:l("middleName"),placeholder:"e.g., Kumar"})}),e.jsx(i,{item:!0,xs:12,md:4,children:e.jsx(o,{fullWidth:!0,label:"Last Name *",value:a.lastName,onChange:l("lastName"),error:!!r.lastName,helperText:r.lastName,placeholder:"e.g., Reddy"})}),e.jsx(i,{item:!0,xs:12,md:6,children:e.jsx(o,{fullWidth:!0,label:"Date of Birth *",type:"date",value:a.dateOfBirth,onChange:l("dateOfBirth"),error:!!r.dateOfBirth,helperText:r.dateOfBirth,InputLabelProps:{shrink:!0}})}),e.jsx(i,{item:!0,xs:12,md:6,children:e.jsxs(C,{fullWidth:!0,error:!!r.gender,children:[e.jsx(N,{children:"Gender *"}),e.jsxs(S,{value:a.gender,onChange:l("gender"),label:"Gender *",children:[e.jsx(x,{value:"Male",children:"Male"}),e.jsx(x,{value:"Female",children:"Female"}),e.jsx(x,{value:"Other",children:"Other"})]})]})}),e.jsx(i,{item:!0,xs:12,md:6,children:e.jsxs(C,{fullWidth:!0,children:[e.jsx(N,{children:"Blood Group"}),e.jsxs(S,{value:a.bloodGroup,onChange:l("bloodGroup"),label:"Blood Group",children:[e.jsx(x,{value:"A+",children:"A+"}),e.jsx(x,{value:"A-",children:"A-"}),e.jsx(x,{value:"B+",children:"B+"}),e.jsx(x,{value:"B-",children:"B-"}),e.jsx(x,{value:"AB+",children:"AB+"}),e.jsx(x,{value:"AB-",children:"AB-"}),e.jsx(x,{value:"O+",children:"O+"}),e.jsx(x,{value:"O-",children:"O-"})]})]})}),e.jsx(i,{item:!0,xs:12,md:6,children:e.jsxs(C,{fullWidth:!0,children:[e.jsx(N,{children:"Preferred Language"}),e.jsx(S,{value:a.preferredLanguage,onChange:l("preferredLanguage"),label:"Preferred Language",children:I.map(m=>e.jsx(x,{value:m.value,children:m.label},m.value))})]})})]})]})},ue=({formData:a,errors:r,handleInputChange:l})=>(T(),e.jsxs(c,{children:[e.jsx(t,{variant:"h6",sx:{mb:3,fontWeight:600},children:"Academic Information"}),e.jsxs(i,{container:!0,spacing:3,children:[e.jsx(i,{item:!0,xs:12,md:6,children:e.jsx(o,{fullWidth:!0,label:"Admission Number *",value:a.admissionNumber,onChange:l("admissionNumber"),error:!!r.admissionNumber,helperText:r.admissionNumber,placeholder:"e.g., VMS2024001"})}),e.jsx(i,{item:!0,xs:12,md:6,children:e.jsx(o,{fullWidth:!0,label:"Roll Number",value:a.rollNumber,onChange:l("rollNumber"),placeholder:"e.g., 15"})}),e.jsx(i,{item:!0,xs:12,md:4,children:e.jsxs(C,{fullWidth:!0,error:!!r.grade,children:[e.jsx(N,{children:"Grade/Class *"}),e.jsx(S,{value:a.grade,onChange:l("grade"),label:"Grade/Class *",children:_.map(n=>e.jsx(x,{value:n.value,children:n.label},n.value))})]})}),e.jsx(i,{item:!0,xs:12,md:4,children:e.jsxs(C,{fullWidth:!0,error:!!r.section,children:[e.jsx(N,{children:"Section *"}),e.jsx(S,{value:a.section,onChange:l("section"),label:"Section *",children:he.map(n=>e.jsxs(x,{value:n,children:["Section ",n]},n))})]})}),e.jsx(i,{item:!0,xs:12,md:4,children:e.jsx(o,{fullWidth:!0,label:"Academic Year",value:a.academicYear,onChange:l("academicYear"),placeholder:"2024-2025"})}),e.jsx(i,{item:!0,xs:12,children:e.jsxs(C,{fullWidth:!0,error:!!r.board,children:[e.jsx(N,{children:"Educational Board *"}),e.jsx(S,{value:a.board,onChange:l("board"),label:"Educational Board *",children:Y.map(n=>e.jsx(x,{value:n.value,children:e.jsxs(c,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(U,{size:"small",sx:{backgroundColor:n.color,color:"white",minWidth:60},label:n.value}),n.label]})},n.value))})]})}),e.jsx(i,{item:!0,xs:12,children:e.jsx(o,{fullWidth:!0,label:"Previous School",value:a.previousSchool,onChange:l("previousSchool"),placeholder:"Name of previous school (if applicable)",multiline:!0,rows:2})})]})]})),me=({formData:a,errors:r,handleInputChange:l})=>e.jsxs(c,{children:[e.jsx(t,{variant:"h6",sx:{mb:3,fontWeight:600},children:"Contact Information"}),e.jsx(t,{variant:"subtitle1",sx:{mb:2,fontWeight:500,color:"primary.main"},children:"Address Details"}),e.jsxs(i,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(i,{item:!0,xs:12,children:e.jsx(o,{fullWidth:!0,label:"Address *",value:a.address,onChange:l("address"),error:!!r.address,helperText:r.address,multiline:!0,rows:3,placeholder:"Complete address with house number, street, area"})}),e.jsx(i,{item:!0,xs:12,md:4,children:e.jsx(o,{fullWidth:!0,label:"City *",value:a.city,onChange:l("city"),error:!!r.city,helperText:r.city,placeholder:"e.g., Hyderabad"})}),e.jsx(i,{item:!0,xs:12,md:4,children:e.jsx(o,{fullWidth:!0,label:"State *",value:a.state,onChange:l("state"),error:!!r.state,helperText:r.state,placeholder:"e.g., Telangana"})}),e.jsx(i,{item:!0,xs:12,md:4,children:e.jsx(o,{fullWidth:!0,label:"Pincode *",value:a.pincode,onChange:l("pincode"),error:!!r.pincode,helperText:r.pincode,placeholder:"e.g., 500001"})}),e.jsx(i,{item:!0,xs:12,md:6,children:e.jsx(o,{fullWidth:!0,label:"Phone Number",value:a.phone,onChange:l("phone"),placeholder:"+91 9876543210"})}),e.jsx(i,{item:!0,xs:12,md:6,children:e.jsx(o,{fullWidth:!0,label:"Email Address",type:"email",value:a.email,onChange:l("email"),placeholder:"<EMAIL>"})})]}),e.jsx(R,{sx:{my:3}}),e.jsx(t,{variant:"subtitle1",sx:{mb:2,fontWeight:500,color:"primary.main"},children:"Parent/Guardian Information"}),e.jsxs(i,{container:!0,spacing:3,children:[e.jsx(i,{item:!0,xs:12,md:6,children:e.jsx(o,{fullWidth:!0,label:"Father's Name *",value:a.fatherName,onChange:l("fatherName"),error:!!r.fatherName,helperText:r.fatherName,placeholder:"Father's full name"})}),e.jsx(i,{item:!0,xs:12,md:6,children:e.jsx(o,{fullWidth:!0,label:"Father's Occupation",value:a.fatherOccupation,onChange:l("fatherOccupation"),placeholder:"e.g., Software Engineer"})}),e.jsx(i,{item:!0,xs:12,md:6,children:e.jsx(o,{fullWidth:!0,label:"Father's Phone",value:a.fatherPhone,onChange:l("fatherPhone"),placeholder:"+91 9876543210"})}),e.jsx(i,{item:!0,xs:12,md:6,children:e.jsx(o,{fullWidth:!0,label:"Mother's Name *",value:a.motherName,onChange:l("motherName"),error:!!r.motherName,helperText:r.motherName,placeholder:"Mother's full name"})}),e.jsx(i,{item:!0,xs:12,md:6,children:e.jsx(o,{fullWidth:!0,label:"Mother's Occupation",value:a.motherOccupation,onChange:l("motherOccupation"),placeholder:"e.g., Teacher"})}),e.jsx(i,{item:!0,xs:12,md:6,children:e.jsx(o,{fullWidth:!0,label:"Mother's Phone",value:a.motherPhone,onChange:l("motherPhone"),placeholder:"+91 9876543210"})})]}),e.jsx(R,{sx:{my:3}}),e.jsx(t,{variant:"subtitle1",sx:{mb:2,fontWeight:500,color:"primary.main"},children:"Emergency Contact"}),e.jsxs(i,{container:!0,spacing:3,children:[e.jsx(i,{item:!0,xs:12,md:4,children:e.jsx(o,{fullWidth:!0,label:"Emergency Contact Name *",value:a.emergencyContactName,onChange:l("emergencyContactName"),error:!!r.emergencyContactName,helperText:r.emergencyContactName,placeholder:"Contact person name"})}),e.jsx(i,{item:!0,xs:12,md:4,children:e.jsx(o,{fullWidth:!0,label:"Emergency Contact Phone *",value:a.emergencyContactPhone,onChange:l("emergencyContactPhone"),error:!!r.emergencyContactPhone,helperText:r.emergencyContactPhone,placeholder:"+91 9876543210"})}),e.jsx(i,{item:!0,xs:12,md:4,children:e.jsx(o,{fullWidth:!0,label:"Relation",value:a.emergencyContactRelation,onChange:l("emergencyContactRelation"),placeholder:"e.g., Uncle, Aunt"})})]})]}),je=({formData:a,errors:r,handleInputChange:l,profilePhoto:n})=>{var b;const j=T(),u=Y.find(p=>p.value===a.board),m=_.find(p=>p.value===a.grade);return e.jsxs(c,{children:[e.jsx(t,{variant:"h6",sx:{mb:3,fontWeight:600},children:"Review & Submit"}),e.jsx(G,{severity:"info",sx:{mb:3},children:"Please review all information carefully before submitting. You can go back to make changes if needed."}),e.jsx(y,{sx:{mb:3,background:`linear-gradient(135deg, ${P(j.palette.primary.main,.05)} 0%, ${P(j.palette.secondary.main,.05)} 100%)`},children:e.jsxs(f,{children:[e.jsxs(c,{sx:{display:"flex",alignItems:"center",gap:3,mb:3},children:[e.jsx($,{src:n,sx:{width:80,height:80,border:`3px solid ${j.palette.primary.main}`},children:e.jsx(k,{sx:{fontSize:40}})}),e.jsxs(c,{children:[e.jsxs(t,{variant:"h5",sx:{fontWeight:600},children:[a.firstName," ",a.middleName," ",a.lastName]}),e.jsxs(t,{variant:"body1",color:"text.secondary",children:[m==null?void 0:m.label," - Section ",a.section]}),e.jsxs(t,{variant:"body2",color:"text.secondary",children:["Admission No: ",a.admissionNumber]})]})]}),u&&e.jsx(U,{label:u.label,sx:{backgroundColor:u.color,color:"white",fontWeight:500}})]})}),e.jsxs(i,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(i,{item:!0,xs:12,md:6,children:e.jsx(y,{children:e.jsxs(f,{children:[e.jsx(t,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Personal Information"}),e.jsxs(w,{spacing:1,children:[e.jsxs(c,{sx:{display:"flex",justifyContent:"space-between"},children:[e.jsx(t,{variant:"body2",color:"text.secondary",children:"Date of Birth:"}),e.jsx(t,{variant:"body2",children:a.dateOfBirth})]}),e.jsxs(c,{sx:{display:"flex",justifyContent:"space-between"},children:[e.jsx(t,{variant:"body2",color:"text.secondary",children:"Gender:"}),e.jsx(t,{variant:"body2",children:a.gender})]}),e.jsxs(c,{sx:{display:"flex",justifyContent:"space-between"},children:[e.jsx(t,{variant:"body2",color:"text.secondary",children:"Blood Group:"}),e.jsx(t,{variant:"body2",children:a.bloodGroup||"Not specified"})]}),e.jsxs(c,{sx:{display:"flex",justifyContent:"space-between"},children:[e.jsx(t,{variant:"body2",color:"text.secondary",children:"Preferred Language:"}),e.jsx(t,{variant:"body2",children:(b=I.find(p=>p.value===a.preferredLanguage))==null?void 0:b.label})]})]})]})})}),e.jsx(i,{item:!0,xs:12,md:6,children:e.jsx(y,{children:e.jsxs(f,{children:[e.jsx(t,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Contact Information"}),e.jsxs(w,{spacing:1,children:[e.jsxs(c,{sx:{display:"flex",justifyContent:"space-between"},children:[e.jsx(t,{variant:"body2",color:"text.secondary",children:"City:"}),e.jsx(t,{variant:"body2",children:a.city})]}),e.jsxs(c,{sx:{display:"flex",justifyContent:"space-between"},children:[e.jsx(t,{variant:"body2",color:"text.secondary",children:"State:"}),e.jsx(t,{variant:"body2",children:a.state})]}),e.jsxs(c,{sx:{display:"flex",justifyContent:"space-between"},children:[e.jsx(t,{variant:"body2",color:"text.secondary",children:"Father's Name:"}),e.jsx(t,{variant:"body2",children:a.fatherName})]}),e.jsxs(c,{sx:{display:"flex",justifyContent:"space-between"},children:[e.jsx(t,{variant:"body2",color:"text.secondary",children:"Mother's Name:"}),e.jsx(t,{variant:"body2",children:a.motherName})]})]})]})})})]}),e.jsx(y,{children:e.jsxs(f,{children:[e.jsx(t,{variant:"h6",sx:{mb:2,fontWeight:600,color:"primary.main"},children:"Consent & Permissions"}),e.jsxs(w,{spacing:2,children:[e.jsx(L,{control:e.jsx(q,{checked:a.dataConsent,onChange:l("dataConsent"),color:"primary"}),label:e.jsx(t,{variant:"body2",children:"I consent to the collection and processing of student data for educational purposes and SWOT analysis. *"})}),r.dataConsent&&e.jsx(t,{variant:"caption",color:"error",children:r.dataConsent}),e.jsx(L,{control:e.jsx(q,{checked:a.communicationConsent,onChange:l("communicationConsent"),color:"primary"}),label:e.jsx(t,{variant:"body2",children:"I consent to receive communications about student progress, events, and important updates."})}),e.jsx(G,{severity:"warning",sx:{mt:2},children:e.jsx(t,{variant:"body2",children:"By submitting this form, you confirm that all information provided is accurate and complete. The student data will be used for educational assessment, SWOT analysis, and academic progress tracking."})})]})]})})]})};export{ye as default};
//# sourceMappingURL=StudentRegistration-B1REbrCi.js.map
