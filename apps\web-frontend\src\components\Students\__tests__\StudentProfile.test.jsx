/**
 * VidyaMitra Platform - StudentProfile Component Tests
 * 
 * Comprehensive unit tests for student profile with SWOT analysis and Indian context
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import StudentProfile from '../StudentProfile';

// Mock Chart.js
jest.mock('react-chartjs-2', () => ({
  Line: () => <div data-testid="line-chart">Line Chart</div>,
  Radar: () => <div data-testid="radar-chart">Radar Chart</div>,
  Bar: () => <div data-testid="bar-chart">Bar Chart</div>,
}));

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }) => children,
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useParams: () => ({ studentId: '1' }),
}));

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

describe('StudentProfile Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders student profile with Indian student data', () => {
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    expect(screen.getByText('Student Profile')).toBeInTheDocument();
    expect(screen.getByText('Sanju Kumar Reddy')).toBeInTheDocument();
    expect(screen.getByText('Class 10-A')).toBeInTheDocument();
    expect(screen.getByText('CBSE Board')).toBeInTheDocument();
  });

  test('displays all profile tabs', () => {
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    expect(screen.getByText('Personal Info')).toBeInTheDocument();
    expect(screen.getByText('Academic Performance')).toBeInTheDocument();
    expect(screen.getByText('SWOT Analysis')).toBeInTheDocument();
    expect(screen.getByText('Achievements')).toBeInTheDocument();
  });

  test('switches between tabs correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    // Click on Academic Performance tab
    await user.click(screen.getByText('Academic Performance'));
    
    await waitFor(() => {
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
      expect(screen.getByText('Academic Progress')).toBeInTheDocument();
    });

    // Click on SWOT Analysis tab
    await user.click(screen.getByText('SWOT Analysis'));
    
    await waitFor(() => {
      expect(screen.getByText('SWOT Matrix')).toBeInTheDocument();
      expect(screen.getByText('Strengths')).toBeInTheDocument();
      expect(screen.getByText('Weaknesses')).toBeInTheDocument();
      expect(screen.getByText('Opportunities')).toBeInTheDocument();
      expect(screen.getByText('Threats')).toBeInTheDocument();
    });
  });

  test('displays Indian educational context in personal info', () => {
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    // Should show Indian-specific information
    expect(screen.getByText('Mother Tongue')).toBeInTheDocument();
    expect(screen.getByText('Telugu')).toBeInTheDocument();
    expect(screen.getByText('Father\'s Name')).toBeInTheDocument();
    expect(screen.getByText('Mother\'s Name')).toBeInTheDocument();
  });

  test('shows academic performance with Indian grading system', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    await user.click(screen.getByText('Academic Performance'));
    
    await waitFor(() => {
      // Should show Indian grades
      expect(screen.getByText('A1')).toBeInTheDocument();
      expect(screen.getByText('A2')).toBeInTheDocument();
      expect(screen.getByText('Mathematics')).toBeInTheDocument();
      expect(screen.getByText('Science')).toBeInTheDocument();
      expect(screen.getByText('English')).toBeInTheDocument();
      expect(screen.getByText('Hindi')).toBeInTheDocument();
      expect(screen.getByText('Telugu')).toBeInTheDocument();
    });
  });

  test('displays SWOT analysis with cultural context', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    await user.click(screen.getByText('SWOT Analysis'));
    
    await waitFor(() => {
      // Should show culturally relevant SWOT items
      expect(screen.getByText('Strong mathematical reasoning')).toBeInTheDocument();
      expect(screen.getByText('Excellent memory retention')).toBeInTheDocument();
      expect(screen.getByText('Active in cultural events')).toBeInTheDocument();
      expect(screen.getByText('Science Olympiad participation')).toBeInTheDocument();
    });
  });

  test('handles SWOT analysis navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    await user.click(screen.getByText('SWOT Analysis'));
    
    await waitFor(() => {
      const updateButton = screen.getByText('Update SWOT');
      expect(updateButton).toBeInTheDocument();
    });

    await user.click(screen.getByText('Update SWOT'));
    
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard/swot/wizard');
  });

  test('displays achievements with Indian context', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    await user.click(screen.getByText('Achievements'));
    
    await waitFor(() => {
      expect(screen.getByText('Academic Excellence')).toBeInTheDocument();
      expect(screen.getByText('Cultural Participation')).toBeInTheDocument();
      expect(screen.getByText('Mathematics Olympiad - District Level')).toBeInTheDocument();
      expect(screen.getByText('Classical Dance Competition - 1st Prize')).toBeInTheDocument();
    });
  });

  test('shows attendance statistics', () => {
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    expect(screen.getByText('95%')).toBeInTheDocument(); // Attendance percentage
    expect(screen.getByText('Attendance')).toBeInTheDocument();
  });

  test('displays contact information with Indian format', () => {
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    // Should show Indian phone number format
    expect(screen.getByText('+91 9876543210')).toBeInTheDocument();
    expect(screen.getByText('Hyderabad, Telangana')).toBeInTheDocument();
  });

  test('handles profile editing navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    const editButton = screen.getByLabelText('Edit Profile');
    await user.click(editButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard/students/1/edit');
  });

  test('supports accessibility features', () => {
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    // Check for proper ARIA labels
    expect(screen.getByRole('tablist')).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: /personal info/i })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: /academic performance/i })).toBeInTheDocument();

    // Check for proper heading structure
    expect(screen.getByRole('heading', { name: /student profile/i })).toBeInTheDocument();
  });

  test('displays performance charts', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    await user.click(screen.getByText('Academic Performance'));
    
    await waitFor(() => {
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
      expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
    });
  });

  test('shows board-specific information', () => {
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    expect(screen.getByText('CBSE Board')).toBeInTheDocument();
    expect(screen.getByText('Roll Number: 2024001')).toBeInTheDocument();
  });

  test('displays parent information', () => {
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    expect(screen.getByText('Mr. Rajesh Kumar Reddy')).toBeInTheDocument();
    expect(screen.getByText('Mrs. Lakshmi Reddy')).toBeInTheDocument();
  });

  test('handles responsive design elements', () => {
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    // Check for responsive grid containers
    const profileContainer = screen.getByTestId('student-profile-container');
    expect(profileContainer).toBeInTheDocument();
  });
});

// Integration tests
describe('StudentProfile Integration', () => {
  test('complete profile interaction workflow', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    // Navigate through all tabs
    const tabs = ['Academic Performance', 'SWOT Analysis', 'Achievements'];
    
    for (const tab of tabs) {
      await user.click(screen.getByText(tab));
      await waitFor(() => {
        expect(screen.getByRole('tabpanel')).toBeInTheDocument();
      });
    }

    // Test SWOT update workflow
    await user.click(screen.getByText('SWOT Analysis'));
    await user.click(screen.getByText('Update SWOT'));
    
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard/swot/wizard');
  });

  test('profile data consistency across tabs', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <StudentProfile />
      </TestWrapper>
    );

    // Student name should be consistent across all views
    expect(screen.getByText('Sanju Kumar Reddy')).toBeInTheDocument();

    await user.click(screen.getByText('Academic Performance'));
    await waitFor(() => {
      expect(screen.getByText('Sanju Kumar Reddy')).toBeInTheDocument();
    });

    await user.click(screen.getByText('SWOT Analysis'));
    await waitFor(() => {
      expect(screen.getByText('Sanju Kumar Reddy')).toBeInTheDocument();
    });
  });
});
