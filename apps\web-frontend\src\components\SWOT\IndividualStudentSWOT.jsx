/**
 * VidyaMitra Platform - Individual Student SWOT Analysis View
 * 
 * This component implements the individual student SWOT analysis view as specified 
 * in the visualization mockups with comprehensive student profile and analysis.
 * 
 * Based on: docs/visualization_mockups.md - Individual Student SWOT Analysis View
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Avatar,
  LinearProgress,
  IconButton,
  Tooltip,
  Paper,
  List,
  ListItem,
  ListItemText,
  Divider,
  useTheme,
  alpha,
  Stack,
  Badge,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  ArrowBack,
  Search,
  Download,
  Share,
  Print,
  Psychology,
  School,
  CalendarToday,
  TrendingUp,
  TrendingDown,
  Star,
  Warning,
  CheckCircle,
  Timeline
} from '@mui/icons-material';
import { Radar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip as ChartTooltip,
  Legend,
} from 'chart.js';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';

// Register Chart.js components
ChartJS.register(RadialLinearScale, PointElement, LineElement, Filler, ChartTooltip, Legend);

// Academic Performance Radar Chart Component
const AcademicRadarChart = ({ performanceData, loading }) => {
  if (loading) {
    return (
      <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <LinearProgress sx={{ width: '80%' }} />
      </Box>
    );
  }

  const chartData = {
    labels: ['Math', 'English', 'Science', 'Geography', 'History', 'Art', 'PE'],
    datasets: [
      {
        label: 'Student Performance',
        data: [87, 92, 95, 75, 78, 96, 72],
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 2,
        pointBackgroundColor: 'rgba(54, 162, 235, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(54, 162, 235, 1)',
      },
      {
        label: 'Class Average',
        data: [75, 78, 80, 72, 74, 82, 76],
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 2,
        pointBackgroundColor: 'rgba(255, 99, 132, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(255, 99, 132, 1)',
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      r: {
        angleLines: {
          display: true,
        },
        suggestedMin: 0,
        suggestedMax: 100,
        ticks: {
          stepSize: 20,
        },
      },
    },
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
  };

  return (
    <Box sx={{ height: 300 }}>
      <Radar data={chartData} options={chartOptions} />
    </Box>
  );
};

// SWOT Analysis Quadrant Component
const SWOTQuadrant = ({ title, items, color, icon: Icon }) => {
  const theme = useTheme();

  return (
    <Card sx={{ height: '100%', border: `2px solid ${color}` }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
          <Icon sx={{ color }} />
          <Typography variant="h6" sx={{ fontWeight: 600, color }}>
            {title}
          </Typography>
        </Box>
        <List dense>
          {items.map((item, index) => (
            <ListItem key={index} sx={{ px: 0 }}>
              <ListItemText
                primary={
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    • {item}
                  </Typography>
                }
              />
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

// Attendance Calendar Component
const AttendanceCalendar = ({ attendanceData, loading }) => {
  if (loading) {
    return (
      <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <LinearProgress sx={{ width: '80%' }} />
      </Box>
    );
  }

  // Mock calendar data
  const calendarDays = Array.from({ length: 30 }, (_, i) => ({
    day: i + 1,
    status: Math.random() > 0.05 ? 'present' : Math.random() > 0.5 ? 'absent' : 'late'
  }));

  const getStatusColor = (status) => {
    switch (status) {
      case 'present': return '#4CAF50';
      case 'absent': return '#F44336';
      case 'late': return '#FF9800';
      default: return '#E0E0E0';
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
        Attendance
      </Typography>
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: 1, mb: 2 }}>
        {calendarDays.map((day) => (
          <Tooltip key={day.day} title={`Day ${day.day}: ${day.status}`}>
            <Box
              sx={{
                width: 32,
                height: 32,
                borderRadius: 1,
                backgroundColor: getStatusColor(day.status),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '0.75rem',
                color: 'white',
                fontWeight: 500,
                cursor: 'pointer'
              }}
            >
              {day.day}
            </Box>
          </Tooltip>
        ))}
      </Box>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Typography variant="body2">
          <strong>Present:</strong> 43 days (95.6%)
        </Typography>
        <Typography variant="body2">
          <strong>Absent:</strong> 2 days
        </Typography>
        <Typography variant="body2">
          <strong>Tardy:</strong> 1 day
        </Typography>
      </Box>
    </Box>
  );
};

// Behavior Timeline Component
const BehaviorTimeline = ({ behaviorData, loading }) => {
  if (loading) {
    return (
      <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <LinearProgress sx={{ width: '80%' }} />
      </Box>
    );
  }

  const incidents = [
    { date: 5, type: 'positive', description: 'Helped new student' },
    { date: 10, type: 'positive', description: 'Excellent project presentation' },
    { date: 15, type: 'negative', description: 'Late to class' },
    { date: 20, type: 'positive', description: 'Volunteered for cleanup' },
  ];

  return (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
        Behavior
      </Typography>
      <Box sx={{ position: 'relative', height: 60, mb: 2 }}>
        {/* Timeline line */}
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: 0,
            right: 0,
            height: 2,
            backgroundColor: '#E0E0E0',
            transform: 'translateY(-50%)',
          }}
        />
        {/* Timeline markers */}
        {incidents.map((incident, index) => (
          <Tooltip key={index} title={incident.description}>
            <Box
              sx={{
                position: 'absolute',
                left: `${(incident.date / 30) * 100}%`,
                top: '50%',
                transform: 'translate(-50%, -50%)',
                width: 16,
                height: 16,
                borderRadius: '50%',
                backgroundColor: incident.type === 'positive' ? '#4CAF50' : '#F44336',
                cursor: 'pointer',
                '&:hover': {
                  transform: 'translate(-50%, -50%) scale(1.2)',
                },
                transition: 'transform 0.2s ease-in-out',
              }}
            />
          </Tooltip>
        ))}
        {/* Date labels */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Typography variant="caption">1</Typography>
          <Typography variant="caption">10</Typography>
          <Typography variant="caption">20</Typography>
          <Typography variant="caption">30</Typography>
        </Box>
      </Box>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Typography variant="body2">
          <strong>Positive Incidents:</strong> 3
        </Typography>
        <Typography variant="body2">
          <strong>Negative Incidents:</strong> 1
        </Typography>
        <Typography variant="body2">
          <strong>Trend:</strong> <span style={{ color: '#4CAF50' }}>Improving</span>
        </Typography>
      </Box>
    </Box>
  );
};

// Extracurricular Activities Component
const ExtracurricularActivities = ({ activitiesData, loading }) => {
  if (loading) {
    return (
      <Box sx={{ height: 150, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <LinearProgress sx={{ width: '80%' }} />
      </Box>
    );
  }

  const activities = [
    { name: 'Chess Club', hours: 2, attendance: 100 },
    { name: 'School Newspaper', hours: 3, attendance: 92 },
  ];

  return (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
        Extracurricular Activities
      </Typography>
      <Stack spacing={2}>
        {activities.map((activity, index) => (
          <Card key={index} variant="outlined">
            <CardContent sx={{ py: 2 }}>
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {activity.name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {activity.hours} hrs/week, {activity.attendance}% attendance
              </Typography>
            </CardContent>
          </Card>
        ))}
      </Stack>
    </Box>
  );
};

// Recommendations Component
const Recommendations = ({ recommendations, loading }) => {
  if (loading) {
    return (
      <Box sx={{ height: 150, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <LinearProgress sx={{ width: '80%' }} />
      </Box>
    );
  }

  const recommendationsList = [
    'Consider Math Olympiad to strengthen skills',
    'Monitor History performance - offer additional resources',
    'Encourage consistent PE participation',
  ];

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
          Recommendations
        </Typography>
        <List>
          {recommendationsList.map((recommendation, index) => (
            <ListItem key={index} sx={{ px: 0 }}>
              <ListItemText
                primary={
                  <Typography variant="body2">
                    • {recommendation}
                  </Typography>
                }
              />
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

// Main Individual Student SWOT Component
const IndividualStudentSWOT = () => {
  const { t } = useTranslation(['swot', 'common']);
  const theme = useTheme();
  const navigate = useNavigate();
  const { studentId } = useParams();
  const [loading, setLoading] = useState(true);

  // Mock student data
  const [studentData, setStudentData] = useState({
    name: 'Jane Doe',
    grade: 9,
    id: 'STU12345',
    quarter: 'Q1 2024-2025',
    gpa: 3.7,
    classRank: 8,
    totalStudents: 28,
    subjects: [
      { name: 'Math', grade: 'B+', score: 87 },
      { name: 'English', grade: 'A-', score: 92 },
      { name: 'Science', grade: 'A', score: 95 },
      { name: 'Geography', grade: 'C', score: 75 },
      { name: 'History', grade: 'C+', score: 78 },
      { name: 'Art', grade: 'A', score: 96 },
      { name: 'PE', grade: 'C-', score: 72 },
    ],
    swotData: {
      strengths: ['Math', 'Science', 'Art'],
      weaknesses: ['Geography', 'History', 'PE'],
      opportunities: ['Join Science Club', 'Math tutoring'],
      threats: ['Attendance pattern in History class', 'Declining PE scores'],
    },
  });

  useEffect(() => {
    // Simulate data loading
    const loadStudentData = async () => {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1500));
      setLoading(false);
    };

    loadStudentData();
  }, [studentId]);

  const handleBackToClass = () => {
    navigate('/dashboard/students');
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <IconButton onClick={handleBackToClass}>
              <ArrowBack />
            </IconButton>
            <Typography variant="h4" sx={{ fontWeight: 700 }}>
              Student: {studentData.name}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <IconButton>
              <Search />
            </IconButton>
          </Box>
        </Box>

        {/* Student Info Bar */}
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', mb: 2 }}>
          <Chip label={`Grade: ${studentData.grade}`} color="primary" />
          <Chip label={`ID: ${studentData.id}`} variant="outlined" />
          <Chip label={studentData.quarter} variant="outlined" />
        </Box>
      </Box>

      {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Left Column - Academic Performance */}
        <Grid item xs={12} md={6}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Academic Performance
              </Typography>
              <AcademicRadarChart loading={loading} />

              {/* Subject Scores */}
              <Box sx={{ mt: 3 }}>
                {studentData.subjects.map((subject, index) => (
                  <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">{subject.name}:</Typography>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {subject.grade} ({subject.score}%)
                    </Typography>
                  </Box>
                ))}
                <Divider sx={{ my: 2 }} />
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>GPA:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{studentData.gpa}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>Class Rank:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>
                    {studentData.classRank}/{studentData.totalStudents}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Right Column - SWOT Analysis */}
        <Grid item xs={12} md={6}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                SWOT Analysis
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <SWOTQuadrant
                    title="Strengths"
                    items={studentData.swotData.strengths}
                    color="#4CAF50"
                    icon={CheckCircle}
                  />
                </Grid>
                <Grid item xs={6}>
                  <SWOTQuadrant
                    title="Weaknesses"
                    items={studentData.swotData.weaknesses}
                    color="#FF5722"
                    icon={Warning}
                  />
                </Grid>
                <Grid item xs={6}>
                  <SWOTQuadrant
                    title="Opportunities"
                    items={studentData.swotData.opportunities}
                    color="#2196F3"
                    icon={TrendingUp}
                  />
                </Grid>
                <Grid item xs={6}>
                  <SWOTQuadrant
                    title="Threats"
                    items={studentData.swotData.threats}
                    color="#FF9800"
                    icon={TrendingDown}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Bottom Row - Attendance and Behavior */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <AttendanceCalendar loading={loading} />
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <BehaviorTimeline loading={loading} />
            </CardContent>
          </Card>
        </Grid>

        {/* Extracurricular Activities */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <ExtracurricularActivities loading={loading} />
            </CardContent>
          </Card>
        </Grid>

        {/* Recommendations */}
        <Grid item xs={12} md={6}>
          <Recommendations loading={loading} />
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
        <Button
          variant="outlined"
          startIcon={<Download />}
          sx={{ textTransform: 'none' }}
        >
          Download Report
        </Button>
        <Button
          variant="outlined"
          startIcon={<Share />}
          sx={{ textTransform: 'none' }}
        >
          Share
        </Button>
        <Button
          variant="outlined"
          startIcon={<Print />}
          sx={{ textTransform: 'none' }}
        >
          Print
        </Button>
        <Button
          variant="contained"
          startIcon={<Psychology />}
          sx={{ textTransform: 'none' }}
        >
          Generate New Analysis
        </Button>
      </Box>
    </Box>
  );
};

export default IndividualStudentSWOT;
