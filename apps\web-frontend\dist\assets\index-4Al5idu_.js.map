{"version": 3, "mappings": ";ijEAEIA,GAAIC,gBAEeD,GAAEE,0BACDF,GAAEG,YCA1B,MCQMC,GAAeC,mBAYRC,GAAuB,EAAGC,eAErC,MAAOC,EAAMC,GAAWC,aAAS,IACbC,aAAaC,QAAQ,0BACnB,UAIhBC,ED5B6B,EAACL,EAAO,WAC3C,MAAMM,EAAmB,UAATN,EAEhB,OAAOO,EAAY,CACjBC,QAAS,CACPR,OACAS,QAAS,CACP,GAAIH,EAAU,UAAY,UAC1B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3BI,KAAMJ,EAAU,UAAY,UAC5BK,MAAOL,EAAU,UAAY,UAC7BM,KAAMN,EAAU,UAAY,UAC5BO,aAAc,WAEhBC,UAAW,CACT,GAAIR,EAAU,UAAY,UAC1B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3B,IAAKA,EAAU,UAAY,UAC3BI,KAAMJ,EAAU,UAAY,UAC5BK,MAAOL,EAAU,UAAY,UAC7BM,KAAMN,EAAU,UAAY,UAC5BO,aAAcP,EAAU,UAAY,WAEtCS,QAAS,CACPL,KAAMJ,EAAU,UAAY,UAC5BK,MAAOL,EAAU,UAAY,UAC7BM,KAAMN,EAAU,UAAY,UAC5BO,aAAc,WAEhBG,QAAS,CACPN,KAAMJ,EAAU,UAAY,UAC5BK,MAAOL,EAAU,UAAY,UAC7BM,KAAMN,EAAU,UAAY,UAC5BO,aAAcP,EAAU,UAAY,WAEtCW,MAAO,CACLP,KAAMJ,EAAU,UAAY,UAC5BK,MAAOL,EAAU,UAAY,UAC7BM,KAAMN,EAAU,UAAY,UAC5BO,aAAc,WAEhBK,KAAM,CACJR,KAAMJ,EAAU,UAAY,UAC5BK,MAAOL,EAAU,UAAY,UAC7BM,KAAMN,EAAU,UAAY,UAC5BO,aAAc,WAEhBM,WAAY,CACVC,QAASd,EAAU,UAAY,UAC/Be,MAAOf,EAAU,UAAY,UAC7BgB,QAAShB,EAAU,UAAY,UAC/BiB,SAAUjB,EAAU,UAAY,WAElCkB,KAAM,CACJf,QAASH,EAAU,UAAY,UAC/BQ,UAAWR,EAAU,UAAY,UACjCmB,SAAUnB,EAAU,UAAY,WAElCoB,QAASpB,EAAU,UAAY,UAG/BqB,MAAO,CACLlB,QAASH,EACL,4BACA,4BACJQ,UAAWR,EACP,yBACA,0BACJsB,SAAUtB,EACN,2BACA,wBACJgB,QAAShB,EACL,2BACA,yBAINuB,UAAW,CACTpB,QAASH,EACL,oDACA,oDACJQ,UAAWR,EACP,oDACA,oDACJS,QAAST,EACL,oDACA,oDACJgB,QAAShB,EACL,oDACA,oDACJqB,MAAOrB,EACH,iFACA,mFAINwB,MAAO,CACLC,KAAMzB,EAAU,UAAY,UAC5B0B,KAAM1B,EAAU,UAAY,UAC5B2B,MAAO3B,EAAU,UAAY,UAC7B4B,GAAI5B,EAAU,UAAY,YAI9B6B,WAAY,CACVC,WAAY,CACV,QACA,SACA,YACA,gBACA,qBACA,aACA,QACA,cACAC,KAAK,KAGPC,GAAI,CACFC,SAAU,kCACVC,WAAY,IACZC,WAAY,IACZC,cAAe,WACfC,MAAOrC,EAAU,UAAY,WAE/BsC,GAAI,CACFL,SAAU,oCACVC,WAAY,IACZC,WAAY,IACZC,cAAe,WACfC,MAAOrC,EAAU,UAAY,WAE/BuC,GAAI,CACFN,SAAU,0CACVC,WAAY,IACZC,WAAY,KACZE,MAAOrC,EAAU,UAAY,WAE/BwC,GAAI,CACFP,SAAU,oCACVC,WAAY,IACZC,WAAY,IACZE,MAAOrC,EAAU,UAAY,WAE/ByC,GAAI,CACFR,SAAU,2CACVC,WAAY,IACZC,WAAY,KACZE,MAAOrC,EAAU,UAAY,WAE/B0C,GAAI,CACFT,SAAU,0CACVC,WAAY,IACZC,WAAY,IACZE,MAAOrC,EAAU,UAAY,WAG/B2C,MAAO,CACLV,SAAU,OACVE,WAAY,IACZD,WAAY,IACZG,MAAOrC,EAAU,UAAY,WAE/B4C,MAAO,CACLX,SAAU,WACVE,WAAY,IACZD,WAAY,IACZG,MAAOrC,EAAU,UAAY,WAI/B6C,OAAQ,CACNZ,SAAU,WACVC,WAAY,IACZY,cAAe,OACfV,cAAe,YAInBW,QAAS,EAETC,MAAO,CACLC,aAAc,GACdC,aAAc,GACdC,YAAa,IAIfC,QAAS,CACP,OACApD,EACI,mEACA,mEACJA,EACI,mEACA,mEACJA,EACI,oEACA,oEACJA,EACI,qEACA,qEACJA,EACI,sEACA,sEAEJA,EACI,oFACA,+EAGNqD,YAAa,CACXC,OAAQ,CACNC,UAAW,+BACXC,QAAS,+BACTC,OAAQ,6BACRC,MAAO,+BACPC,OAAQ,uCACRC,OAAQ,0CAEVC,SAAU,CACRC,SAAU,IACVC,QAAS,IACTC,MAAO,IACPC,SAAU,IACVC,QAAS,IACTP,OAAQ,IACRQ,eAAgB,IAChBC,cAAe,MAKnBC,WAAY,CACVC,QAAS,CACPC,eAAgB,CACdC,KAAM,CACJvB,aAAc,GACdwB,OAAQzE,EAAU,qCAAuC,kCACzD0E,eAAgB,aAChB7D,WAAYb,EACR,2BACA,wBACJ2E,WAAY,gDACZ,UAAW,CACTC,UAAW,mBACXC,UAAW7E,EACP,wCACA,uCAKZ8E,UAAW,CACTP,eAAgB,CACdC,KAAM,CACJvB,aAAc,GACdH,cAAe,OACfZ,WAAY,IACZ6C,QAAS,YACTJ,WAAY,gDACZ,UAAW,CACTC,UAAW,qBAGfI,UAAW,CACTH,UAAW,sCACX,UAAW,CACTA,UAAW,0CAKnBI,QAAS,CACPV,eAAgB,CACdC,KAAM,CACJvB,aAAc,GACdf,WAAY,IACZwC,eAAgB,kBAKzB,EC7QaQ,CAA6BxF,GAUrCyF,EAAgBC,IACJ,UAAZA,GAAmC,SAAZA,IACzBzF,EAAQyF,GACKvF,aAAAwF,QAAQ,wBAAyBD,GAAO,EAYzDE,cAAU,KACF,MAAAC,EAAaC,OAAOC,WAAW,gCAE/BC,EAAgBC,IAEF9F,aAAaC,QAAQ,0BAE7BH,EAAAgG,EAAEC,QAAU,OAAS,QAAO,EAMxC,OAFWL,EAAAM,iBAAiB,SAAUH,GAE/B,KACMH,EAAAO,oBAAoB,SAAUJ,EAAY,CACvD,GACC,IAGH,MAAMK,EAAe,CACnBrG,OACAK,QACAiG,WA5CiB,KACX,MAAAZ,EAAmB,UAAT1F,EAAmB,OAAS,QAC5CC,EAAQyF,GACKvF,aAAAwF,QAAQ,wBAAyBD,EAAO,EA0CrDD,eACAc,cA/BoB,KACpB,MAAMC,EAAoBV,OAAOC,WAAW,gCAAgCG,QAE5ET,EADmBe,EAAoB,OAAS,QACzB,EA6BvBC,OAAiB,SAATzG,EACRM,QAAkB,UAATN,GAIT,aAACJ,GAAa8G,SAAb,CAAsBC,MAAON,EAC5BtG,gBAAC6G,GAAcvG,QACbN,SAAA,CAAA8G,EAAAC,IAACC,EAAY,IACZhH,MAEL,EC5DEiH,GAAkB,EAAGL,QAAOxC,WAAW,IAAM8C,SAAS,GAAIC,SAAS,OACvE,MAAOC,EAAcC,GAAmBlH,YAAS,GA8BjD,OA5BA0F,cAAU,KACJ,IAAAyB,EACAC,EAEE,MAAAC,EAAWC,IACVH,IAAuBA,EAAAG,GAC5B,MAAMC,EAAWC,KAAKC,KAAKH,EAAYH,GAAalD,EAAU,GAGxDyD,EAAe,EAAIF,KAAKG,IAAI,EAAIJ,EAAU,GAC1CK,EAAeJ,KAAKK,MAAMH,EAAejB,GAE/CS,EAAgBU,GAEZL,EAAW,IACbH,EAAiBU,sBAAsBT,GAAO,EAMlD,OAFAD,EAAiBU,sBAAsBT,GAEhC,KACDD,GACFW,qBAAqBX,EAAc,CAEvC,GACC,CAACX,EAAOxC,WAGR,OACE,CAAApE,SAAA,CAAAmH,EAAQC,EAAae,iBAAkBjB,IAC1C,EAKEkB,GAAmB,EAAGxB,QAAOhE,QAAQ,UAAWyF,QAAQ,MAC5D,MAAOX,EAAUY,GAAenI,YAAS,GAWvC,OATF0F,cAAU,KACF,MAAA0C,EAAQC,YAAW,KACvBF,EAAY1B,EAAK,GAChByB,GAEI,UAAMI,aAAaF,EAAK,GAC9B,CAAC3B,EAAOyB,IAGTvB,EAAAC,IAAC2B,EAAA,CACCC,QAAQ,cACR/B,MAAOc,EACP9E,QACAgG,GAAI,CACFC,OAAQ,EACRrF,aAAc,EACdsF,gBAAiBC,EAAM,OAAQ,IAC/B,2BAA4B,CAC1BvF,aAAc,EACd0B,WAAY,yDAGlB,EAKE8D,GAAiB,EAAGC,QAAOrC,QAAOsC,YACtC,MAAM5I,EAAQ6I,IACRC,EAAuB,OAAVH,EAGjB,OAAAnC,EAAAuC,KAACC,EAAA,CACCV,GAAI,CACFW,QAAS,OACTC,WAAY,SACZC,IAAK,GACLC,GAAI,EACJC,GAAI,GACJnG,aAAc,EACdsF,gBAAiBC,EACfK,EAAa9I,EAAMG,QAAQO,QAAQL,KAAOL,EAAMG,QAAQS,MAAMP,KAC9D,KAIHX,SAAA,CACCoJ,EAAArC,MAAC6C,GAAY,CAAAhB,GAAI,CAAEpG,SAAU,GAAII,MAAOtC,EAAMG,QAAQO,QAAQL,QAE7DoG,MAAA8C,GAAA,CAAcjB,GAAI,CAAEpG,SAAU,GAAII,MAAOtC,EAAMG,QAAQS,MAAMP,QAEhEmG,EAAAuC,KAACS,EAAA,CACCnB,QAAQ,UACRC,GAAI,CACFnG,WAAY,IACZG,MAAOwG,EAAa9I,EAAMG,QAAQO,QAAQL,KAAOL,EAAMG,QAAQS,MAAMP,MAGtEX,SAAA,CAAA4G,EAAM,aAERkD,EAAW,CAAAnB,QAAQ,UAAU/F,MAAM,iBACjC5C,SACHkJ,MACF,EAKEa,GAAmB,EACvBC,QACApD,QACAqD,WACAC,KAAMC,EACNlB,QACAmB,aACAC,aAAa,gBACb3C,WACA4C,gBACA1H,QAAQ,UACR+F,UAAU,UACV4B,UACAC,WAAU,EACVC,aACAC,gBACAC,UACAC,YAAW,EACXC,iBAAgB,YAEhB,MAAMvK,EAAQ6I,KACP2B,EAAWC,GAAgB5K,aAAS,GAiCrC6K,EAAoB,IACpBJ,EACKtK,EAAMG,QAAQqB,UAAUc,IAAUtC,EAAMG,QAAQqB,UAAUpB,QAG/DmK,EACK9B,EAAMzI,EAAMG,QAAQW,WAAWE,MAAO,IAGxChB,EAAMG,QAAQW,WAAWE,MAQ5B2J,EAAe,IACfL,EAAiB,UACdtK,EAAMG,QAAQgB,KAAKf,qBAIzBwK,EAAQ,CAAAlB,MAAOW,EAASQ,OAAK,EAACC,UAAU,MACvCpL,SAAA8G,EAAAC,IAACsE,GAAOC,IAAP,CACCC,SAxDe,CACnBC,QAAS,CACPC,MAAO,EACPC,EAAG,EACHtG,UAAW,mCAEbuG,MAAO,CACLF,MAAO,KACPC,GAAG,EACHtG,UAAW,wCACXF,WAAY,CACV0G,KAAM,SACNC,UAAW,IACXC,QAAS,MA4CTN,QAAQ,UACRO,WAAW,QACXC,aAAc,IAAMjB,GAAa,GACjCkB,WAAY,IAAMlB,GAAa,GAE/B/K,SAAA8G,EAAAC,IAACmF,EAAA,CACC3B,UACA3B,GAAI,CACFC,OAAQ,OACRsD,OAAQ5B,EAAU,UAAY,UAC9B/G,aAAc,EACdpC,WAAY4J,IACZ/F,eAAgB4F,EAAgB,aAAe,OAC/C7F,OAAQ6F,EACJ,aAAa9B,EAAMzI,EAAMG,QAAQC,QAAQC,KAAM,MAC/C,OACJyL,SAAU,SACVC,SAAU,WACV,YAAazB,EAAW,CACtB0B,QAAS,KACTD,SAAU,WACVE,IAAK,EACLC,KAAM,EACNC,MAAO,EACPC,OAAQ,EACRtL,WAAY4J,IACZ2B,QAAQ,GACN,IAGN3M,SAAAqJ,OAACuD,GAAYhE,GAAI,CAAEiE,EAAG,EAAGhE,OAAQ,QAE/B7I,SAAA,CAAA8G,EAAAuC,KAACC,EAAA,CACCV,GAAI,CACFW,QAAS,OACTC,WAAY,aACZsD,eAAgB,gBAChBC,GAAI,GAGN/M,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,GACrDzJ,SAAA,CAAA+G,MAACsE,GAAOC,IAAP,CAAWC,SAhFL,CACnBC,QAAS,CAAEwB,OAAQ,EAAGvB,MAAO,GAC7BE,MAAO,CACLqB,OAAQ,EACRvB,MAAO,IACPvG,WAAY,CACV0G,KAAM,SACNC,UAAW,IACXC,QAAS,MAyEC9L,SAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFsE,QAAStC,EACL7B,EAAM,UAAW,IACjBA,GAAM,OAAAoE,EAAA7M,EAAMG,QAAQmC,SAAQ,EAAAuK,EAAAxM,OAAQL,EAAMG,QAAQC,QAAQC,KAAM,IACpEiC,MA5DdgI,EAAiB,WACd,OAAAuC,EAAA7M,EAAMG,QAAQmC,WAAduK,EAAsBxM,OAAQL,EAAMG,QAAQC,QAAQC,KA4DzCyM,MAAO,GACPvE,OAAQ,IAGT7I,SACCwK,EAAA1D,EAAAC,IAACsG,EAAiB,CAAAC,KAAM,GAAI1K,MAAM,YAElCmE,MAACoD,EAAK,CAAAvB,GAAI,CAAEpG,SAAU,iBAK3B8G,EACC,CAAAtJ,SAAA,CAAA8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,QACRC,GAAI,CACFhG,MAAOgI,EACH7B,EAAM,UAAW,IACjBzI,EAAMG,QAAQgB,KAAKV,UACvB0B,WAAY,IACZsK,GAAI,IAGL/M,SAAAgK,IAEFC,GACCnD,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,UACRC,GAAI,CACFhG,MAAOgI,EACH7B,EAAM,UAAW,IACjBzI,EAAMG,QAAQgB,KAAKC,UAGxB1B,SAAAiK,WAMPQ,GAAcC,IACd5D,EAAAC,IAACwG,EAAA,CACCD,KAAK,QACL/C,QAASG,EACT9B,GAAI,CACFhG,MAAOgI,EACH7B,EAAM,UAAW,IACjBzI,EAAMG,QAAQgB,KAAKV,WAGxBf,SAAAyK,SAAe+C,GAAS,gBAM9BlE,EAAI,CAAAV,GAAI,CAAEmE,GAAI,GACb/M,SAAA,CAAA8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,KACRC,GAAI,CACFnG,WAAY,IACZG,MAAOqI,IACPvI,WAAY,EACZqK,GAAI,GAGL/M,SAAAwK,EACEnB,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,GACrDzJ,SAAA,CAAA+G,MAACsG,EAAiB,CAAAC,KAAM,GAAI1K,MAAM,cAClCmE,IAAC,QAAK/G,SAAE,UAES,iBAAV4G,EACRE,EAAAC,IAAAE,GAAA,CAAgBL,UAEjBA,IAKHqC,GAASmB,GACRtD,EAAAC,IAACiC,GAAA,CACCC,QACArC,MAAOwD,EACPlB,MAAOmB,YAMC,IAAb3C,GACE2B,OAAAC,EAAA,CAAIV,GAAI,CAAEmE,GAAI,GACb/M,SAAA,CAAA8G,EAAAuC,KAACC,EAAA,CACCV,GAAI,CACFW,QAAS,OACTuD,eAAgB,gBAChBtD,WAAY,SACZuD,GAAI,GAGN/M,SAAA,CAAA8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,UACRC,GAAI,CACFhG,MAAOgI,EACH7B,EAAM,UAAW,IACjBzI,EAAMG,QAAQgB,KAAKV,UACvB0B,WAAY,KAGbzC,SAAiBsK,GAAA,aAEpBxD,EAAAuC,KAACS,EAAA,CACCnB,QAAQ,UACRC,GAAI,CACFhG,MAAOqI,IACPxI,WAAY,KAGbzC,SAAA,CAAA0H,EAAS,UAGdZ,EAAAC,IAACqB,GAAA,CACCxB,MAAOc,EACP9E,QACAyF,MAAO,SAMbvB,EAAAC,IAACsE,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAG5E,OAAQ,GAC/BrB,QAAS,CACPiG,QAAS3C,EAAY,EAAI,EACzBjC,OAAQiC,EAAY,OAAS,GAE/B5F,WAAY,CAAEd,SAAU,IAExBpE,eAACsJ,EAAI,CAAAV,GAAI,CAAE8E,GAAI,EAAGC,GAAI,EAAGC,UAAW,aAAa7E,EAAMkC,IAAgB,OACrEjL,SAAA8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,UACRC,GAAI,CACFhG,MAAOgI,EACH7B,EAAM,UAAW,IACjBzI,EAAMG,QAAQgB,KAAKC,UAE1B1B,SAAA,yCA/MM,KAuNnB,EChaE6N,GAAW,CACfC,KAAM,CAAC,cAAe,UAAW,UAAW,QAAS,iBAAkB,oBACvEC,KAAM,CAAC,cAAe,UAAW,YAAa,UAAW,UAAW,QAAS,UAAW,aACxFC,MAAO,CAAC,cAAe,UAAW,UAAW,wBAAyB,iBAAkB,yBACxFC,GAAI,CAAC,cAAe,WAAY,UAAW,QAAS,0BAA2B,SAI3EC,GAA0B,CAACnM,EAAOoM,KACtC,MAAMC,EAAcP,GAAS9L,IAAU8L,GAASC,KAC1CO,EAAkBF,EAAeG,cAEhC,OAAAF,EAAYG,KAAgBC,IAAA,CACjCA,UACAC,aAAc9G,KAAK+G,IAAI,GAAI/G,KAAKC,IAAI,IAAKyG,EAA0C,IAAvB1G,KAAKgH,SAAW,MAC5EC,cAAejH,KAAK+G,IAAI,GAAI/G,KAAKC,IAAI,GAAIyG,EAA0C,IAAvB1G,KAAKgH,SAAW,MAC5E1F,MAAOtB,KAAKgH,SAAW,GAAM,YAAchH,KAAKgH,SAAW,GAAM,SAAW,YAC5EE,YAAalH,KAAKK,MAAsB,GAAhBL,KAAKgH,UAAiB,GAC9CG,qBAAsBnH,KAAKK,MAAsB,EAAhBL,KAAKgH,UAAgB,MACtD,EAeEI,GAAyB,KAC7B,MAAMC,EAAY,CAChB,gBAAiB,sBAAuB,sBAAuB,0BAC/D,oBAAqB,2BAA4B,oBAAqB,uBAGlEC,EAAQtH,KAAKK,MAAsB,EAAhBL,KAAKgH,UAAgB,EAC9C,OAAOO,MAAMC,KAAK,CAAEC,OAAQH,IAAS,KAAO,CAC1CI,KAAM,IAAIC,KAAK,KAAM3H,KAAKK,MAAsB,GAAhBL,KAAKgH,UAAgBhH,KAAKK,MAAsB,GAAhBL,KAAKgH,UAAiB,GACtF/C,KAAMoD,EAAUrH,KAAKK,MAAML,KAAKgH,SAAWK,EAAUI,SACrDG,SAAU5H,KAAKgH,SAAW,GAAM,OAAShH,KAAKgH,SAAW,GAAM,SAAW,MAC1Ea,YAAa,iDACb,EAIEC,GAA2B,KAC/B,MAAMC,EAAa,CACjB,UAAW,WAAY,aAAc,YAAa,QAAS,cAC3D,eAAgB,WAAY,QAAS,QAAS,QAAS,WACvD,qBAAsB,mBAAoB,wBAGtCT,EAAQtH,KAAKK,MAAsB,EAAhBL,KAAKgH,UAAgB,EAC9C,OAAOO,MAAMC,KAAK,CAAEC,OAAQH,IAAS,KAAO,CAC1CU,SAAUD,EAAW/H,KAAKK,MAAML,KAAKgH,SAAWe,EAAWN,SAC3DQ,MAAOjI,KAAKgH,SAAW,GAAM,WAAahH,KAAKgH,SAAW,GAAM,eAAiB,WACjFkB,aAAclI,KAAKgH,SAAW,GAAM,CAAC,yBAA2BhH,KAAKgH,SAAW,GAAM,CAAC,4BAA8B,MACrH,EAwCSmB,GAAkB,CAC7B,CACEC,GAAI,SACJC,KAAM,cACNC,MAAO,OACPC,QAAS,IACTnO,MAAO,OACPoO,WAAY,cACZ7B,cAAe,GACf8B,OAAQ,YACRC,cAAe,iBACfC,MAAO,qCACPC,YAAa,aACbC,QAAS,wBAEX,CACET,GAAI,SACJC,KAAM,oBACNC,MAAO,MACPC,QAAS,IACTnO,MAAO,QACPoO,WAAY,YACZ7B,cAAe,GACf8B,OAAQ,aACRC,cAAe,iBACfC,MAAO,2CACPC,YAAa,aACbC,QAAS,uBAEX,CACET,GAAI,SACJC,KAAM,eACNC,MAAO,OACPC,QAAS,IACTnO,MAAO,OACPoO,WAAY,cACZ7B,cAAe,GACf8B,OAAQ,iBACRC,cAAe,iBACfC,MAAO,sCACPC,YAAa,aACbC,QAAS,8BAEX,CACET,GAAI,SACJC,KAAM,mBACNC,MAAO,OACPC,QAAS,IACTnO,MAAO,OACPoO,WAAY,cACZ7B,cAAe,GACf8B,OAAQ,YACRC,cAAe,iBACfC,MAAO,qCACPC,YAAa,aACbC,QAAS,wBAEX,CACET,GAAI,SACJC,KAAM,gBACNC,MAAO,OACPC,QAAS,IACTnO,MAAO,OACPoO,WAAY,cACZ7B,cAAe,GACf8B,OAAQ,UACRC,cAAe,iBACfC,MAAO,uCACPC,YAAa,aACbC,QAAS,sBAEX,CACET,GAAI,SACJC,KAAM,eACNC,MAAO,MACPC,QAAS,IACTnO,MAAO,QACPoO,WAAY,YACZ7B,cAAe,GACf8B,OAAQ,SACRC,cAAe,iBACfC,MAAO,sCACPC,YAAa,aACbC,QAAS,iBAEX,CACET,GAAI,SACJC,KAAM,gBACNC,MAAO,OACPC,QAAS,IACTnO,MAAO,OACPoO,WAAY,cACZ7B,cAAe,GACf8B,OAAQ,YACRC,cAAe,iBACfC,MAAO,uCACPC,YAAa,aACbC,QAAS,qBAEX,CACET,GAAI,SACJC,KAAM,cACNC,MAAO,OACPC,QAAS,IACTnO,MAAO,OACPoO,WAAY,cACZ7B,cAAe,GACf8B,OAAQ,SACRC,cAAe,iBACfC,MAAO,qCACPC,YAAa,aACbC,QAAS,sBAEX,CACET,GAAI,SACJC,KAAM,cACNC,MAAO,OACPC,QAAS,IACTnO,MAAO,OACPoO,WAAY,cACZ7B,cAAe,GACf8B,OAAQ,SACRC,cAAe,iBACfC,MAAO,qCACPC,YAAa,aACbC,QAAS,8BAEX,CACET,GAAI,SACJC,KAAM,cACNC,MAAO,MACPC,QAAS,IACTnO,MAAO,QACPoO,WAAY,YACZ7B,cAAe,GACf8B,OAAQ,gBACRC,cAAe,iBACfC,MAAO,qCACPC,YAAa,aACbC,QAAS,2BAKAC,GAA8B,IAClCX,GAAgBvB,KAAgBmC,IAAA,IAClCA,EACHC,YAAazC,GAAwBwC,EAAQ3O,MAAO2O,GACpDE,WAlOa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAC/ErC,KAAcsC,IAAA,CAC1BA,QACAC,QAASnJ,KAAKK,MAAsB,EAAhBL,KAAKgH,UAAgB,GACzCoC,MAAOpJ,KAAKK,MAAsB,EAAhBL,KAAKgH,UAAgB,GACvCqC,WAAYrJ,KAAKK,MAAsB,GAAhBL,KAAKgH,UAAiB,OA8N7CsC,WAAYlC,KACZmC,gBAAiBzB,KACjB0B,aAlKK,CACLC,UAzBoB,CACpB,6BAA8B,iCAAkC,2BAChE,uBAAwB,qBAAsB,wBAAyB,uBACvE,qBAAsB,oBAAqB,uBAAwB,oBAsB1CC,MAAK,IAAM,GAAM1J,KAAKgH,WAAU2C,MAAM,EAAG,GAClEC,WApBqB,CACrB,kBAAmB,0BAA2B,oCAC9C,2BAA4B,gBAAiB,sBAAuB,sBACpE,oBAAqB,mBAAoB,yBAiBdF,MAAK,IAAM,GAAM1J,KAAKgH,WAAU2C,MAAM,EAAG,GACpEE,cAfwB,CACxB,6BAA8B,6BAA8B,mBAC5D,sBAAuB,8BAA+B,sBACtD,4BAA6B,oBAAqB,6BAYjBH,MAAK,IAAM,GAAM1J,KAAKgH,WAAU2C,MAAM,EAAG,GAC1EG,QAVkB,CAClB,oBAAqB,mBAAoB,0BACzC,mBAAoB,uBAAwB,qBAC5C,yBAA0B,kBAAmB,uBAOxBJ,MAAK,IAAM,GAAM1J,KAAKgH,WAAU2C,MAAM,EAAG,GAC9DI,gBAAiBpC,KACjBqC,WAAYhK,KAAKK,MAAsB,GAAhBL,KAAKgH,UAAiB,IA6J7CiD,aAAclB,EAAQpC,eAAiB,GAAK,KAC9BoC,EAAQpC,eAAiB,GAAK,IAC9BoC,EAAQpC,eAAiB,GAAK,IAC9BoC,EAAQpC,eAAiB,GAAK,IAAM,IAClDuD,KAAMlK,KAAKK,MAAsB,GAAhBL,KAAKgH,UAAiB,EACvC+C,gBAAiBpC,SAKRwC,GAAyBC,IACpC,MAAMC,EAAgBD,EAAS3C,OACzB6C,EAAqBF,EAASG,QAAO,CAACC,EAAKC,IAAYD,EAAMC,EAAQ9D,eAAe,GAAK0D,EACzFK,EAAoBN,EAASG,QAAO,CAACC,EAAKC,IAGvCD,EAFcC,EAAQxB,WAAWsB,QAAO,CAACrF,EAAGyF,IAAMzF,EAAIyF,EAAExB,SAAS,GACtDsB,EAAQxB,WAAWsB,QAAO,CAACrF,EAAGyF,IAAMzF,EAAIyF,EAAEvB,OAAO,GAC1B,KACxC,GAAKiB,EAED,OACLA,gBACAC,mBAAoBtK,KAAK4K,MAAMN,GAC/BI,kBAAmB1K,KAAK4K,MAAMF,GAC9BG,cAAeT,EAASU,WAAYC,EAAEpE,eAAiB,KAAIc,OAC3DuD,eAAgBZ,EAASU,WAAYC,EAAEpE,cAAgB,KAAIc,OAC3DwD,kBAAmB,CACjB9E,KAAMiE,EAASU,WAAwB,SAAZC,EAAE3Q,QAAkBqN,OAC/CrB,KAAMgE,EAASU,WAAwB,SAAZC,EAAE3Q,QAAkBqN,OAC/CpB,MAAO+D,EAASU,WAAwB,UAAZC,EAAE3Q,QAAmBqN,OACjDnB,GAAI8D,EAASU,WAAwB,OAAZC,EAAE3Q,QAAgBqN,QAE7CyD,kBAAmB,CACjB,MAAOd,EAASU,WAAwB,QAAZC,EAAEzC,QAAiBb,OAC/C,OAAQ2C,EAASU,WAAwB,SAAZC,EAAEzC,QAAkBb,OACjD,OAAQ2C,EAASU,WAAwB,SAAZC,EAAEzC,QAAkBb,OACjD,OAAQ2C,EAASU,WAAwB,SAAZC,EAAEzC,QAAkBb,QAEnD0D,kBAAmBC,KACnBC,gBAAiBC,GAAwBlB,GACzCmB,mBAAoBC,GAA2BpB,GAC/CqB,iBAAkBC,GAAyBtB,GAC5C,EAIUgB,GAA6BhB,GACzB,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAC/ExD,KAAcsC,IAAA,CAC1BA,QACAyC,QAAS3L,KAAKK,MAAsB,GAAhBL,KAAKgH,UAAiB,GAC1Cb,KAAMnG,KAAKK,MAAsB,GAAhBL,KAAKgH,UAAiB,GACvCZ,KAAMpG,KAAKK,MAAsB,GAAhBL,KAAKgH,UAAiB,GACvCX,MAAOrG,KAAKK,MAAsB,GAAhBL,KAAKgH,UAAiB,OAK/BsE,GAA2BlB,GAClB,CAAC,cAAe,UAAW,UAAW,QAAS,iBAAkB,oBAClExD,KAAgBC,IAAA,CACjCA,UACA+E,aAAc5L,KAAKK,MAAsB,GAAhBL,KAAKgH,UAAiB,GAC/C6E,SAAU7L,KAAKK,MAAsB,GAAhBL,KAAKgH,UAAiB,GAC3C8E,YAAa9L,KAAKK,MAAsB,GAAhBL,KAAKgH,UAAiB,GAC9C+E,gBAAiB/L,KAAKK,MAAML,KAAKgH,SAAWoD,EAAS3C,OAAS,IAC9DuE,gBAAiBhM,KAAKK,MAAML,KAAKgH,SAAWoD,EAAS3C,OAAS,QAKrD+D,GAA8BpB,GAC1B,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAC/ExD,KAAcsC,IAAA,CAC1BA,QACAwB,kBAAmB1K,KAAKK,MAAsB,GAAhBL,KAAKgH,UAAiB,GACpDqD,cAAeD,EAAS3C,OACxBwE,gBAAiBjM,KAAKK,MAAM+J,EAAS3C,QAAU,GAAsB,IAAhBzH,KAAKgH,eAKjD0E,GAA4BtB,IAChC,CACL8B,sBAAuB,CACrB,sBAAuBlM,KAAKK,MAAwB,GAAlB+J,EAAS3C,QAC3C,uBAAwBzH,KAAKK,MAAwB,IAAlB+J,EAAS3C,QAC5C0E,WAAcnM,KAAKK,MAAwB,GAAlB+J,EAAS3C,QAClC2E,WAAcpM,KAAKK,MAAwB,IAAlB+J,EAAS3C,QAClC,mBAAoBzH,KAAKK,MAAwB,GAAlB+J,EAAS3C,SAE1C4E,uBAAwB,CACtB,kBAAmBrM,KAAKK,MAAwB,GAAlB+J,EAAS3C,QACvC,kBAAmBzH,KAAKK,MAAwB,GAAlB+J,EAAS3C,QACvC6E,aAAgBtM,KAAKK,MAAwB,IAAlB+J,EAAS3C,QACpC,oBAAqBzH,KAAKK,MAAwB,IAAlB+J,EAAS3C,SAE3C8E,0BAA2B,CACzB,mBAAoBvM,KAAKK,MAAwB,GAAlB+J,EAAS3C,QACxC,mBAAoBzH,KAAKK,MAAwB,GAAlB+J,EAAS3C,QACxC+E,aAAgBxM,KAAKK,MAAwB,GAAlB+J,EAAS3C,QACpC,oBAAqBzH,KAAKK,MAAwB,GAAlB+J,EAAS3C,SAE3CgF,oBAAqB,CACnB,oBAAqBzM,KAAKK,MAAwB,GAAlB+J,EAAS3C,QACzC,mBAAoBzH,KAAKK,MAAwB,GAAlB+J,EAAS3C,QACxC,0BAA2BzH,KAAKK,MAAwB,GAAlB+J,EAAS3C,QAC/C,mBAAoBzH,KAAKK,MAAwB,IAAlB+J,EAAS3C,WC9VxCiF,GAAiB,EACrBrU,WACAwK,WAAU,EACV9I,YAAW,EACXiH,UAAU,YACV/F,QAAQ,UACR0K,OAAO,SACPgH,YACAC,UACAhK,UACAI,UACA6J,aAAY,EACZ5L,KAAK,CAAC,EACN6L,cAAc,aACdC,mBAAkB,EAClBC,iBAAgB,KACbC,MAEH,MAAMtU,EAAQ6I,KACP0L,EAAWC,GAAgB3U,aAAS,IACpC4U,EAAaC,GAAkB7U,aAAS,IACxC8U,EAAWC,GAAgB/U,aAAS,GA0BrCgV,EAAiB,IACjBJ,EAAoB,UACpBE,EAAkB,QACfrS,EAgEHwS,EAAa,CACjB5R,aAAc,EACdH,cAAe,OACfZ,WAAY,IACZ4S,UAAoB,UAAT/H,EAAmB,GAAc,UAATA,EAAmB,GAAK,GAC3DjB,SAAU,WACVD,SAAU,SACVlH,WAAY,gDAGZ,UAAW,CACTC,UAAW,mBACXC,UAAuB,cAAZuD,EACP,gBAAgBI,EAAMzI,EAAMG,QAAQ0U,KAAkBxU,KAAM,MAC5D,gBAAgBoI,EAAMzI,EAAMG,QAAQ0U,KAAkBxU,KAAM,OAIlE,WAAY,CACVwE,UAAW,mBAIb,kBAAmB,CACjBmQ,QAAS,aAAahV,EAAMG,QAAQ0U,KAAkBxU,OACtD4U,cAAe,OAIjB,aAAc,CACZ9H,QAAS,GACTtI,UAAW,OACXC,UAAW,WAIToF,GAAW,CACbgL,cAAe,WAId5M,GAGC6M,EACJ3O,EAAAC,IAACsE,GAAOC,IAAP,CACCC,SAlEmB,CACrBC,QAAS,CAAEC,MAAO,GAClBE,MAAO,CACLF,MAAO,KACPvG,WAAY,CACV0G,KAAM,SACNC,UAAW,IACXC,QAAS,KAGb4J,IAAK,CACHjK,MAAO,IACPvG,WAAY,CACV0G,KAAM,SACNC,UAAW,IACXC,QAAS,MAoDXN,QAAQ,UACRO,WAAavB,GAAY9I,EAAqB,UAAV,QACpCiU,SAAWnL,GAAY9I,EAAmB,UAAR,MAClCkU,MAAO,CAAErM,QAASiL,EAAY,QAAU,eAAgBpH,MAAOoH,EAAY,OAAS,QAEpFxU,SAAA8G,EAAAC,IAAC8O,EAAA,CACClN,UACA/F,MAAOuS,IACP7H,OACA5L,SAAUA,GAAY8I,EACtBD,QApJcuL,MAAOC,IACzB,IAAIvL,IAAW9I,EAAf,CAEAoT,GAAa,GAET,IACF,GAAIvK,EAAS,CACL,MAAAyL,QAAezL,EAAQwL,GAEzBrB,IAA8B,IAAXsB,IACrBhB,GAAe,GACfxM,YAAW,IAAMwM,GAAe,IAAQ,KAC1C,QAEK9T,GACHyT,IACFO,GAAa,GACb1M,YAAW,IAAM0M,GAAa,IAAQ,KACxC,CACA,QACA1M,YAAW,IAAMsM,GAAa,IAAQ,IAAG,CAnBlB,CAmBkB,EAiIvCN,YACA5L,GAAIwM,KACAR,EAEH5U,SA1HDwK,SAEClB,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,GACrDzJ,SAAA,CAAA8G,EAAAC,IAACsG,EAAA,CACCC,KAAM,GACN1K,MAAM,UACNgG,GAAI,CACFhG,MAAmB,cAAZ+F,EAA0B,QAAUrI,EAAMG,QAAQmC,GAAOjC,QAGnE8T,KAKHM,EAEAjO,EAAAC,IAACuC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,GAAKzJ,SAE5D,cAIAiV,EAEAnO,EAAAC,IAACuC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,GAAKzJ,SAE5D,mBAKDsJ,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,GACpDzJ,SAAA,CAAAsU,EACAtU,EACAuU,SA4FP,OAAI5J,QAECO,EAAQ,CAAAlB,MAAOW,EAASQ,OAAK,EAACC,UAAU,MACvCpL,SAAA+G,MAAC,OAAK,CAAA6O,MAAO,CAAErM,QAASiL,EAAY,QAAU,gBAC3CxU,eAMFyV,CAAA,EC9LHQ,GAAe,EACnBjW,WACAuK,UACA2L,YAAY,EACZvN,UAAU,UACV6B,WAAU,EACV9I,YAAW,EACXyU,aAAY,EACZC,aAAY,EACZxN,KAAK,CAAC,EACNyN,YAAY,CAAC,EACbC,UACAC,SACAC,SACA5T,QAAQ,UACRgI,YAAW,EACXC,iBAAgB,EAChBrH,eAAe,KACZoR,MAEH,MAAMtU,EAAQ6I,KACP2B,EAAWC,GAAgB5K,aAAS,GAqBrCsW,EAAe,CACnBjL,QAAS,CACPC,MAAO,EACPC,EAAG,EACHtG,UAAW9E,EAAMqD,QAAQuS,IAE3BvK,MAAOwK,EAAY,CACjB1K,MAAO,KACPC,GAAG,EACHtG,UAAW9E,EAAMqD,QAAQgE,KAAKC,IAAIsO,EAAY,EAAG,KACjDhR,WAAY,CACV0G,KAAM,SACNC,UAAW,IACXC,QAAS,KAET,CAAC,EACL4J,IAAKU,EAAY,CACf3K,MAAO,IACPvG,WAAY,CACV0G,KAAM,SACNC,UAAW,IACXC,QAAS,KAET,IAGAsJ,EAAa,CACjB5R,eACApC,WAzCIyJ,EAC4B,SAAvBvK,EAAMG,QAAQR,KACjB,2BAA2B8I,EAAM,UAAW,WAAYA,EAAM,UAAW,YACzE,2BAA2BA,EAAM,UAAW,WAAYA,EAAM,UAAW,YAG3E6B,EACK,2BAA2BtK,EAAMG,QAAQmC,GAAOjC,YAAYL,EAAMG,QAAQmC,GAAO/B,aAGnFP,EAAMG,QAAQW,WAAWE,MAgChC2D,eAAgB4F,EAAgB,aAAe,OAC/C7F,OAAQ6F,EACJ,aAAa9B,EAAMzI,EAAMG,QAAQkB,QAAS,MAC1C,OACJwK,OAAQiK,EAAY,UAAY,UAChClR,WAAY,gDACZmH,SAAU,WACVD,SAAU,SAGV,UAAW+J,EAAY,CACrB,kBAAmB,CACjBhR,UAAW,oBAEb,kBAAmB,CACjBsI,QAAS,EACTtI,UAAW,kBAEX,CAAC,KAGDiR,GAAa,CACf,kBAAmB,CACjBd,QAAS,aAAahV,EAAMG,QAAQmC,GAAOjC,OAC3C4U,cAAe,WAKf7T,GAAY,CACd+L,QAAS,GACT+H,cAAe,WAIbhL,GAAW,CACbgL,cAAe,WAGd5M,GAGC8N,EAAoB,CACxBxR,WAAY,yBACTmR,GAGCM,EAAkB,IACtBtN,OAACuD,EACC,CAAA5M,SAAA,CAAC+G,MAAA6P,EAAA,CAASjO,QAAQ,OAAOyE,MAAM,MAAMvE,OAAQ,GAAID,GAAI,CAAEmE,GAAI,KAC1DhG,MAAA6P,EAAA,CAASjO,QAAQ,OAAOyE,MAAM,OAAOvE,OAAQ,GAAID,GAAI,CAAEmE,GAAI,KAC3DhG,MAAA6P,EAAA,CAASjO,QAAQ,OAAOyE,MAAM,MAAMvE,OAAQ,GAAID,GAAI,CAAEmE,GAAI,KAC1DhG,MAAA6P,EAAA,CAASjO,QAAQ,cAAcyE,MAAM,OAAOvE,OAAQ,IAAKD,GAAI,CAAEpF,aAAc,QAKhF,OAAAsD,EAAAC,IAACsE,GAAOC,IAAP,CACCC,SAAUkL,EACVjL,QAAQ,UACRO,WAAarK,GAAa8I,EAAoB,UAAV,QACpCmL,SAAWjU,GAAa8I,IAAW4L,EAAoB,UAAR,MAC/CpK,aAAc,IAAMjB,GAAa,GACjCkB,WAAY,IAAMlB,GAAa,GAE/B/K,SAAA8G,EAAAuC,KAAC6C,EAAA,CACCgK,UAAW,EACX3L,QAnHewL,IACfrU,GAAY8I,IAAYD,GAC5BA,EAAQwL,EAAK,EAkHTnN,GAAIwM,EACJyB,SAAUT,EAAY,GAAI,EAC1BU,KAAMV,EAAY,cAAW,KACzBxB,EAGH5U,SAAA,CACC6K,GAAA/D,EAAAC,IAACuC,EAAA,CACCV,GAAI,CACFyD,SAAU,WACVE,IAAK,EACLC,KAAM,EACNC,MAAO,EACPC,OAAQ,EACRtL,WAAY,2BAA2B2H,EAAM,UAAW,WAAYA,EAAM,UAAW,aACrFyM,cAAe,OACf7I,OAAQ,KAMb4J,GACExP,MAAAuC,EAAA,CAAIV,GAAI,CAAEyD,SAAU,WAAYM,OAAQ,GACtC3M,SACHuW,IAIFzP,EAAAC,IAAC6F,EAAA,CACCmK,UAAU,eACVnO,GAAI8N,EACJd,MAAO,CAAEvJ,SAAU,WAAYM,OAAQ,GAEtC3M,SAAAwK,EAAWzD,MAAA4P,EAAA,CAAgB,GAAK3W,IAIlCsW,GACCxP,EAAAC,IAACiQ,EAAA,CACCD,UAAU,eACVnO,GAAI,CACFyD,SAAU,WACVM,OAAQ,EACRc,QAAS0I,EAAY,GAAM,EAC3BhR,UAAWgR,EAAY,kBAAoB,OAC3CjR,WAAY,iBAGblF,SAAAsW,IAKJE,GACEzP,MAAAuC,EAAA,CAAIV,GAAI,CAAEyD,SAAU,WAAYM,OAAQ,GACtC3M,SACHwW,IAIDJ,GAAatL,GACZhE,EAAAC,IAACuC,EAAA,CACCV,GAAI,CACFyD,SAAU,WACVE,IAAK,MACLC,KAAM,MACNY,MAAO,EACPvE,OAAQ,EACRrF,aAAc,MACdpC,WAAY2H,EAAMzI,EAAMG,QAAQmC,GAAOjC,KAAM,IAC7CwE,UAAW,wBACX8R,UAAW,qBACX,oBAAqB,CACnBC,GAAI,CACF9J,MAAO,OACPvE,OAAQ,OACR4E,QAAS,WAOvB,ECvLE0J,GAAc,EAAGC,WAAUC,eAC/B,MAAM/W,EAAQ6I,IAGXpC,aAAAmE,EAAA,CAAQlB,MAAOoN,EAAW,uBAAyB,sBAClDpX,SAAA8G,EAAAC,IAACwG,EAAA,CACChD,QAAS8M,EACTzO,GAAI,CACFxH,WAAY2H,EAAMzI,EAAMG,QAAQW,WAAWE,MAAO,IAClD2D,eAAgB,aAChBD,OAAQ,aAAa+D,EAAMzI,EAAMG,QAAQkB,QAAS,MAClD,UAAW,CACTP,WAAY2H,EAAMzI,EAAMG,QAAQW,WAAWE,MAAO,IAClD6D,UAAW,eAEbD,WAAY,iBAGdlF,SAAA8G,EAAAC,IAACsE,GAAOC,IAAP,CACCE,SAAS,EACThE,QAAS,CAAEwF,OAAQoK,EAAW,IAAM,GACpClS,WAAY,CAAEd,SAAU,GAAKkT,KAAM,aAElCtX,SAAWoX,QAACG,GAAU,UAAMC,GAAS,SAG5C,EAKEC,GAAe,EAAGL,WAAUM,gBAAeC,iBAC/C,MAAMrX,EAAQ6I,IAGZ,OAAArC,EAAAC,IAAC6Q,EAAA,CACCvL,SAAS,SACT6J,UAAW,EACXtN,GAAI,CACFxH,WAAY2H,EAAMzI,EAAMG,QAAQW,WAAWE,MAAO,IAClD2D,eAAgB,aAChB4S,aAAc,aAAa9O,EAAMzI,EAAMG,QAAQkB,QAAS,MACxDiB,MAAOtC,EAAMG,QAAQgB,KAAKf,SAG5BV,SAAAqJ,OAACyO,GAAQlP,GAAI,CAAEkE,eAAgB,gBAAiBnD,GAAI,GAElD3J,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,GACrDzJ,SAAA,CAAA8G,EAAAC,IAACsE,GAAOC,IAAP,CACCS,WAAY,CAAEN,MAAO,MACrBkK,SAAU,CAAElK,MAAO,KAEnBzL,SAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFsE,QAAS5M,EAAMG,QAAQC,QAAQC,KAC/ByM,MAAO,GACPvE,OAAQ,IAGV7I,eAAC+X,GAAO,eAGXzO,EACC,CAAAtJ,SAAA,CAAA8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,KACRC,GAAI,CACFnG,WAAY,IACZrB,WAAY,2BAA2Bd,EAAMG,QAAQC,QAAQC,YAAYL,EAAMG,QAAQM,UAAUJ,aACjGqX,qBAAsB,OACtBC,oBAAqB,cACrBC,eAAgB,QAEnBlY,SAAA,qBAGA8J,EAAW,CAAAnB,QAAQ,UAAU/F,MAAM,iBAAiB5C,SAErD,8CAKHmY,EAAM,CAAAC,UAAU,MAAM9U,QAAS,EAAGkG,WAAW,SAC5CxJ,SAAA,CAAC+G,MAAAwG,EAAA,CACCvN,SAAC8G,EAAAC,IAAAsR,GAAA,CAAO,KAETtR,MAAAwG,EAAA,CACCvN,SAAC8G,EAAAC,IAAAuR,GAAA,CAAW,KAEdvR,MAACwR,EAAM,CAAAC,aAAc,EAAG5V,MAAM,QAC5B5C,SAAA8G,EAAAC,IAACwG,EACC,CAAAvN,WAAA+G,IAAC0R,GAAc,UAGlB1R,IAAAoQ,GAAA,CAAYC,WAAoBC,SAAUK,IAC3C5Q,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFwE,MAAO,GACPvE,OAAQ,GACRsD,OAAQ,UACRnH,OAAQ,aAAa1E,EAAMG,QAAQC,QAAQC,QAG5CX,UAAM,OAAAmN,EAAA,MAAAwK,OAAA,EAAAA,EAAA3H,WAAA,EAAA7C,EAAMuL,OAAO,KAAM,aAIlC,EAKEC,GAAa,EAAGnO,UAASoO,oBAC7B,MAAMC,EAAWC,KAEXC,EAAYH,EAAgB,CAChC,CACE5O,MAAO,iBACPpD,MAAOgS,EAAc5G,cACrB/H,SAAU,kBACVC,KAAM8O,GACN/P,MAAO,KACPmB,WAAY,GACZ1C,SAAU,GACV4C,cAAe,aACf1H,MAAO,UACP2H,QAAS,IAAMsO,EAAS,wBAE1B,CACE7O,MAAO,gBACPpD,MAAOgS,EAAc5G,cACrB/H,SAAU,uBACVC,KAAM+O,GACNhQ,MAAO,KACPmB,WAAY,EACZ1C,SAAU,GACV4C,cAAe,aACf1H,MAAO,YACPgI,UAAU,EACVL,QAAS,IAAMsO,EAAS,oBAE1B,CACE7O,MAAO,sBACPpD,MAAO,GAAGgS,EAAc3G,sBACxBhI,SAAU,gBACVC,KAAMgP,GACNjQ,MAAO,KACPmB,WAAY,EACZ1C,SAAUkR,EAAc3G,mBACxB3H,cAAe,cACf1H,MAAO,UACP2H,QAAS,IAAMsO,EAAS,yBAE1B,CACE7O,MAAO,kBACPpD,MAAO,GAAGgS,EAAcvG,qBACxBpI,SAAU,qBACVC,KAAMiP,GACNlQ,MAAO2P,EAAcvG,mBAAqB,GAAK,KAAO,OACtDjI,WAAY,EACZ1C,SAAUkR,EAAcvG,kBACxB/H,cAAe,cACf1H,MAAO,OACP2H,QAAS,IAAMsO,EAAS,wBAExB,GAGF9R,aAACqS,GAAKC,WAAS,EAAC/V,QAAS,EACtBtD,SAAA+Y,EAAUxK,KAAI,CAAC+K,EAAMC,IACnBxS,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAC5B3Z,SAAA8G,EAAAC,IAACsE,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAG/B,EAAG,IAC1BlE,QAAS,CAAEiG,QAAS,EAAG/B,EAAG,GAC1BxG,WAAY,CAAEmD,MAAe,GAARkR,EAAanV,SAAU,IAE5CpE,SAAA8G,EAAAC,IAACgD,GAAA,IACKuP,EACJ9O,UACAD,QAAS,KAAyC,EAClDI,QAAS,iBAAiB2O,EAAKtP,MAAM4P,+BAVLN,EAAKtP,UAe/C,EAKE6P,GAAiB,EAAGrP,UAASsP,mBACjC,MAAMjB,EAAWC,KAqDXpJ,EAlDqB,gBACzB,IAAKoK,GAAwC,IAAxBA,EAAa1K,aAAqB,GAEvD,MAAMM,EAAa,GAInBoK,EAAaxI,MAAM,EAAG,GAAGyI,SAAQ,CAAC3H,EAASmH,KACzC7J,EAAWsK,KAAK,CACdjK,GAAI,QAAQqC,EAAQrC,KACpBnE,KAAM,iBACN5B,MAAO,0BACPwF,YAAa,eAAe4C,EAAQpC,SAASoC,EAAQnC,SAASmC,EAAQlC,WACtE+J,KAAuB,GAAbV,EAAQ,GAAZ,eACNrP,KAAM+O,GACNrW,MAAO,UACP2H,QAAS,IAAMsO,EAAS,uBAAuBzG,EAAQrC,YACxD,IAIH,MAAMmK,EAAgBJ,EAAarH,QAAYC,KAAEpE,cAAgB,KA0B1DoB,OAzBHwK,EAAc9K,OAAS,GACzBM,EAAWsK,KAAK,CACdjK,GAAI,oBACJnE,KAAM,oBACN5B,MAAO,oBACPwF,YAAa,GAAG0K,EAAc9K,iCAC9B6K,KAAM,iBACN/P,KAAMgP,GACNtW,MAAO,UACP2H,QAAS,IAAMsO,EAAS,0BAK5BnJ,EAAWsK,KAAK,CACdjK,GAAI,cACJnE,KAAM,cACN5B,MAAO,uBACPwF,YAAa,GAAG,OAAArC,EAAA2M,EAAaA,EAAa1K,OAAS,SAAnC,EAAAjC,EAAuC6C,eAAe,OAAAmK,EAAaL,IAAa1K,OAAS,SAAI,EAAA+K,EAAAlK,SAAS,OAAAmK,EAAAN,EAAaA,EAAa1K,OAAS,SAAnC,EAAAgL,EAAuClK,UAC7J+J,KAAM,aACN/P,KAAM8O,GACNpW,MAAO,UACP2H,QAAS,IAAMsO,EAAS,yBAGnBnJ,EAAW4B,MAAM,EAAG,EAAC,EAGX+I,GAGjB,OAAAvT,EAAAuC,KAAC4M,GAAA,CACCzL,UACAK,eAAe,EACfsL,WAAW,EACXvN,GAAI,CAAEC,OAAQ,QAEd7I,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQuD,eAAgB,gBAAiBtD,WAAY,SAAUuD,GAAI,GACrF/M,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,KAAOzC,SAElD,oBACA8G,EAAAC,IAACsN,GAAA,CACC/G,KAAK,QACL3E,QAAQ,WACR2L,gBAAYgG,GAAQ,IACpB3P,QAAQ,qBACRJ,QAAS,IAAMxE,OAAOwU,SAASC,SAChCxa,SAAA,eAKD+G,MAACoR,GAAM7U,QAAS,EACbtD,WAAWuO,KAAI,CAACoB,EAAU4J,IACzBzS,EAAAC,IAACsE,GAAOC,IAAP,CAECE,QAAS,CAAEiC,QAAS,EAAGgN,GAAO,IAC9BjT,QAAS,CAAEiG,QAAS,EAAGgN,EAAG,GAC1BvV,WAAY,CAAEmD,MAAe,GAARkR,GAErBvZ,SAAA8G,EAAAuC,KAACC,EAAA,CACCV,GAAI,CACFW,QAAS,OACTE,IAAK,EACLoD,EAAG,EACHrJ,aAAc,EACd2I,OAAQwD,EAASpF,QAAU,UAAY,UACvCrF,WAAY,gBACZ,UAAW,CACT9D,WAAY2H,EAAM,OAAQ,KAC1B5D,UAAW,oBAGfoF,QAASoF,EAASpF,QAElBvK,SAAA,CAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFsE,QAAS,GAAGyC,EAAS/M,aACrBwK,MAAO,GACPvE,OAAQ,IAGV7I,SAAA+G,MAAC4I,EAASzF,KAAT,CAActB,GAAI,CAAEpG,SAAU,eAEhC8G,EAAI,CAAAV,GAAI,CAAE8R,KAAM,GACf1a,SAAA,GAAC+G,IAAA+C,EAAA,CAAWnB,QAAQ,QAAQC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,IACpD/M,SAAA2P,EAAS3F,UAEXjD,IAAA+C,EAAA,CAAWnB,QAAQ,UAAU/F,MAAM,iBAAiBgG,GAAI,CAAEW,QAAS,QAASwD,GAAI,IAC9E/M,WAASwP,oBAEX1F,EAAW,CAAAnB,QAAQ,UAAU/F,MAAM,gBACjC5C,WAASia,cArCXtK,EAASI,UA4CxB,EAKE4K,GAAe,KACIxR,IACvB,MAAM0P,EAAWC,KAEXxC,EAAU,CACd,CACEpN,MAAO,gBACPgB,KAAM+O,GACNrW,MAAO,UACP2H,QAAS,IAAMsO,EAAS,oBAE1B,CACE3P,MAAO,cACPgB,KAAM8O,GACNpW,MAAO,YACP2H,QAAS,IAAMsO,EAAS,wBAE1B,CACE3P,MAAO,eACPgB,KAAM0Q,GACNhY,MAAO,UACP2H,QAAS,IAAMsO,EAAS,uBAE1B,CACE3P,MAAO,YACPgB,KAAM2Q,GACNjY,MAAO,OACP2H,QAAS,IAAMsO,EAAS,0BAK1B,aAACvP,GAAIV,GAAI,CAAEyD,SAAU,QAASK,OAAQ,GAAID,MAAO,GAAIE,OAAQ,KAC3D3M,eAACmY,EAAM,CAAA7U,QAAS,EACbtD,SAAQsW,EAAA/H,KAAI,CAACuM,EAAQvB,IACpBzS,EAAAC,IAACsE,GAAOC,IAAP,CAECE,QAAS,CAAEiC,QAAS,EAAGhC,MAAO,GAC9BjE,QAAS,CAAEiG,QAAS,EAAGhC,MAAO,GAC9BvG,WAAY,CAAEmD,MAAe,GAARkR,EAAa3N,KAAM,UAExC5L,SAAA8G,EAAAC,IAACsN,GAAA,CACC1L,QAAQ,YACR/F,MAAOkY,EAAOlY,MACd2H,QAASuQ,EAAOvQ,QAChBI,QAASmQ,EAAO5R,MAChBoL,UAAWxN,EAAAC,IAAC+T,EAAO5Q,KAAP,IACZtB,GAAI,CACFpF,aAAc,MACduX,SAAU,GACV3N,MAAO,GACPvE,OAAQ,GACR5D,eAAgB,aAChBG,UAAW,uCAjBV0V,EAAO5R,YAwBpB,EAKE8R,GAAkB,KACtB,MAAMC,EAAEA,GAAMC,GAAe,CAAC,YAAa,WACrC5a,EAAQ6I,KACPiO,EAAU+D,GAAehb,aAAS,IAClCqK,EAAS4Q,GAAcjb,aAAS,IAChC2Z,EAAcuB,GAAmBlb,YAAS,KAC1CyY,EAAe0C,GAAoBnb,YAAS,OAC5CwX,GAAQxX,YAAS,CACtB6P,KAAM,mBACN8G,KAAM,YACNyE,OAAQ,wBAIV1V,cAAU,KACSiQ,WACfsF,GAAW,GACP,UAEI,IAAII,SAAQC,GAAWjT,WAAWiT,EAAS,QAEjD,MAAM1J,EAAWtB,KACXiL,EAAY5J,GAAsBC,GAExCsJ,EAAgBtJ,GAChBuJ,EAAiBI,SACVxa,GAC2C,CAClD,QACAka,GAAW,EAAK,GAIXO,EAAA,GACR,IAQD,OAAA7U,EAAAuC,KAACC,EAAA,CACCV,GAAI,CACFyM,UAAW,QACXjU,WAAY,2BAA2Bd,EAAMG,QAAQW,WAAWC,eAAef,EAAMG,QAAQW,WAAWG,SAAWjB,EAAMG,QAAQW,WAAWE,eAI9ItB,SAAA,CAAA8G,EAAAC,IAAC0Q,GAAA,CACCL,WACAM,cAfoB,KACxByD,GAAa/D,EAAQ,EAejBO,gBAIDrO,EAAI,CAAAV,GAAI,CAAEiE,EAAG,GAEZ7M,SAAA,CAAA8G,EAAAC,IAACsE,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAG/B,GAAO,IAC9BlE,QAAS,CAAEiG,QAAS,EAAG/B,EAAG,GAC1BxG,WAAY,CAAEd,SAAU,IAExBpE,gBAACsJ,EAAI,CAAAV,GAAI,CAAEmE,GAAI,GACb/M,SAAA,CAAA8G,EAAAuC,KAACS,EAAA,CACCnB,QAAQ,KACRC,GAAI,CACFnG,WAAY,IACZsK,GAAI,EACJ3L,WAAY,2BAA2Bd,EAAMG,QAAQC,QAAQC,YAAYL,EAAMG,QAAQM,UAAUJ,aACjGqX,qBAAsB,OACtBC,oBAAqB,cACrBC,eAAgB,QAEnBlY,SAAA,kBACgB2X,EAAK3H,KAAK,UAE3B3G,OAACS,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAAiBgG,GAAI,CAAEpG,SAAU,UAAYxC,SAAA,+BACjD2X,EAAK4D,OAAO,mBAM9CxU,IAACuC,EAAI,CAAAV,GAAI,CAAEmE,GAAI,GACb/M,SAAC+G,MAAA4R,GAAA,CAAWnO,UAAkBoO,oBAI/BvP,OAAA+P,EAAA,CAAKC,WAAS,EAAC/V,QAAS,EAEvBtD,SAAA,CAAA+G,MAACqS,GAAKI,MAAI,EAACC,GAAI,GAAIE,GAAI,EACrB3Z,SAAA8G,EAAAC,IAACsE,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAGgN,GAAO,IAC9BjT,QAAS,CAAEiG,QAAS,EAAGgN,EAAG,GAC1BvV,WAAY,CAAEmD,MAAO,GAAKjE,SAAU,IAEpCpE,SAAA+G,MAAC8S,GAAe,CAAArP,UAAkBsP,2BAKrCV,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIE,GAAI,EACrB3Z,SAAA8G,EAAAC,IAACsE,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAGgN,EAAG,IAC1BjT,QAAS,CAAEiG,QAAS,EAAGgN,EAAG,GAC1BvV,WAAY,CAAEmD,MAAO,GAAKjE,SAAU,IAEpCpE,SAAA+G,MAACmF,GAAKtD,GAAI,CAAEC,OAAQ,QAClB7I,gBAAC4M,EACC,CAAA5M,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,yBAECwK,EACC1D,EAAAuC,KAAC8O,EAAM,CAAA7U,QAAS,EACdtD,SAAA,CAAA+G,MAAC6P,EAAS,CAAAjO,QAAQ,cAAcE,OAAQ,QACxC9B,IAAC6P,EAAS,CAAAjO,QAAQ,SACjB5B,MAAA6P,EAAA,CAASjO,QAAQ,OAAOyE,MAAM,iBAGhC9D,EAEC,CAAAtJ,SAAA8G,EAAAuC,KAACC,GAAIV,GAAI,CAAEmE,GAAI,GACb/M,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,QAAQC,GAAI,CAAEmE,GAAI,EAAGtK,WAAY,KAAOzC,SAE5D,wBACA+G,IAACoR,EAAM,CAAA7U,QAAS,EACbtD,SAAA,CACC,CAAE+B,MAAO,OAAQ6Z,MAAO,GAAI7J,SAAU,KACtC,CAAEhQ,MAAO,OAAQ6Z,MAAO,GAAI7J,SAAU,KACtC,CAAEhQ,MAAO,cAAe6Z,MAAO,GAAI7J,SAAU,MAC7CxD,KAAI,CAACiL,EAAMD,eACX,OAAAzS,EAAAC,IAACsE,GAAOC,IAAP,CAECE,QAAS,CAAEiC,QAAS,EAAGgN,GAAO,IAC9BjT,QAAS,CAAEiG,QAAS,EAAGgN,EAAG,GAC1BvV,WAAY,CAAEmD,MAAO,GAAc,GAARkR,GAE3BvZ,SAAA8G,EAAAuC,KAACC,EAAA,CACCV,GAAI,CACFW,QAAS,OACTuD,eAAgB,gBAChBtD,WAAY,SACZqD,EAAG,EACHrJ,aAAc,EACdpC,WAAY2H,GAAM,OAAAoE,EAAM7M,EAAAG,QAAQsB,YAAd,EAAAoL,EAAsBqM,EAAKzX,SAAUzB,EAAMG,QAAQC,QAAQC,KAAM,IACnFqE,OAAQ,aAAa+D,GAAM,OAAAoR,EAAA7Z,EAAMG,QAAQsB,YAAd,EAAAoY,EAAsBX,EAAKzX,SAAUzB,EAAMG,QAAQC,QAAQC,KAAM,OAG9FX,SAAA,QAACsJ,EACC,CAAAtJ,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,QAAQC,GAAI,CAAEnG,WAAY,KAC3CzC,SAAAwZ,EAAKzX,QAEPsH,OAAAS,EAAA,CAAWnB,QAAQ,UAAU/F,MAAM,iBACjC5C,SAAA,CAAKwZ,EAAAzH,SAAS,kBAGnBjL,EAAAC,IAAC8U,EAAA,CACC3S,MAAO,GAAGsQ,EAAKoC,SACftO,KAAK,QACL1E,GAAI,CACFsE,SAAS,OAAAkN,IAAM3Z,QAAQsB,gBAAQyX,EAAKzX,SAAUzB,EAAMG,QAAQC,QAAQC,KACpEiC,MAAO,QACPH,WAAY,WA9Bb+W,EAAKzX,MAAA,yBA+C/BgF,IAAAuC,EAAA,CAAIV,GAAI,CAAE8E,GAAI,GACb1N,SAAAqJ,OAAC+P,EAAK,CAAAC,WAAS,EAAC/V,QAAS,EACvBtD,SAAA,CAAA+G,MAACqS,GAAKI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAA8G,EAAAC,IAACsE,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAG/B,EAAG,IAC1BlE,QAAS,CAAEiG,QAAS,EAAG/B,EAAG,GAC1BxG,WAAY,CAAEmD,MAAO,GAAKjE,SAAU,IAEpCpE,SAAA8G,EAAAuC,KAAC6C,EAAA,CACCtD,GAAI,CACFiE,EAAG,EACHkP,UAAW,SACX3a,WAAY,2BAA2Bd,EAAMG,QAAQC,QAAQC,YAAYL,EAAMG,QAAQC,QAAQG,aAC/F+B,MAAO,QACPuJ,OAAQ,UACRjH,WAAY,gBACZ,UAAW,CACTC,UAAW,mBACXC,UAAW,yCAIfpF,SAAA,CAAA+G,MAACkS,IAAWrQ,GAAI,CAAEpG,SAAU,GAAIuK,GAAI,KACpChG,MAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,2BACA+G,MAAC+C,GAAWnB,QAAQ,QAAQC,GAAI,CAAE6E,QAAS,IAAOzN,SAElD,0DAKLoZ,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAA8G,EAAAC,IAACsE,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAG/B,EAAG,IAC1BlE,QAAS,CAAEiG,QAAS,EAAG/B,EAAG,GAC1BxG,WAAY,CAAEmD,MAAO,GAAKjE,SAAU,IAEpCpE,SAAA8G,EAAAuC,KAAC6C,EAAA,CACCtD,GAAI,CACFiE,EAAG,EACHkP,UAAW,SACX3a,WAAY,2BAA2Bd,EAAMG,QAAQM,UAAUJ,YAAYL,EAAMG,QAAQM,UAAUF,aACnG+B,MAAO,QACPuJ,OAAQ,UACRjH,WAAY,gBACZ,UAAW,CACTC,UAAW,mBACXC,UAAW,0CAIfpF,SAAA,CAAA+G,MAAC6T,IAAShS,GAAI,CAAEpG,SAAU,GAAIuK,GAAI,KAClChG,MAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,mBACA+G,MAAC+C,GAAWnB,QAAQ,QAAQC,GAAI,CAAE6E,QAAS,IAAOzN,SAElD,+CAKLoZ,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAA8G,EAAAC,IAACsE,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAG/B,EAAG,IAC1BlE,QAAS,CAAEiG,QAAS,EAAG/B,EAAG,GAC1BxG,WAAY,CAAEmD,MAAO,GAAKjE,SAAU,IAEpCpE,SAAA8G,EAAAuC,KAAC6C,EAAA,CACCtD,GAAI,CACFiE,EAAG,EACHkP,UAAW,SACX3a,WAAY,2BAA2Bd,EAAMG,QAAQO,QAAQL,YAAYL,EAAMG,QAAQO,QAAQH,aAC/F+B,MAAO,QACPuJ,OAAQ,UACRjH,WAAY,gBACZ,UAAW,CACTC,UAAW,mBACXC,UAAW,wCAIfpF,SAAA,CAAA+G,MAACiV,IAAMpT,GAAI,CAAEpG,SAAU,GAAIuK,GAAI,KAC/BhG,MAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,oBACA+G,MAAC+C,GAAWnB,QAAQ,QAAQC,GAAI,CAAE6E,QAAS,IAAOzN,SAElD,sDASX2a,GAAa,MAChB,ECrqBEsB,GAAqB,KACzB,MAAM3b,EAAQ6I,IAGZ,OAAArC,EAAAC,IAACuC,EAAA,CACCV,GAAI,CACFyD,SAAU,WACVE,IAAK,EACLC,KAAM,EACNC,MAAO,EACPC,OAAQ,EACRtL,WAAY,2BAA2Bd,EAAMG,QAAQC,QAAQC,cAAcL,EAAMG,QAAQM,UAAUJ,eACnGyL,SAAU,SACVO,QAAQ,GAIT3M,SAAA,IAAIkP,MAAM,IAAIX,KAAI,CAAC2N,EAAGC,IACrBrV,EAAAC,IAACsE,GAAOC,IAAP,CAECE,QAAS,CAAEiC,QAAS,EAAGhC,MAAO,GAC9BjE,QAAS,CACPiG,QAAS,CAAC,GAAK,GAAK,IACpBhC,MAAO,CAAC,EAAG,IAAK,GAChBgP,EAAG,CAAC,EAAG,IAAK,GACZ/O,EAAG,CAAC,GAAG,GAAK,IAEdxG,WAAY,CACVd,SAAU,EAAQ,EAAJ+X,EACdC,OAAQC,IACRhU,MAAW,IAAJ8T,GAETvG,MAAO,CACLvJ,SAAU,WACVE,IAAQ,GAAS,GAAJ4P,EAAR,IACL3P,KAAS,GAAS,GAAJ2P,EAAR,IACN/O,MAAO,GAAS,GAAJ+O,EACZtT,OAAQ,GAAS,GAAJsT,EACb3Y,aAAc,MACdpC,WAAY+a,EAAI,GAAM,EAClB,2BAA2B7b,EAAMG,QAAQC,QAAQC,WAAWL,EAAMG,QAAQC,QAAQE,WAClF,2BAA2BN,EAAMG,QAAQM,UAAUJ,WAAWL,EAAMG,QAAQM,UAAUH,WAC1FqE,eAAgB,eAvBbkX,MA2BX,EAKEG,GAAmB,EAAGpS,KAAMC,EAAMH,QAAOwF,cAAanH,QAAQ,MAClE,MAAM/H,EAAQ6I,IAGZ,OAAArC,EAAAC,IAACsE,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAG/B,EAAG,IAC1BlE,QAAS,CAAEiG,QAAS,EAAG/B,EAAG,GAC1BxG,WAAY,CAAEmD,QAAOjE,SAAU,IAE/BpE,SAAA8G,EAAAuC,KAACC,EAAA,CACCV,GAAI,CACFW,QAAS,OACTC,WAAY,SACZC,IAAK,EACLoD,EAAG,EACHrJ,aAAc,EACdpC,WAAY2H,EAAMzI,EAAMG,QAAQW,WAAWE,MAAO,IAClD2D,eAAgB,aAChBD,OAAQ,aAAa+D,EAAMzI,EAAMG,QAAQC,QAAQC,KAAM,MACvDuE,WAAY,gBACZ,UAAW,CACTC,UAAW,kBACX/D,WAAY2H,EAAMzI,EAAMG,QAAQW,WAAWE,MAAO,MAItDtB,SAAA,CAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFsE,QAAS5M,EAAMG,QAAQC,QAAQC,KAC/ByM,MAAO,GACPvE,OAAQ,IAGV7I,eAACmK,EAAK,aAEPb,EACC,CAAAtJ,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,IACjD/M,SACHgK,UACCF,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAC/B5C,SACHwP,WAGN,EAKE+M,GAAY,EAAGC,WAAUhS,cAC7B,MAAOiS,EAAcC,GAAmBvc,aAAS,IAC1Cwc,EAAUC,GAAezc,YAAS,CACvCmQ,MAAO,GACPuM,SAAU,KAGN5W,EAAgB6W,GAAW/G,IAC/B6G,GAAqBG,IAAA,IAChBA,EACHD,CAACA,GAAQ/G,EAAMiH,OAAOpW,SACtB,EASDG,aAAAuC,EAAA,CAAI2T,UAAU,OAAOT,SANFzG,IACpBA,EAAMmH,iBACNV,EAASG,EAAQ,EAI6B/T,GAAI,CAAEwE,MAAO,QACzDpN,SAAC8G,EAAAuC,KAAA8O,EAAA,CAAM7U,QAAS,EACdtD,SAAA,CAAA8G,EAAAC,IAACoW,EAAA,CACC3I,WAAS,EACTtL,MAAM,gBACN0C,KAAK,QACLhF,MAAO+V,EAASrM,MAChB8M,SAAUnX,EAAa,SACvBoX,WAAY,CACVC,qBACGC,EAAe,CAAAlR,SAAS,QACvBrM,SAAC+G,MAAAyW,GAAA,CAAM5a,MAAM,eAInBgG,GAAI,CACF,2BAA4B,CAC1BpF,aAAc,EACdyB,eAAgB,aAChB7D,WAAY2H,EAAM,OAAQ,QAKhCjC,EAAAC,IAACoW,EAAA,CACC3I,WAAS,EACTtL,MAAM,WACN0C,KAAM6Q,EAAe,OAAS,WAC9B7V,MAAO+V,EAASE,SAChBO,SAAUnX,EAAa,YACvBoX,WAAY,CACVC,qBACGC,EAAe,CAAAlR,SAAS,QACvBrM,SAAC8G,EAAAC,IAAA0W,GAAA,CAAK7a,MAAM,cAGhB8a,eACE3W,IAACwW,EAAe,CAAAlR,SAAS,MACvBrM,SAAA8G,EAAAC,IAACwG,EAAA,CACChD,QAAS,IAAMmS,GAAiBD,GAChCkB,KAAK,MAEJ3d,SAAeyc,QAACmB,GAAc,UAAMC,GAAW,SAKxDjV,GAAI,CACF,2BAA4B,CAC1BpF,aAAc,EACdyB,eAAgB,aAChB7D,WAAY2H,EAAM,OAAQ,QAKhCjC,EAAAC,IAAC8O,EAAA,CACCjK,KAAK,SACL4I,WAAS,EACT7L,QAAQ,YACR2E,KAAK,QACL5L,SAAU8I,EACV+J,cAAUuJ,GAAa,IACvBlV,GAAI,CACFe,GAAI,IACJnG,aAAc,EACdpC,WAAY,oDACZgE,UAAW,sCACX,UAAW,CACThE,WAAY,oDACZgE,UAAW,uCACXD,UAAW,qBAIdnF,WAAU,gBAAkB,gBAGnC,EAKE+d,GAAa,EAAGvB,WAAUhS,cAC9B,MAAOiS,EAAcC,GAAmBvc,aAAS,IAC1Cwc,EAAUC,GAAezc,YAAS,CACvC6P,KAAM,GACNM,MAAO,GACPuM,SAAU,GACV/F,KAAM,UACN/U,MAAO,SAGHkE,EAAgB6W,GAAW/G,IAC/B6G,GAAqBG,IAAA,IAChBA,EACHD,CAACA,GAAQ/G,EAAMiH,OAAOpW,SACtB,EASDG,aAAAuC,EAAA,CAAI2T,UAAU,OAAOT,SANFzG,IACpBA,EAAMmH,iBACNV,EAASG,EAAQ,EAI6B/T,GAAI,CAAEwE,MAAO,QACzDpN,SAAC8G,EAAAuC,KAAA8O,EAAA,CAAM7U,QAAS,EACdtD,SAAA,CAAA8G,EAAAC,IAACoW,EAAA,CACC3I,WAAS,EACTtL,MAAM,YACNtC,MAAO+V,EAAS3M,KAChBoN,SAAUnX,EAAa,QACvBoX,WAAY,CACVC,qBACGC,EAAe,CAAAlR,SAAS,QACvBrM,SAAC+G,MAAAiS,GAAA,CAAOpW,MAAM,eAIpBgG,GAAI,CACF,2BAA4B,CAC1BpF,aAAc,EACdyB,eAAgB,aAChB7D,WAAY2H,EAAM,OAAQ,QAKhCjC,EAAAC,IAACoW,EAAA,CACC3I,WAAS,EACTtL,MAAM,gBACN0C,KAAK,QACLhF,MAAO+V,EAASrM,MAChB8M,SAAUnX,EAAa,SACvBoX,WAAY,CACVC,qBACGC,EAAe,CAAAlR,SAAS,QACvBrM,SAAC+G,MAAAyW,GAAA,CAAM5a,MAAM,eAInBgG,GAAI,CACF,2BAA4B,CAC1BpF,aAAc,EACdyB,eAAgB,aAChB7D,WAAY2H,EAAM,OAAQ,QAKhCjC,EAAAC,IAACoW,EAAA,CACC3I,WAAS,EACTtL,MAAM,WACN0C,KAAM6Q,EAAe,OAAS,WAC9B7V,MAAO+V,EAASE,SAChBO,SAAUnX,EAAa,YACvBoX,WAAY,CACVC,qBACGC,EAAe,CAAAlR,SAAS,QACvBrM,SAAC8G,EAAAC,IAAA0W,GAAA,CAAK7a,MAAM,cAGhB8a,eACE3W,IAACwW,EAAe,CAAAlR,SAAS,MACvBrM,SAAA8G,EAAAC,IAACwG,EAAA,CACChD,QAAS,IAAMmS,GAAiBD,GAChCkB,KAAK,MAEJ3d,SAAeyc,QAACmB,GAAc,UAAMC,GAAW,SAKxDjV,GAAI,CACF,2BAA4B,CAC1BpF,aAAc,EACdyB,eAAgB,aAChB7D,WAAY2H,EAAM,OAAQ,eAM/BO,EACC,CAAAtJ,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,QAAQC,GAAI,CAAEmE,GAAI,EAAGtK,WAAY,KAAOzC,SAE5D,0BACCmY,EAAM,CAAAC,UAAU,MAAM9U,QAAS,EAAG0a,SAAS,OACzChe,SAAC,QAAQ,OAAQ,cAAe,MAAMuO,KAAKxM,GAC1C+E,EAAAC,IAAC8U,EAAA,CAEC3S,MAAOnH,EACPqU,WAAS,EACTxT,MAAO+Z,EAAS5a,QAAUA,EAAQ,UAAY,UAC9CwI,QAAS,IAAMqS,GAAYG,QAAcA,EAAMhb,YAC/C6G,GAAI,CACFpF,aAAc,EACdf,WAAY,MAPTV,UAcb+E,EAAAC,IAAC8O,EAAA,CACCjK,KAAK,SACL4I,WAAS,EACT7L,QAAQ,YACR2E,KAAK,QACL5L,SAAU8I,EACV+J,cAAUuJ,GAAa,IACvBlV,GAAI,CACFe,GAAI,IACJnG,aAAc,EACdpC,WAAY,oDACZgE,UAAW,uCACX,UAAW,CACThE,WAAY,oDACZgE,UAAW,wCACXD,UAAW,qBAIdnF,WAAU,sBAAwB,uBAGzC,EAKEie,GAAkB,EAAGC,UAASC,eAClC,MAAMlD,EAAEA,GAAMC,GAAe,CAAC,OAAQ,WAChC5a,EAAQ6I,KACPiV,EAAWC,GAAgBle,YAAS,IACpCqK,EAAS4Q,GAAcjb,aAAS,GAyCrC,OAAA2G,EAAAuC,KAACC,EAAA,CACCV,GAAI,CACFyM,UAAW,QACX9L,QAAS,OACT8C,SAAU,WACVjL,WAAY,2BAA2Bd,EAAMG,QAAQW,WAAWC,eAAef,EAAMG,QAAQW,WAAWG,iBAG1GvB,SAAA,CAAA8G,EAAAC,IAACkV,GAAmB,IAGpBnV,EAAAC,IAACuC,EAAA,CACCV,GAAI,CACF8R,KAAM,EACNnR,QAAS,CAAEkQ,GAAI,OAAQqC,GAAI,QAC3BwC,cAAe,SACfxR,eAAgB,SAChBD,EAAG,EACHR,SAAU,YAGZrM,SAAA8G,EAAAuC,KAACgC,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAGgN,GAAO,IAC9BjT,QAAS,CAAEiG,QAAS,EAAGgN,EAAG,GAC1BvV,WAAY,CAAEd,SAAU,IAExBpE,SAAA,CAAAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEmE,GAAI,GACb/M,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,EAAGsD,GAAI,GAC5D/M,SAAA,CAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFsE,QAAS5M,EAAMG,QAAQC,QAAQC,KAC/ByM,MAAO,GACPvE,OAAQ,IAGV7I,eAAC+X,GAAO,CAAAnP,GAAI,CAAEpG,SAAU,QAE1BsE,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,KACRC,GAAI,CACFnG,WAAY,IACZG,MAA8B,SAAvBtC,EAAMG,QAAQR,KAAkB,UAAY,UACnDse,WAAmC,SAAvBje,EAAMG,QAAQR,KACtB,4BACA,6BAEPD,SAAA,kBAIH8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,KACRC,GAAI,CACFnG,WAAY,IACZG,MAAOtC,EAAMG,QAAQgB,KAAKf,QAC1BqM,GAAI,GAEP/M,SAAA,6DAGD8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,QACRC,GAAI,CACFhG,MAAOtC,EAAMG,QAAQgB,KAAKV,UAC1ByB,SAAU,SACVE,WAAY,KAEf1C,SAAA,iJAMHqJ,KAAC8O,EAAM,CAAA7U,QAAS,EACdtD,SAAA,CAAA8G,EAAAC,IAACuV,GAAA,CACCpS,KAAMgP,GACNlP,MAAM,sBACNwF,YAAY,2DACZnH,MAAO,KAETvB,EAAAC,IAACuV,GAAA,CACCpS,KAAM6N,GACN/N,MAAM,0BACNwF,YAAY,qDACZnH,MAAO,KAETvB,EAAAC,IAACuV,GAAA,CACCpS,KAAMsU,GACNxU,MAAM,yBACNwF,YAAY,uDACZnH,MAAO,aAOfvB,EAAAC,IAACuC,EAAA,CACCV,GAAI,CACF8R,KAAM,CAAEjB,GAAI,EAAGqC,GAAI,IACnBvS,QAAS,OACTC,WAAY,SACZsD,eAAgB,SAChBD,EAAG,GAGL7M,SAAA8G,EAAAC,IAACsE,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAG/B,EAAG,IAC1BlE,QAAS,CAAEiG,QAAS,EAAG/B,EAAG,GAC1BxG,WAAY,CAAEd,SAAU,GAAKiE,MAAO,IACpCuN,MAAO,CAAExI,MAAO,OAAQqR,SAAU,KAElCze,SAAA8G,EAAAC,IAACmF,EAAA,CACCtD,GAAI,CACFiE,EAAG,EACHrJ,aAAc,EACdpC,WAAY2H,EAAMzI,EAAMG,QAAQW,WAAWE,MAAO,IAClD2D,eAAgB,aAChBD,OAAQ,aAAa+D,EAAMzI,EAAMG,QAAQC,QAAQC,KAAM,MACvDyE,UAAW,yCAGbpF,gBAAC4M,EAAY,CAAAhE,GAAI,CAAEiE,EAAG,GAEpB7M,SAAA,CAAAqJ,OAACC,GAAIV,GAAI,CAAEmT,UAAW,SAAUhP,GAAI,GAClC/M,SAAA,CAAA8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,KACRC,GAAI,CACFnG,WAAY,IACZsK,GAAI,EACJnK,MAAOtC,EAAMG,QAAQgB,KAAKf,SAE7BV,SAAA,uBAGA8J,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAAiB5C,SAEnD,yCAIF8G,EAAAuC,KAACqV,EAAA,CACC9X,MAAOwX,EACPhB,SAtLU,CAACrH,EAAO4I,KAC9BN,EAAaM,EAAQ,EAsLTC,UAAQ,EACRhW,GAAI,CACFmE,GAAI,EACJ,iBAAkB,CAChB1J,cAAe,OACfZ,WAAY,IACZD,SAAU,OACVuY,SAAU,KAEZ,uBAAwB,CACtBvX,aAAc,EACdqF,OAAQ,IAIZ7I,SAAA,GAAC+G,IAAA8X,EAAA,CAAI3V,MAAM,cACXnC,IAAC8X,EAAI,CAAA3V,MAAM,eAIZnC,MAAA+X,GAAA,CAAgB7e,KAAK,OACnBD,SAAc,MACb8G,EAAAC,IAACsE,GAAOC,IAAP,CAECE,QAAS,CAAEiC,QAAS,EAAGgN,GAAO,IAC9BjT,QAAS,CAAEiG,QAAS,EAAGgN,EAAG,GAC1BsE,KAAM,CAAEtR,QAAS,EAAGgN,EAAG,IACvBvV,WAAY,CAAEd,SAAU,IAExBpE,WAAC+G,IAAAwV,GAAA,CAAUC,SAhNT1G,MAAO6G,IACzBvB,GAAW,GACP,IACE8C,QACIA,EAAQvB,SAGR,IAAInB,SAAQC,GAAWjT,WAAWiT,EAAS,aAG5Cva,GAC4B,CACnC,QACAka,GAAW,EAAK,GAmMgC5Q,aAN9B,SASN1D,EAAAC,IAACsE,GAAOC,IAAP,CAECE,QAAS,CAAEiC,QAAS,EAAGgN,EAAG,IAC1BjT,QAAS,CAAEiG,QAAS,EAAGgN,EAAG,GAC1BsE,KAAM,CAAEtR,QAAS,EAAGgN,GAAO,IAC3BvV,WAAY,CAAEd,SAAU,IAExBpE,WAAC+G,IAAAgX,GAAA,CAAWvB,SAzMT1G,MAAO6G,IAC1BvB,GAAW,GACP,IACE+C,QACIA,EAASxB,SAGT,IAAInB,SAAQC,GAAWjT,WAAWiT,EAAS,aAG5Cva,GAC6B,CACpC,QACAka,GAAW,EAAK,GA4LkC5Q,aANhC,mBAYTlB,EAAI,CAAAV,GAAI,CAAE8E,GAAI,GACb1N,SAAA,CAAA+G,MAACiY,EAAQ,CAAApW,GAAI,CAAEmE,GAAI,GACjB/M,SAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,QAAQ/F,MAAM,iBAAiB5C,uCAKpDmY,EAAM,CAAAC,UAAU,MAAM9U,QAAS,EAAGwJ,eAAe,SAChD9M,SAAA,CAAA8G,EAAAC,IAACwG,EAAA,CACC3E,GAAI,CACF5D,OAAQ,aAAa+D,EAAMzI,EAAMG,QAAQkB,QAAS,MAClD6B,aAAc,EACdqJ,EAAG,IACH,UAAW,CACTzL,WAAY2H,EAAMzI,EAAMG,QAAQC,QAAQC,KAAM,IAC9CwE,UAAW,qBAIfnF,eAACif,GAAO,MAEVnY,EAAAC,IAACwG,EAAA,CACC3E,GAAI,CACF5D,OAAQ,aAAa+D,EAAMzI,EAAMG,QAAQkB,QAAS,MAClD6B,aAAc,EACdqJ,EAAG,IACH,UAAW,CACTzL,WAAY2H,EAAMzI,EAAMG,QAAQC,QAAQC,KAAM,IAC9CwE,UAAW,qBAIfnF,eAACkf,GAAM,MAETpY,EAAAC,IAACwG,EAAA,CACC3E,GAAI,CACF5D,OAAQ,aAAa+D,EAAMzI,EAAMG,QAAQkB,QAAS,MAClD6B,aAAc,EACdqJ,EAAG,IACH,UAAW,CACTzL,WAAY2H,EAAMzI,EAAMG,QAAQC,QAAQC,KAAM,IAC9CwE,UAAW,qBAIfnF,eAACmf,GAAS,uBAQ1B,EC7nBJC,GAAQC,SACNC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAzU,GACA0U,GACAC,IAGF,MAAMC,GAAqB,KACzB,MAAMxf,EAAQ6I,KACPiV,EAAWC,GAAgBle,YAAS,IACpC4f,EAAWC,GAAgB7f,YAAS,SACpC8f,EAAeC,GAAoB/f,YAAS,QAC5C2Z,EAAcuB,GAAmBlb,YAAS,KAC1CyY,EAAe0C,GAAoBnb,YAAS,MAEnD0F,cAAU,KACR,MAAMsa,EAAO1P,KACb4K,EAAgB8E,GACC7E,EAAAxJ,GAAsBqO,GAAK,GAC3C,IAMH,IAAKvH,EACI,SAAA7R,IAACuC,GAAItJ,SAAU,eAIxB,MAAMogB,EAAyB,CAC7BD,KAAM,CACJE,OAAQzH,EAAc9F,kBAAkBvE,KAAI+R,GAAKA,EAAEzP,QACnD0P,SAAU,CACR,CACErX,MAAO,kBACPiX,KAAMvH,EAAc9F,kBAAkBvE,KAAI+R,GAAKA,EAAEhN,UACjDkN,YAAalgB,EAAMG,QAAQC,QAAQC,KACnCmI,gBAAiBC,EAAMzI,EAAMG,QAAQC,QAAQC,KAAM,IACnD8f,QAAS,IAEX,CACEvX,MAAO,OACPiX,KAAMvH,EAAc9F,kBAAkBvE,KAAI+R,GAAKA,EAAExS,OACjD0S,YAAalgB,EAAMG,QAAQM,UAAUJ,KACrCmI,gBAAiBC,EAAMzI,EAAMG,QAAQM,UAAUJ,KAAM,IACrD8f,QAAS,IAEX,CACEvX,MAAO,OACPiX,KAAMvH,EAAc9F,kBAAkBvE,KAAI+R,GAAKA,EAAEvS,OACjDyS,YAAalgB,EAAMG,QAAQO,QAAQL,KACnCmI,gBAAiBC,EAAMzI,EAAMG,QAAQO,QAAQL,KAAM,IACnD8f,QAAS,IAEX,CACEvX,MAAO,cACPiX,KAAMvH,EAAc9F,kBAAkBvE,KAAI+R,GAAKA,EAAEtS,QACjDwS,YAAalgB,EAAMG,QAAQQ,QAAQN,KACnCmI,gBAAiBC,EAAMzI,EAAMG,QAAQQ,QAAQN,KAAM,IACnD8f,QAAS,MAIfC,QAAS,CACPC,YAAY,EACZC,QAAS,CACPC,OAAQ,CACNxU,SAAU,OAEZrC,MAAO,CACLT,SAAS,EACT9H,KAAM,gCAGVqf,OAAQ,CACNpV,EAAG,CACDqV,aAAa,EACbnZ,IAAK,GACL8G,IAAK,QAMPsS,EAAwB,CAC5Bb,KAAM,CACJE,OAAQzH,EAAc5F,gBAAgBzE,KAAImE,GAAKA,EAAElE,UACjD+R,SAAU,CACR,CACErX,MAAO,gBACPiX,KAAMvH,EAAc5F,gBAAgBzE,KAAImE,GAAKA,EAAEa,eAC/CzK,gBAAiB,CACfC,EAAMzI,EAAMG,QAAQC,QAAQC,KAAM,IAClCoI,EAAMzI,EAAMG,QAAQM,UAAUJ,KAAM,IACpCoI,EAAMzI,EAAMG,QAAQO,QAAQL,KAAM,IAClCoI,EAAMzI,EAAMG,QAAQQ,QAAQN,KAAM,IAClCoI,EAAMzI,EAAMG,QAAQS,MAAMP,KAAM,IAChCoI,EAAMzI,EAAMG,QAAQU,KAAKR,KAAM,KAEjC6f,YAAa,CACXlgB,EAAMG,QAAQC,QAAQC,KACtBL,EAAMG,QAAQM,UAAUJ,KACxBL,EAAMG,QAAQO,QAAQL,KACtBL,EAAMG,QAAQQ,QAAQN,KACtBL,EAAMG,QAAQS,MAAMP,KACpBL,EAAMG,QAAQU,KAAKR,MAErBsgB,YAAa,KAInBP,QAAS,CACPC,YAAY,EACZC,QAAS,CACPC,OAAQ,CACNtX,SAAS,GAEXS,MAAO,CACLT,SAAS,EACT9H,KAAM,sCAGVqf,OAAQ,CACNpV,EAAG,CACDqV,aAAa,EACbnZ,IAAK,GACL8G,IAAK,QAMPwS,EAA0B,CAC9Bf,KAAM,CACJE,OAAQ,CAAC,OAAQ,OAAQ,cAAe,MACxCE,SAAU,CACR,CACEJ,KAAM,CACJvH,EAAchG,kBAAkB9E,KAChC8K,EAAchG,kBAAkB7E,KAChC6K,EAAchG,kBAAkB5E,MAChC4K,EAAchG,kBAAkB3E,IAElCnF,gBAAiB,CACfC,EAAMzI,EAAMG,QAAQC,QAAQC,KAAM,IAClCoI,EAAMzI,EAAMG,QAAQM,UAAUJ,KAAM,IACpCoI,EAAMzI,EAAMG,QAAQO,QAAQL,KAAM,IAClCoI,EAAMzI,EAAMG,QAAQQ,QAAQN,KAAM,KAEpC6f,YAAa,CACXlgB,EAAMG,QAAQC,QAAQC,KACtBL,EAAMG,QAAQM,UAAUJ,KACxBL,EAAMG,QAAQO,QAAQL,KACtBL,EAAMG,QAAQQ,QAAQN,MAExBsgB,YAAa,KAInBP,QAAS,CACPC,YAAY,EACZC,QAAS,CACPC,OAAQ,CACNxU,SAAU,UAEZrC,MAAO,CACLT,SAAS,EACT9H,KAAM,oCAMR0f,EAA0B,CAC9BhB,KAAM,CACJE,OAAQzH,EAAc1F,mBAAmB3E,KAAI+R,GAAKA,EAAEzP,QACpD0P,SAAU,CACR,CACErX,MAAO,wBACPiX,KAAMvH,EAAc1F,mBAAmB3E,KAAI+R,GAAKA,EAAEjO,oBAClDvJ,gBAAiBC,EAAMzI,EAAMG,QAAQO,QAAQL,KAAM,IACnD6f,YAAalgB,EAAMG,QAAQO,QAAQL,KACnCsgB,YAAa,KAInBP,QAAS,CACPC,YAAY,EACZC,QAAS,CACPC,OAAQ,CACNtX,SAAS,GAEXS,MAAO,CACLT,SAAS,EACT9H,KAAM,gCAGVqf,OAAQ,CACNpV,EAAG,CACDqV,aAAa,EACbnZ,IAAK,GACL8G,IAAK,QAMP0S,EAAc,CAClB,CACEpX,MAAO,iBACPpD,MAAOgS,EAAc5G,cACrB9H,KAAMmX,GACNze,MAAO,UACPqG,MAAO,QACPuG,YAAa,4BAEf,CACExF,MAAO,sBACPpD,MAAO,GAAGgS,EAAc3G,sBACxB/H,KAAMgP,GACNtW,MAAO,UACPqG,MAAO,QACPuG,YAAa,gCAEf,CACExF,MAAO,qBACPpD,MAAO,GAAGgS,EAAcvG,qBACxBnI,KAAM6N,GACNnV,MAAO,OACPqG,MAAO,QACPuG,YAAa,2BAEf,CACExF,MAAO,iBACPpD,MAAOgS,EAAcpG,cACrBtI,KAAMoX,GACN1e,MAAO,UACPqG,MAAO,QACPuG,YAAa,0BAKf,aAAC+R,GAAU9C,SAAS,KAAK7V,GAAI,CAAEe,GAAI,GACjC3J,SAAA8G,EAAAuC,KAACgC,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAG/B,EAAG,IAC1BlE,QAAS,CAAEiG,QAAS,EAAG/B,EAAG,GAC1BxG,WAAY,CAAEd,SAAU,IAGxBpE,SAAA,CAAAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEmE,GAAI,GACb/M,SAAA,CAAA8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,KACRC,GAAI,CACFnG,WAAY,IACZsK,GAAI,EACJnK,MAAOtC,EAAMG,QAAQgB,KAAKf,SAE7BV,SAAA,8BAGA8J,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAAiB5C,SAEnD,iFAIDqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEmE,GAAI,EAAGxD,QAAS,OAAQE,IAAK,EAAGuU,SAAU,QACnDhe,SAAA,CAAAqJ,OAACmY,GAAYlU,KAAK,QAAQ1E,GAAI,CAAEmS,SAAU,KACxC/a,SAAA,GAAA+G,IAAC0a,GAAWzhB,SAAU,eACtB8G,EAAAuC,KAACqY,EAAA,CACC9a,MAAOmZ,EACP7W,MAAM,aACNkU,SAAWlX,GAAM8Z,EAAa9Z,EAAE8W,OAAOpW,OAEvC5G,SAAA,CAAC+G,MAAA4a,EAAA,CAAS/a,MAAM,QAAQ5G,SAAU,eACjC+G,MAAA4a,EAAA,CAAS/a,MAAM,UAAU5G,SAAY,iBACrC+G,MAAA4a,EAAA,CAAS/a,MAAM,OAAO5G,SAAS,cAC/B+G,MAAA4a,EAAA,CAAS/a,MAAM,MAAM5G,SAAQ,mBAIlCqJ,OAACmY,GAAYlU,KAAK,QAAQ1E,GAAI,CAAEmS,SAAU,KACxC/a,SAAA,GAAA+G,IAAC0a,GAAWzhB,SAAK,UACjB8G,EAAAuC,KAACqY,EAAA,CACC9a,MAAOqZ,EACP/W,MAAM,QACNkU,SAAWlX,GAAMga,EAAiBha,EAAE8W,OAAOpW,OAE3C5G,SAAA,CAAC+G,MAAA4a,EAAA,CAAS/a,MAAM,MAAM5G,SAAU,eAC/B+G,MAAA4a,EAAA,CAAS/a,MAAM,OAAO5G,SAAI,SAC1B+G,MAAA4a,EAAA,CAAS/a,MAAM,OAAO5G,SAAI,SAC1B+G,MAAA4a,EAAA,CAAS/a,MAAM,QAAQ5G,SAAW,gBAClC+G,MAAA4a,EAAA,CAAS/a,MAAM,KAAK5G,SAAE,gBAM7B+G,MAACqS,EAAK,CAAAC,WAAS,EAAC/V,QAAS,EAAGsF,GAAI,CAAEmE,GAAI,GACnC/M,SAAAohB,EAAY7S,KAAI,CAACqT,EAAMrI,IACtBxS,MAACqS,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGoC,GAAI,EAC5B9b,SAAA8G,EAAAC,IAACsE,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAG/B,EAAG,IAC1BlE,QAAS,CAAEiG,QAAS,EAAG/B,EAAG,GAC1BxG,WAAY,CAAEd,SAAU,GAAKiE,MAAe,GAARkR,GAEpCvZ,SAAA8G,EAAAC,IAACgD,GAAkB,IAAG6X,OANYA,EAAK5X,WAa/ClD,EAAAuC,KAAC6C,EAAA,CACCtD,GAAI,CACFxH,WAAY2H,EAAMzI,EAAMG,QAAQW,WAAWE,MAAO,IAClD2D,eAAgB,aAChBD,OAAQ,aAAa+D,EAAMzI,EAAMG,QAAQkB,QAAS,OAGpD3B,SAAA,CAAA8G,EAAAuC,KAACqV,EAAA,CACC9X,MAAOwX,EACPhB,SA/Sc,CAACrH,EAAO4I,KAC9BN,EAAaM,EAAQ,EA+Sb/V,GAAI,CACFiP,aAAc,aAAa9O,EAAMzI,EAAMG,QAAQkB,QAAS,MACxD+H,GAAI,GAGN1J,SAAA,OAAC6e,GAAI3V,MAAM,qBAAqBgB,KAAMnD,MAAC8T,IAAS,WAC/CgE,EAAI,CAAA3V,MAAM,mBAAmBgB,KAAMnD,MAAC6T,IAAS,WAC7CiE,EAAI,CAAA3V,MAAM,qBAAqBgB,KAAMnD,MAACgR,IAAO,WAC7C8G,EAAI,CAAA3V,MAAM,sBAAsBgB,KAAMnD,MAACmS,mBAGzCtM,EAAY,CAAAhE,GAAI,CAAEiE,EAAG,GACnB7M,SAAA,CAAc,IAAdoe,GACCtX,EAAAC,IAACuC,EAAI,CAAAV,GAAI,CAAEC,OAAQ,KACjB7I,SAAC+G,MAAA8a,GAAA,IAASzB,MAGC,IAAdhC,GACEtX,EAAAC,IAAAuC,EAAA,CAAIV,GAAI,CAAEC,OAAQ,KACjB7I,SAAA+G,MAAC+a,GAAK,IAAGd,MAGE,IAAd5C,GACCrX,MAACuC,EAAI,CAAAV,GAAI,CAAEC,OAAQ,IAAKU,QAAS,OAAQuD,eAAgB,UACvD9M,eAACsJ,EAAA,CAAIV,GAAI,CAAEwE,MAAO,KAChBpN,SAAA8G,EAAAC,IAACgb,GAAU,IAAGb,QAIL,IAAd9C,GACEtX,EAAAC,IAAAuC,EAAA,CAAIV,GAAI,CAAEC,OAAQ,KACjB7I,SAAA+G,MAAC+a,GAAK,IAAGX,gBAMrB,ECtXEa,GAAc,EAAG5P,UAAS6P,SAAQC,SAAQC,qBAC9C,MAAM7hB,EAAQ6I,KACPiZ,EAAUC,GAAeliB,YAAS,OAClC2K,EAAWC,GAAgB5K,aAAS,GAOrCmiB,EAAkB,KACtBD,EAAY,KAAI,EAGZE,EAAuB3G,GACvBA,GAAS,GAAWtb,EAAMG,QAAQO,QAAQL,KAC1Cib,GAAS,GAAWtb,EAAMG,QAAQQ,QAAQN,KACvCL,EAAMG,QAAQS,MAAMP,KAGvB6hB,EAAuBvZ,GACvBA,EAAQ,EAAU,CAAEiB,KAAMuY,GAAgB7f,MAAOtC,EAAMG,QAAQO,QAAQL,MACvEsI,EAAQ,EAAU,CAAEiB,KAAMwY,GAAkB9f,MAAOtC,EAAMG,QAAQS,MAAMP,MACpE,CAAEuJ,KAAM,KAAMtH,MAAOtC,EAAMG,QAAQgB,KAAKV,WAG3C4hB,EAAYH,EAAoBpQ,EAAQwQ,kBAAkB1Y,KAG7DnD,aAAA8b,EAAA,CAAKC,IAAE,EAACC,QAAS,IAChB/iB,SAAA8G,EAAAuC,KAAC6C,EAAA,CACCtD,GAAI,CACFC,OAAQ,OACRsD,OAAQ,UACRjH,WAAY,uBACZC,UAAW2F,EAAY,mBAAqB,gBAC5C1F,UAAW0F,EAAYxK,EAAMqD,QAAQ,GAAKrD,EAAMqD,QAAQ,GACxDqB,OAAQ,aAAa+D,EAAMzI,EAAMG,QAAQC,QAAQC,KAAM,MACvD,UAAW,CACT6f,YAAalgB,EAAMG,QAAQC,QAAQC,OAGvCqiB,aAAc,IAAMjY,GAAa,GACjCkY,aAAc,IAAMlY,GAAa,GACjCR,QAAS,IAAM0X,EAAO7P,GAEtBpS,SAAA,CAAAqJ,OAACuD,EAAY,CAAAhE,GAAI,CAAEiE,EAAG,GAEpB7M,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,aAAcsD,eAAgB,gBAAiBC,GAAI,GACzF/M,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,EAAGiR,KAAM,GAC9D1a,SAAA,CAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFwE,MAAO,GACPvE,OAAQ,GACRqE,QAAS5M,EAAMG,QAAQC,QAAQC,KAC/B6B,SAAU,UACVC,WAAY,KAGbzC,SAAQoS,EAAApC,KAAKkT,MAAM,KAAK3U,KAAI4U,GAAKA,EAAE,KAAI7gB,KAAK,IAAI8gB,gBAEnD/Z,OAACC,GAAIV,GAAI,CAAE8R,KAAM,EAAGK,SAAU,GAC5B/a,SAAA,GAAC+G,IAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,IACjD/M,SAAAoS,EAAQpC,OAEV3G,OAAAS,EAAA,CAAWnB,QAAQ,QAAQ/F,MAAM,iBAC/B5C,SAAA,CAAQoS,EAAAiR,MAAM,eAAajR,EAAQjC,cAErC9G,OAAAS,EAAA,CAAWnB,QAAQ,UAAU/F,MAAM,iBAAiB5C,SAAA,QAC9CoS,EAAQkR,yBAInBxc,EAAAC,IAACwG,EAAA,CACCD,KAAK,QACL/C,QAtEYwL,IACtBA,EAAMwN,kBACNlB,EAAYtM,EAAMyN,cAAa,EAqErB5a,GAAI,CACF6E,QAAS3C,EAAY,EAAI,GACzB5F,WAAY,4BAGdlF,eAACyjB,GAAa,gBAKjBna,EAAI,CAAAV,GAAI,CAAEmE,GAAI,GACb/M,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUsD,eAAgB,gBAAiBC,GAAI,GACrF/M,SAAA,CAAA+G,MAAC+C,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAAiB5C,SAEnD,wBACAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,IACpDzJ,SAAA,CACC2iB,GAAA7b,EAAAC,IAAC4b,EAAA,CACC/Z,GAAI,CACFpG,SAAU,GACVI,MAAO4f,EAAoBpQ,EAAQwQ,kBAAkBhgB,SAI3DkE,EAAAuC,KAACS,EAAA,CACCnB,QAAQ,QACRC,GAAI,CACFnG,WAAY,IACZG,MAAO2f,EAAoBnQ,EAAQsR,eAGpC1jB,SAAA,CAAQoS,EAAAsR,aAAa,aAI5B5c,EAAAC,IAACuC,EAAA,CACCV,GAAI,CACFC,OAAQ,EACRrF,aAAc,EACd0J,QAASnE,EAAMwZ,EAAoBnQ,EAAQsR,cAAe,IAC1DtX,SAAU,UAGZpM,SAAA8G,EAAAC,IAACuC,EAAA,CACCV,GAAI,CACFC,OAAQ,OACRuE,MAAO,GAAGgF,EAAQsR,gBAClBxW,QAASqV,EAAoBnQ,EAAQsR,cACrClgB,aAAc,EACd0B,WAAY,iCAOnBmE,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQE,IAAK,EAAGuU,SAAU,OAAQjR,GAAI,GACxD/M,SAAA,CAAA8G,EAAAC,IAAC8U,EAAA,CACC3S,MAAO,GAAGkJ,EAAQxB,yBAClBtD,KAAK,QACL1K,MAAOwP,EAAQxB,YAAc,GAAK,UAAYwB,EAAQxB,YAAc,GAAK,UAAY,QACrFjI,QAAQ,aAETyJ,EAAQuR,eACP7c,EAAAC,IAAC8U,EAAA,CACC3S,MAAM,cACNoE,KAAK,QACL1K,MAAM,UACN+F,QAAQ,cAMdU,OAACC,GAAIV,GAAI,CAAEW,QAAS,OAAQE,IAAK,GAC/BzJ,SAAA,CAAA8G,EAAAC,IAAC8O,EAAA,CACCvI,KAAK,QACL3E,QAAQ,WACR2L,gBAAYsP,GAAS,IACrBrZ,QAAUrE,IACRA,EAAEqd,kBACFtB,EAAO7P,EAAO,EAEhBxJ,GAAI,CAAE8R,KAAM,GACb1a,SAAA,SAGD8G,EAAAC,IAAC8O,EAAA,CACCvI,KAAK,QACL3E,QAAQ,YACR2L,gBAAYuP,GAAe,IAC3BtZ,QAAUrE,IACRA,EAAEqd,kBACFpB,EAAe/P,EAAO,EAExBxJ,GAAI,CAAE8R,KAAM,GACb1a,SAAA,eAOL8G,EAAAuC,KAACya,EAAA,CACC1B,WACA2B,KAAMC,QAAQ5B,GACd6B,QAAS3B,EACT/X,QAAUrE,GAAMA,EAAEqd,kBAElBvjB,SAAA,QAAC2hB,EAAA,CAASpX,QAAS,KAAQ0X,EAAO7P,GAA0BkQ,GAAA,EAC1DtiB,SAAA,CAAA+G,MAAC6c,GAAS,CAAAhb,GAAI,CAAEsb,GAAI,KAAO,yBAG5BvC,EAAS,CAAApX,QAAS,KAAQ2X,EAAO9P,GAA0BkQ,GAAA,EAC1DtiB,SAAA,CAAA+G,MAACod,GAAS,CAAAvb,GAAI,CAAEsb,GAAI,KAAO,yBAG5BvC,EAAS,CAAApX,QAAS,KAAQ4X,EAAe/P,GAA0BkQ,GAAA,EAClEtiB,SAAA,CAAA+G,MAAC8c,GAAe,CAAAjb,GAAI,CAAEsb,GAAI,KAAO,0BAKzC,EAKEE,GAAe,EAAGL,OAAME,UAASI,UAASC,sBAC9C,MAAMrJ,EAAEA,GAAMC,GAAe,CAAC,yBAG3BqJ,EAAO,CAAAR,OAAYE,UAAkBxF,SAAS,KAAKjK,WAAS,EAC3DxU,SAAA,GAAC+G,IAAAyd,EAAA,CACCxkB,SAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUsD,eAAgB,iBAAmB9M,SAAA,mBAEnF+G,MAACwG,GAAWhD,QAAS0Z,EAAS3W,KAAK,QACjCtN,WAAA+G,IAAC0d,IAAU,YAIhB1d,IAAA2d,EAAA,CACC1kB,SAACqJ,OAAA+P,EAAA,CAAKC,WAAS,EAAC/V,QAAS,EAAGsF,GAAI,CAAE8E,GAAI,GACpC1N,SAAA,CAAA+G,MAACqS,GAAKI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB1Z,SAAA8G,EAAAuC,KAAC8T,EAAA,CACC3I,WAAS,EACTtL,MAAM,QACNyb,QAAM,EACN/d,MAAOyd,EAAQhB,OAAS,GACxBjG,SAAWlX,GAAMoe,EAAgB,IAAKD,EAAShB,MAAOnd,EAAE8W,OAAOpW,QAE/D5G,SAAA,CAAC+G,MAAA4a,EAAA,CAAS/a,MAAM,GAAG5G,SAAW,gBAC7B+G,MAAA4a,EAAA,CAAS/a,MAAM,OAAO5G,SAAU,eAChC+G,MAAA4a,EAAA,CAAS/a,MAAM,OAAO5G,SAAU,eAChC+G,MAAA4a,EAAA,CAAS/a,MAAM,aAAa5G,SAAgB,qBAC5C+G,MAAA4a,EAAA,CAAS/a,MAAM,cAAc5G,SAAiB,iCAGlDoZ,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB1Z,SAAA8G,EAAAuC,KAAC8T,EAAA,CACC3I,WAAS,EACTtL,MAAM,cACNyb,QAAM,EACN/d,MAAOyd,EAAQ1T,aAAe,GAC9ByM,SAAWlX,GAAMoe,EAAgB,IAAKD,EAAS1T,YAAazK,EAAE8W,OAAOpW,QAErE5G,SAAA,CAAC+G,MAAA4a,EAAA,CAAS/a,MAAM,GAAG5G,SAAe,oBACjC+G,MAAA4a,EAAA,CAAS/a,MAAM,YAAY5G,SAAgB,qBAC3C+G,MAAA4a,EAAA,CAAS/a,MAAM,OAAO5G,SAAa,kBACnC+G,MAAA4a,EAAA,CAAS/a,MAAM,oBAAoB5G,SAA2B,wCAGlEoZ,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB1Z,SAAA8G,EAAAuC,KAAC8T,EAAA,CACC3I,WAAS,EACTtL,MAAM,aACNyb,QAAM,EACN/d,MAAOyd,EAAQzT,YAAc,GAC7BwM,SAAWlX,GAAMoe,EAAgB,IAAKD,EAASzT,WAAY1K,EAAE8W,OAAOpW,QAEpE5G,SAAA,CAAC+G,MAAA4a,EAAA,CAAS/a,MAAM,GAAG5G,SAAc,mBAChC+G,MAAA4a,EAAA,CAAS/a,MAAM,YAAY5G,SAAgB,qBAC3C+G,MAAA4a,EAAA,CAAS/a,MAAM,OAAO5G,SAAa,kBACnC+G,MAAA4a,EAAA,CAAS/a,MAAM,OAAO5G,SAAc,2BAGxCoZ,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB1Z,SAAA8G,EAAAuC,KAAC8T,EAAA,CACC3I,WAAS,EACTtL,MAAM,cACNyb,QAAM,EACN/d,MAAOyd,EAAQO,YAAc,GAC7BxH,SAAWlX,GAAMoe,EAAgB,IAAKD,EAASO,WAAY1e,EAAE8W,OAAOpW,QAEpE5G,SAAA,CAAC+G,MAAA4a,EAAA,CAAS/a,MAAM,GAAG5G,SAAY,iBAC9B+G,MAAA4a,EAAA,CAAS/a,MAAM,SAAS5G,SAAe,oBACvC+G,MAAA4a,EAAA,CAAS/a,MAAM,UAAU5G,SAAY,kCAK7C6kB,EAAc,CAAAjc,GAAI,CAAEiE,EAAG,GACtB7M,SAAA,OAAC6V,GAAOtL,QAAS,IAAM+Z,EAAgB,IAAKtkB,SAE5C,oBACC6V,EAAO,CAAAlN,QAAQ,YAAY4B,QAAS0Z,EAASjkB,SAE9C,uBAEJ,EAKE8kB,GAAoB,KACxB,MAAM7J,EAAEA,GAAMC,GAAe,CAAC,WACxB5a,EAAQ6I,IACR4b,EAAWC,EAAc1kB,EAAM2kB,YAAYC,KAAK,QAE/CnT,EAAUoT,GAAehlB,YAAS,KAClCqK,EAAS4Q,GAAcjb,aAAS,IAChCilB,EAAYC,GAAiBllB,YAAS,KACtCkkB,EAASiB,GAAcnlB,YAAS,KAChColB,EAAkBC,GAAuBrlB,aAAS,GAGzD0F,cAAU,KACR,MAAM4f,EAAe,CACnB,CACE1V,GAAI,EACJC,KAAM,eACNqT,MAAO,OACPlT,WAAY,MACZmT,gBAAiB,YACjBI,aAAc,GACd9S,WAAY,GACZgS,iBAAkB,EAClBe,eAAe,GAEjB,CACE5T,GAAI,EACJC,KAAM,cACNqT,MAAO,OACPlT,WAAY,MACZmT,gBAAiB,YACjBI,aAAc,GACd9S,WAAY,GACZgS,kBAAkB,EAClBe,eAAe,GAEjB,CACE5T,GAAI,EACJC,KAAM,cACNqT,MAAO,OACPlT,WAAY,MACZmT,gBAAiB,YACjBI,aAAc,GACd9S,WAAY,GACZgS,iBAAkB,EAClBe,eAAe,GAEjB,CACE5T,GAAI,EACJC,KAAM,eACNqT,MAAO,aACPlT,WAAY,MACZmT,gBAAiB,YACjBI,aAAc,GACd9S,WAAY,GACZgS,iBAAkB,EAClBe,eAAe,IAInBnb,YAAW,KACT2c,EAAYM,GACZrK,GAAW,EAAK,GACf,IAAI,GACN,IAGG,MAAAsK,EAAmBC,YAAQ,IACxB5T,EAASU,QAAkBL,IAC1B,MAAAwT,EAAgBxT,EAAQpC,KAAK4J,cAAciM,SAAST,EAAWxL,gBAChDxH,EAAQkR,gBAAgB1J,cAAciM,SAAST,EAAWxL,gBAC1DxH,EAAQiR,MAAMzJ,cAAciM,SAAST,EAAWxL,eAE/DkM,GAAgBzB,EAAQhB,OAASjR,EAAQiR,QAAUgB,EAAQhB,MAE3D0C,GAAsB1B,EAAQ1T,aACT,cAAxB0T,EAAQ1T,aAA+ByB,EAAQsR,cAAgB,IACvC,SAAxBW,EAAQ1T,aAA0ByB,EAAQsR,cAAgB,IAAMtR,EAAQsR,aAAe,IAC/D,sBAAxBW,EAAQ1T,aAAuCyB,EAAQsR,aAAe,GAEnEsC,GAAqB3B,EAAQzT,YACT,cAAvByT,EAAQzT,YAA8BwB,EAAQxB,YAAc,IACrC,SAAvByT,EAAQzT,YAAyBwB,EAAQxB,YAAc,IAAMwB,EAAQxB,WAAa,IAC3D,SAAvByT,EAAQzT,YAAyBwB,EAAQxB,WAAa,GAEnDqV,GAAe5B,EAAQO,YACH,WAAvBP,EAAQO,YAA2BxS,EAAQuR,eACpB,YAAvBU,EAAQO,aAA6BxS,EAAQuR,cAEzC,OAAAiC,GAAiBE,GAAgBC,GAAsBC,GAAqBC,CAAA,KAEpF,CAAClU,EAAUqT,EAAYf,IAEpB6B,EAAqB9T,IACW,EAGhC+T,EAAqB/T,IACW,EAGhCgU,EAAsBhU,IACe,EAGrCiU,EAAmB,KACM,EAI7B,cAAC/c,EAAI,CAAAV,GAAI,CAAEiE,EAAG,CAAE4M,GAAI,EAAGqC,GAAI,IAEzB9b,SAAA,CAAAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEmE,GAAI,GACb/M,SAAA,GAAA+G,IAAC+C,EAAW,CAAAnB,QAAQ,KAAKsU,UAAU,KAAKrU,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAExE,6BACC8J,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAAiB5C,SAEnD,8EAID+G,MAAAuC,EAAA,CAAIV,GAAI,CAAEmE,GAAI,GACb/M,WAACqJ,KAAA+P,EAAA,CAAKC,WAAS,EAAC/V,QAAS,EAAGkG,WAAW,SACrCxJ,SAAA,CAAA+G,MAACqS,GAAKI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAA8G,EAAAC,IAACoW,EAAA,CACC3I,WAAS,EACT8R,YAAY,2CACZ1f,MAAOwe,EACPhI,SAAWlX,GAAMmf,EAAcnf,EAAE8W,OAAOpW,OACxCyW,WAAY,CACVC,qBACGC,EAAe,CAAAlR,SAAS,QACvBrM,SAAC+G,MAAAwf,GAAA,CAAW3jB,MAAM,cAIxBgG,GAAI,CACF,2BAA4B,CAC1BpF,aAAc,cAKrB4V,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQE,IAAK,EAAGqD,eAAgB,YAClD9M,SAAA,CAAA8G,EAAAC,IAAC8O,EAAA,CACClN,QAAQ,WACR2L,gBAAYkS,GAAW,IACvBjc,QAAS,IAAMib,GAAoB,GACnC5c,GAAI,CAAEpF,aAAc,GACrBxD,SAAA,WAGD8G,EAAAC,IAAC8O,EAAA,CACClN,QAAQ,YACR2L,gBAAYmS,GAAQ,IACpBlc,QAAS8b,EACTzd,GAAI,CAAEpF,aAAc,GACrBxD,SAAA,4BASR+G,IAAAuC,EAAA,CAAIV,GAAI,CAAEmE,GAAI,GACb/M,SAAAqJ,OAACS,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAAiB5C,SAAA,YACxC0lB,EAAiBtW,OAAO,OAAK2C,EAAS3C,OAAO,uBAKzDgK,EAAK,CAAAC,WAAS,EAAC/V,QAAS,EACtBtD,WAAiBuO,KAAK6D,SACpBgH,EAAA,CAAKI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAC5B3Z,SAAA8G,EAAAC,IAACib,GAAA,CACC5P,UACA6P,OAAQiE,EACRhE,OAAQiE,EACRhE,eAAgBiE,KALkBhU,EAAQrC,QAYrB,IAA5B2V,EAAiBtW,SAAiB5E,KACjCnB,KAACC,EAAI,CAAAV,GAAI,CAAEmT,UAAW,SAAUpS,GAAI,GAClC3J,SAAA,CAAC+G,MAAA2f,GAAA,CAAW9d,GAAI,CAAEpG,SAAU,GAAII,MAAO,gBAAiBmK,GAAI,KAC5DhG,MAAC+C,GAAWnB,QAAQ,KAAK/F,MAAM,iBAAiB+jB,cAAY,EAAC3mB,SAE7D,sBACA+G,MAAC+C,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAAiBgG,GAAI,CAAEmE,GAAI,GAAK/M,SAElE,8DACA+G,IAAC8O,EAAO,CAAAlN,QAAQ,YAAY2L,gBAAYmS,GAAQ,IAAIlc,QAAS8b,EAAkBrmB,SAE/E,yBAKH+kB,GACCje,EAAAC,IAAC6f,EAAA,CACChkB,MAAM,UACN,aAAW,cACX2H,QAAS8b,EACTzd,GAAI,CACFyD,SAAU,QACVK,OAAQ,GACRD,MAAO,GACPE,OAAQrM,EAAMqM,OAAOka,KAGvB7mB,eAACymB,GAAQ,MAKb3f,EAAAC,IAACqd,GAAA,CACCL,KAAMwB,EACNtB,QAAS,IAAMuB,GAAoB,GACnCnB,UACAC,gBAAiBgB,MAErB,EClhBEwB,GAAmB,EACvBlb,OACA5B,QACA+c,QACAnkB,QACAsH,KAAMC,EACNK,WAAU,EACVwc,kBAAkB,YAElB,MAAM1mB,EAAQ6I,IAiBd,OAAIqB,IAEAzD,IAACmF,EAAK,CAAAtD,GAAI,CAAEC,OAAQ,OAAQwM,UAAW,KACrCrV,SAAAqJ,OAACuD,EACC,CAAA5M,SAAA,CAAA+G,MAAC6P,GAASjO,QAAQ,WAAWyE,MAAO,GAAIvE,OAAQ,KAChD9B,MAAC6P,EAAS,CAAAjO,QAAQ,OAAOyE,MAAM,MAAMxE,GAAI,CAAE8E,GAAI,KAC9C,IAAIwB,MAAM,IAAIX,KAAI,CAAC2N,EAAG3C,IACrBxS,MAAC6P,GAAqBjO,QAAQ,OAAOyE,MAAM,MAAMxE,GAAI,CAAE8E,GAAI,IAA5C6L,UAQtBxS,MAAA8b,EAAA,CAAKC,IAAE,EAACC,QAAS,IAChB/iB,SAAA8G,EAAAC,IAACmF,EAAA,CACCtD,GAAI,CACFC,OAAQ,OACRwM,UAAW,IACXjU,WAnCqB,CAAC6lB,IAC5B,OAAQA,GACN,IAAK,QACH,MAAO,sCAAsCrkB,kFACAA,2BAC/C,IAAK,UACH,MAAO,wCAAwCA,QAAYA,QAAYA,OACzE,IAAK,UACH,MAAO,0BAA0BA,wEACCA,4BACpC,QACS,iCAA2BA,WAAeA,YAAK,EAwBxCskB,CAAqBF,GACjChiB,OAAQ,aAAapC,MACrBsC,WAAY,uBACZ,UAAW,CACTC,UAAW,mBACXC,UAAW9E,EAAMqD,QAAQ,GACzBqB,OAAQ,aAAapC,QAIzB5C,gBAAC4M,EAAY,CAAAhE,GAAI,CAAEiE,EAAG,GACpB7M,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUuD,GAAI,GACpD/M,SAAA,CAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFsE,QAAStK,EACTwK,MAAO,GACPvE,OAAQ,GACRqb,GAAI,GAGNlkB,eAACmK,EAAK,aAEPb,EACC,CAAAtJ,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKG,SAC7C5C,SACHgK,IACCX,OAAAS,EAAA,CAAWnB,QAAQ,QAAQ/F,MAAM,iBAC/B5C,SAAA,CAAM+mB,EAAA3X,OAAO,6BAKnBrI,MAAAuC,EAAA,CACEtJ,SAAM+mB,EAAAxY,KAAI,CAACiL,EAAMD,MACfxS,IAAA8b,EAAA,CAAKC,IAAE,EAACC,QAAS,IAAc,IAARxJ,EACtBvZ,SAAA8G,EAAAuC,KAACC,EAAA,CACCV,GAAI,CACFiE,EAAG,EACHE,GAAI,EACJvJ,aAAc,EACd0J,QAAS,mBACTlI,OAAQ,aAAapC,MACrBsC,WAAY,uBACZ,UAAW,CACTgI,QAAS,GAAGtK,MACZoC,OAAQ,aAAapC,QAIzB5C,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUuD,GAAI,GACpD/M,SAAA,CAAA8G,EAAAC,IAACuC,EAAA,CACCV,GAAI,CACFwE,MAAO,EACPvE,OAAQ,EACRrF,aAAc,MACd0J,QAAStK,EACTshB,GAAI,KAGRnd,MAAC+C,EAAW,CAAAnB,QAAQ,QAAQC,GAAI,CAAEnG,WAAY,KAC3CzC,SAAAwZ,EAAKxP,QAEPwP,EAAK2N,UACJrgB,EAAAC,IAAC8U,EAAA,CACC3S,MAAOsQ,EAAK2N,SACZ7Z,KAAK,QACL1E,GAAI,CAAEwe,GAAI,OAAQ5kB,SAAU,WAC5BI,MAAyB,SAAlB4W,EAAK2N,SAAsB,QAA4B,WAAlB3N,EAAK2N,SAAwB,UAAY,qBAI1Frd,EAAW,CAAAnB,QAAQ,UAAU/F,MAAM,iBACjC5C,WAAKwP,cAEPgK,EAAK6N,wBACHvd,EAAW,CAAAnB,QAAQ,UAAUC,GAAI,CAChCW,QAAS,QACTmE,GAAI,GACJ4Z,UAAW,SACX1kB,SACC5C,SAAA,sBACkBwZ,EAAK6N,uBA/CU9N,aAwDpD,EAKEgO,GAA4B,EAAGC,YAAWC,YAAY,WAC1D,MAAMnnB,EAAQ6I,IACG6b,EAAc1kB,EAAM2kB,YAAYC,KAAK,OAEtD,MAAO1a,EAAS4Q,GAAcjb,aAAS,IAChCunB,EAAUC,GAAexnB,YAAS,KAClCynB,EAAiBC,GAAsB1nB,YAAS,MAEvD0F,cAAU,KACaiQ,WACnBsF,GAAW,SAGL,IAAII,SAAQC,GAAWjT,WAAWiT,EAAS,QA2FjDkM,EAzFqB,CACnBvW,UAAW,CACT,CACEpH,MAAO,iCACPwF,YAAa,gDACb2X,SAAU,OACVE,gBAAiB,+BAEnB,CACErd,MAAO,6BACPwF,YAAa,+CACb2X,SAAU,OACVE,gBAAiB,2BAEnB,CACErd,MAAO,8BACPwF,YAAa,sCACb2X,SAAU,SACVE,gBAAiB,+BAEnB,CACErd,MAAO,yBACPwF,YAAa,mDACb2X,SAAU,SACVE,gBAAiB,kCAGrB9V,WAAY,CACV,CACEvH,MAAO,wBACPwF,YAAa,iDACb2X,SAAU,OACVE,gBAAiB,+BAEnB,CACErd,MAAO,wBACPwF,YAAa,4CACb2X,SAAU,SACVE,gBAAiB,2CAEnB,CACErd,MAAO,kBACPwF,YAAa,qCACb2X,SAAU,SACVE,gBAAiB,qCAGrB7V,cAAe,CACb,CACExH,MAAO,+BACPwF,YAAa,6CACb2X,SAAU,OACVE,gBAAiB,0CAEnB,CACErd,MAAO,yBACPwF,YAAa,wCACb2X,SAAU,OACVE,gBAAiB,kCAEnB,CACErd,MAAO,sBACPwF,YAAa,4CACb2X,SAAU,SACVE,gBAAiB,qCAGrB5V,QAAS,CACP,CACEzH,MAAO,oBACPwF,YAAa,+CACb2X,SAAU,OACVE,gBAAiB,sCAEnB,CACErd,MAAO,iBACPwF,YAAa,wCACb2X,SAAU,SACVE,gBAAiB,8BAEnB,CACErd,MAAO,kBACPwF,YAAa,8CACb2X,SAAU,SACVE,gBAAiB,kCAMJQ,EAAA,CACjB7X,KAAM,eACNqT,MAAO,YACPthB,MAAO0lB,EACPtX,WAAY,cAEdiL,GAAW,EAAK,EAGL0M,EAAA,GACZ,CAACN,EAAWC,IAEf,MAAMM,EAAa,CACjB,CACEnc,KAAM,YACN5B,MAAO,uBACPpH,MAAOtC,EAAMG,QAAQO,QAAQL,KAC7BuJ,KAAM8d,GACNf,QAAS,QACTF,MAAOW,EAAStW,WAAa,IAE/B,CACExF,KAAM,aACN5B,MAAO,yBACPpH,MAAOtC,EAAMG,QAAQS,MAAMP,KAC3BuJ,KAAM+d,GACNhB,QAAS,UACTF,MAAOW,EAASnW,YAAc,IAEhC,CACE3F,KAAM,gBACN5B,MAAO,uBACPpH,MAAOtC,EAAMG,QAAQC,QAAQC,KAC7BuJ,KAAMge,GACNjB,QAAS,UACTF,MAAOW,EAASlW,eAAiB,IAEnC,CACE5F,KAAM,UACN5B,MAAO,sBACPpH,MAAOtC,EAAMG,QAAQQ,QAAQN,KAC7BuJ,KAAMie,GACNlB,QAAS,QACTF,MAAOW,EAASjW,SAAW,KAK7B,cAACnI,EAAI,CAAAV,GAAI,CAAEiE,EAAG,CAAE4M,GAAI,EAAGqC,GAAI,IAEzB9b,SAAA,CAAAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEmE,GAAI,GACb/M,SAAA,GAAA+G,IAAC+C,EAAW,CAAAnB,QAAQ,KAAKsU,UAAU,KAAKrU,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAExE,2BACC4nB,GACC9gB,EAAAuC,KAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,EAAGsD,GAAI,GAC5D/M,SAAA,CAAC+G,MAAAkG,EAAA,CAAOrE,GAAI,CAAEsE,QAAS,gBACpBlN,SAAgB4nB,EAAA5X,KAAK0I,OAAO,YAE9BpP,EACC,CAAAtJ,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,KACxCzC,SAAA4nB,EAAgB5X,OAElB3G,OAAAS,EAAA,CAAWnB,QAAQ,QAAQ/F,MAAM,iBAC/B5C,SAAA,CAAgB4nB,EAAAvE,MAAM,MAAIuE,EAAgB7lB,MAAM,kBAAgB6lB,EAAgBzX,0BAKxFrG,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAAiB5C,SAEnD,iFAIDoZ,EAAK,CAAAC,WAAS,EAAC/V,QAAS,EACtBtD,SAAW+nB,EAAAxZ,KAAK6Z,SACdhP,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAA8G,EAAAC,IAAC+f,GAAA,CACClb,KAAMwc,EAAOxc,KACb5B,MAAOoe,EAAOpe,MACd+c,MAAOqB,EAAOrB,MACdnkB,MAAOwlB,EAAOxlB,MACdsH,KAAMke,EAAOle,KACb8c,gBAAiBoB,EAAOnB,QACxBzc,aAR2B4d,EAAOxc,YAa5C,EC3SJwT,GAAQC,SAASgJ,GAAmB7I,GAAcC,GAAa6I,GAAQC,GAAc3I,IAGrF,MAAM4I,GAAqB,EAAGC,kBAAiBje,cAC7C,GAAIA,EAEAzD,aAACuC,GAAIV,GAAI,CAAEC,OAAQ,IAAKU,QAAS,OAAQC,WAAY,SAAUsD,eAAgB,UAC7E9M,eAAC0I,EAAe,CAAAE,GAAI,CAAEwE,MAAO,WAwDhCrG,aAAAuC,EAAA,CAAIV,GAAI,CAAEC,OAAQ,KACjB7I,WAAA+G,IAAC2hB,GAAM,CAAAvI,KApDO,CAChBE,OAAQ,CAAC,OAAQ,UAAW,UAAW,YAAa,UAAW,MAAO,MACtEE,SAAU,CACR,CACErX,MAAO,sBACPiX,KAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAC/BrX,gBAAiB,0BACjB0X,YAAa,wBACbS,YAAa,EACb0H,qBAAsB,wBACtBC,iBAAkB,OAClBC,0BAA2B,OAC3BC,sBAAuB,yBAEzB,CACE5f,MAAO,gBACPiX,KAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAC/BrX,gBAAiB,0BACjB0X,YAAa,wBACbS,YAAa,EACb0H,qBAAsB,wBACtBC,iBAAkB,OAClBC,0BAA2B,OAC3BC,sBAAuB,2BA6BDpI,QAxBP,CACnBC,YAAY,EACZoI,qBAAqB,EACrBjI,OAAQ,CACNkI,EAAG,CACDC,WAAY,CACV1f,SAAS,GAEX2f,aAAc,EACdC,aAAc,IACdC,MAAO,CACLC,SAAU,MAIhBzI,QAAS,CACPC,OAAQ,CACNxU,SAAU,eAQd,EAKEid,GAAe,EAAGtf,QAAO+c,QAAOnkB,QAAOsH,KAAMC,MAC1BhB,MAGpBpC,IAAAmF,EAAA,CAAKtD,GAAI,CAAEC,OAAQ,OAAQ7D,OAAQ,aAAapC,KAC/C5C,WAAAqJ,KAACuD,EACC,CAAA5M,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,EAAGsD,GAAI,GAC5D/M,SAAA,CAAA+G,MAACoD,EAAK,CAAAvB,GAAI,CAAEhG,WACZmE,MAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKG,SAC7C5C,SACHgK,OAEDjD,MAAAwiB,EAAA,CAAKC,OAAK,EACRxpB,WAAMuO,KAAI,CAACiL,EAAMD,UACfkQ,EAAqB,CAAA7gB,GAAI,CAAEc,GAAI,GAC9B1J,SAAA8G,EAAAC,IAAC2iB,EAAA,CACChpB,eACGoJ,EAAW,CAAAnB,QAAQ,QAAQC,GAAI,CAAEnG,WAAY,KAAOzC,SAAA,MAChDwZ,QAJID,aAiBrBoQ,GAAqB,EAAGC,iBAAgBpf,cAC5C,GAAIA,EAEAzD,aAACuC,GAAIV,GAAI,CAAEC,OAAQ,IAAKU,QAAS,OAAQC,WAAY,SAAUsD,eAAgB,UAC7E9M,eAAC0I,EAAe,CAAAE,GAAI,CAAEwE,MAAO,WAM7B,MAAAyc,EAAe3a,MAAMC,KAAK,CAAEC,OAAQ,KAAM,CAAC8M,EAAGC,KAAO,CACzD2N,IAAK3N,EAAI,EACT4N,OAAQpiB,KAAKgH,SAAW,IAAO,UAAYhH,KAAKgH,SAAW,GAAM,SAAW,WAGxEqb,EAAkBD,IACtB,OAAQA,GACN,IAAK,UAAkB,gBACvB,IAAK,SAAiB,gBACtB,IAAK,OAAe,gBACpB,QAAgB,kBAIpB,cACGzgB,EACC,CAAAtJ,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKge,cAAY,EAAC/d,GAAI,CAAEnG,WAAY,KAAOzC,SAE/D,eACA+G,MAACuC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQ0gB,oBAAqB,iBAAkBxgB,IAAK,EAAGsD,GAAI,GAC5E/M,WAAauO,KAAKub,GACjBhjB,EAAAC,IAACmE,EAAsB,CAAAlB,MAAO,OAAO8f,EAAIA,QAAQA,EAAIC,SACnD/pB,SAAA8G,EAAAC,IAACuC,EAAA,CACCV,GAAI,CACFwE,MAAO,GACPvE,OAAQ,GACRrF,aAAc,EACdsF,gBAAiBkhB,EAAeF,EAAIC,QACpCxgB,QAAS,OACTC,WAAY,SACZsD,eAAgB,SAChBtK,SAAU,UACVI,MAAO,QACPH,WAAY,IACZ0J,OAAQ,WAGTnM,SAAI8pB,SAhBKA,EAAIA,SAqBtBzgB,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQ+U,cAAe,SAAU7U,IAAK,GACxDzJ,SAAA,GAACqJ,KAAAS,EAAA,CAAWnB,QAAQ,QAClB3I,SAAA,GAAA+G,IAAC,UAAO/G,SAAQ,aAAS,wBAE3BqJ,KAACS,EAAW,CAAAnB,QAAQ,QAClB3I,SAAA,GAAA+G,IAAC,UAAO/G,SAAO,YAAS,eAE1BqJ,KAACS,EAAW,CAAAnB,QAAQ,QAClB3I,SAAA,GAAA+G,IAAC,UAAO/G,SAAM,WAAS,iBAG7B,EAKEkqB,GAAmB,EAAGC,eAAc3f,cACxC,GAAIA,EAEAzD,aAACuC,GAAIV,GAAI,CAAEC,OAAQ,IAAKU,QAAS,OAAQC,WAAY,SAAUsD,eAAgB,UAC7E9M,eAAC0I,EAAe,CAAAE,GAAI,CAAEwE,MAAO,WAYnC,cACG9D,EACC,CAAAtJ,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKge,cAAY,EAAC/d,GAAI,CAAEnG,WAAY,KAAOzC,SAE/D,aACAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEyD,SAAU,WAAYxD,OAAQ,GAAIkE,GAAI,GAE/C/M,SAAA,CAAA8G,EAAAC,IAACuC,EAAA,CACCV,GAAI,CACFyD,SAAU,WACVE,IAAK,MACLC,KAAM,EACNC,MAAO,EACP5D,OAAQ,EACRC,gBAAiB,UACjB3D,UAAW,sBAtBH,CAChB,CAAEkK,KAAM,EAAGzD,KAAM,WAAY4D,YAAa,sBAC1C,CAAEH,KAAM,GAAIzD,KAAM,WAAY4D,YAAa,kCAC3C,CAAEH,KAAM,GAAIzD,KAAM,WAAY4D,YAAa,iBAC3C,CAAEH,KAAM,GAAIzD,KAAM,WAAY4D,YAAa,4BAsB5BjB,KAAI,CAAC6b,EAAU7Q,IACvBxS,MAAAmE,EAAA,CAAoBlB,MAAOogB,EAAS5a,YACnCxP,SAAA8G,EAAAC,IAACuC,EAAA,CACCV,GAAI,CACFyD,SAAU,WACVG,KAAU4d,EAAS/a,KAAO,GAAM,IAA1B,IACN9C,IAAK,MACLpH,UAAW,wBACXiI,MAAO,GACPvE,OAAQ,GACRrF,aAAc,MACdsF,gBAAmC,aAAlBshB,EAASxe,KAAsB,UAAY,UAC5DO,OAAQ,UACR,UAAW,CACThH,UAAW,oCAEbD,WAAY,iCAfJqU,KAqBhBlQ,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQuD,eAAgB,gBAAiBY,GAAI,GAC/D1N,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,UAAU3I,SAAC,MAC9B+G,MAAA+C,EAAA,CAAWnB,QAAQ,UAAU3I,SAAE,OAC/B+G,MAAA+C,EAAA,CAAWnB,QAAQ,UAAU3I,SAAE,OAC/B+G,MAAA+C,EAAA,CAAWnB,QAAQ,UAAU3I,SAAE,aAGpCqJ,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQ+U,cAAe,SAAU7U,IAAK,GACxDzJ,SAAA,GAACqJ,KAAAS,EAAA,CAAWnB,QAAQ,QAClB3I,SAAA,GAAA+G,IAAC,UAAO/G,SAAmB,wBAAS,UAEtCqJ,KAACS,EAAW,CAAAnB,QAAQ,QAClB3I,SAAA,GAAA+G,IAAC,UAAO/G,SAAmB,wBAAS,UAEtCqJ,KAACS,EAAW,CAAAnB,QAAQ,QAClB3I,SAAA,GAAA+G,IAAC,UAAO/G,SAAM,WAAS,UAAE,OAAK,CAAA4V,MAAO,CAAEhT,MAAO,WAAa5C,SAAS,sBAG1E,EAKEqqB,GAA4B,EAAGC,iBAAgB9f,cACnD,GAAIA,EAEAzD,aAACuC,GAAIV,GAAI,CAAEC,OAAQ,IAAKU,QAAS,OAAQC,WAAY,SAAUsD,eAAgB,UAC7E9M,eAAC0I,EAAe,CAAAE,GAAI,CAAEwE,MAAO,WAUnC,cACG9D,EACC,CAAAtJ,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKge,cAAY,EAAC/d,GAAI,CAAEnG,WAAY,KAAOzC,SAE/D,qCACCmY,EAAM,CAAA7U,QAAS,EACbtD,SAXY,CACjB,CAAEgQ,KAAM,aAAcua,MAAO,EAAG3Z,WAAY,KAC5C,CAAEZ,KAAM,mBAAoBua,MAAO,EAAG3Z,WAAY,KASlCrC,KAAI,CAACoB,EAAU4J,UACxBrN,EAAiB,CAAAvD,QAAQ,WACxB3I,SAACqJ,OAAAuD,EAAA,CAAYhE,GAAI,CAAEe,GAAI,GACrB3J,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,QAAQC,GAAI,CAAEnG,WAAY,KAC3CzC,SAAA2P,EAASK,OAEX3G,OAAAS,EAAA,CAAWnB,QAAQ,QAAQ/F,MAAM,iBAC/B5C,SAAA,CAAS2P,EAAA4a,MAAM,cAAY5a,EAASiB,WAAW,sBAN3C2I,SAYjB,EAKEiR,GAAkB,EAAGC,kBAAiBjgB,cAC1C,GAAIA,EAEAzD,aAACuC,GAAIV,GAAI,CAAEC,OAAQ,IAAKU,QAAS,OAAQC,WAAY,SAAUsD,eAAgB,UAC7E9M,eAAC0I,EAAe,CAAAE,GAAI,CAAEwE,MAAO,WAYjCrG,aAACmF,EACC,CAAAlM,SAAAqJ,OAACuD,EACC,CAAA5M,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKge,cAAY,EAAC/d,GAAI,CAAEnG,WAAY,KAAOzC,SAE/D,oBACC+G,MAAAwiB,EAAA,CACEvpB,SAbmB,CAC1B,8CACA,2DACA,yCAU2BuO,KAAI,CAACmc,EAAgBnR,MACxCxS,IAAC0iB,EAAqB,CAAA7gB,GAAI,CAAEc,GAAI,GAC9B1J,SAAA8G,EAAAC,IAAC2iB,EAAA,CACChpB,QACEoG,EAAAuC,KAACS,EAAW,CAAAnB,QAAQ,QAAQ3I,SAAA,MACvB0qB,QAJInR,WAYvB,EAKEoR,GAAwB,KAC5B,MAAM1P,EAAEA,GAAMC,GAAe,CAAC,OAAQ,WACf/R,IACvB,MAAM0P,EAAWC,MACX0O,UAAEA,GAAcoD,MACfpgB,EAAS4Q,GAAcjb,aAAS,IAGhC0qB,EAAaC,GAAkB3qB,YAAS,CAC7C6P,KAAM,WACNC,MAAO,EACPF,GAAI,WACJgb,QAAS,eACTC,IAAK,IACLC,UAAW,EACXjZ,cAAe,GACfnE,SAAU,CACR,CAAEmC,KAAM,OAAQC,MAAO,KAAM2L,MAAO,IACpC,CAAE5L,KAAM,UAAWC,MAAO,KAAM2L,MAAO,IACvC,CAAE5L,KAAM,UAAWC,MAAO,IAAK2L,MAAO,IACtC,CAAE5L,KAAM,YAAaC,MAAO,IAAK2L,MAAO,IACxC,CAAE5L,KAAM,UAAWC,MAAO,KAAM2L,MAAO,IACvC,CAAE5L,KAAM,MAAOC,MAAO,IAAK2L,MAAO,IAClC,CAAE5L,KAAM,KAAMC,MAAO,KAAM2L,MAAO,KAEpC8L,SAAU,CACRtW,UAAW,CAAC,OAAQ,UAAW,OAC/BG,WAAY,CAAC,YAAa,UAAW,MACrCC,cAAe,CAAC,oBAAqB,iBACrCC,QAAS,CAAC,sCAAuC,0BAIrD5L,cAAU,KAEgBiQ,WACtBsF,GAAW,SACL,IAAII,SAAQC,GAAWjT,WAAWiT,EAAS,QACjDL,GAAW,EAAK,EAGF8P,EAAA,GACf,CAAC1D,kBAODle,EAAI,CAAAV,GAAI,CAAEiE,EAAG,GAEZ7M,SAAA,CAAAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEmE,GAAI,GACb/M,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUsD,eAAgB,gBAAiBC,GAAI,GACrF/M,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,GACrDzJ,SAAA,OAACuN,EAAW,CAAAhD,QAVI,KACxBsO,EAAS,sBAAqB,EAUpB7Y,SAAA+G,MAACokB,IAAU,KAEb9hB,OAACS,GAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,KAAOzC,SAAA,aACtC6qB,EAAY7a,iBAGzB1G,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQE,IAAK,EAAGD,WAAY,UAC9CxJ,SAAC+G,MAAAwG,EAAA,CACCvN,SAAC+G,MAAAsR,GAAA,WAMNhP,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQE,IAAK,EAAGD,WAAY,SAAUuD,GAAI,GAC5D/M,SAAA,CAAA+G,MAAC8U,GAAK3S,MAAO,UAAU2hB,EAAY5a,QAASrN,MAAM,YAClDmE,MAAC8U,GAAK3S,MAAO,OAAO2hB,EAAY9a,KAAMpH,QAAQ,mBAC7CkT,EAAK,CAAA3S,MAAO2hB,EAAYE,QAASpiB,QAAQ,mBAK7CU,OAAA+P,EAAA,CAAKC,WAAS,EAAC/V,QAAS,EAEvBtD,SAAA,CAAA+G,MAACqS,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAC+G,MAAAmF,EAAA,CAAKtD,GAAI,CAAEmE,GAAI,GACd/M,gBAAC4M,EACC,CAAA5M,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKge,cAAY,EAAC/d,GAAI,CAAEnG,WAAY,KAAOzC,SAE/D,+BACCwoB,IAAmBhe,mBAGnBlB,EAAI,CAAAV,GAAI,CAAE8E,GAAI,GACZ1N,SAAA,CAAA6qB,EAAYhd,SAASU,KAAI,CAACC,EAAS+K,MACjClQ,KAAAC,EAAA,CAAgBV,GAAI,CAAEW,QAAS,OAAQuD,eAAgB,gBAAiBC,GAAI,GAC3E/M,SAAA,GAACqJ,KAAAS,EAAA,CAAWnB,QAAQ,QAAS3I,SAAA,CAAQwO,EAAAwB,KAAK,OAC1C3G,OAACS,GAAWnB,QAAQ,QAAQC,GAAI,CAAEnG,WAAY,KAC3CzC,SAAA,CAAQwO,EAAAyB,MAAM,KAAGzB,EAAQoN,MAAM,UAH1BrC,WAOXyF,EAAQ,CAAApW,GAAI,CAAEwiB,GAAI,KACnB/hB,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQuD,eAAgB,gBAAiBC,GAAI,GAC/D/M,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,QAAQC,GAAI,CAAEnG,WAAY,KAAOzC,SAAI,SACzD+G,MAAC+C,EAAW,CAAAnB,QAAQ,QAAQC,GAAI,CAAEnG,WAAY,KAAQzC,SAAA6qB,EAAYG,SAEpE3hB,OAACC,GAAIV,GAAI,CAAEW,QAAS,OAAQuD,eAAgB,iBAC1C9M,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,QAAQC,GAAI,CAAEnG,WAAY,KAAOzC,SAAW,gBAChEqJ,OAACS,GAAWnB,QAAQ,QAAQC,GAAI,CAAEnG,WAAY,KAC3CzC,SAAA,CAAY6qB,EAAAI,UAAU,IAAEJ,EAAY7Y,oCAShDoH,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAC+G,MAAAmF,EAAA,CAAKtD,GAAI,CAAEmE,GAAI,GACd/M,gBAAC4M,EACC,CAAA5M,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKge,cAAY,EAAC/d,GAAI,CAAEnG,WAAY,KAAOzC,SAE/D,kBACCqJ,OAAA+P,EAAA,CAAKC,WAAS,EAAC/V,QAAS,EACvBtD,SAAA,CAAA+G,MAACqS,EAAK,CAAAI,MAAI,EAACC,GAAI,EACbzZ,SAAA8G,EAAAC,IAACuiB,GAAA,CACCtf,MAAM,YACN+c,MAAO8D,EAAYnD,SAAStW,UAC5BxO,MAAM,UACNsH,KAAMsU,OAGTzX,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,EACbzZ,SAAA8G,EAAAC,IAACuiB,GAAA,CACCtf,MAAM,aACN+c,MAAO8D,EAAYnD,SAASnW,WAC5B3O,MAAM,UACNsH,KAAMmhB,OAGTtkB,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,EACbzZ,SAAA8G,EAAAC,IAACuiB,GAAA,CACCtf,MAAM,gBACN+c,MAAO8D,EAAYnD,SAASlW,cAC5B5O,MAAM,UACNsH,KAAMgP,OAGTnS,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,EACbzZ,SAAA8G,EAAAC,IAACuiB,GAAA,CACCtf,MAAM,UACN+c,MAAO8D,EAAYnD,SAASjW,QAC5B7O,MAAM,UACNsH,KAAMohB,mBASlBvkB,IAACqS,GAAKI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAA8G,EAAAC,IAACmF,EAAK,CAAAtD,GAAI,CAAEC,OAAQ,QAClB7I,eAAC4M,EAAA,CACC5M,eAAC2pB,GAAmB,CAAAnf,oBAI1BzD,IAACqS,GAAKI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAA8G,EAAAC,IAACmF,EAAK,CAAAtD,GAAI,CAAEC,OAAQ,QAClB7I,eAAC4M,EAAA,CACC5M,eAACkqB,GAAiB,CAAA1f,wBAMvB4O,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAC8G,EAAAC,IAAAmF,EAAA,CACClM,eAAC4M,EACC,CAAA5M,SAAA+G,MAACsjB,IAA0B7f,oBAMjCzD,IAACqS,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,WAAA+G,IAACyjB,GAAgB,CAAAhgB,iBAKpBnB,OAAAC,EAAA,CAAIV,GAAI,CAAE8E,GAAI,EAAGnE,QAAS,OAAQE,IAAK,EAAGqD,eAAgB,YACzD9M,SAAA,CAAA8G,EAAAC,IAAC8O,EAAA,CACClN,QAAQ,WACR2L,gBAAYiX,GAAS,IACrB3iB,GAAI,CAAEvF,cAAe,QACtBrD,SAAA,oBAGD8G,EAAAC,IAAC8O,EAAA,CACClN,QAAQ,WACR2L,gBAAYkX,GAAM,IAClB5iB,GAAI,CAAEvF,cAAe,QACtBrD,SAAA,UAGD8G,EAAAC,IAAC8O,EAAA,CACClN,QAAQ,WACR2L,gBAAYmX,GAAM,IAClB7iB,GAAI,CAAEvF,cAAe,QACtBrD,SAAA,UAGD8G,EAAAC,IAAC8O,EAAA,CACClN,QAAQ,YACR2L,gBAAY2E,GAAW,IACvBrQ,GAAI,CAAEvF,cAAe,QACtBrD,SAAA,+BAIL,ECjmBS0rB,GAA0B,EACrCC,YAAY,GACZ5mB,OAAO,KACP6mB,aAAa,MACbC,QAAO,EACPC,WAAU,MAEV,MAAOC,EAAgBC,GAAqB7rB,aAAS,IAC9C8rB,EAAgBC,GAAqB/rB,aAAS,GAC/CgsB,EAAYC,GAAMA,OAAC,MACnBC,EAAcD,GAAMA,OAAC,MAkD3B,OAhDAvmB,cAAU,KACR,IAAKimB,EAAS,OAEd,MAAM9O,EAASmP,EAAUG,QACzB,IAAKtP,EAAQ,OAGT,IAACjX,OAAOwmB,qBAIV,OAFAP,GAAkB,QAClBE,GAAkB,GA8BpB,OATYG,EAAAC,QAAU,IAAIC,sBAjBEC,IACpB,MAACC,GAASD,EACVE,EAA0BD,EAAMV,eAEtCC,EAAkBU,GAEdA,IAA4BT,IAC9BC,GAAkB,GAGdL,GAAQQ,EAAYC,SACtBD,EAAYC,QAAQK,aAE9B,GAIuE,CACjEhB,UAAWzc,MAAM0d,QAAQjB,GAAaA,EAAY,CAACA,GACnD5mB,OACA6mB,eAGUS,EAAAC,QAAQO,QAAQ7P,GAGrB,KACDqP,EAAYC,SACdD,EAAYC,QAAQK,YAC5B,CACK,GACA,CAAChB,EAAW5mB,EAAM6mB,EAAYC,EAAMC,EAASG,IAGzC,CAACE,EAAWN,EAAOI,EAAiBF,EAAgBE,EAAc,ECzBrEa,GAAmB,KACvB,MAAMjU,EAAWC,KACXxY,EAAQ6I,IACR4b,EAAWC,EAAc1kB,EAAM2kB,YAAYC,KAAK,QAC/C6H,EAAYC,GAAiB7sB,aAAS,GAEvC8sB,EAAqB,KACzBD,GAAeD,EAAU,EAGrBG,EAAkB,CACtB,CAAEhkB,MAAO,OAAQikB,KAAM,KACvB,CAAEjkB,MAAO,QAASikB,KAAM,UACxB,CAAEjkB,MAAO,WAAYikB,KAAM,aAC3B,CAAEjkB,MAAO,UAAWikB,KAAM,aAGtBC,SACH9jB,EAAI,CAAAiB,QAAS0iB,EAAoBrkB,GAAI,CAAEmT,UAAW,UACjD/b,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEwiB,GAAI,EAAG3oB,WAAY,KAAOzC,SAEzD,sBACCupB,EACE,CAAAvpB,SAAA,CAAAktB,EAAgB3e,KAAKiL,GACnBzS,MAAA0iB,EAAA,CAA0B4D,gBAAc,EACvCrtB,SAAA8G,EAAAC,IAAC8O,EAAA,CACCrB,WAAS,EACTjK,QAAS,IAAMsO,EAASW,EAAK2T,MAC7BvkB,GAAI,CAAEmT,UAAW,SAAUpS,GAAI,GAE/B3J,WAAC+G,IAAA2iB,EAAA,CAAahpB,QAAS8Y,EAAKtQ,WANjBsQ,EAAKtQ,WAUtBnC,IAAC0iB,EAAS,CAAA4D,gBAAc,EACtBrtB,SAAA8G,EAAAC,IAAC8O,EAAA,CACCrB,WAAS,EACT7L,QAAQ,YACR4B,QAAS,IAAMsO,EAAS,UACxBjQ,GAAI,CAAEnJ,EAAG,GACVO,SAAA,kBAQT,OAEI8G,EAAAuC,KAAAikB,WAAA,CAAAttB,SAAA,CAAA8G,EAAAC,IAAC6Q,EAAA,CACCvL,SAAS,QACTzD,GAAI,CACFsE,QAAS,4BACTjI,eAAgB,aAChBG,UAAW,4BACXxC,MAAO,gBAGT5C,gBAAC8X,EACC,CAAA9X,SAAA,CAAA8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,KACRsU,UAAU,MACVrU,GAAI,CACF2kB,SAAU,EACV9qB,WAAY,IACZG,MAAOtC,EAAMG,QAAQC,QAAQC,KAC7BwL,OAAQ,WAEV5B,QAAS,IAAMsO,EAAS,KACzB7Y,SAAA,eAGA+kB,EACCje,EAAAC,IAACwG,EAAA,CACC3K,MAAM,UACN,aAAW,cACX+a,KAAK,QACLpT,QAAS0iB,EAETjtB,eAACwtB,GAAS,MAGXnkB,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,GACpDzJ,SAAA,CAAgBktB,EAAA3e,KAAKiL,GACpB1S,EAAAC,IAAC8O,EAAA,CAECtL,QAAS,IAAMsO,EAASW,EAAK2T,MAC7BvkB,GAAI,CACFhG,MAAO,eACPH,WAAY,IACZ,UAAW,CACTyK,QAAS,2BAIZlN,SAAKwZ,EAAAtQ,OAVDsQ,EAAKtQ,SAadpC,EAAAC,IAAC8O,EAAA,CACClN,QAAQ,YACR4B,QAAS,IAAMsO,EAAS,UACxBjQ,GAAI,CACFwe,GAAI,EACJ5jB,aAAc,EACdkG,GAAI,GAEP1J,SAAA,kBAOT8G,EAAAC,IAAC0mB,EAAA,CACC9kB,QAAQ,YACRob,KAAMgJ,EACN9I,QAASgJ,EACTS,WAAY,CACVC,aAAa,GAEf/kB,GAAI,CACFW,QAAS,CAAEkQ,GAAI,QAASqC,GAAI,QAC5B,qBAAsB,CAAE8R,UAAW,aAAcxgB,MAAO,MAGzDpN,SAAAotB,MAEL,EAKES,GAAc,KAClB,MAAMhV,EAAWC,KACXxY,EAAQ6I,KACP2kB,EAASC,GAAiBrC,GAAwB,CAAEC,UAAW,KAGpE,OAAA7kB,EAAAC,IAACuC,EAAA,CACC0kB,IAAKF,EACLllB,GAAI,CACFyM,UAAW,QACXjU,WAAY,2BAA2Bd,EAAMG,QAAQC,QAAQC,YAAYL,EAAMG,QAAQM,UAAUJ,aACjG4I,QAAS,OACTC,WAAY,SACZ6C,SAAU,WACVD,SAAU,SACV,YAAa,CACXE,QAAS,KACTD,SAAU,WACVE,IAAK,EACLC,KAAM,EACNC,MAAO,EACPC,OAAQ,EACRtL,WAAY,sQAIhBpB,eAACuhB,EAAU,CAAA9C,SAAS,KAAK7V,GAAI,CAAEyD,SAAU,WAAYM,OAAQ,GAC3D3M,gBAACoZ,EAAK,CAAAC,WAAS,EAAC/V,QAAS,EAAGkG,WAAW,SACrCxJ,SAAA,CAAA+G,MAACqS,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAC+G,MAAA8b,EAAA,CAAKC,GAAIiL,EAAehL,QAAS,IAChC/iB,gBAACsJ,EACC,CAAAtJ,SAAA,CAAA8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,KACRC,GAAI,CACFhG,MAAO,QACPH,WAAY,IACZsK,GAAI,EACJwR,WAAY,4BACZ/b,SAAU,CAAEiX,GAAI,SAAUqC,GAAI,SAAUnC,GAAI,SAE/C3Z,SAAA,eAGD8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,KACRC,GAAI,CACFhG,MAAO,wBACPmK,GAAI,EACJtK,WAAY,IACZD,SAAU,CAAEiX,GAAI,SAAUqC,GAAI,WAEjC9b,SAAA,mCAGD8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,KACRC,GAAI,CACFhG,MAAO,wBACPmK,GAAI,EACJrK,WAAY,IACZF,SAAU,CAAEiX,GAAI,OAAQqC,GAAI,WAE/B9b,SAAA,wIAIDqJ,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQE,IAAK,EAAGuU,SAAU,QAC5Che,SAAA,CAAA8G,EAAAC,IAAC8O,EAAA,CACClN,QAAQ,YACR2E,KAAK,QACL/C,QAAS,IAAMsO,EAAS,UACxBjQ,GAAI,CACFsE,QAAS,QACTtK,MAAOtC,EAAMG,QAAQC,QAAQC,KAC7B+I,GAAI,EACJC,GAAI,IACJnH,SAAU,SACVC,WAAY,IACZe,aAAc,EACd4B,UAAW,6BACX,UAAW,CACT8H,QAAS,wBACT/H,UAAW,mBACXC,UAAW,+BAGfmP,cAAU0Z,GAAiB,IAC5BjuB,SAAA,gBAGD8G,EAAAC,IAAC8O,EAAA,CACClN,QAAQ,WACR2E,KAAK,QACL/C,QAAS,IAAMsO,EAAS,aACxBjQ,GAAI,CACFhG,MAAO,QACP4d,YAAa,QACb9W,GAAI,EACJC,GAAI,IACJnH,SAAU,SACVC,WAAY,IACZe,aAAc,EACd,UAAW,CACT0J,QAAS,wBACTsT,YAAa,QACbrb,UAAW,qBAGhBnF,SAAA,yBAOR+G,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,WAAA+G,IAACmnB,GAAM9V,UAAU,OAAO0K,GAAIiL,EAAehL,QAAS,KAClD/iB,SAAA8G,EAAAC,IAACuC,EAAA,CACCV,GAAI,CACFW,QAAS,OACTuD,eAAgB,SAChBtD,WAAY,SACZX,OAAQ,CAAE4Q,GAAI,IAAKqC,GAAI,MAGzB9b,SAAA8G,EAAAC,IAAConB,EAAA,CACCjY,UAAW,EACXtN,GAAI,CACFiE,EAAG,EACHrJ,aAAc,EACd0J,QAAS,yBACTjI,eAAgB,aAChBwZ,SAAU,IACVrR,MAAO,QAGTpN,gBAACsJ,EAAI,CAAAV,GAAI,CAAEmT,UAAW,UACpB/b,SAAA,CAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFwE,MAAO,GACPvE,OAAQ,GACRqE,QAAS5M,EAAMG,QAAQM,UAAUJ,KACjCytB,GAAI,OACJrhB,GAAI,GAGN/M,eAACquB,GAAW,CAAAzlB,GAAI,CAAEpG,SAAU,QAE9BuE,MAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEmE,GAAI,EAAGtK,WAAY,KAAOzC,SAEzD,mCACA+G,MAAC+C,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAAiBgG,GAAI,CAAEmE,GAAI,GAAK/M,SAElE,oEACAqJ,OAACC,GAAIV,GAAI,CAAEW,QAAS,OAAQuD,eAAgB,gBAC1C9M,SAAA,CAAAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEmT,UAAW,UACpB/b,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAK/F,MAAM,UAAUgG,GAAI,CAAEnG,WAAY,KAAOzC,SAElE,UACC+G,MAAA+C,EAAA,CAAWnB,QAAQ,UAAU3I,SAAQ,uBAEvCsJ,EAAI,CAAAV,GAAI,CAAEmT,UAAW,UACpB/b,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAK/F,MAAM,YAAYgG,GAAI,CAAEnG,WAAY,KAAOzC,SAEpE,QACC+G,MAAA+C,EAAA,CAAWnB,QAAQ,UAAU3I,SAAO,sBAEtCsJ,EAAI,CAAAV,GAAI,CAAEmT,UAAW,UACpB/b,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAK/F,MAAM,eAAegG,GAAI,CAAEnG,WAAY,KAAOzC,SAEvE,QACC+G,MAAA+C,EAAA,CAAWnB,QAAQ,UAAU3I,SAAY,wCAU9D,EAKEsuB,GAAkB,KACtB,MAAMhuB,EAAQ6I,KACPolB,EAAaC,GAAqB9C,GAAwB,CAAEC,UAAW,KAExE8C,EAAW,CACf,CACEvkB,KAAM2Z,GACN7Z,MAAO,2BACPwF,YAAa,oHACb5M,MAAOtC,EAAMG,QAAQC,QAAQC,MAE/B,CACEuJ,KAAMmkB,GACNrkB,MAAO,sBACPwF,YAAa,6FACb5M,MAAOtC,EAAMG,QAAQM,UAAUJ,MAEjC,CACEuJ,KAAMuY,GACNzY,MAAO,uBACPwF,YAAa,uFACb5M,MAAOtC,EAAMG,QAAQO,QAAQL,MAE/B,CACEuJ,KAAMwkB,GACN1kB,MAAO,2BACPwF,YAAa,wGACb5M,MAAOtC,EAAMG,QAAQQ,QAAQN,OAK9BoG,aAAAuC,EAAA,CAAI0kB,IAAKO,EAAa3lB,GAAI,CAAEe,GAAI,EAAGuD,QAAS,sBAC3ClN,SAAC8G,EAAAuC,KAAAkY,EAAA,CAAU9C,SAAS,KAClBze,SAAA,CAAA+G,MAAC8b,EAAK,CAAAC,GAAI0L,EAAmBzL,QAAS,IACpC/iB,SAAAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEmT,UAAW,SAAUhP,GAAI,GAClC/M,SAAA,GAAA+G,IAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,EAAGnK,MAAO,gBAAkB5C,SAEhF,6CACC+G,IAAA+C,EAAA,CAAWnB,QAAQ,KAAK/F,MAAM,iBAAiBgG,GAAI,CAAE6V,SAAU,IAAK2P,GAAI,QAAUpuB,SAEnF,2HAGH+G,IAAAqS,EAAA,CAAKC,WAAS,EAAC/V,QAAS,EACtBtD,SAASyuB,EAAAlgB,KAAI,CAACogB,EAASpV,IACtBzS,EAAAC,IAACqS,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGoC,GAAI,EAC5B9b,eAAC4uB,EAAK,CAAA9L,GAAI0L,EAAmBzL,QAAS,IAAc,IAARxJ,EAC1CvZ,SAAA8G,EAAAuC,KAAC6C,EAAA,CACCtD,GAAI,CACFC,OAAQ,OACRkT,UAAW,SACXlP,EAAG,EACH3H,WAAY,uBACZ,UAAW,CACTC,UAAW,mBACXC,UAAW9E,EAAMqD,QAAQ,KAI7B3D,SAAA,CAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFwE,MAAO,GACPvE,OAAQ,GACRqE,QAASyhB,EAAQ/rB,MACjBwrB,GAAI,OACJrhB,GAAI,GAGN/M,SAAA+G,MAAC4nB,EAAQzkB,KAAR,CAAatB,GAAI,CAAEpG,SAAU,UAE/BuE,IAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GACjD/M,SAAA2uB,EAAQ3kB,cAEVF,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAC/B5C,WAAQwP,oBA7BqB+J,WAqC9C,EAKEsV,GAAuB,KACrB,MAACC,EAAWC,GAAmBrD,GAAwB,CAAEC,UAAW,KAGxE,OAAA7kB,EAAAC,IAACuC,EAAI,CAAA0kB,IAAKc,EAAWlmB,GAAI,CAAEe,GAAI,EAAGuD,QAAS,eAAgBtK,MAAO,SAChE5C,eAACuhB,EAAA,CAAU9C,SAAS,KAClBze,SAAC8G,EAAAuC,KAAA+P,EAAA,CAAKC,WAAS,EAAC/V,QAAS,EAAGkG,WAAW,SACrCxJ,SAAA,CAAA+G,MAACqS,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAC+G,MAAA8b,EAAA,CAAKC,GAAIiM,EAAiBhM,QAAS,IAClC/iB,gBAACsJ,EACC,CAAAtJ,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,iBACC+G,IAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEmE,GAAI,EAAGrK,WAAY,IAAK+K,QAAS,IAAOzN,SAIvE,qMACAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQE,IAAK,EAAGuU,SAAU,QAC5Che,SAAA,CAAA8G,EAAAC,IAAC8U,EAAA,CACC3S,MAAM,aACNN,GAAI,CAAEsE,QAAS,wBAAyBtK,MAAO,WAEjDkE,EAAAC,IAAC8U,EAAA,CACC3S,MAAM,uBACNN,GAAI,CAAEsE,QAAS,wBAAyBtK,MAAO,WAEjDkE,EAAAC,IAAC8U,EAAA,CACC3S,MAAM,kBACNN,GAAI,CAAEsE,QAAS,wBAAyBtK,MAAO,2BAMxDwW,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,eAACkuB,EAAA,CAAM9V,UAAU,OAAO0K,GAAIiM,EAAiBhM,QAAS,IACpD/iB,gBAACsJ,EACC,CAAAtJ,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,kBACC+G,IAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEmE,GAAI,EAAGrK,WAAY,IAAK+K,QAAS,IAAOzN,SAIvE,2LACAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQE,IAAK,EAAGuU,SAAU,QAC5Che,SAAA,CAAA8G,EAAAC,IAAC8U,EAAA,CACC3S,MAAM,sBACNN,GAAI,CAAEsE,QAAS,wBAAyBtK,MAAO,WAEjDkE,EAAAC,IAAC8U,EAAA,CACC3S,MAAM,sBACNN,GAAI,CAAEsE,QAAS,wBAAyBtK,MAAO,WAEjDkE,EAAAC,IAAC8U,EAAA,CACC3S,MAAM,yBACNN,GAAI,CAAEsE,QAAS,wBAAyBtK,MAAO,2BAQ/D,EAKEosB,GAAkB,KACtB,MAAM1uB,EAAQ6I,KACP8lB,EAAaC,GAAqBxD,GAAwB,CAAEC,UAAW,KAExEwD,EAAW,CACf,CACEnlB,MAAO,eACPwF,YAAa,4HACbif,SAAU,CAAC,4BAA6B,oBAAqB,uBAAwB,mBACrF7rB,MAAOtC,EAAMG,QAAQC,QAAQC,KAC7BuJ,KAAM,SAER,CACEF,MAAO,cACPwF,YAAa,0GACbif,SAAU,CAAC,kBAAmB,qBAAsB,0BAA2B,sBAC/E7rB,MAAOtC,EAAMG,QAAQM,UAAUJ,KAC/BuJ,KAAM,eAER,CACEF,MAAO,qBACPwF,YAAa,+FACbif,SAAU,CAAC,mBAAoB,sBAAuB,oBAAqB,sBAC3E7rB,MAAOtC,EAAMG,QAAQO,QAAQL,KAC7BuJ,KAAM,OAKPnD,aAAAuC,EAAA,CAAI0kB,IAAKiB,EAAarmB,GAAI,CAAEe,GAAI,EAAGuD,QAAS,oBAC3ClN,SAAC8G,EAAAuC,KAAAkY,EAAA,CAAU9C,SAAS,KAClBze,SAAA,CAAA+G,MAAC8b,EAAK,CAAAC,GAAIoM,EAAmBnM,QAAS,IACpC/iB,SAAAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEmT,UAAW,SAAUhP,GAAI,GAClC/M,SAAA,GAAA+G,IAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,EAAGnK,MAAO,gBAAkB5C,SAEhF,qCACC+G,IAAA+C,EAAA,CAAWnB,QAAQ,KAAK/F,MAAM,iBAAiBgG,GAAI,CAAE6V,SAAU,IAAK2P,GAAI,QAAUpuB,SAEnF,0GAGJ+G,IAACqS,EAAK,CAAAC,WAAS,EAAC/V,QAAS,EACtBtD,SAAAmvB,EAAS5gB,KAAI,CAAC6gB,EAAS7V,IACrBxS,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,WAAA+G,IAAC6nB,EAAK,CAAA9L,GAAIoM,EAAmBnM,QAAS,IAAc,IAARxJ,EAC1CvZ,SAAA8G,EAAAuC,KAAC6C,EAAA,CACCtD,GAAI,CACFC,OAAQ,OACRgE,EAAG,EACH3H,WAAY,uBACZF,OAAQ,wBACR,UAAW,CACTG,UAAW,mBACXC,UAAW9E,EAAMqD,QAAQ,IACzB6c,YAAa4O,EAAQxsB,QAIzB5C,SAAA,CAAAqJ,OAACC,GAAIV,GAAI,CAAEmT,UAAW,SAAUhP,GAAI,GAClC/M,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEmE,GAAI,GAChC/M,SAAAovB,EAAQllB,SAEVnD,IAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKG,MAAOwsB,EAAQxsB,OAC5D5C,WAAQgK,aAGZjD,IAAA+C,EAAA,CAAWnB,QAAQ,QAAQC,GAAI,CAAEmE,GAAI,EAAGrK,WAAY,KAClD1C,SAAAovB,EAAQ5f,oBAEVlG,EACE,CAAAtJ,SAAAovB,EAAQX,SAASlgB,KAAI,CAACogB,EAASU,IAC9BhmB,OAACC,EAAuB,CAAAV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUuD,GAAI,GACvE/M,SAAA,CAAC+G,MAAAuoB,GAAA,CAAS1mB,GAAI,CAAEpG,SAAU,GAAII,MAAOwsB,EAAQxsB,MAAOshB,GAAI,KACvDnd,MAAA+C,EAAA,CAAWnB,QAAQ,QAAS3I,SAAQ2uB,MAF7BU,aA5BW9V,WAwCvC,EAKEgW,GAAS,KACb,MAAM1W,EAAWC,KAGd/R,aAAAuC,EAAA,CAAIV,GAAI,CAAEsE,QAAS,WAAYtK,MAAO,QAAS+G,GAAI,GAClD3J,SAAC8G,EAAAuC,KAAAkY,EAAA,CAAU9C,SAAS,KAClBze,SAAA,CAAAqJ,OAAC+P,EAAK,CAAAC,WAAS,EAAC/V,QAAS,EACvBtD,SAAA,CAAAqJ,OAAC+P,GAAKI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,eACA+G,MAAC+C,EAAW,CAAAnB,QAAQ,QAAQC,GAAI,CAAEmE,GAAI,EAAGU,QAAS,IAAOzN,SAGzD,wIACAqJ,OAACC,GAAIV,GAAI,CAAEW,QAAS,OAAQE,IAAK,GAC/BzJ,SAAA,CAAA8G,EAAAC,IAAC8U,EAAA,CACC3S,MAAM,aACNoE,KAAK,QACL1E,GAAI,CAAEsE,QAAS,wBAAyBtK,MAAO,WAEjDkE,EAAAC,IAAC8U,EAAA,CACC3S,MAAM,cACNoE,KAAK,QACL1E,GAAI,CAAEsE,QAAS,wBAAyBtK,MAAO,iBAIrDyG,OAAC+P,GAAKI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGoC,GAAI,EAC5B9b,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,aACAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQ+U,cAAe,SAAU7U,IAAK,GACxDzJ,SAAA,CAAA8G,EAAAC,IAAC8O,EAAA,CACCjT,MAAM,UACNgG,GAAI,CAAEkE,eAAgB,aAAcD,EAAG,GACvCtC,QAAS,IAAMsO,EAAS,aACzB7Y,SAAA,aAGD8G,EAAAC,IAAC8O,EAAA,CACCjT,MAAM,UACNgG,GAAI,CAAEkE,eAAgB,aAAcD,EAAG,GACvCtC,QAAS,IAAMsO,EAAS,UACzB7Y,SAAA,UAGD8G,EAAAC,IAAC8O,EAAA,CACCjT,MAAM,UACNgG,GAAI,CAAEkE,eAAgB,aAAcD,EAAG,GACvCtC,QAAS,IAAMsO,EAAS,UACzB7Y,SAAA,gBAKLqJ,OAAC+P,GAAKI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGoC,GAAI,EAC5B9b,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,YACAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQ+U,cAAe,SAAU7U,IAAK,GACxDzJ,SAAA,CAAA8G,EAAAC,IAAC8O,EAAA,CACCjT,MAAM,UACNgG,GAAI,CAAEkE,eAAgB,aAAcD,EAAG,GACvCtC,QAAS,IAAMsO,EAAS,YACzB7Y,SAAA,eAGD8G,EAAAC,IAAC8O,EAAA,CACCjT,MAAM,UACNgG,GAAI,CAAEkE,eAAgB,aAAcD,EAAG,GACxC7M,SAAA,gBAGD8G,EAAAC,IAAC8O,EAAA,CACCjT,MAAM,UACNgG,GAAI,CAAEkE,eAAgB,aAAcD,EAAG,GACxC7M,SAAA,+BAKJoZ,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,iBACCqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,EAAGsD,GAAI,GAC5D/M,SAAA,CAAA+G,MAACyoB,GAAU,CAAA5mB,GAAI,CAAEpG,SAAU,MAC1BuE,MAAA+C,EAAA,CAAWnB,QAAQ,QAAQ3I,SAAsB,8BAEnDqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,EAAGsD,GAAI,GAC5D/M,SAAA,CAAA+G,MAAC0oB,GAAU,CAAA7mB,GAAI,CAAEpG,SAAU,MAC1BuE,MAAA+C,EAAA,CAAWnB,QAAQ,QAAQ3I,SAAe,uBAE7CqJ,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,GACrDzJ,SAAA,CAAA+G,MAAC2oB,GAAe,CAAA9mB,GAAI,CAAEpG,SAAU,MAC/BuE,MAAA+C,EAAA,CAAWnB,QAAQ,QAAQ3I,SAAa,2BAI/C8G,EAAAC,IAACuC,EAAA,CACCV,GAAI,CACFgF,UAAW,kCACXF,GAAI,EACJC,GAAI,EACJoO,UAAW,UAGb/b,SAAA+G,MAAC+C,GAAWnB,QAAQ,QAAQC,GAAI,CAAE6E,QAAS,IAAOzN,SAElD,sFAGN,EAIJ,SAAwB2vB,KACtB,cACGrmB,EACC,CAAAtJ,SAAA,CAAA8G,EAAAC,IAAC+lB,GAAiB,WACjBxjB,EAAI,CAAAV,GAAI,CAAE+E,GAAI,GAAK3N,SAAA,WACjB6tB,GAAY,UACZS,GAAgB,UAChBO,GAAqB,UACrBG,GAAgB,aAElBO,GAAO,MAGd,CCvsBA,MAAMK,GAAY,KAChB,MAAM3U,EAAEA,GAAMC,GAAe,UACvBrC,EAAWC,KACXxY,EAAQ6I,IAER0mB,EAAS,CACb,CACE3lB,KAAMmkB,GACNrkB,MAAO,yBACPwF,YAAa,qHACb5M,MAAOtC,EAAMG,QAAQC,QAAQC,MAE/B,CACEuJ,KAAM4lB,GACN9lB,MAAO,wBACPwF,YAAa,yGACb5M,MAAOtC,EAAMG,QAAQM,UAAUJ,MAEjC,CACEuJ,KAAM6lB,GACN/lB,MAAO,yBACPwF,YAAa,qGACb5M,MAAOtC,EAAMG,QAAQO,QAAQL,MAE/B,CACEuJ,KAAMuY,GACNzY,MAAO,yBACPwF,YAAa,0FACb5M,MAAOtC,EAAMG,QAAQQ,QAAQN,OAWjC,cACG2I,EAEC,CAAAtJ,SAAA,CAAA+G,MAACuC,EAAI,CAAAV,GAAI,CAAEsE,QAAS,eAAgBtK,MAAO,QAAS+G,GAAI,GACtD3J,SAAC8G,EAAAuC,KAAAkY,EAAA,CAAU9C,SAAS,KAClBze,SAAA,CAAA8G,EAAAC,IAAC8O,EAAA,CACCvB,gBAAY0b,GAAc,IAC1BzlB,QAAS,IAAMsO,EAAS,KACxBjQ,GAAI,CAAEhG,MAAO,QAASmK,GAAI,GAC3B/M,SAAA,iBAGD+G,MAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,qBACA+G,MAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAE6E,QAAS,GAAKgR,SAAU,KAAOze,SAG9D,gKAKHsJ,EAAI,CAAAV,GAAI,CAAEe,GAAI,EAAGuD,QAAS,sBACzBlN,SAAC+G,MAAAwa,EAAA,CAAU9C,SAAS,KAClBze,SAAAqJ,OAAC+P,GAAKC,WAAS,EAAC/V,QAAS,EACvBtD,SAAA,CAAC+G,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAC+G,MAAA8b,EAAA,CAAKC,IAAE,EAACC,QAAS,IAChB/iB,SAAAqJ,OAAC8kB,EAAM,CAAAjY,UAAW,EAAGtN,GAAI,CAAEiE,EAAG,EAAGhE,OAAQ,QACvC7I,SAAA,GAAA+G,IAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,EAAGnK,MAAO,gBAAkB5C,SAEhF,gBACA+G,MAAC+C,EAAW,CAAAnB,QAAQ,QAAQC,GAAI,CAAElG,WAAY,IAAKqK,GAAI,GAAK/M,SAI5D,4LACA+G,MAAC+C,GAAWnB,QAAQ,QAAQC,GAAI,CAAElG,WAAY,KAAO1C,SAIrD,iNAIN+G,MAACqS,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAC+G,MAAA8b,EAAA,CAAKC,IAAE,EAACC,QAAS,IAChB/iB,SAAAqJ,OAAC8kB,EAAM,CAAAjY,UAAW,EAAGtN,GAAI,CAAEiE,EAAG,EAAGhE,OAAQ,QACvC7I,SAAA,GAAA+G,IAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,EAAGnK,MAAO,kBAAoB5C,SAElF,eACA+G,MAAC+C,EAAW,CAAAnB,QAAQ,QAAQC,GAAI,CAAElG,WAAY,IAAKqK,GAAI,GAAK/M,SAI5D,mLACA+G,MAAC+C,GAAWnB,QAAQ,QAAQC,GAAI,CAAElG,WAAY,KAAO1C,SAGrD,2KASX+G,IAAAuC,EAAA,CAAIV,GAAI,CAAEe,GAAI,EAAGuD,QAAS,oBACzBlN,SAAAqJ,OAACkY,EAAU,CAAA9C,SAAS,KAClBze,SAAA,GAAA+G,IAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEmT,UAAW,SAAUtZ,WAAY,IAAKsK,GAAI,GAAK/M,SAE9E,sBACC+G,IAAAqS,EAAA,CAAKC,WAAS,EAAC/V,QAAS,EACtBtD,SAAO6vB,EAAAthB,KAAI,CAAC3H,EAAO2S,IAClBzS,EAAAC,IAACqS,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGoC,GAAI,EAC5B9b,eAAC6iB,EAAK,CAAAC,IAAE,EAACC,QAAS,IAAc,IAARxJ,EACtBvZ,SAAA8G,EAAAuC,KAAC6C,EAAA,CACCtD,GAAI,CACFC,OAAQ,OACRkT,UAAW,SACXlP,EAAG,EACH3H,WAAY,uBACZ,UAAW,CACTC,UAAW,mBACXC,UAAW9E,EAAMqD,QAAQ,KAI7B3D,SAAA,CAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFwE,MAAO,GACPvE,OAAQ,GACRqE,QAAStG,EAAMhE,MACfwrB,GAAI,OACJrhB,GAAI,GAGN/M,SAAA+G,MAACH,EAAMsD,KAAN,CAAWtB,GAAI,CAAEpG,SAAU,UAE7BuE,IAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GACjD/M,SAAA4G,EAAMoD,cAERF,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAC/B5C,WAAMwP,oBA7BuB+J,YAwC7CxS,MAAAuC,EAAA,CAAIV,GAAI,CAAEe,GAAI,EAAGuD,QAAS,eAAgBtK,MAAO,SAChD5C,SAAC8G,EAAAuC,KAAAkY,EAAA,CAAU9C,SAAS,KAClBze,SAAA,GAAA+G,IAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEmT,UAAW,SAAUtZ,WAAY,IAAKsK,GAAI,GAAK/M,SAE9E,eACC+G,MAAAqS,EAAA,CAAKC,WAAS,EAAC/V,QAAS,EACtBtD,SAhIG,CACZ,CAAEiwB,OAAQ,QAAS/mB,MAAO,qBAC1B,CAAE+mB,OAAQ,MAAO/mB,MAAO,mBACxB,CAAE+mB,OAAQ,MAAO/mB,MAAO,qBACxB,CAAE+mB,OAAQ,IAAK/mB,MAAO,uBA4HPqF,KAAI,CAAC+K,EAAMC,IACfxS,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,EAAGqC,GAAI,EACpB9b,WAAA+G,IAAC8b,EAAK,CAAAC,IAAE,EAACC,QAAS,IAAc,IAARxJ,EACtBvZ,gBAACsJ,EAAI,CAAAV,GAAI,CAAEmT,UAAW,UACpB/b,SAAA,GAAC+G,IAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GACjD/M,SAAAsZ,EAAK2W,SAERlpB,MAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAE6E,QAAS,IACrCzN,SAAAsZ,EAAKpQ,cAPgBqQ,YAkBrCxS,MAAAuC,EAAA,CAAIV,GAAI,CAAEe,GAAI,EAAGuD,QAAS,qBAAsB6O,UAAW,UAC1D/b,SAAC8G,EAAAuC,KAAAkY,EAAA,CAAU9C,SAAS,KAClBze,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,kCACA+G,MAAC+C,EAAW,CAAAnB,QAAQ,KAAK/F,MAAM,iBAAiBgG,GAAI,CAAEmE,GAAI,GAAK/M,SAE/D,8FACCqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQE,IAAK,EAAGqD,eAAgB,SAAUkR,SAAU,QACtEhe,SAAA,CAAA8G,EAAAC,IAAC8O,EAAA,CACClN,QAAQ,YACR2E,KAAK,QACL/C,QAAS,IAAMsO,EAAS,UACxBjQ,GAAI,CAAEc,GAAI,EAAGC,GAAI,KAClB3J,SAAA,sBAGD8G,EAAAC,IAAC8O,EAAA,CACClN,QAAQ,WACR2E,KAAK,QACL/C,QAAS,IAAMsO,EAAS,YACxBjQ,GAAI,CAAEc,GAAI,EAAGC,GAAI,KAClB3J,SAAA,yBAMT,ECpMEkwB,GAAe,KACnB,MAAMjV,EAAEA,GAAMC,GAAe,UACvBrC,EAAWC,KACXxY,EAAQ6I,IAERgnB,EAAe,CACnB,CACEjmB,KAAM2Z,GACN7Z,MAAO,2BACPwF,YAAa,2GACbif,SAAU,CACR,oCACA,+BACA,sBACA,oBACA,gCAEF7rB,MAAOtC,EAAMG,QAAQC,QAAQC,MAE/B,CACEuJ,KAAMmkB,GACNrkB,MAAO,sBACPwF,YAAa,wFACbif,SAAU,CACR,4BACA,4BACA,4BACA,4BACA,4BAEF7rB,MAAOtC,EAAMG,QAAQM,UAAUJ,MAEjC,CACEuJ,KAAMuY,GACNzY,MAAO,uBACPwF,YAAa,oFACbif,SAAU,CACR,iBACA,wBACA,sBACA,yBACA,wBAEF7rB,MAAOtC,EAAMG,QAAQO,QAAQL,MAE/B,CACEuJ,KAAMwkB,GACN1kB,MAAO,6BACPwF,YAAa,4EACbif,SAAU,CACR,oBACA,gBACA,kBACA,oBACA,0BAEF7rB,MAAOtC,EAAMG,QAAQQ,QAAQN,OAI3ByvB,EAAqB,CACzB,CACElmB,KAAMmmB,GACNrmB,MAAO,yBACPwF,YAAa,wEAEf,CACEtF,KAAMomB,GACNtmB,MAAO,gBACPwF,YAAa,wEAEf,CACEtF,KAAMqmB,GACNvmB,MAAO,oBACPwF,YAAa,6DAEf,CACEtF,KAAMsmB,GACNxmB,MAAO,qBACPwF,YAAa,wEAEf,CACEtF,KAAM4lB,GACN9lB,MAAO,sBACPwF,YAAa,4EAEf,CACEtF,KAAMumB,GACNzmB,MAAO,uBACPwF,YAAa,mFAIjB,cACGlG,EAEC,CAAAtJ,SAAA,CAAA+G,MAACuC,EAAI,CAAAV,GAAI,CAAEsE,QAAS,eAAgBtK,MAAO,QAAS+G,GAAI,GACtD3J,SAAC8G,EAAAuC,KAAAkY,EAAA,CAAU9C,SAAS,KAClBze,SAAA,CAAA8G,EAAAC,IAAC8O,EAAA,CACCvB,gBAAY0b,GAAc,IAC1BzlB,QAAS,IAAMsO,EAAS,KACxBjQ,GAAI,CAAEhG,MAAO,QAASmK,GAAI,GAC3B/M,SAAA,iBAGD+G,MAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,sBACA+G,MAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAE6E,QAAS,GAAKgR,SAAU,KAAOze,SAG9D,oIAKH+G,IAAAuC,EAAA,CAAIV,GAAI,CAAEe,GAAI,EAAGuD,QAAS,sBACzBlN,SAAAqJ,OAACkY,EAAU,CAAA9C,SAAS,KAClBze,SAAA,GAAA+G,IAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEmT,UAAW,SAAUtZ,WAAY,IAAKsK,GAAI,GAAK/M,SAE9E,oBACA+G,IAACqS,EAAK,CAAAC,WAAS,EAAC/V,QAAS,EACtBtD,SAAAmwB,EAAa5hB,KAAI,CAACogB,EAASpV,IACzBxS,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,WAAA+G,IAAC8b,EAAK,CAAAC,IAAE,EAACC,QAAS,IAAc,IAARxJ,EACtBvZ,SAAA8G,EAAAuC,KAAC6C,EAAA,CACCtD,GAAI,CACFC,OAAQ,OACRgE,EAAG,EACH3H,WAAY,uBACZ,UAAW,CACTC,UAAW,mBACXC,UAAW9E,EAAMqD,QAAQ,KAI7B3D,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,aAAcuD,GAAI,GACxD/M,SAAA,CAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFwE,MAAO,GACPvE,OAAQ,GACRqE,QAASyhB,EAAQ/rB,MACjBshB,GAAI,GAGNlkB,SAAA+G,MAAC4nB,EAAQzkB,KAAR,CAAatB,GAAI,CAAEpG,SAAU,eAE/B8G,EACC,CAAAtJ,SAAA,GAAC+G,IAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GACjD/M,SAAA2uB,EAAQ3kB,cAEVF,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAC/B5C,WAAQwP,0BAId+Z,EAAK,CAAAC,OAAK,EACRxpB,SAAA2uB,EAAQF,SAASlgB,KAAI,CAACiL,EAAMkX,WAC1BjH,EAAyB,CAAA7gB,GAAI,CAAEc,GAAI,GAClC1J,SAAA,CAAA+G,MAAC4pB,GAAa/nB,GAAI,CAAEmS,SAAU,IAC5B/a,eAAC4wB,GAAA,CAAgBhoB,GAAI,CAAEpG,SAAU,GAAII,MAAO+rB,EAAQ/rB,WAEtDkE,EAAAC,IAAC2iB,EAAA,CACChpB,QAAS8Y,EACTqX,uBAAwB,CAAEloB,QAAS,aANxB+nB,aAnCMnX,cAuDtCxS,IAAAuC,EAAA,CAAIV,GAAI,CAAEe,GAAI,EAAGuD,QAAS,oBACzBlN,SAAAqJ,OAACkY,EAAU,CAAA9C,SAAS,KAClBze,SAAA,GAAA+G,IAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEmT,UAAW,SAAUtZ,WAAY,IAAKsK,GAAI,GAAK/M,SAE9E,8BACC+G,IAAAqS,EAAA,CAAKC,WAAS,EAAC/V,QAAS,EACtBtD,SAAmBowB,EAAA7hB,KAAI,CAACogB,EAASpV,IAChCzS,EAAAC,IAACqS,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGoC,GAAI,EAC5B9b,eAAC6iB,EAAK,CAAAC,IAAE,EAACC,QAAS,IAAc,IAARxJ,EACtBvZ,SAAA8G,EAAAuC,KAAC6C,EAAA,CACCtD,GAAI,CACFC,OAAQ,OACRkT,UAAW,SACXlP,EAAG,EACH3H,WAAY,uBACZ,UAAW,CACTC,UAAW,mBACXC,UAAW9E,EAAMqD,QAAQ,KAI7B3D,SAAA,CAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFwE,MAAO,GACPvE,OAAQ,GACRqE,QAAS,eACTkhB,GAAI,OACJrhB,GAAI,GAGN/M,SAAA+G,MAAC4nB,EAAQzkB,KAAR,CAAatB,GAAI,CAAEpG,SAAU,UAE/BuE,IAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GACjD/M,SAAA2uB,EAAQ3kB,cAEVF,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAC/B5C,WAAQwP,oBA7BqB+J,YAwC9CxS,MAACuC,GAAIV,GAAI,CAAEe,GAAI,EAAGuD,QAAS,iBAAkBtK,MAAO,SAClD5C,eAACuhB,EAAU,CAAA9C,SAAS,KAClBze,WAACqJ,KAAA+P,EAAA,CAAKC,WAAS,EAAC/V,QAAS,EAAGkG,WAAW,SACrCxJ,SAAA,CAAAqJ,OAAC+P,GAAKI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,+BACA+G,MAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEmE,GAAI,EAAGU,QAAS,IAAOzN,SAGtD,8HACAqJ,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQE,IAAK,EAAGuU,SAAU,QAC5Che,SAAA,CAAA8G,EAAAC,IAAC8U,EAAA,CACC3S,MAAM,eACNN,GAAI,CAAEsE,QAAS,wBAAyBtK,MAAO,WAEjDkE,EAAAC,IAAC8U,EAAA,CACC3S,MAAM,kBACNN,GAAI,CAAEsE,QAAS,wBAAyBtK,MAAO,WAEjDkE,EAAAC,IAAC8U,EAAA,CACC3S,MAAM,oBACNN,GAAI,CAAEsE,QAAS,wBAAyBtK,MAAO,WAEjDkE,EAAAC,IAAC8U,EAAA,CACC3S,MAAM,iBACNN,GAAI,CAAEsE,QAAS,wBAAyBtK,MAAO,uBAIpDwW,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,WAACqJ,KAAA8kB,EAAA,CAAMjY,UAAW,EAAGtN,GAAI,CAAEiE,EAAG,GAC5B7M,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,wCACCupB,EACC,CAAAvpB,SAAA,QAACypB,EACC,CAAAzpB,SAAA,OAAC2wB,EACC,CAAA3wB,SAAA+G,MAAC6pB,GAAgB,CAAAhuB,MAAM,gBAEzBmE,IAAC2iB,EAAa,CAAAhpB,QAAQ,0DAEvB+oB,EACC,CAAAzpB,SAAA,OAAC2wB,EACC,CAAA3wB,SAAA+G,MAAC6pB,GAAgB,CAAAhuB,MAAM,gBAEzBmE,IAAC2iB,EAAa,CAAAhpB,QAAQ,+DAEvB+oB,EACC,CAAAzpB,SAAA,OAAC2wB,EACC,CAAA3wB,SAAA+G,MAAC6pB,GAAgB,CAAAhuB,MAAM,gBAEzBmE,IAAC2iB,EAAa,CAAAhpB,QAAQ,wDAEvB+oB,EACC,CAAAzpB,SAAA,OAAC2wB,EACC,CAAA3wB,SAAA+G,MAAC6pB,GAAgB,CAAAhuB,MAAM,gBAEzBmE,IAAC2iB,EAAa,CAAAhpB,QAAQ,uDAUnCqG,MAAAuC,EAAA,CAAIV,GAAI,CAAEe,GAAI,EAAGuD,QAAS,qBAAsB6O,UAAW,UAC1D/b,SAAC8G,EAAAuC,KAAAkY,EAAA,CAAU9C,SAAS,KAClBze,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,uCACA+G,MAAC+C,EAAW,CAAAnB,QAAQ,KAAK/F,MAAM,iBAAiBgG,GAAI,CAAEmE,GAAI,GAAK/M,SAE/D,4DACCqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQE,IAAK,EAAGqD,eAAgB,SAAUkR,SAAU,QACtEhe,SAAA,CAAA8G,EAAAC,IAAC8O,EAAA,CACClN,QAAQ,YACR2E,KAAK,QACL/C,QAAS,IAAMsO,EAAS,UACxBjQ,GAAI,CAAEc,GAAI,EAAGC,GAAI,KAClB3J,SAAA,mBAGD8G,EAAAC,IAAC8O,EAAA,CACClN,QAAQ,WACR2E,KAAK,QACL/C,QAAS,IAAMsO,EAAS,YACxBjQ,GAAI,CAAEc,GAAI,EAAGC,GAAI,KAClB3J,SAAA,2BAMT,ECtUE8wB,GAAc,KAClB,MAAM7V,EAAEA,GAAMC,GAAe,UACvBrC,EAAWC,KACXxY,EAAQ6I,KAEPwT,EAAUC,GAAezc,YAAS,CACvC6P,KAAM,GACNM,MAAO,GACPygB,aAAc,GACdviB,QAAS,GACTwiB,QAAS,MAEJjc,EAAaC,GAAkB7U,aAAS,GAEzC8wB,EAAqB/qB,IACb0W,EAAA,IACPD,EACH,CAACzW,EAAE8W,OAAOhN,MAAO9J,EAAE8W,OAAOpW,OAC3B,EAiBGsqB,EAAc,CAClB,CACEhnB,KAAMslB,GACNxlB,MAAO,WACPtJ,QAAS,yBACTK,UAAW,uBACX6B,MAAOtC,EAAMG,QAAQC,QAAQC,MAE/B,CACEuJ,KAAMulB,GACNzlB,MAAO,UACPtJ,QAAS,kBACTK,UAAW,kBACX6B,MAAOtC,EAAMG,QAAQM,UAAUJ,MAEjC,CACEuJ,KAAMwlB,GACN1lB,MAAO,WACPtJ,QAAS,sBACTK,UAAW,iBACX6B,MAAOtC,EAAMG,QAAQO,QAAQL,MAE/B,CACEuJ,KAAMinB,GACNnnB,MAAO,iBACPtJ,QAAS,+BACTK,UAAW,0BACX6B,MAAOtC,EAAMG,QAAQQ,QAAQN,OAI3BywB,EAAiB,CACrB,CACElnB,KAAMmnB,GACNrnB,MAAO,oBACPwF,YAAa,uEACbsL,OAAQ,eAEV,CACE5Q,KAAMonB,GACNtnB,MAAO,gBACPwF,YAAa,kFACbsL,OAAQ,iBAEV,CACE5Q,KAAMmkB,GACNrkB,MAAO,0BACPwF,YAAa,sEACbsL,OAAQ,oBAIZ,cACGxR,EAEC,CAAAtJ,SAAA,CAAA+G,MAACuC,EAAI,CAAAV,GAAI,CAAEsE,QAAS,eAAgBtK,MAAO,QAAS+G,GAAI,GACtD3J,SAAC8G,EAAAuC,KAAAkY,EAAA,CAAU9C,SAAS,KAClBze,SAAA,CAAA8G,EAAAC,IAAC8O,EAAA,CACCvB,gBAAY0b,GAAc,IAC1BzlB,QAAS,IAAMsO,EAAS,KACxBjQ,GAAI,CAAEhG,MAAO,QAASmK,GAAI,GAC3B/M,SAAA,iBAGD+G,MAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,eACA+G,MAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAE6E,QAAS,GAAKgR,SAAU,KAAOze,SAG9D,8IAKH+G,IAAAuC,EAAA,CAAIV,GAAI,CAAEe,GAAI,EAAGuD,QAAS,sBACzBlN,SAAAqJ,OAACkY,EAAU,CAAA9C,SAAS,KAClBze,SAAA,GAAA+G,IAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEmT,UAAW,SAAUtZ,WAAY,IAAKsK,GAAI,GAAK/M,SAE9E,mBACC+G,IAAAqS,EAAA,CAAKC,WAAS,EAAC/V,QAAS,EACtBtD,SAAYkxB,EAAA3iB,KAAI,CAACpN,EAAMoY,IACtBzS,EAAAC,IAACqS,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGoC,GAAI,EAC5B9b,eAAC6iB,EAAK,CAAAC,IAAE,EAACC,QAAS,IAAc,IAARxJ,EACtBvZ,SAAA8G,EAAAuC,KAAC6C,EAAA,CACCtD,GAAI,CACFC,OAAQ,OACRkT,UAAW,SACXlP,EAAG,EACH3H,WAAY,uBACZ,UAAW,CACTC,UAAW,mBACXC,UAAW9E,EAAMqD,QAAQ,KAI7B3D,SAAA,CAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFwE,MAAO,GACPvE,OAAQ,GACRqE,QAAS/L,EAAKyB,MACdwrB,GAAI,OACJrhB,GAAI,GAGN/M,SAAA+G,MAAC5F,EAAK+I,KAAL,CAAUtB,GAAI,CAAEpG,SAAU,UAE5BuE,IAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GACjD/M,SAAAmB,EAAK6I,QAERjD,MAAC+C,EAAW,CAAAnB,QAAQ,QAAQC,GAAI,CAAEmE,GAAI,GACnC/M,SAAAmB,EAAKT,gBAEPoJ,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAC/B5C,WAAKe,kBAhCwBwY,kBA2C7CjQ,EAAI,CAAAV,GAAI,CAAEe,GAAI,EAAGuD,QAAS,oBACzBlN,SAAC+G,MAAAwa,EAAA,CAAU9C,SAAS,KAClBze,SAAAqJ,OAAC+P,GAAKC,WAAS,EAAC/V,QAAS,EAEvBtD,SAAA,CAAA+G,MAACqS,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,WAACqJ,KAAA8kB,EAAA,CAAMjY,UAAW,EAAGtN,GAAI,CAAEiE,EAAG,GAC5B7M,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,wBACA+G,IAACuC,EAAI,CAAA2T,UAAU,OAAOT,SApJdtW,IACpBA,EAAEgX,iBAGFlI,GAAe,GACH4H,EAAA,CACV5M,KAAM,GACNM,MAAO,GACPygB,aAAc,GACdviB,QAAS,GACTwiB,QAAS,IACV,EA0IahxB,SAAAqJ,OAAC+P,EAAK,CAAAC,WAAS,EAAC/V,QAAS,EACvBtD,SAAA,CAAA+G,MAACqS,GAAKI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB1Z,SAAA8G,EAAAC,IAACoW,EAAA,CACC3I,WAAS,EACTtL,MAAM,YACN8G,KAAK,OACLpJ,MAAO+V,EAAS3M,KAChBoN,SAAU6T,EACVM,UAAQ,YAGXnY,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIC,GAAI,EACrB1Z,SAAA8G,EAAAC,IAACoW,EAAA,CACC3I,WAAS,EACTtL,MAAM,gBACN8G,KAAK,QACLpE,KAAK,QACLhF,MAAO+V,EAASrM,MAChB8M,SAAU6T,EACVM,UAAQ,MAGXxqB,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,GACbzZ,SAAA8G,EAAAC,IAACoW,EAAA,CACC3I,WAAS,EACTtL,MAAM,sBACN8G,KAAK,eACLpJ,MAAO+V,EAASoU,aAChB3T,SAAU6T,MAGblqB,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,GACbzZ,SAAA8G,EAAAC,IAACoW,EAAA,CACC3I,WAAS,EACTtL,MAAM,UACN8G,KAAK,UACLpJ,MAAO+V,EAASnO,QAChB4O,SAAU6T,EACVM,UAAQ,MAGXxqB,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,GACbzZ,SAAA8G,EAAAC,IAACoW,EAAA,CACC3I,WAAS,EACTtL,MAAM,UACN8G,KAAK,UACLwhB,WAAS,EACTC,KAAM,EACN7qB,MAAO+V,EAASqU,QAChB5T,SAAU6T,EACVM,UAAQ,MAGXxqB,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,GACbzZ,SAAA8G,EAAAC,IAAC8O,EAAA,CACCjK,KAAK,SACLjD,QAAQ,YACR2E,KAAK,QACLiH,cAAUmd,GAAS,IACnB9oB,GAAI,CAAEc,GAAI,EAAGC,GAAI,KAClB3J,SAAA,oCAUVoZ,EAAK,CAAAI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,qBACCoxB,EAAe7iB,KAAI,CAACojB,EAAQpY,IAC1BxS,MAAA8b,EAAA,CAAKC,IAAE,EAACC,QAAS,IAAc,IAARxJ,EACtBvZ,eAACkM,EAAK,CAAAtD,GAAI,CAAEmE,GAAI,EAAGF,EAAG,GACpB7M,gBAACsJ,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQC,WAAY,aAAcuD,GAAI,GACxD/M,SAAA,CAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFwE,MAAO,GACPvE,OAAQ,GACRqE,QAAS,eACTgX,GAAI,GAGNlkB,SAAA+G,MAAC4qB,EAAOznB,KAAP,CAAYtB,GAAI,CAAEpG,SAAU,eAE9B8G,EACC,CAAAtJ,SAAA,GAAC+G,IAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GACjD/M,SAAA2xB,EAAO3nB,UAETjD,IAAA+C,EAAA,CAAWnB,QAAQ,QAAQ/F,MAAM,iBAAiBgG,GAAI,CAAEmE,GAAI,GAC1D/M,SAAA2xB,EAAOniB,cAEV1I,EAAAC,IAAC8O,EAAA,CACClN,QAAQ,WACR2E,KAAK,QACL1E,GAAI,CAAEvF,cAAe,QAEpBrD,SAAO2xB,EAAA7W,kBAzBwBvB,iBAsCnDxS,IAAAuC,EAAA,CAAIV,GAAI,CAAEe,GAAI,EAAGuD,QAAS,sBACzBlN,SAAAqJ,OAACkY,EAAU,CAAA9C,SAAS,KAClBze,SAAA,GAAA+G,IAAC+C,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEmT,UAAW,SAAUtZ,WAAY,IAAKsK,GAAI,GAAK/M,SAE9E,+BACCqJ,OAAA+P,EAAA,CAAKC,WAAS,EAAC/V,QAAS,EACvBtD,SAAA,CAAA+G,MAACqS,EAAK,CAAAI,MAAI,EAACC,GAAI,GACbzZ,SAAAqJ,OAAC8kB,EAAM,CAAAjY,UAAW,EAAGtN,GAAI,CAAEiE,EAAG,GAC5B7M,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,mDACC8J,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAAiB5C,SAGnD,iKAGH+G,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,GACbzZ,SAAAqJ,OAAC8kB,EAAM,CAAAjY,UAAW,EAAGtN,GAAI,CAAEiE,EAAG,GAC5B7M,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,kDACC8J,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAAiB5C,SAGnD,8IAGH+G,MAAAqS,EAAA,CAAKI,MAAI,EAACC,GAAI,GACbzZ,SAAAqJ,OAAC8kB,EAAM,CAAAjY,UAAW,EAAGtN,GAAI,CAAEiE,EAAG,GAC5B7M,SAAA,CAAC+G,MAAA+C,EAAA,CAAWnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GAAK/M,SAEzD,uDACC8J,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAAiB5C,SAGnD,6IAQV8G,EAAAC,IAAC6qB,EAAA,CACC7N,KAAMhP,EACN8c,iBAAkB,IAClB5N,QAAS,IAAMjP,GAAe,GAC9B8c,aAAc,CAAEC,SAAU,SAAUC,WAAY,UAEhDhyB,SAAA8G,EAAAC,IAACkrB,EAAA,CACChO,QAAS,IAAMjP,GAAe,GAC9BzF,SAAS,UACT3G,GAAI,CAAEwE,MAAO,QACdpN,SAAA,2EAIL,ECnVEkyB,GAAe,KACnB,MAAM5xB,EAAQ6I,IACRgpB,GAAc,IAAI7iB,MAAO8iB,cAyCzBC,EAAc,CAClB,CAAEnoB,KAAMiV,GAAUmT,KAAM,IAAKppB,MAAO,YACpC,CAAEgB,KAAMqoB,GAASD,KAAM,IAAKppB,MAAO,WACnC,CAAEgB,KAAMsoB,GAAUF,KAAM,IAAKppB,MAAO,YACpC,CAAEgB,KAAMuoB,GAAWH,KAAM,IAAKppB,MAAO,aACrC,CAAEgB,KAAMwoB,GAASJ,KAAM,IAAKppB,MAAO,YAInC,OAAApC,EAAAC,IAACuC,EAAA,CACC2T,UAAU,SACVrU,GAAI,CACFxH,WAAmC,SAAvBd,EAAMG,QAAQR,KACtB,2BAA2B8I,EAAM,UAAW,YAAaA,EAAM,UAAW,aAC1E,2BAA2BA,EAAM,UAAW,YAAaA,EAAM,UAAW,aAC9E9D,eAAgB,aAChB2I,UAAW,aAAa7E,EAAMzI,EAAMG,QAAQkB,QAAS,MACrD+L,GAAI,OACJ/D,GAAI,GAGN3J,SAAA8G,EAAAuC,KAACkY,EAAU,CAAA9C,SAAS,KAClBze,SAAA,CAAAqJ,OAAC+P,EAAK,CAAAC,WAAS,EAAC/V,QAAS,EAEvBtD,SAAA,CAAA+G,MAACqS,GAAKI,MAAI,EAACC,GAAI,GAAIqC,GAAI,EACrB9b,SAAA8G,EAAAC,IAACsE,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAG/B,EAAG,IAC1BinB,YAAa,CAAEllB,QAAS,EAAG/B,EAAG,GAC9BxG,WAAY,CAAEd,SAAU,IACxBwuB,SAAU,CAAE/G,MAAM,GAElB7rB,gBAACsJ,EAAI,CAAAV,GAAI,CAAEmE,GAAI,GACb/M,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,EAAGsD,GAAI,GAC5D/M,SAAA,CAAA8G,EAAAC,IAACgR,GAAA,CACCnP,GAAI,CACFpG,SAAU,GACVI,MAAOtC,EAAMG,QAAQC,QAAQC,QAGjCmG,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,KACRC,GAAI,CACFnG,WAAY,IACZG,MAAOtC,EAAMG,QAAQgB,KAAKf,SAE7BV,SAAA,kBAIH8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,QACRC,GAAI,CACFhG,MAAOtC,EAAMG,QAAQgB,KAAKV,UAC1B2B,WAAY,IACZqK,GAAI,GAEP/M,SAAA,0IAMDqJ,KAAC8O,EAAM,CAAA7U,QAAS,IACdtD,SAAA,CAACqJ,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,KACrDzJ,SAAA,OAAC6yB,IAAWjqB,GAAI,CACdpG,SAAU,GACVI,MAAOtC,EAAMG,QAAQC,QAAQC,KAC7B8M,QAAS,MAEX3G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,QACRC,GAAI,CACFhG,MAA8B,SAAvBtC,EAAMG,QAAQR,KACjBK,EAAMG,QAAQgB,KAAKf,QACnBJ,EAAMG,QAAQgB,KAAKV,UACvB0B,WAAY,IACZgL,QAAgC,SAAvBnN,EAAMG,QAAQR,KAAkB,GAAM,IAElDD,SAAA,wBAIHqJ,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,KACrDzJ,SAAA,OAAC8yB,IAAMlqB,GAAI,CACTpG,SAAU,GACVI,MAAOtC,EAAMG,QAAQC,QAAQC,KAC7B8M,QAAS,MAEX3G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,QACRC,GAAI,CACFhG,MAA8B,SAAvBtC,EAAMG,QAAQR,KACjBK,EAAMG,QAAQgB,KAAKf,QACnBJ,EAAMG,QAAQgB,KAAKV,UACvB0B,WAAY,IACZgL,QAAgC,SAAvBnN,EAAMG,QAAQR,KAAkB,GAAM,IAElDD,SAAA,sBAIHqJ,OAACC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,KACrDzJ,SAAA,OAACwd,IAAM5U,GAAI,CACTpG,SAAU,GACVI,MAAOtC,EAAMG,QAAQC,QAAQC,KAC7B8M,QAAS,MAEX3G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,QACRC,GAAI,CACFhG,MAA8B,SAAvBtC,EAAMG,QAAQR,KACjBK,EAAMG,QAAQgB,KAAKf,QACnBJ,EAAMG,QAAQgB,KAAKV,UACvB0B,WAAY,IACZgL,QAAgC,SAAvBnN,EAAMG,QAAQR,KAAkB,GAAM,IAElDD,SAAA,wCA3JI,CACrB,CACEgK,MAAO,WACP+oB,MAAO,CACL,CAAE7pB,MAAO,YAAaopB,KAAM,cAC5B,CAAEppB,MAAO,qBAAsBopB,KAAM,uBACrC,CAAEppB,MAAO,gBAAiBopB,KAAM,mBAChC,CAAEppB,MAAO,UAAWopB,KAAM,wBAG9B,CACEtoB,MAAO,qBACP+oB,MAAO,CACL,CAAE7pB,MAAO,mBAAoBopB,KAAM,kBACnC,CAAEppB,MAAO,eAAgBopB,KAAM,kBAC/B,CAAEppB,MAAO,eAAgBopB,KAAM,mBAC/B,CAAEppB,MAAO,uBAAwBopB,KAAM,6BAG3C,CACEtoB,MAAO,YACP+oB,MAAO,CACL,CAAE7pB,MAAO,gBAAiBopB,KAAM,SAChC,CAAEppB,MAAO,gBAAiBopB,KAAM,QAChC,CAAEppB,MAAO,iBAAkBopB,KAAM,YACjC,CAAEppB,MAAO,qBAAsBopB,KAAM,eAGzC,CACEtoB,MAAO,UACP+oB,MAAO,CACL,CAAE7pB,MAAO,WAAYopB,KAAM,UAC3B,CAAEppB,MAAO,iBAAkBopB,KAAM,YACjC,CAAEppB,MAAO,mBAAoBopB,KAAM,UACnC,CAAEppB,MAAO,UAAWopB,KAAM,eAmIR/jB,KAAI,CAAC2B,EAASqJ,MAC5BxS,IAACqS,EAAK,CAAAI,MAAI,EAACC,GAAI,EAAGqC,GAAI,EACpB9b,SAAA8G,EAAAuC,KAACgC,GAAOC,IAAP,CACCE,QAAS,CAAEiC,QAAS,EAAG/B,EAAG,IAC1BinB,YAAa,CAAEllB,QAAS,EAAG/B,EAAG,GAC9BxG,WAAY,CAAEd,SAAU,GAAKiE,MAAe,GAARkR,GACpCqZ,SAAU,CAAE/G,MAAM,GAElB7rB,SAAA,CAAA8G,EAAAC,IAAC+C,EAAA,CACCnB,QAAQ,KACRC,GAAI,CACFnG,WAAY,IACZsK,GAAI,EACJnK,MAAOtC,EAAMG,QAAQgB,KAAKf,SAG3BV,SAAQkQ,EAAAlG,QAEXjD,MAACoR,GAAM7U,QAAS,EACbtD,WAAQ+yB,MAAMxkB,KAAKykB,GAClBlsB,EAAAC,IAACksB,EAAA,CAECX,KAAMU,EAAKV,KACX1pB,GAAI,CACFhG,MAAOtC,EAAMG,QAAQgB,KAAKV,UAC1BmyB,eAAgB,OAChB1wB,SAAU,WACV0C,WAAY,gBACZ,UAAW,CACTtC,MAAOtC,EAAMG,QAAQC,QAAQC,KAC7BwE,UAAW,oBAIdnF,SAAKgzB,EAAA9pB,OAbD8pB,EAAK9pB,eApBUgH,EAAQlG,YA0C1CjD,MAACiY,GAAQpW,GAAI,CAAEwiB,GAAI,EAAG3d,QAAS,MAG/B3G,EAAAuC,KAACC,EAAA,CACCV,GAAI,CACFW,QAAS,OACT+U,cAAe,CAAE7E,GAAI,SAAUqC,GAAI,OACnChP,eAAgB,gBAChBtD,WAAY,SACZC,IAAK,GAGPzJ,SAAA,CAAA8G,EAAAuC,KAACS,EAAA,CACCnB,QAAQ,QACRC,GAAI,CACFhG,MAA8B,SAAvBtC,EAAMG,QAAQR,KACjBK,EAAMG,QAAQgB,KAAKf,QACnBJ,EAAMG,QAAQgB,KAAKV,UACvBgb,UAAW,CAAEtC,GAAI,SAAUqC,GAAI,QAC/BrZ,WAAY,IACZgL,QAAgC,SAAvBnN,EAAMG,QAAQR,KAAkB,GAAM,IAElDD,SAAA,MACImyB,EAAY,4FAIjBprB,MAACoR,GAAMC,UAAU,MAAM9U,QAAS,EAC7BtD,SAAAqyB,EAAY9jB,KAAK4kB,GAChBrsB,EAAAC,IAACwG,EAAA,CAEC+kB,KAAMa,EAAOb,KACb,aAAYa,EAAOjqB,MACnBN,GAAI,CACFhG,MAAOtC,EAAMG,QAAQgB,KAAKV,UAC1BiE,OAAQ,aAAa+D,EAAMzI,EAAMG,QAAQkB,QAAS,MAClD6B,aAAc,EACdqJ,EAAG,EACH3H,WAAY,gBACZ,UAAW,CACTtC,MAAOtC,EAAMG,QAAQC,QAAQC,KAC7B6f,YAAalgB,EAAMG,QAAQC,QAAQC,KACnCwE,UAAW,mBACX/D,WAAY2H,EAAMzI,EAAMG,QAAQC,QAAQC,KAAM,MAIlDX,SAAA+G,MAACosB,EAAOjpB,KAAP,CAAYtB,GAAI,CAAEpG,SAAU,OAjBxB2wB,EAAOjqB,kBAuBxB,EC9REkqB,GAActzB,mBAEPuzB,GAAe,EAAGrzB,eACvB,MAACszB,EAAiBC,GAAsBpzB,YAAqD,SAA5CC,aAAaC,QAAQ,qBACrEsX,EAAM6b,GAAWrzB,YAASszB,KAAKC,MAAMtzB,aAAaC,QAAQ,UAyB/D,aAAC+yB,GAAYzsB,SAAZ,CAAqBC,MAAO,CAAE0sB,kBAAiB3b,OAAMgc,MAtBzCC,GAEN,IAAIpY,SAASC,IAClBjT,YAAW,KACH,MAAAqrB,EAAW,CAAEC,SAAUF,EAASE,SAAUhd,KAAM,UAAWid,UAAW,YAAa/jB,KAAM,aAClF5P,aAAAwF,QAAQ,kBAAmB,QACxCxF,aAAawF,QAAQ,OAAQ6tB,KAAKO,UAAUH,IAC5CN,GAAmB,GACnBC,EAAQK,GACRpY,EAAQoY,EAAQ,GACf,IAAG,IAYqDI,OARhD,KACb7zB,aAAa8zB,WAAW,mBACxB9zB,aAAa8zB,WAAW,QACxBX,GAAmB,GACnBC,EAAQ,KAAI,GAKTxzB,YACH,EAIEm0B,GAAU,IAAMC,cAAWhB,IAG3BiB,GAAqBv0B,mBAErBw0B,GAAsB,EAAGt0B,eACvB,MAACu0B,EAAUC,GAAer0B,YAASC,aAAaC,QAAQ,gBAAkB,MAEhFwF,cAAU,KACR4uB,GAAKC,eAAeH,GACPn0B,aAAAwF,QAAQ,cAAe2uB,EAAQ,GAC3C,CAACA,IAOFxtB,aAACstB,GAAmB1tB,SAAnB,CAA4BC,MAAO,CAAE2tB,WAAUG,eAL1BC,IACtBH,EAAYG,EAAI,GAKb30B,YACH,EAQJy0B,GACGG,IAAIC,IACJD,IAAIE,IACJC,KAAK,CACJC,cAAe,CAAC,KAAM,MACtBC,YAAa,KACbC,OAAO,EACPC,cAAe,CACbC,aAAa,GAEfC,QAAS,CACPC,SAAU,gCAEZC,GAAI,CAAC,SAAU,YAAa,SAC5BC,UAAW,WAmBf,MAAMC,GAAsBC,GAAMC,MAAK,IAAMC,IAAA,IAAAC,OAAO,sCAA4CC,sCAC1FC,GAAiBL,GAAMC,MAAK,IAAMC,IAAA,IAAAC,OAAO,iCAAuCC,sCAChFE,GAAuBN,GAAMC,MAAK,IAAMC,IAAA,IAAAC,OAAO,uCAA+CC,qCAC9FG,GAAaP,GAAMC,MAAK,IAAMC,IAAA,IAAAC,OAAO,6BAAiCC,qCACtEI,GAAmBR,GAAMC,MAAK,IAAMC,IAAA,IAAAC,OAAO,mCAA0CC,yCACrFK,GAAaT,GAAMC,MAAK,IAAMC,IAAA,IAAAC,OAAO,6BAA+BC,qCACpEM,GAAmBV,GAAMC,MAAK,IAAMC,IAAA,IAAAC,OAAO,mCAAwCC,qCAMnFO,GAAS,KACb,MAAMpb,EAAEA,GAAMC,GAAe,WACvB+Y,OAAEA,EAAAtc,KAAQA,GAASwc,MACnBI,SAAEA,EAAAG,eAAUA,GApDSN,cAAWC,IAqDhCxb,EAAWC,KAYf,aAAClB,EAAO,CAAAvL,SAAS,QAAQzD,GAAI,CAAE+D,OAASrM,GAAUA,EAAMqM,OAAOygB,OAAS,GACtEptB,SAACqJ,OAAAyO,EAAQ,CAAAlP,GAAI,CAAEc,GAAI,CAAE+P,GAAI,EAAGqC,GAAI,IAC9B9b,SAAA,CAAA+G,MAAC+C,GAAWnB,QAAQ,KAAK2tB,QAAM,EAACrZ,UAAU,MAAMrU,GAAI,CAAE2kB,SAAU,EAAG9qB,WAAY,KAC5EzC,SAAAib,EAAE,mBAGJ5R,OAAAC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,GACrDzJ,SAAA,CAAAqJ,OAACmY,GAAYlU,KAAK,QAAQ1E,GAAI,CAAEmS,SAAU,KACxC/a,SAAA,CAAA+G,MAAC0a,EAAW,CAAA1R,GAAG,wBAAyB/P,SAAAib,EAAE,cAC1CnU,EAAAuC,KAACqY,EAAA,CACC6U,QAAQ,wBACRxmB,GAAG,kBACHnJ,MAAO2tB,EACPrrB,MAAO+R,EAAE,YACTmC,SAnBkBrH,IACb2e,EAAA3e,EAAMiH,OAAOpW,MAAK,EAmBvBgC,GAAI,CACF,qCAAsC,CACpC4X,YAAa,4BAEf,2CAA4C,CAC1CA,YAAa,6BAIjBxgB,SAAA,OAAC2hB,EAAA,CAAS/a,MAAM,KAAK5G,SAAY,uBAChC2hB,EAAA,CAAS/a,MAAM,KAAK5G,SAAW,sBAInC2X,GACEtO,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQC,WAAY,SAAUC,IAAK,GACrDzJ,SAAA,CAAA8G,EAAAC,IAACkG,EAAA,CACCrE,GAAI,CACFwE,MAAO,GACPvE,OAAQ,GACRqE,QAAS,iBACT1K,SAAU,WACVC,WAAY,KAGZzC,YAAKgQ,MAAQ2H,EAAKmc,UAAUpb,OAAO,GAAG0K,sBAEzCtZ,EAAA,CAAWlB,GAAI,CAAEW,QAAS,CAAEkQ,GAAI,OAAQC,GAAI,UAC1C1Z,SAAK2X,EAAA3H,MAAQ2H,EAAKmc,cAKzBhtB,EAAAC,IAAC8O,EAAA,CACCjT,MAAM,UACN2H,QA5DW,KACZ0pB,IACPpb,EAAS,SAAQ,EA2DTvE,gBAAYkiB,GAAc,IAC1B5tB,GAAI,CACFpF,aAAc,EACd,UAAW,CACT0J,QAAS,6BAIZlN,WAAE,mBAIX,EAKEy2B,GAAU,KACd,MAAMxb,EAAEA,GAAMC,GAAe,UACvBwb,EAAY,CAChB,CAAEj1B,KAAMwZ,EAAE,aAAc/Q,WAAOumB,GAAA,CAAc,GAAItD,KAAM,cACvD,CAAE1rB,KAAMwZ,EAAE,YAAa/Q,WAAOwkB,GAAA,CAAW,GAAIvB,KAAM,aACnD,CAAE1rB,KAAMwZ,EAAE,WAAY/Q,WAAO2Z,GAAA,CAAe,GAAIsJ,KAAM,YACtD,CAAE1rB,KAAMwZ,EAAE,YAAa/Q,KAAOpD,EAAAC,IAAA4vB,GAAA,IAAiBxJ,KAAM,cAIrD,OAAArmB,EAAAuC,KAACokB,EAAA,CACC9kB,QAAQ,YACRC,GAAI,CACFwE,MApGY,IAqGZwpB,WAAY,EACZ,qBAAwB,CAAExpB,MAtGd,IAsGkCwgB,UAAW,aAAc9kB,gBAAiB,UAAWlG,MAAO,UAG5G5C,SAAA,CAAA8G,EAAAC,IAAC+Q,EAAQ,IAAE,IACX/Q,MAACuC,GAAIV,GAAI,CAAEwD,SAAU,QACnBpM,SAAC8G,EAAAC,IAAAwiB,EAAA,CACEvpB,SAAU02B,EAAAnoB,KAAKiL,GACbnQ,OAAAogB,GAASrmB,QAAM,EAAC6Z,UAAWgW,GAAM/b,GAAIsC,EAAK2T,KAAsBvkB,GAAI,CACnE,UAAW,CACTE,gBAAiB,4BAEnB,0BAA2B,CACzBlG,MAAO,UAGT5C,SAAA,OAAC2wB,EAAA,CAAc3wB,WAAKkK,aACnBwf,EAAA,CAAahpB,QAAS8Y,EAAK/X,SATwB+X,EAAK/X,cAcnE,EAKEo1B,GAAa,WAEdvtB,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQ+U,cAAe,SAAUjJ,UAAW,SAC9DrV,SAAA,CAAAqJ,OAACC,GAAIV,GAAI,CAAEW,QAAS,OAAQmR,KAAM,GAChC1a,SAAA,CAAA8G,EAAAC,IAACC,EAAY,UACZqvB,GAAO,UACPI,GAAQ,IACT3vB,EAAAuC,KAACC,EAAA,CACC2T,UAAU,OACVrU,GAAI,CACF2kB,SAAU,EACVrgB,QAAS,qBACTL,EAAG,EACHO,MAAO,qBACP7D,QAAS,OACT+U,cAAe,UAGjBte,SAAA,CAAA8G,EAAAC,IAAC+Q,EAAQ,IAAE,UACVxO,EAAA,CAAIV,GAAI,CAAE8R,KAAM,GACf1a,SAACqJ,OAAAytB,YAAS,CAAAC,SAAWhwB,MAAAiwB,GAAA,IACnBh3B,SAAA,CAAA8G,EAAAC,IAACkwB,GAAO,IAAE,qBAKjB/E,GAAa,OASdgF,GAAmB,KACjB,MAAAvD,MAAEA,GAAUQ,KACZtb,EAAWC,kBAuBTmF,GAAA,CAAgBC,QArBJpI,MAAO6G,IACrB,UACIgX,EAAM,CAAEG,SAAUnX,EAASrM,MAAOuM,SAAUF,EAASE,WAC3DhE,EAAS,oBACFse,GAC2B,GAgBQhZ,SAZzBrI,MAAO6G,IACtB,UAIIgX,EAAM,CAAEG,SAAUnX,EAASrM,MAAOuM,SAAUF,EAASE,WAC3DhE,EAAS,oBACFse,GAC4B,IAI+B,EAIlEC,GAAgB,UACZpc,GAAgB,IAGpBqc,GAAe,UACXvS,GAAkB,IAItBwS,GAA0B,MAE3BvwB,IAAA+vB,aAASC,eAAWQ,OACnBv3B,SAAC8G,EAAAC,IAAA0uB,SAKD+B,GAAqB,MAEtBzwB,IAAA+vB,aAASC,eAAWQ,OACnBv3B,SAAC8G,EAAAC,IAAAgvB,SAKD0B,GAAuB,MAExB1wB,IAAA+vB,aAASC,eAAWQ,OACnBv3B,SAAC8G,EAAAC,IAAAmvB,SAKDwB,GAA2B,MAE5B3wB,IAAA+vB,aAASC,eAAWQ,OACnBv3B,SAAC8G,EAAAC,IAAAivB,SAKD2B,GAAiB,MAElB5wB,IAAA+vB,aAASC,eAAWQ,OACnBv3B,SAAC8G,EAAAC,IAAAkvB,SAKD2B,GAAiB,MAElB7wB,IAAA+vB,aAASC,eAAWQ,OACnBv3B,SAAC8G,EAAAC,IAAAovB,SAKD0B,GAAuB,MAExB9wB,IAAA+vB,aAASC,eAAWQ,OACnBv3B,SAAC8G,EAAAC,IAAAqvB,SAKD0B,GAAW,UACPvQ,GAA0B,IAK9BwQ,GAAqB,UACjBpN,GAAsB,IAG1BqN,GAAgB,UACZlY,GAAmB,IAGvBmY,GAAc,KAClB,MAAMhd,EAAEA,GAAMC,GAAe,wBAE1B5R,EAAI,CAAAV,GAAI,CAAEiE,EAAG,GACZ7M,SAAA,OAAC8J,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GACjD/M,SAAEib,EAAA,mBAEJnR,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAAiB5C,SAEnD,2CACF,EAIEk4B,GAAe,KACnB,MAAMjd,EAAEA,GAAMC,GAAe,wBAE1B5R,EAAI,CAAAV,GAAI,CAAEiE,EAAG,GACZ7M,SAAA,OAAC8J,EAAW,CAAAnB,QAAQ,KAAKC,GAAI,CAAEnG,WAAY,IAAKsK,GAAI,GACjD/M,SAAEib,EAAA,oBAEJnR,EAAW,CAAAnB,QAAQ,QAAQ/F,MAAM,iBAAiB5C,SAEnD,4CACF,EAGEm4B,GAAe,KACnB,MAAMld,EAAEA,GAAMC,GAAe,UAE3B7R,cAACC,GAAIV,GAAI,CAAEmT,UAAW,SAAUrO,GAAI,GAClC1N,SAAA,CAAA+G,MAAC+C,EAAW,CAAAnB,QAAQ,KAAM3I,SAAAib,EAAE,yBAC3BnR,EAAA,CAAY9J,SAAEib,EAAA,2BACdpF,EAAA,CAAOoH,UAAWgW,GAAM/b,GAAG,aAAavO,QAAQ,YAAYC,GAAI,CAAE8E,GAAI,GACpE1N,SAAAib,EAAE,cAEP,EAGE+b,GAAiB,IACpBjwB,MAAAuC,EAAI,CAAAV,GAAI,CAAEW,QAAS,OAAQuD,eAAgB,SAAUtD,WAAY,SAAUX,OAAQ,QAClF7I,eAACqN,GAAiB,KAIhBkqB,GAAkB,IACrBluB,OAAAC,EAAA,CAAIV,GAAI,CAAEW,QAAS,OAAQuD,eAAgB,SAAUtD,WAAY,SAAUX,OAAQ,QAClF7I,SAAA,GAAC+G,IAAAsG,EAAA,CAAiBC,KAAM,WACvBxD,EAAW,CAAAlB,GAAI,CAAEwe,GAAI,GAAKpnB,SAAoB,4BAM7Co4B,GAAiB,EAAGp4B,eAClB,MAAAszB,gBAAEA,GAAoBa,KAC5B,OAAKb,EAGEtzB,QAFGq4B,GAAA,CAASnhB,GAAG,SAASohB,SAAO,GAE/B,EAKT,SAASC,KACP,aACGx4B,GAAA,CACCC,WAAC+G,IAAAssB,GAAA,CACCrzB,WAAC+G,IAAAutB,GACC,CAAAt0B,WAAC+G,IAAA+vB,YAAS,CAAAC,SAAWhwB,MAAAiwB,GAAe,IAClCh3B,SAAC+G,MAAAyxB,GAAA,CACCx4B,WAACqJ,KAAAovB,GAEC,CAAAz4B,SAAA,OAAC04B,IAAMvL,KAAK,IAAIwL,QAAS5xB,MAAC4oB,IAAY,WACrC+I,GAAM,CAAAvL,KAAK,SAASwL,QAAS5xB,MAAC6oB,IAAU,WACxC8I,GAAM,CAAAvL,KAAK,YAAYwL,QAAS5xB,MAACmpB,IAAa,WAC9CwI,GAAM,CAAAvL,KAAK,WAAWwL,QAAS5xB,MAAC+pB,IAAY,WAC5C4H,GAAM,CAAAvL,KAAK,SAASwL,QAAS5xB,MAACmwB,IAAiB,KAGhDpwB,EAAAuC,KAACqvB,GAAA,CACCvL,KAAK,aACLwL,QACG5xB,MAAAqxB,GACC,CAAAp4B,eAAC62B,IAAW,KAKhB72B,SAAA,OAAC04B,IAAMnf,OAAK,EAACof,QAAS5xB,MAACqwB,IAAc,WACpCsB,GAAM,CAAAvL,KAAK,WAAWwL,QAAS5xB,MAACswB,IAAa,WAE7CqB,GAAM,CAAAvL,KAAK,oBAAoBwL,QAAS5xB,MAACuwB,IAAwB,WACjEoB,GAAM,CAAAvL,KAAK,sBAAsBwL,QAAS5xB,MAACywB,IAAmB,WAC9DkB,GAAM,CAAAvL,KAAK,2BAA2BwL,QAAS5xB,MAACgxB,IAAmB,WACnEW,GAAM,CAAAvL,KAAK,UAAUwL,QAAS5xB,MAAC0wB,IAAqB,WACpDiB,GAAM,CAAAvL,KAAK,aAAawL,QAAS5xB,MAAC2wB,IAAyB,WAC3DgB,GAAM,CAAAvL,KAAK,SAASwL,QAAS5xB,MAAC4wB,IAAe,WAC7Ce,GAAM,CAAAvL,KAAK,cAAcwL,QAAS5xB,MAAC6wB,IAAe,WAClDc,GAAM,CAAAvL,KAAK,YAAYwL,QAAS5xB,MAACixB,IAAc,WAC/CU,GAAM,CAAAvL,KAAK,OAAOwL,QAAS5xB,MAAC+wB,IAAS,WACrCY,GAAM,CAAAvL,KAAK,UAAUwL,QAAS5xB,MAACkxB,IAAY,WAC3CS,GAAM,CAAAvL,KAAK,mBAAmBwL,QAAS5xB,MAAC8wB,IAAqB,WAC7Da,GAAM,CAAAvL,KAAK,WAAWwL,QAAS5xB,MAACmxB,kBAElCQ,GAAM,CAAAvL,KAAK,IAAIwL,QAAS5xB,MAACoxB,qBAQ1C,CC/eA,MAAMS,WAAsBlD,GAAMmD,UAChC,WAAAC,CAAYlkB,GACVmkB,MAAMnkB,GACDokB,KAAAhrB,MAAQ,CAAEirB,UAAU,EAAM,CAGjC,+BAAOC,CAAyBh4B,GACvB,OAAE+3B,UAAU,EAAK,CAG1B,iBAAAE,CAAkBj4B,EAAOk4B,GAC6B,CAGtD,MAAAC,GACM,OAAAL,KAAKhrB,MAAMirB,SAEXnyB,EAAAuC,KAAC,OAAIuM,MAAO,CACVrM,QAAS,OACT+U,cAAe,SACf9U,WAAY,SACZsD,eAAgB,SAChBjE,OAAQ,QACRxG,WAAY,qBAEZrC,SAAA,GAAA+G,IAAC,MAAG/G,SAAqB,4BACzB+G,IAAC,KAAE/G,SAAmE,wEACtE8G,EAAAC,IAAC,UACCwD,QAAS,IAAMxE,OAAOwU,SAASC,SAC/B5E,MAAO,CACLtQ,QAAS,YACTwD,gBAAiB,UACjBlG,MAAO,QACPoC,OAAQ,OACRxB,aAAc,MACd2I,OAAQ,WAEXnM,SAAA,oBAOAg5B,KAAKpkB,MAAM5U,QAAA,EAItBs5B,GAAS35B,WAAW45B,SAASC,eAAe,SAASH,SACnDtyB,IAAC2uB,GAAM+D,WAAN,CACCz5B,eAAC44B,GACC,CAAA54B,SAAA+G,MAACwxB,GAAI", "names": ["m", "require$$0", "createRoot", "hydrateRoot", "ThemeContext", "createContext", "ThemeContextProvider", "children", "mode", "setMode", "useState", "localStorage", "getItem", "theme", "isLight", "createTheme", "palette", "primary", "main", "light", "dark", "contrastText", "secondary", "success", "warning", "error", "info", "background", "default", "paper", "surface", "elevated", "text", "disabled", "divider", "glass", "backdrop", "gradients", "board", "CBSE", "ICSE", "STATE", "IB", "typography", "fontFamily", "join", "h1", "fontSize", "fontWeight", "lineHeight", "letterSpacing", "color", "h2", "h3", "h4", "h5", "h6", "body1", "body2", "button", "textTransform", "spacing", "shape", "borderRadius", "modernRadius", "glassRadius", "shadows", "transitions", "easing", "easeInOut", "easeOut", "easeIn", "sharp", "modern", "bounce", "duration", "shortest", "shorter", "short", "standard", "complex", "enteringScreen", "leavingScreen", "components", "MuiCard", "styleOverrides", "root", "border", "<PERSON><PERSON>ilter", "transition", "transform", "boxShadow", "MuiB<PERSON>on", "padding", "contained", "MuiChip", "createModernEducationalTheme", "setThemeMode", "newMode", "setItem", "useEffect", "mediaQuery", "window", "matchMedia", "handleChange", "e", "matches", "addEventListener", "removeEventListener", "contextValue", "toggleMode", "setSystemMode", "systemPrefersDark", "isDark", "Provider", "value", "ThemeProvider", "jsxRuntimeExports", "jsx", "CssBaseline", "Animated<PERSON>ounter", "suffix", "prefix", "displayValue", "setDisplayValue", "startTime", "animationFrame", "animate", "timestamp", "progress", "Math", "min", "easeOutQuart", "pow", "currentValue", "floor", "requestAnimationFrame", "cancelAnimationFrame", "toLocaleString", "AnimatedProgress", "delay", "setProgress", "timer", "setTimeout", "clearTimeout", "LinearProgress", "variant", "sx", "height", "backgroundColor", "alpha", "TrendIndicator", "trend", "label", "useTheme", "isPositive", "jsxs", "Box", "display", "alignItems", "gap", "px", "py", "ArrowUpward", "ArrowDownward", "Typography", "ModernMetricCard", "title", "subtitle", "icon", "Icon", "trendValue", "trendLabel", "progressLabel", "onClick", "loading", "actionIcon", "onActionClick", "tooltip", "gradient", "glassmorphism", "isHovered", "setIsHovered", "getCardBackground", "getTextColor", "<PERSON><PERSON><PERSON>", "arrow", "placement", "motion", "div", "variants", "initial", "scale", "y", "hover", "type", "stiffness", "damping", "whileHover", "onHoverStart", "onHoverEnd", "Card", "cursor", "overflow", "position", "content", "top", "left", "right", "bottom", "zIndex", "<PERSON><PERSON><PERSON><PERSON>", "p", "justifyContent", "mb", "rotate", "Avatar", "bgcolor", "_a", "width", "CircularProgress", "size", "IconButton", "<PERSON><PERSON><PERSON>", "opacity", "mt", "pt", "borderTop", "subjects", "cbse", "icse", "state", "ib", "generatePerformanceData", "studentProfile", "subjectList", "basePerformance", "academicLevel", "map", "subject", "currentScore", "max", "random", "previousScore", "assignments", "assignmentsCompleted", "generateBehavioralData", "incidents", "count", "Array", "from", "length", "date", "Date", "severity", "description", "generateExtracurriculars", "activities", "activity", "level", "achievements", "studentProfiles", "id", "name", "grade", "section", "rollNumber", "region", "parentContact", "email", "dateOfBirth", "address", "generateCompleteStudentData", "profile", "performance", "attendance", "month", "present", "total", "percentage", "behavioral", "extracurricular", "swotAnalysis", "strengths", "sort", "slice", "weaknesses", "opportunities", "threats", "lastUpdated", "confidence", "overallGrade", "rank", "generateAnalyticsData", "students", "totalStudents", "averagePerformance", "reduce", "sum", "student", "averageAttendance", "a", "round", "topPerformers", "filter", "s", "needsAttention", "boardDistribution", "gradeDistribution", "performanceTrends", "generatePerformanceTrends", "subjectAnalysis", "generateSubjectAnalysis", "attendancePatterns", "generateAttendancePatterns", "swotDistribution", "generateSWOTDistribution", "average", "averageScore", "topScore", "lowestScore", "studentsAbove80", "studentsBelow60", "presentStudents", "strengthsDistribution", "Leadership", "Creativity", "weaknessesDistribution", "Organization", "opportunitiesDistribution", "Competitions", "threatsDistribution", "Enhanced<PERSON><PERSON><PERSON>", "startIcon", "endIcon", "fullWidth", "loadingText", "successFeedback", "errorFeedback", "props", "isClicked", "setIsClicked", "showSuccess", "setShowSuccess", "showError", "setShowError", "getButtonColor", "enhancedSx", "minHeight", "outline", "outlineOffset", "pointerEvents", "ButtonComponent", "tap", "whileTap", "style", "<PERSON><PERSON>", "async", "event", "result", "EnhancedCard", "elevation", "hoverable", "clickable", "contentSx", "actions", "header", "footer", "cardVariants", "contentEnhancedSx", "LoadingSkeleton", "Skeleton", "tabIndex", "role", "className", "CardActions", "animation", "to", "ThemeToggle", "darkMode", "onToggle", "ease", "LightMode", "DarkMode", "ModernHeader", "onThemeToggle", "user", "AppBar", "borderBottom", "<PERSON><PERSON><PERSON>", "School", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "<PERSON><PERSON>", "direction", "Search", "FilterList", "Badge", "badgeContent", "Notifications", "char<PERSON>t", "QuickStats", "analyticsData", "navigate", "useNavigate", "statsData", "Person", "Psychology", "TrendingUp", "CalendarToday", "Grid", "container", "stat", "index", "item", "xs", "sm", "lg", "toLowerCase", "RecentActivity", "studentsData", "for<PERSON>ach", "push", "time", "lowPerformers", "_b", "_c", "generateActivities", "Refresh", "location", "reload", "x", "flex", "QuickActions", "<PERSON><PERSON><PERSON>", "Timeline", "action", "min<PERSON><PERSON><PERSON>", "ModernDashboard", "t", "useTranslation", "setDarkMode", "setLoading", "setStudentsData", "setAnalyticsData", "school", "Promise", "resolve", "analytics", "loadData", "score", "Chip", "md", "textAlign", "Group", "AnimatedBackground", "_", "i", "repeat", "Infinity", "FeatureHighlight", "LoginForm", "onSubmit", "showPassword", "setShowPassword", "formData", "setFormData", "password", "field", "prev", "target", "component", "preventDefault", "TextField", "onChange", "InputProps", "startAdornment", "InputAdornment", "Email", "Lock", "endAdornment", "edge", "VisibilityOff", "Visibility", "ArrowForward", "SignupForm", "flexWrap", "ModernLoginPage", "onLogin", "onSignup", "activeTab", "setActiveTab", "flexDirection", "textShadow", "CheckCircle", "max<PERSON><PERSON><PERSON>", "Tabs", "newValue", "centered", "Tab", "AnimatePresence", "exit", "Divider", "Google", "Apple", "Facebook", "ChartJS", "register", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "Title", "Legend", "ArcElement", "AnalyticsDashboard", "timeRange", "setTimeRange", "selectedBoard", "setSelectedBoard", "data", "performanceTrendConfig", "labels", "d", "datasets", "borderColor", "tension", "options", "responsive", "plugins", "legend", "scales", "beginAtZero", "subjectAnalysisConfig", "borderWidth", "boardDistributionConfig", "attendancePatternConfig", "metricCards", "People", "Assessment", "Container", "FormControl", "InputLabel", "Select", "MenuItem", "card", "Line", "Bar", "Doughnut", "StudentCard", "onView", "onEdit", "onGenerateSWOT", "anchorEl", "setAnchorEl", "handleMenuClose", "getPerformanceColor", "getPerformanceTrend", "TrendingUpIcon", "TrendingDownIcon", "TrendIcon", "performanceTrend", "Fade", "in", "timeout", "onMouseEnter", "onMouseLeave", "split", "n", "toUpperCase", "class", "admissionNumber", "stopPropagation", "currentTarget", "MoreVertIcon", "overallScore", "hasActiveSWOT", "ViewIcon", "AssessmentIcon", "<PERSON><PERSON>", "open", "Boolean", "onClose", "mr", "EditIcon", "FilterDialog", "filters", "onFiltersChange", "Dialog", "DialogTitle", "CloseIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select", "swotStatus", "DialogActions", "StudentManagement", "isMobile", "useMediaQuery", "breakpoints", "down", "setStudents", "searchTerm", "setSearchTerm", "setFilters", "filterDialogOpen", "setFilterDialogOpen", "mockStudents", "filteredStudents", "useMemo", "matchesSearch", "includes", "matchesClass", "matchesPerformance", "matchesAttendance", "matchesSWOT", "handleViewStudent", "handleEditStudent", "handleGenerateSWOT", "handleAddStudent", "placeholder", "SearchIcon", "FilterIcon", "AddIcon", "PersonIcon", "gutterBottom", "Fab", "fab", "CulturalSWOTCard", "items", "culturalPattern", "pattern", "getPatternBackground", "priority", "ml", "culturalContext", "fontStyle", "CulturalSWOTVisualization", "studentId", "boardType", "swotData", "setSWOTData", "selectedStudent", "setSelectedStudent", "loadSWOTData", "swotConfig", "StrengthIcon", "WeaknessIcon", "OpportunityIcon", "ThreatIcon", "config", "RadialLinearScale", "Filler", "ChartTooltip", "AcademicRadar<PERSON>hart", "performanceData", "Radar", "pointBackgroundColor", "pointBorderColor", "pointHoverBackgroundColor", "pointHoverBorderColor", "maintainAspectRatio", "r", "angleLines", "suggested<PERSON><PERSON>", "suggestedMax", "ticks", "stepSize", "SWOTQuadrant", "List", "dense", "ListItem", "ListItemText", "AttendanceCalendar", "attendanceData", "calendarDays", "day", "status", "getStatusColor", "gridTemplateColumns", "BehaviorTimeline", "behaviorData", "incident", "ExtracurricularActivities", "activitiesData", "hours", "Recommendations", "recommendations", "recommendation", "IndividualStudentSWOT", "useParams", "studentData", "setStudentData", "quarter", "gpa", "classRank", "loadStudentData", "ArrowBack", "my", "Warning", "TrendingDown", "Download", "Share", "Print", "useIntersectionObserver", "threshold", "rootMargin", "once", "enabled", "isIntersecting", "setIsIntersecting", "hasIntersected", "setHasIntersected", "targetRef", "useRef", "observerRef", "current", "IntersectionObserver", "entries", "entry", "isCurrentlyIntersecting", "disconnect", "isArray", "observe", "NavigationHeader", "mobileOpen", "setMobileOpen", "handleDrawerToggle", "navigationItems", "path", "drawer", "disablePadding", "Fragment", "flexGrow", "MenuIcon", "Drawer", "ModalProps", "keepMounted", "boxSizing", "HeroSection", "hero<PERSON><PERSON>", "isHeroVisible", "ref", "ArrowForwardIcon", "Slide", "Paper", "mx", "SchoolIcon", "FeaturesSection", "featuresRef", "isFeaturesVisible", "features", "PeopleIcon", "feature", "Zoom", "VisionMissionSection", "visionRef", "isVisionVisible", "UseCasesSection", "useCasesRef", "isUseCasesVisible", "useCases", "useCase", "featureIndex", "StarIcon", "Footer", "EmailIcon", "PhoneIcon", "LocationOnIcon", "LandingPage", "AboutPage", "values", "PsychologyIcon", "GroupsIcon", "ArrowBackIcon", "number", "FeaturesPage", "coreFeatures", "additionalFeatures", "LanguageIcon", "SecurityIcon", "CloudSyncIcon", "AnalyticsIcon", "DashboardIcon", "itemIndex", "ListItemIcon", "CheckCircleIcon", "primaryTypographyProps", "ContactPage", "organization", "message", "handleInputChange", "contactInfo", "ScheduleIcon", "supportOptions", "SupportIcon", "BusinessIcon", "required", "multiline", "rows", "SendIcon", "option", "Snackbar", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "<PERSON><PERSON>", "ModernFooter", "currentYear", "getFullYear", "socialLinks", "href", "Twitter", "LinkedIn", "Instagram", "YouTube", "whileInView", "viewport", "LocationOn", "Phone", "links", "link", "Link", "textDecoration", "social", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "isAuthenticated", "setIsAuthenticated", "setUser", "JSON", "parse", "login", "userData", "mockUser", "username", "school_id", "stringify", "logout", "removeItem", "useAuth", "useContext", "AppSettingsContext", "AppSettingsProvider", "language", "setLanguage", "i18n", "changeLanguage", "lang", "use", "HttpApi", "initReactI18next", "init", "supportedLngs", "fallbackLng", "debug", "interpolation", "escapeValue", "backend", "loadPath", "ns", "defaultNS", "StudentRegistration", "React", "lazy", "__vitePreload", "import", "__VITE_PRELOAD__", "StudentProfile", "AttendanceManagement", "GradeEntry", "TeacherDashboard", "SWOTWizard", "ReportGeneration", "Header", "noWrap", "labelId", "ExitToAppIcon", "Sidebar", "menuItems", "SettingsIcon", "flexShrink", "MainLayout", "Suspense", "fallback", "LoadingSpinner", "Outlet", "LoginPageWrapper", "err", "DashboardPage", "StudentsPage", "StudentRegistrationPage", "LazyLoadWrapper", "StudentProfilePage", "TeacherDashboardPage", "AttendanceManagementPage", "GradeEntryPage", "SWOTWizardPage", "ReportGenerationPage", "SWOTPage", "IndividualSWOTPage", "AnalyticsPage", "ReportsPage", "SettingsPage", "NotFoundPage", "ProtectedRoute", "Navigate", "replace", "App", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "element", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "super", "this", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "componentDidCatch", "errorInfo", "render", "ReactDOM", "document", "getElementById", "StrictMode"], "ignoreList": [0], "sources": ["../../../../node_modules/react-dom/client.js", "../../src/theme/modernEducationalTheme.js", "../../src/contexts/ThemeContext.jsx", "../../src/components/Dashboard/ModernMetricCard.jsx", "../../src/data/studentData.js", "../../src/components/Common/EnhancedButton.jsx", "../../src/components/Common/EnhancedCard.jsx", "../../src/components/Dashboard/ModernDashboard.jsx", "../../src/components/Auth/ModernLoginPage.jsx", "../../src/components/Analytics/AnalyticsDashboard.jsx", "../../src/components/Students/StudentManagement.jsx", "../../src/components/SWOT/CulturalSWOTVisualization.jsx", "../../src/components/SWOT/IndividualStudentSWOT.jsx", "../../src/hooks/useIntersectionObserver.js", "../../src/components/Landing/LandingPage.jsx", "../../src/components/Pages/AboutPage.jsx", "../../src/components/Pages/FeaturesPage.jsx", "../../src/components/Pages/ContactPage.jsx", "../../src/components/Common/ModernFooter.jsx", "../../src/App.jsx", "../../src/main.jsx"], "sourcesContent": ["'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n", "import { createTheme } from '@mui/material/styles';\n\n// Modern VidyaMitra Educational Theme with Contemporary UI/UX\n// Supports dark/light mode with glassmorphism and modern design elements\n\nconst createModernEducationalTheme = (mode = 'light') => {\n  const isLight = mode === 'light';\n  \n  return createTheme({\n    palette: {\n      mode,\n      primary: {\n        50: isLight ? '#E8F4FD' : '#0F172A',\n        100: isLight ? '#C5E4FA' : '#1E293B',\n        200: isLight ? '#9FD3F7' : '#334155',\n        300: isLight ? '#79C2F4' : '#475569',\n        400: isLight ? '#5CB5F1' : '#64748B',\n        500: isLight ? '#2E5BA8' : '#4A90E2',\n        600: isLight ? '#1E4A97' : '#6BA3E8',\n        700: isLight ? '#1A4086' : '#8BB8ED',\n        800: isLight ? '#163675' : '#ABCDF2',\n        900: isLight ? '#0F2654' : '#CBE2F7',\n        main: isLight ? '#2E5BA8' : '#4A90E2',\n        light: isLight ? '#5CB5F1' : '#6BA3E8',\n        dark: isLight ? '#1A4086' : '#2E5BA8',\n        contrastText: '#FFFFFF',\n      },\n      secondary: {\n        50: isLight ? '#FFF8E1' : '#1A0F00',\n        100: isLight ? '#FFECB3' : '#331F00',\n        200: isLight ? '#FFE082' : '#4D2E00',\n        300: isLight ? '#FFD54F' : '#663D00',\n        400: isLight ? '#FFCA28' : '#804D00',\n        500: isLight ? '#FF9933' : '#FFB366',\n        600: isLight ? '#FFB300' : '#FFCC80',\n        700: isLight ? '#FF8F00' : '#FFD699',\n        800: isLight ? '#FF6F00' : '#FFE0B3',\n        900: isLight ? '#E65100' : '#FFEBCC',\n        main: isLight ? '#FF9933' : '#FFB366',\n        light: isLight ? '#FFCA28' : '#FFCC80',\n        dark: isLight ? '#FF8F00' : '#FF9933',\n        contrastText: isLight ? '#000000' : '#FFFFFF',\n      },\n      success: {\n        main: isLight ? '#00C853' : '#4CAF50',\n        light: isLight ? '#4CAF50' : '#81C784',\n        dark: isLight ? '#00A046' : '#388E3C',\n        contrastText: '#FFFFFF',\n      },\n      warning: {\n        main: isLight ? '#FF9800' : '#FFB74D',\n        light: isLight ? '#FFB74D' : '#FFCC80',\n        dark: isLight ? '#F57C00' : '#FF9800',\n        contrastText: isLight ? '#000000' : '#FFFFFF',\n      },\n      error: {\n        main: isLight ? '#F44336' : '#EF5350',\n        light: isLight ? '#EF5350' : '#E57373',\n        dark: isLight ? '#D32F2F' : '#C62828',\n        contrastText: '#FFFFFF',\n      },\n      info: {\n        main: isLight ? '#2196F3' : '#42A5F5',\n        light: isLight ? '#42A5F5' : '#64B5F6',\n        dark: isLight ? '#1976D2' : '#1565C0',\n        contrastText: '#FFFFFF',\n      },\n      background: {\n        default: isLight ? '#F8FAFC' : '#0F172A',\n        paper: isLight ? '#FFFFFF' : '#1E293B',\n        surface: isLight ? '#F1F5F9' : '#334155',\n        elevated: isLight ? '#FFFFFF' : '#475569',\n      },\n      text: {\n        primary: isLight ? '#1E293B' : '#F1F5F9',\n        secondary: isLight ? '#64748B' : '#94A3B8',\n        disabled: isLight ? '#CBD5E1' : '#475569',\n      },\n      divider: isLight ? '#E2E8F0' : '#334155',\n      \n      // Modern glassmorphism colors\n      glass: {\n        primary: isLight \n          ? 'rgba(255, 255, 255, 0.25)' \n          : 'rgba(255, 255, 255, 0.05)',\n        secondary: isLight \n          ? 'rgba(46, 91, 168, 0.1)' \n          : 'rgba(74, 144, 226, 0.1)',\n        backdrop: isLight \n          ? 'rgba(255, 255, 255, 0.8)' \n          : 'rgba(15, 23, 42, 0.8)',\n        surface: isLight\n          ? 'rgba(248, 250, 252, 0.8)'\n          : 'rgba(30, 41, 59, 0.8)',\n      },\n      \n      // Modern gradient colors\n      gradients: {\n        primary: isLight\n          ? 'linear-gradient(135deg, #2E5BA8 0%, #4A90E2 100%)'\n          : 'linear-gradient(135deg, #4A90E2 0%, #6BA3E8 100%)',\n        secondary: isLight\n          ? 'linear-gradient(135deg, #FF9933 0%, #FFB366 100%)'\n          : 'linear-gradient(135deg, #FFB366 0%, #FFCC80 100%)',\n        success: isLight\n          ? 'linear-gradient(135deg, #00C853 0%, #4CAF50 100%)'\n          : 'linear-gradient(135deg, #4CAF50 0%, #81C784 100%)',\n        surface: isLight\n          ? 'linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%)'\n          : 'linear-gradient(135deg, #1E293B 0%, #334155 100%)',\n        glass: isLight\n          ? 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)'\n          : 'linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%)',\n      },\n      \n      // Board-specific modern colors\n      board: {\n        CBSE: isLight ? '#2E5BA8' : '#4A90E2',\n        ICSE: isLight ? '#FF9933' : '#FFB366',\n        STATE: isLight ? '#00C853' : '#4CAF50',\n        IB: isLight ? '#9C27B0' : '#BA68C8',\n      },\n    },\n\n    typography: {\n      fontFamily: [\n        'Inter',\n        'Roboto',\n        'Noto Sans',\n        '-apple-system',\n        'BlinkMacSystemFont',\n        '\"Segoe UI\"',\n        'Arial',\n        'sans-serif',\n      ].join(','),\n      \n      // Modern heading styles with improved readability\n      h1: {\n        fontSize: 'clamp(2.5rem, 2rem + 2vw, 4rem)',\n        fontWeight: 600, // Reduced from 800\n        lineHeight: 1.1,\n        letterSpacing: '-0.025em',\n        color: isLight ? '#1E293B' : '#F1F5F9', // Solid color instead of gradient for better visibility\n      },\n      h2: {\n        fontSize: 'clamp(2rem, 1.7rem + 1.5vw, 3rem)',\n        fontWeight: 500, // Reduced from 700\n        lineHeight: 1.2,\n        letterSpacing: '-0.025em',\n        color: isLight ? '#1E293B' : '#F1F5F9',\n      },\n      h3: {\n        fontSize: 'clamp(1.75rem, 1.5rem + 1.25vw, 2.5rem)',\n        fontWeight: 500, // Reduced from 600\n        lineHeight: 1.25,\n        color: isLight ? '#1E293B' : '#F1F5F9',\n      },\n      h4: {\n        fontSize: 'clamp(1.5rem, 1.3rem + 1vw, 2rem)',\n        fontWeight: 500, // Reduced from 600\n        lineHeight: 1.3,\n        color: isLight ? '#1E293B' : '#F1F5F9',\n      },\n      h5: {\n        fontSize: 'clamp(1.25rem, 1.1rem + 0.75vw, 1.75rem)',\n        fontWeight: 500, // Reduced from 600\n        lineHeight: 1.35,\n        color: isLight ? '#1E293B' : '#F1F5F9',\n      },\n      h6: {\n        fontSize: 'clamp(1.125rem, 1rem + 0.625vw, 1.5rem)',\n        fontWeight: 500, // Reduced from 600\n        lineHeight: 1.4,\n        color: isLight ? '#1E293B' : '#F1F5F9',\n      },\n      \n      body1: {\n        fontSize: '1rem',\n        lineHeight: 1.6,\n        fontWeight: 400,\n        color: isLight ? '#334155' : '#CBD5E1', // Better contrast\n      },\n      body2: {\n        fontSize: '0.875rem',\n        lineHeight: 1.5,\n        fontWeight: 400,\n        color: isLight ? '#475569' : '#94A3B8', // Better contrast\n      },\n\n      // Modern button typography\n      button: {\n        fontSize: '0.875rem',\n        fontWeight: 500, // Reduced from 600\n        textTransform: 'none',\n        letterSpacing: '0.025em',\n      },\n    },\n\n    spacing: 4,\n\n    shape: {\n      borderRadius: 16,\n      modernRadius: 20,\n      glassRadius: 24,\n    },\n\n    // Modern shadows with glassmorphism\n    shadows: [\n      'none',\n      isLight \n        ? '0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.08)'\n        : '0px 1px 3px rgba(0, 0, 0, 0.24), 0px 1px 2px rgba(0, 0, 0, 0.16)',\n      isLight\n        ? '0px 3px 6px rgba(0, 0, 0, 0.16), 0px 2px 4px rgba(0, 0, 0, 0.08)'\n        : '0px 3px 6px rgba(0, 0, 0, 0.32), 0px 2px 4px rgba(0, 0, 0, 0.16)',\n      isLight\n        ? '0px 6px 12px rgba(0, 0, 0, 0.16), 0px 4px 8px rgba(0, 0, 0, 0.08)'\n        : '0px 6px 12px rgba(0, 0, 0, 0.32), 0px 4px 8px rgba(0, 0, 0, 0.16)',\n      isLight\n        ? '0px 8px 16px rgba(0, 0, 0, 0.16), 0px 6px 12px rgba(0, 0, 0, 0.08)'\n        : '0px 8px 16px rgba(0, 0, 0, 0.32), 0px 6px 12px rgba(0, 0, 0, 0.16)',\n      isLight\n        ? '0px 12px 24px rgba(0, 0, 0, 0.16), 0px 8px 16px rgba(0, 0, 0, 0.08)'\n        : '0px 12px 24px rgba(0, 0, 0, 0.32), 0px 8px 16px rgba(0, 0, 0, 0.16)',\n      // Glassmorphism shadow\n      isLight\n        ? '0px 8px 32px rgba(46, 91, 168, 0.15), 0px 1px 0px rgba(255, 255, 255, 0.05) inset'\n        : '0px 8px 32px rgba(0, 0, 0, 0.4), 0px 1px 0px rgba(255, 255, 255, 0.1) inset',\n    ],\n\n    transitions: {\n      easing: {\n        easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',\n        easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',\n        easeIn: 'cubic-bezier(0.4, 0, 1, 1)',\n        sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',\n        modern: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n        bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',\n      },\n      duration: {\n        shortest: 150,\n        shorter: 200,\n        short: 250,\n        standard: 300,\n        complex: 375,\n        modern: 400,\n        enteringScreen: 225,\n        leavingScreen: 195,\n      },\n    },\n\n    // Modern component overrides\n    components: {\n      MuiCard: {\n        styleOverrides: {\n          root: {\n            borderRadius: 20,\n            border: isLight ? '1px solid rgba(226, 232, 240, 0.8)' : '1px solid rgba(51, 65, 85, 0.8)',\n            backdropFilter: 'blur(20px)',\n            background: isLight \n              ? 'rgba(255, 255, 255, 0.8)'\n              : 'rgba(30, 41, 59, 0.8)',\n            transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n            '&:hover': {\n              transform: 'translateY(-4px)',\n              boxShadow: isLight\n                ? '0px 20px 40px rgba(46, 91, 168, 0.15)'\n                : '0px 20px 40px rgba(0, 0, 0, 0.4)',\n            },\n          },\n        },\n      },\n      MuiButton: {\n        styleOverrides: {\n          root: {\n            borderRadius: 12,\n            textTransform: 'none',\n            fontWeight: 600,\n            padding: '12px 24px',\n            transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n            '&:hover': {\n              transform: 'translateY(-2px)',\n            },\n          },\n          contained: {\n            boxShadow: '0px 4px 12px rgba(46, 91, 168, 0.3)',\n            '&:hover': {\n              boxShadow: '0px 8px 24px rgba(46, 91, 168, 0.4)',\n            },\n          },\n        },\n      },\n      MuiChip: {\n        styleOverrides: {\n          root: {\n            borderRadius: 12,\n            fontWeight: 500,\n            backdropFilter: 'blur(10px)',\n          },\n        },\n      },\n    },\n  });\n};\n\nexport default createModernEducationalTheme;\n", "/**\n * VidyaMitra Platform - Theme Context Provider\n * \n * Provides theme management with dark/light mode switching,\n * modern design system, and cultural adaptations\n */\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport createModernEducationalTheme from '../theme/modernEducationalTheme';\n\n// Create Theme Context\nconst ThemeContext = createContext();\n\n// Custom hook to use theme context\nexport const useThemeMode = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useThemeMode must be used within a ThemeContextProvider');\n  }\n  return context;\n};\n\n// Theme Context Provider Component\nexport const ThemeContextProvider = ({ children }) => {\n  // Get initial theme mode from localStorage or default to light\n  const [mode, setMode] = useState(() => {\n    const savedMode = localStorage.getItem('vidyamitra-theme-mode');\n    return savedMode || 'light';\n  });\n\n  // Create theme based on current mode\n  const theme = createModernEducationalTheme(mode);\n\n  // Toggle between light and dark mode\n  const toggleMode = () => {\n    const newMode = mode === 'light' ? 'dark' : 'light';\n    setMode(newMode);\n    localStorage.setItem('vidyamitra-theme-mode', newMode);\n  };\n\n  // Set specific mode\n  const setThemeMode = (newMode) => {\n    if (newMode === 'light' || newMode === 'dark') {\n      setMode(newMode);\n      localStorage.setItem('vidyamitra-theme-mode', newMode);\n    }\n  };\n\n  // Auto-detect system preference\n  const setSystemMode = () => {\n    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    const systemMode = systemPrefersDark ? 'dark' : 'light';\n    setThemeMode(systemMode);\n  };\n\n  // Listen for system theme changes\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    \n    const handleChange = (e) => {\n      // Only auto-switch if user hasn't manually set a preference\n      const savedMode = localStorage.getItem('vidyamitra-theme-mode');\n      if (!savedMode) {\n        setMode(e.matches ? 'dark' : 'light');\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    \n    return () => {\n      mediaQuery.removeEventListener('change', handleChange);\n    };\n  }, []);\n\n  // Context value\n  const contextValue = {\n    mode,\n    theme,\n    toggleMode,\n    setThemeMode,\n    setSystemMode,\n    isDark: mode === 'dark',\n    isLight: mode === 'light',\n  };\n\n  return (\n    <ThemeContext.Provider value={contextValue}>\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        {children}\n      </ThemeProvider>\n    </ThemeContext.Provider>\n  );\n};\n\n// Enhanced Theme Provider with additional features\nexport const EnhancedThemeProvider = ({ children }) => {\n  const [mode, setMode] = useState(() => {\n    const savedMode = localStorage.getItem('vidyamitra-theme-mode');\n    return savedMode || 'light';\n  });\n\n  const [customizations, setCustomizations] = useState(() => {\n    const savedCustomizations = localStorage.getItem('vidyamitra-theme-customizations');\n    return savedCustomizations ? JSON.parse(savedCustomizations) : {};\n  });\n\n  // Create theme with customizations\n  const theme = createTheme(createModernEducationalTheme(mode), customizations);\n\n  const toggleMode = () => {\n    const newMode = mode === 'light' ? 'dark' : 'light';\n    setMode(newMode);\n    localStorage.setItem('vidyamitra-theme-mode', newMode);\n  };\n\n  const updateCustomizations = (newCustomizations) => {\n    setCustomizations(prev => {\n      const updated = { ...prev, ...newCustomizations };\n      localStorage.setItem('vidyamitra-theme-customizations', JSON.stringify(updated));\n      return updated;\n    });\n  };\n\n  const resetCustomizations = () => {\n    setCustomizations({});\n    localStorage.removeItem('vidyamitra-theme-customizations');\n  };\n\n  // Board-specific theme switching\n  const setBoardTheme = (board) => {\n    const boardColors = {\n      CBSE: { primary: { main: '#2E5BA8' } },\n      ICSE: { primary: { main: '#FF9933' } },\n      STATE: { primary: { main: '#00C853' } },\n      IB: { primary: { main: '#9C27B0' } },\n    };\n\n    if (boardColors[board]) {\n      updateCustomizations({\n        palette: boardColors[board],\n      });\n    }\n  };\n\n  // Cultural theme variants\n  const setCulturalTheme = (variant) => {\n    const culturalThemes = {\n      traditional: {\n        palette: {\n          primary: { main: '#8B4513' }, // Henna brown\n          secondary: { main: '#FF69B4' }, // Lotus pink\n        },\n      },\n      modern: {\n        palette: {\n          primary: { main: '#2E5BA8' },\n          secondary: { main: '#FF9933' },\n        },\n      },\n      festive: {\n        palette: {\n          primary: { main: '#FF6B35' }, // Marigold\n          secondary: { main: '#F7931E' }, // Saffron\n        },\n      },\n    };\n\n    if (culturalThemes[variant]) {\n      updateCustomizations(culturalThemes[variant]);\n    }\n  };\n\n  // Accessibility enhancements\n  const setAccessibilityMode = (enabled) => {\n    if (enabled) {\n      updateCustomizations({\n        palette: {\n          mode: mode,\n          contrastThreshold: 4.5, // WCAG AA compliance\n        },\n        typography: {\n          fontSize: 16, // Larger base font size\n        },\n        components: {\n          MuiButton: {\n            styleOverrides: {\n              root: {\n                minHeight: 44, // Larger touch targets\n              },\n            },\n          },\n        },\n      });\n    } else {\n      resetCustomizations();\n    }\n  };\n\n  const contextValue = {\n    mode,\n    theme,\n    customizations,\n    toggleMode,\n    setThemeMode: setMode,\n    updateCustomizations,\n    resetCustomizations,\n    setBoardTheme,\n    setCulturalTheme,\n    setAccessibilityMode,\n    isDark: mode === 'dark',\n    isLight: mode === 'light',\n  };\n\n  return (\n    <ThemeContext.Provider value={contextValue}>\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        {children}\n      </ThemeProvider>\n    </ThemeContext.Provider>\n  );\n};\n\n// Theme customization hook\nexport const useThemeCustomization = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useThemeCustomization must be used within a ThemeContextProvider');\n  }\n  \n  return {\n    setBoardTheme: context.setBoardTheme,\n    setCulturalTheme: context.setCulturalTheme,\n    setAccessibilityMode: context.setAccessibilityMode,\n    updateCustomizations: context.updateCustomizations,\n    resetCustomizations: context.resetCustomizations,\n  };\n};\n\n// Performance monitoring hook\nexport const useThemePerformance = () => {\n  const [renderTime, setRenderTime] = useState(0);\n  \n  useEffect(() => {\n    const startTime = performance.now();\n    \n    const observer = new PerformanceObserver((list) => {\n      const entries = list.getEntries();\n      const paintEntry = entries.find(entry => entry.name === 'first-contentful-paint');\n      if (paintEntry) {\n        setRenderTime(paintEntry.startTime - startTime);\n      }\n    });\n    \n    observer.observe({ entryTypes: ['paint'] });\n    \n    return () => observer.disconnect();\n  }, []);\n  \n  return { renderTime };\n};\n\nexport default ThemeContextProvider;\n", "/**\n * VidyaMitra Platform - Modern Metric Card Component\n * \n * Contemporary metric cards with glassmorphism, animations, and interactive elements\n * Features animated counters, progress indicators, and smooth hover effects\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  CardContent,\n  Typography,\n  Box,\n  Avatar,\n  IconButton,\n  LinearProgress,\n  Chip,\n  useTheme,\n  alpha,\n  Tooltip,\n  CircularProgress,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  TrendingDown,\n  MoreVert,\n  InfoOutlined,\n  ArrowUpward,\n  ArrowDownward,\n} from '@mui/icons-material';\nimport { motion, useSpring, useTransform } from 'framer-motion';\n\n// Animated Counter Component\nconst AnimatedCounter = ({ value, duration = 2000, suffix = '', prefix = '' }) => {\n  const [displayValue, setDisplayValue] = useState(0);\n\n  useEffect(() => {\n    let startTime;\n    let animationFrame;\n\n    const animate = (timestamp) => {\n      if (!startTime) startTime = timestamp;\n      const progress = Math.min((timestamp - startTime) / duration, 1);\n      \n      // Easing function for smooth animation\n      const easeOutQuart = 1 - Math.pow(1 - progress, 4);\n      const currentValue = Math.floor(easeOutQuart * value);\n      \n      setDisplayValue(currentValue);\n\n      if (progress < 1) {\n        animationFrame = requestAnimationFrame(animate);\n      }\n    };\n\n    animationFrame = requestAnimationFrame(animate);\n\n    return () => {\n      if (animationFrame) {\n        cancelAnimationFrame(animationFrame);\n      }\n    };\n  }, [value, duration]);\n\n  return (\n    <span>\n      {prefix}{displayValue.toLocaleString()}{suffix}\n    </span>\n  );\n};\n\n// Animated Progress Bar Component\nconst AnimatedProgress = ({ value, color = 'primary', delay = 0 }) => {\n  const [progress, setProgress] = useState(0);\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setProgress(value);\n    }, delay);\n\n    return () => clearTimeout(timer);\n  }, [value, delay]);\n\n  return (\n    <LinearProgress\n      variant=\"determinate\"\n      value={progress}\n      color={color}\n      sx={{\n        height: 8,\n        borderRadius: 4,\n        backgroundColor: alpha('#000', 0.1),\n        '& .MuiLinearProgress-bar': {\n          borderRadius: 4,\n          transition: 'transform 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n        },\n      }}\n    />\n  );\n};\n\n// Trend Indicator Component\nconst TrendIndicator = ({ trend, value, label }) => {\n  const theme = useTheme();\n  const isPositive = trend === 'up';\n  \n  return (\n    <Box\n      sx={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: 0.5,\n        px: 1,\n        py: 0.5,\n        borderRadius: 1,\n        backgroundColor: alpha(\n          isPositive ? theme.palette.success.main : theme.palette.error.main,\n          0.1\n        ),\n      }}\n    >\n      {isPositive ? (\n        <ArrowUpward sx={{ fontSize: 16, color: theme.palette.success.main }} />\n      ) : (\n        <ArrowDownward sx={{ fontSize: 16, color: theme.palette.error.main }} />\n      )}\n      <Typography\n        variant=\"caption\"\n        sx={{\n          fontWeight: 600,\n          color: isPositive ? theme.palette.success.main : theme.palette.error.main,\n        }}\n      >\n        {value}%\n      </Typography>\n      <Typography variant=\"caption\" color=\"text.secondary\">\n        {label}\n      </Typography>\n    </Box>\n  );\n};\n\n// Main Modern Metric Card Component\nconst ModernMetricCard = ({\n  title,\n  value,\n  subtitle,\n  icon: Icon,\n  trend,\n  trendValue,\n  trendLabel = 'vs last month',\n  progress,\n  progressLabel,\n  color = 'primary',\n  variant = 'default',\n  onClick,\n  loading = false,\n  actionIcon,\n  onActionClick,\n  tooltip,\n  gradient = false,\n  glassmorphism = true,\n}) => {\n  const theme = useTheme();\n  const [isHovered, setIsHovered] = useState(false);\n\n  const cardVariants = {\n    initial: { \n      scale: 1, \n      y: 0,\n      boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',\n    },\n    hover: { \n      scale: 1.02, \n      y: -8,\n      boxShadow: '0px 20px 40px rgba(46, 91, 168, 0.15)',\n      transition: {\n        type: 'spring',\n        stiffness: 300,\n        damping: 20,\n      },\n    },\n  };\n\n  const iconVariants = {\n    initial: { rotate: 0, scale: 1 },\n    hover: { \n      rotate: 5, \n      scale: 1.1,\n      transition: {\n        type: 'spring',\n        stiffness: 400,\n        damping: 10,\n      },\n    },\n  };\n\n  const getCardBackground = () => {\n    if (gradient) {\n      return theme.palette.gradients[color] || theme.palette.gradients.primary;\n    }\n    \n    if (glassmorphism) {\n      return alpha(theme.palette.background.paper, 0.8);\n    }\n    \n    return theme.palette.background.paper;\n  };\n\n  const getIconColor = () => {\n    if (gradient) return '#FFFFFF';\n    return theme.palette[color]?.main || theme.palette.primary.main;\n  };\n\n  const getTextColor = () => {\n    if (gradient) return '#FFFFFF';\n    return theme.palette.text.primary;\n  };\n\n  return (\n    <Tooltip title={tooltip} arrow placement=\"top\">\n      <motion.div\n        variants={cardVariants}\n        initial=\"initial\"\n        whileHover=\"hover\"\n        onHoverStart={() => setIsHovered(true)}\n        onHoverEnd={() => setIsHovered(false)}\n      >\n        <Card\n          onClick={onClick}\n          sx={{\n            height: '100%',\n            cursor: onClick ? 'pointer' : 'default',\n            borderRadius: 3,\n            background: getCardBackground(),\n            backdropFilter: glassmorphism ? 'blur(20px)' : 'none',\n            border: glassmorphism \n              ? `1px solid ${alpha(theme.palette.primary.main, 0.1)}`\n              : 'none',\n            overflow: 'hidden',\n            position: 'relative',\n            '&::before': gradient ? {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: getCardBackground(),\n              zIndex: -1,\n            } : {},\n          }}\n        >\n          <CardContent sx={{ p: 3, height: '100%' }}>\n            {/* Header */}\n            <Box\n              sx={{\n                display: 'flex',\n                alignItems: 'flex-start',\n                justifyContent: 'space-between',\n                mb: 2,\n              }}\n            >\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                <motion.div variants={iconVariants}>\n                  <Avatar\n                    sx={{\n                      bgcolor: gradient \n                        ? alpha('#FFFFFF', 0.2)\n                        : alpha(theme.palette[color]?.main || theme.palette.primary.main, 0.1),\n                      color: getIconColor(),\n                      width: 56,\n                      height: 56,\n                    }}\n                  >\n                    {loading ? (\n                      <CircularProgress size={24} color=\"inherit\" />\n                    ) : (\n                      <Icon sx={{ fontSize: 28 }} />\n                    )}\n                  </Avatar>\n                </motion.div>\n                \n                <Box>\n                  <Typography\n                    variant=\"body2\"\n                    sx={{\n                      color: gradient \n                        ? alpha('#FFFFFF', 0.8)\n                        : theme.palette.text.secondary,\n                      fontWeight: 500,\n                      mb: 0.5,\n                    }}\n                  >\n                    {title}\n                  </Typography>\n                  {subtitle && (\n                    <Typography\n                      variant=\"caption\"\n                      sx={{\n                        color: gradient \n                          ? alpha('#FFFFFF', 0.6)\n                          : theme.palette.text.disabled,\n                      }}\n                    >\n                      {subtitle}\n                    </Typography>\n                  )}\n                </Box>\n              </Box>\n\n              {(actionIcon || onActionClick) && (\n                <IconButton\n                  size=\"small\"\n                  onClick={onActionClick}\n                  sx={{\n                    color: gradient \n                      ? alpha('#FFFFFF', 0.8)\n                      : theme.palette.text.secondary,\n                  }}\n                >\n                  {actionIcon || <MoreVert />}\n                </IconButton>\n              )}\n            </Box>\n\n            {/* Main Value */}\n            <Box sx={{ mb: 2 }}>\n              <Typography\n                variant=\"h3\"\n                sx={{\n                  fontWeight: 800,\n                  color: getTextColor(),\n                  lineHeight: 1,\n                  mb: 1,\n                }}\n              >\n                {loading ? (\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <CircularProgress size={20} color=\"inherit\" />\n                    <span>--</span>\n                  </Box>\n                ) : typeof value === 'number' ? (\n                  <AnimatedCounter value={value} />\n                ) : (\n                  value\n                )}\n              </Typography>\n\n              {/* Trend Indicator */}\n              {trend && trendValue && (\n                <TrendIndicator\n                  trend={trend}\n                  value={trendValue}\n                  label={trendLabel}\n                />\n              )}\n            </Box>\n\n            {/* Progress Bar */}\n            {progress !== undefined && (\n              <Box sx={{ mb: 1 }}>\n                <Box\n                  sx={{\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    mb: 1,\n                  }}\n                >\n                  <Typography\n                    variant=\"caption\"\n                    sx={{\n                      color: gradient \n                        ? alpha('#FFFFFF', 0.8)\n                        : theme.palette.text.secondary,\n                      fontWeight: 500,\n                    }}\n                  >\n                    {progressLabel || 'Progress'}\n                  </Typography>\n                  <Typography\n                    variant=\"caption\"\n                    sx={{\n                      color: getTextColor(),\n                      fontWeight: 600,\n                    }}\n                  >\n                    {progress}%\n                  </Typography>\n                </Box>\n                <AnimatedProgress\n                  value={progress}\n                  color={color}\n                  delay={500}\n                />\n              </Box>\n            )}\n\n            {/* Additional Content Slot */}\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{\n                opacity: isHovered ? 1 : 0,\n                height: isHovered ? 'auto' : 0,\n              }}\n              transition={{ duration: 0.3 }}\n            >\n              <Box sx={{ mt: 2, pt: 2, borderTop: `1px solid ${alpha(getTextColor(), 0.1)}` }}>\n                <Typography\n                  variant=\"caption\"\n                  sx={{\n                    color: gradient \n                      ? alpha('#FFFFFF', 0.6)\n                      : theme.palette.text.disabled,\n                  }}\n                >\n                  Click for detailed view\n                </Typography>\n              </Box>\n            </motion.div>\n          </CardContent>\n        </Card>\n      </motion.div>\n    </Tooltip>\n  );\n};\n\nexport default ModernMetricCard;\n", "/**\n * VidyaMitra Platform - Authentic Indian Student Data\n * \n * Comprehensive student profiles with realistic Indian names, academic performance,\n * attendance records, behavioral data, and SWOT analysis results\n */\n\n// Academic subjects for different boards\nconst subjects = {\n  cbse: ['Mathematics', 'Science', 'English', 'Hindi', 'Social Studies', 'Computer Science'],\n  icse: ['Mathematics', 'Physics', 'Chemistry', 'Biology', 'English', 'Hindi', 'History', 'Geography'],\n  state: ['Mathematics', 'Science', 'English', 'Telugu/Tamil/Regional', 'Social Studies', 'Environmental Science'],\n  ib: ['Mathematics', 'Sciences', 'English', 'Hindi', 'Individuals & Societies', 'Arts']\n};\n\n// Generate realistic performance data\nconst generatePerformanceData = (board, studentProfile) => {\n  const subjectList = subjects[board] || subjects.cbse;\n  const basePerformance = studentProfile.academicLevel;\n  \n  return subjectList.map(subject => ({\n    subject,\n    currentScore: Math.max(35, Math.min(100, basePerformance + (Math.random() - 0.5) * 20)),\n    previousScore: Math.max(30, Math.min(95, basePerformance + (Math.random() - 0.5) * 25)),\n    trend: Math.random() > 0.6 ? 'improving' : Math.random() > 0.3 ? 'stable' : 'declining',\n    assignments: Math.floor(Math.random() * 10) + 15,\n    assignmentsCompleted: Math.floor(Math.random() * 5) + 12,\n  }));\n};\n\n// Generate attendance data\nconst generateAttendanceData = () => {\n  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n  return months.map(month => ({\n    month,\n    present: Math.floor(Math.random() * 8) + 18, // 18-25 days\n    total: Math.floor(Math.random() * 3) + 22, // 22-24 days\n    percentage: Math.floor(Math.random() * 20) + 75, // 75-95%\n  }));\n};\n\n// Generate behavioral incidents\nconst generateBehavioralData = () => {\n  const incidents = [\n    'Late to class', 'Incomplete homework', 'Disruptive behavior', 'Excellent participation',\n    'Helped classmates', 'Leadership in group work', 'Creative thinking', 'Respectful behavior'\n  ];\n  \n  const count = Math.floor(Math.random() * 6) + 2; // 2-7 incidents\n  return Array.from({ length: count }, () => ({\n    date: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),\n    type: incidents[Math.floor(Math.random() * incidents.length)],\n    severity: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low',\n    description: 'Detailed incident description would be here',\n  }));\n};\n\n// Generate extracurricular activities\nconst generateExtracurriculars = () => {\n  const activities = [\n    'Cricket', 'Football', 'Basketball', 'Badminton', 'Chess', 'Debate Club',\n    'Science Club', 'Art Club', 'Music', 'Dance', 'Drama', 'Robotics',\n    'Environmental Club', 'Literary Society', 'Mathematics Olympiad'\n  ];\n  \n  const count = Math.floor(Math.random() * 4) + 1; // 1-4 activities\n  return Array.from({ length: count }, () => ({\n    activity: activities[Math.floor(Math.random() * activities.length)],\n    level: Math.random() > 0.6 ? 'advanced' : Math.random() > 0.3 ? 'intermediate' : 'beginner',\n    achievements: Math.random() > 0.7 ? ['District Level Winner'] : Math.random() > 0.4 ? ['School Level Participant'] : [],\n  }));\n};\n\n// Generate SWOT analysis\nconst generateSWOTAnalysis = (studentProfile) => {\n  const strengthsPool = [\n    'Strong analytical thinking', 'Excellent communication skills', 'Creative problem solving',\n    'Leadership qualities', 'Team collaboration', 'Mathematical aptitude', 'Scientific reasoning',\n    'Artistic abilities', 'Sports excellence', 'Language proficiency', 'Technical skills'\n  ];\n  \n  const weaknessesPool = [\n    'Time management', 'Public speaking anxiety', 'Difficulty with abstract concepts',\n    'Procrastination tendency', 'Perfectionism', 'Attention to detail', 'Organization skills',\n    'Stress management', 'Peer interaction', 'Technology adaptation'\n  ];\n  \n  const opportunitiesPool = [\n    'Advanced placement courses', 'Science fair participation', 'Leadership roles',\n    'Internship programs', 'Skill development workshops', 'Mentorship programs',\n    'Competition participation', 'Community service', 'Online learning platforms'\n  ];\n  \n  const threatsPool = [\n    'Academic pressure', 'Peer competition', 'Technology distractions',\n    'Time constraints', 'Resource limitations', 'Career uncertainty',\n    'Social media influence', 'Health concerns', 'Family expectations'\n  ];\n  \n  return {\n    strengths: strengthsPool.sort(() => 0.5 - Math.random()).slice(0, 3),\n    weaknesses: weaknessesPool.sort(() => 0.5 - Math.random()).slice(0, 2),\n    opportunities: opportunitiesPool.sort(() => 0.5 - Math.random()).slice(0, 3),\n    threats: threatsPool.sort(() => 0.5 - Math.random()).slice(0, 2),\n    lastUpdated: new Date(),\n    confidence: Math.floor(Math.random() * 20) + 75, // 75-95% confidence\n  };\n};\n\n// Comprehensive student profiles\nexport const studentProfiles = [\n  {\n    id: 'STU001',\n    name: 'Sanju Kumar',\n    grade: '10th',\n    section: 'A',\n    board: 'cbse',\n    rollNumber: 'CBSE2024001',\n    academicLevel: 85,\n    region: 'Telangana',\n    parentContact: '+91 **********',\n    email: '<EMAIL>',\n    dateOfBirth: '2009-03-15',\n    address: 'Hyderabad, Telangana',\n  },\n  {\n    id: 'STU002',\n    name: 'Niraimathi Selvam',\n    grade: '9th',\n    section: 'B',\n    board: 'state',\n    rollNumber: 'TN2024002',\n    academicLevel: 78,\n    region: 'Tamil Nadu',\n    parentContact: '+91 9876543211',\n    email: '<EMAIL>',\n    dateOfBirth: '2010-07-22',\n    address: 'Chennai, Tamil Nadu',\n  },\n  {\n    id: 'STU003',\n    name: 'Mahesh Reddy',\n    grade: '11th',\n    section: 'A',\n    board: 'cbse',\n    rollNumber: 'CBSE2024003',\n    academicLevel: 92,\n    region: 'Andhra Pradesh',\n    parentContact: '+91 9876543212',\n    email: '<EMAIL>',\n    dateOfBirth: '2008-11-08',\n    address: 'Vijayawada, Andhra Pradesh',\n  },\n  {\n    id: 'STU004',\n    name: 'Ravi Teja Sharma',\n    grade: '10th',\n    section: 'C',\n    board: 'icse',\n    rollNumber: 'ICSE2024004',\n    academicLevel: 88,\n    region: 'Karnataka',\n    parentContact: '+91 9876543213',\n    email: '<EMAIL>',\n    dateOfBirth: '2009-01-30',\n    address: 'Bangalore, Karnataka',\n  },\n  {\n    id: 'STU005',\n    name: 'Ankitha Patel',\n    grade: '12th',\n    section: 'A',\n    board: 'cbse',\n    rollNumber: 'CBSE2024005',\n    academicLevel: 95,\n    region: 'Gujarat',\n    parentContact: '+91 9876543214',\n    email: '<EMAIL>',\n    dateOfBirth: '2007-05-12',\n    address: 'Ahmedabad, Gujarat',\n  },\n  {\n    id: 'STU006',\n    name: 'Sirisha Nair',\n    grade: '9th',\n    section: 'B',\n    board: 'state',\n    rollNumber: 'KL2024006',\n    academicLevel: 82,\n    region: 'Kerala',\n    parentContact: '+91 9876543215',\n    email: '<EMAIL>',\n    dateOfBirth: '2010-09-18',\n    address: 'Kochi, Kerala',\n  },\n  {\n    id: 'STU007',\n    name: 'Priya Agarwal',\n    grade: '11th',\n    section: 'B',\n    board: 'cbse',\n    rollNumber: 'CBSE2024007',\n    academicLevel: 89,\n    region: 'Rajasthan',\n    parentContact: '+91 9876543216',\n    email: '<EMAIL>',\n    dateOfBirth: '2008-12-03',\n    address: 'Jaipur, Rajasthan',\n  },\n  {\n    id: 'STU008',\n    name: 'Arjun Singh',\n    grade: '10th',\n    section: 'A',\n    board: 'cbse',\n    rollNumber: 'CBSE2024008',\n    academicLevel: 76,\n    region: 'Punjab',\n    parentContact: '+91 9876543217',\n    email: '<EMAIL>',\n    dateOfBirth: '2009-04-25',\n    address: 'Chandigarh, Punjab',\n  },\n  {\n    id: 'STU009',\n    name: 'Kavya Menon',\n    grade: '12th',\n    section: 'C',\n    board: 'icse',\n    rollNumber: 'ICSE2024009',\n    academicLevel: 91,\n    region: 'Kerala',\n    parentContact: '+91 9876543218',\n    email: '<EMAIL>',\n    dateOfBirth: '2007-08-14',\n    address: 'Thiruvananthapuram, Kerala',\n  },\n  {\n    id: 'STU010',\n    name: 'Rohit Gupta',\n    grade: '9th',\n    section: 'A',\n    board: 'state',\n    rollNumber: 'UP2024010',\n    academicLevel: 84,\n    region: 'Uttar Pradesh',\n    parentContact: '+91 9876543219',\n    email: '<EMAIL>',\n    dateOfBirth: '2010-02-28',\n    address: 'Lucknow, Uttar Pradesh',\n  },\n];\n\n// Generate complete student data\nexport const generateCompleteStudentData = () => {\n  return studentProfiles.map(profile => ({\n    ...profile,\n    performance: generatePerformanceData(profile.board, profile),\n    attendance: generateAttendanceData(),\n    behavioral: generateBehavioralData(),\n    extracurricular: generateExtracurriculars(),\n    swotAnalysis: generateSWOTAnalysis(profile),\n    overallGrade: profile.academicLevel >= 90 ? 'A+' : \n                  profile.academicLevel >= 80 ? 'A' :\n                  profile.academicLevel >= 70 ? 'B' :\n                  profile.academicLevel >= 60 ? 'C' : 'D',\n    rank: Math.floor(Math.random() * 50) + 1, // Rank out of 50 students\n    lastUpdated: new Date(),\n  }));\n};\n\n// Analytics data for dashboard\nexport const generateAnalyticsData = (students) => {\n  const totalStudents = students.length;\n  const averagePerformance = students.reduce((sum, student) => sum + student.academicLevel, 0) / totalStudents;\n  const averageAttendance = students.reduce((sum, student) => {\n    const totalPresent = student.attendance.reduce((p, a) => p + a.present, 0);\n    const totalDays = student.attendance.reduce((p, a) => p + a.total, 0);\n    return sum + (totalPresent / totalDays * 100);\n  }, 0) / totalStudents;\n\n  return {\n    totalStudents,\n    averagePerformance: Math.round(averagePerformance),\n    averageAttendance: Math.round(averageAttendance),\n    topPerformers: students.filter(s => s.academicLevel >= 90).length,\n    needsAttention: students.filter(s => s.academicLevel < 70).length,\n    boardDistribution: {\n      cbse: students.filter(s => s.board === 'cbse').length,\n      icse: students.filter(s => s.board === 'icse').length,\n      state: students.filter(s => s.board === 'state').length,\n      ib: students.filter(s => s.board === 'ib').length,\n    },\n    gradeDistribution: {\n      '9th': students.filter(s => s.grade === '9th').length,\n      '10th': students.filter(s => s.grade === '10th').length,\n      '11th': students.filter(s => s.grade === '11th').length,\n      '12th': students.filter(s => s.grade === '12th').length,\n    },\n    performanceTrends: generatePerformanceTrends(students),\n    subjectAnalysis: generateSubjectAnalysis(students),\n    attendancePatterns: generateAttendancePatterns(students),\n    swotDistribution: generateSWOTDistribution(students),\n  };\n};\n\n// Generate performance trends over time\nexport const generatePerformanceTrends = (students) => {\n  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n  return months.map(month => ({\n    month,\n    average: Math.floor(Math.random() * 15) + 75, // 75-90 range\n    cbse: Math.floor(Math.random() * 15) + 78,\n    icse: Math.floor(Math.random() * 15) + 80,\n    state: Math.floor(Math.random() * 15) + 73,\n  }));\n};\n\n// Generate subject-wise analysis\nexport const generateSubjectAnalysis = (students) => {\n  const allSubjects = ['Mathematics', 'Science', 'English', 'Hindi', 'Social Studies', 'Computer Science'];\n  return allSubjects.map(subject => ({\n    subject,\n    averageScore: Math.floor(Math.random() * 20) + 70,\n    topScore: Math.floor(Math.random() * 10) + 90,\n    lowestScore: Math.floor(Math.random() * 20) + 45,\n    studentsAbove80: Math.floor(Math.random() * students.length * 0.6),\n    studentsBelow60: Math.floor(Math.random() * students.length * 0.2),\n  }));\n};\n\n// Generate attendance patterns\nexport const generateAttendancePatterns = (students) => {\n  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n  return months.map(month => ({\n    month,\n    averageAttendance: Math.floor(Math.random() * 15) + 80, // 80-95%\n    totalStudents: students.length,\n    presentStudents: Math.floor(students.length * (0.8 + Math.random() * 0.15)),\n  }));\n};\n\n// Generate SWOT distribution\nexport const generateSWOTDistribution = (students) => {\n  return {\n    strengthsDistribution: {\n      'Analytical Thinking': Math.floor(students.length * 0.3),\n      'Communication Skills': Math.floor(students.length * 0.25),\n      'Leadership': Math.floor(students.length * 0.2),\n      'Creativity': Math.floor(students.length * 0.35),\n      'Technical Skills': Math.floor(students.length * 0.4),\n    },\n    weaknessesDistribution: {\n      'Time Management': Math.floor(students.length * 0.4),\n      'Public Speaking': Math.floor(students.length * 0.3),\n      'Organization': Math.floor(students.length * 0.25),\n      'Stress Management': Math.floor(students.length * 0.35),\n    },\n    opportunitiesDistribution: {\n      'Advanced Courses': Math.floor(students.length * 0.6),\n      'Leadership Roles': Math.floor(students.length * 0.3),\n      'Competitions': Math.floor(students.length * 0.5),\n      'Skill Development': Math.floor(students.length * 0.7),\n    },\n    threatsDistribution: {\n      'Academic Pressure': Math.floor(students.length * 0.5),\n      'Peer Competition': Math.floor(students.length * 0.4),\n      'Technology Distractions': Math.floor(students.length * 0.6),\n      'Time Constraints': Math.floor(students.length * 0.45),\n    },\n  };\n};\n\nexport default generateCompleteStudentData;\n", "/**\n * VidyaMitra Platform - Enhanced Button Component\n * \n * Comprehensive button component with proper hover states, loading states,\n * and accessibility features for the Indian educational context\n */\n\nimport React, { useState } from 'react';\nimport {\n  Button,\n  CircularProgress,\n  Box,\n  Tooltip,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport { motion } from 'framer-motion';\n\nconst EnhancedButton = ({\n  children,\n  loading = false,\n  disabled = false,\n  variant = 'contained',\n  color = 'primary',\n  size = 'medium',\n  startIcon,\n  endIcon,\n  onClick,\n  tooltip,\n  fullWidth = false,\n  sx = {},\n  loadingText = 'Loading...',\n  successFeedback = false,\n  errorFeedback = false,\n  ...props\n}) => {\n  const theme = useTheme();\n  const [isClicked, setIsClicked] = useState(false);\n  const [showSuccess, setShowSuccess] = useState(false);\n  const [showError, setShowError] = useState(false);\n\n  const handleClick = async (event) => {\n    if (loading || disabled) return;\n\n    setIsClicked(true);\n    \n    try {\n      if (onClick) {\n        const result = await onClick(event);\n        \n        if (successFeedback && result !== false) {\n          setShowSuccess(true);\n          setTimeout(() => setShowSuccess(false), 2000);\n        }\n      }\n    } catch (error) {\n      if (errorFeedback) {\n        setShowError(true);\n        setTimeout(() => setShowError(false), 2000);\n      }\n    } finally {\n      setTimeout(() => setIsClicked(false), 150);\n    }\n  };\n\n  const getButtonColor = () => {\n    if (showSuccess) return 'success';\n    if (showError) return 'error';\n    return color;\n  };\n\n  const getButtonContent = () => {\n    if (loading) {\n      return (\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <CircularProgress \n            size={16} \n            color=\"inherit\" \n            sx={{ \n              color: variant === 'contained' ? 'white' : theme.palette[color].main \n            }} \n          />\n          {loadingText}\n        </Box>\n      );\n    }\n\n    if (showSuccess) {\n      return (\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          ✓ Success\n        </Box>\n      );\n    }\n\n    if (showError) {\n      return (\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          ✗ Error\n        </Box>\n      );\n    }\n\n    return (\n      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n        {startIcon}\n        {children}\n        {endIcon}\n      </Box>\n    );\n  };\n\n  const buttonVariants = {\n    initial: { scale: 1 },\n    hover: { \n      scale: 1.02,\n      transition: { \n        type: 'spring', \n        stiffness: 400, \n        damping: 25 \n      }\n    },\n    tap: { \n      scale: 0.98,\n      transition: { \n        type: 'spring', \n        stiffness: 400, \n        damping: 25 \n      }\n    },\n  };\n\n  const enhancedSx = {\n    borderRadius: 3,\n    textTransform: 'none',\n    fontWeight: 600,\n    minHeight: size === 'small' ? 36 : size === 'large' ? 52 : 44,\n    position: 'relative',\n    overflow: 'hidden',\n    transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n    \n    // Enhanced hover effects\n    '&:hover': {\n      transform: 'translateY(-2px)',\n      boxShadow: variant === 'contained' \n        ? `0px 8px 24px ${alpha(theme.palette[getButtonColor()].main, 0.4)}`\n        : `0px 4px 12px ${alpha(theme.palette[getButtonColor()].main, 0.2)}`,\n    },\n\n    // Active state\n    '&:active': {\n      transform: 'translateY(0px)',\n    },\n\n    // Focus state for accessibility\n    '&:focus-visible': {\n      outline: `2px solid ${theme.palette[getButtonColor()].main}`,\n      outlineOffset: '2px',\n    },\n\n    // Disabled state\n    '&:disabled': {\n      opacity: 0.6,\n      transform: 'none',\n      boxShadow: 'none',\n    },\n\n    // Loading state\n    ...(loading && {\n      pointerEvents: 'none',\n    }),\n\n    // Custom styles\n    ...sx,\n  };\n\n  const ButtonComponent = (\n    <motion.div\n      variants={buttonVariants}\n      initial=\"initial\"\n      whileHover={!loading && !disabled ? \"hover\" : \"initial\"}\n      whileTap={!loading && !disabled ? \"tap\" : \"initial\"}\n      style={{ display: fullWidth ? 'block' : 'inline-block', width: fullWidth ? '100%' : 'auto' }}\n    >\n      <Button\n        variant={variant}\n        color={getButtonColor()}\n        size={size}\n        disabled={disabled || loading}\n        onClick={handleClick}\n        fullWidth={fullWidth}\n        sx={enhancedSx}\n        {...props}\n      >\n        {getButtonContent()}\n      </Button>\n    </motion.div>\n  );\n\n  if (tooltip) {\n    return (\n      <Tooltip title={tooltip} arrow placement=\"top\">\n        <span style={{ display: fullWidth ? 'block' : 'inline-block' }}>\n          {ButtonComponent}\n        </span>\n      </Tooltip>\n    );\n  }\n\n  return ButtonComponent;\n};\n\nexport default EnhancedButton;\n", "/**\n * VidyaMitra Platform - Enhanced Card Component\n * \n * Interactive card component with glassmorphism design, hover effects,\n * and proper accessibility for the Indian educational context\n */\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  CardContent,\n  CardActions,\n  Box,\n  useTheme,\n  alpha,\n  Skeleton,\n} from '@mui/material';\nimport { motion } from 'framer-motion';\n\nconst EnhancedCard = ({\n  children,\n  onClick,\n  elevation = 2,\n  variant = 'default', // 'default', 'glassmorphism', 'gradient'\n  loading = false,\n  disabled = false,\n  hoverable = true,\n  clickable = false,\n  sx = {},\n  contentSx = {},\n  actions,\n  header,\n  footer,\n  color = 'primary',\n  gradient = false,\n  glassmorphism = false,\n  borderRadius = 3,\n  ...props\n}) => {\n  const theme = useTheme();\n  const [isHovered, setIsHovered] = useState(false);\n\n  const handleClick = (event) => {\n    if (disabled || loading || !onClick) return;\n    onClick(event);\n  };\n\n  const getCardBackground = () => {\n    if (glassmorphism) {\n      return theme.palette.mode === 'dark'\n        ? `linear-gradient(135deg, ${alpha('#1E293B', 0.8)} 0%, ${alpha('#334155', 0.6)} 100%)`\n        : `linear-gradient(135deg, ${alpha('#FFFFFF', 0.9)} 0%, ${alpha('#F8FAFC', 0.8)} 100%)`;\n    }\n\n    if (gradient) {\n      return `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`;\n    }\n\n    return theme.palette.background.paper;\n  };\n\n  const cardVariants = {\n    initial: { \n      scale: 1, \n      y: 0,\n      boxShadow: theme.shadows[elevation],\n    },\n    hover: hoverable ? { \n      scale: 1.02, \n      y: -8,\n      boxShadow: theme.shadows[Math.min(elevation + 4, 24)],\n      transition: {\n        type: 'spring',\n        stiffness: 300,\n        damping: 20,\n      },\n    } : {},\n    tap: clickable ? {\n      scale: 0.98,\n      transition: {\n        type: 'spring',\n        stiffness: 400,\n        damping: 25,\n      },\n    } : {},\n  };\n\n  const enhancedSx = {\n    borderRadius: borderRadius,\n    background: getCardBackground(),\n    backdropFilter: glassmorphism ? 'blur(20px)' : 'none',\n    border: glassmorphism \n      ? `1px solid ${alpha(theme.palette.divider, 0.1)}`\n      : 'none',\n    cursor: clickable ? 'pointer' : 'default',\n    transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n    position: 'relative',\n    overflow: 'hidden',\n    \n    // Hover effects\n    '&:hover': hoverable ? {\n      '& .card-content': {\n        transform: 'translateY(-2px)',\n      },\n      '& .card-actions': {\n        opacity: 1,\n        transform: 'translateY(0)',\n      },\n    } : {},\n\n    // Focus state for accessibility\n    ...(clickable && {\n      '&:focus-visible': {\n        outline: `2px solid ${theme.palette[color].main}`,\n        outlineOffset: '2px',\n      },\n    }),\n\n    // Disabled state\n    ...(disabled && {\n      opacity: 0.6,\n      pointerEvents: 'none',\n    }),\n\n    // Loading state\n    ...(loading && {\n      pointerEvents: 'none',\n    }),\n\n    ...sx,\n  };\n\n  const contentEnhancedSx = {\n    transition: 'transform 0.3s ease',\n    ...contentSx,\n  };\n\n  const LoadingSkeleton = () => (\n    <CardContent>\n      <Skeleton variant=\"text\" width=\"60%\" height={32} sx={{ mb: 2 }} />\n      <Skeleton variant=\"text\" width=\"100%\" height={20} sx={{ mb: 1 }} />\n      <Skeleton variant=\"text\" width=\"80%\" height={20} sx={{ mb: 2 }} />\n      <Skeleton variant=\"rectangular\" width=\"100%\" height={120} sx={{ borderRadius: 2 }} />\n    </CardContent>\n  );\n\n  return (\n    <motion.div\n      variants={cardVariants}\n      initial=\"initial\"\n      whileHover={!disabled && !loading ? \"hover\" : \"initial\"}\n      whileTap={!disabled && !loading && clickable ? \"tap\" : \"initial\"}\n      onHoverStart={() => setIsHovered(true)}\n      onHoverEnd={() => setIsHovered(false)}\n    >\n      <Card\n        elevation={0}\n        onClick={handleClick}\n        sx={enhancedSx}\n        tabIndex={clickable ? 0 : -1}\n        role={clickable ? 'button' : undefined}\n        {...props}\n      >\n        {/* Glassmorphism overlay effect */}\n        {glassmorphism && (\n          <Box\n            sx={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: `linear-gradient(135deg, ${alpha('#FFFFFF', 0.1)} 0%, ${alpha('#FFFFFF', 0.05)} 100%)`,\n              pointerEvents: 'none',\n              zIndex: 0,\n            }}\n          />\n        )}\n\n        {/* Header */}\n        {header && (\n          <Box sx={{ position: 'relative', zIndex: 1 }}>\n            {header}\n          </Box>\n        )}\n\n        {/* Content */}\n        <CardContent \n          className=\"card-content\"\n          sx={contentEnhancedSx}\n          style={{ position: 'relative', zIndex: 1 }}\n        >\n          {loading ? <LoadingSkeleton /> : children}\n        </CardContent>\n\n        {/* Actions */}\n        {actions && (\n          <CardActions \n            className=\"card-actions\"\n            sx={{\n              position: 'relative',\n              zIndex: 1,\n              opacity: hoverable ? 0.8 : 1,\n              transform: hoverable ? 'translateY(4px)' : 'none',\n              transition: 'all 0.3s ease',\n            }}\n          >\n            {actions}\n          </CardActions>\n        )}\n\n        {/* Footer */}\n        {footer && (\n          <Box sx={{ position: 'relative', zIndex: 1 }}>\n            {footer}\n          </Box>\n        )}\n\n        {/* Ripple effect for clickable cards */}\n        {clickable && isHovered && (\n          <Box\n            sx={{\n              position: 'absolute',\n              top: '50%',\n              left: '50%',\n              width: 0,\n              height: 0,\n              borderRadius: '50%',\n              background: alpha(theme.palette[color].main, 0.1),\n              transform: 'translate(-50%, -50%)',\n              animation: 'ripple 0.6s linear',\n              '@keyframes ripple': {\n                to: {\n                  width: '200%',\n                  height: '200%',\n                  opacity: 0,\n                },\n              },\n            }}\n          />\n        )}\n      </Card>\n    </motion.div>\n  );\n};\n\nexport default EnhancedCard;\n", "/**\n * VidyaMitra Platform - Modern Dashboard\n * \n * Contemporary dashboard with glassmorphism, smooth animations, and modern UI patterns\n * Features dark/light theme toggle, advanced filtering, and real-time updates\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Typography,\n  Button,\n  IconButton,\n  Avatar,\n  Stack,\n  Chip,\n  Card,\n  CardContent,\n  Switch,\n  FormControlLabel,\n  useTheme,\n  alpha,\n  Skeleton,\n  Fab,\n  Tooltip,\n  Badge,\n  AppBar,\n  Toolbar,\n} from '@mui/material';\nimport {\n  School,\n  Person,\n  Assessment,\n  TrendingUp,\n  Notifications,\n  Settings,\n  Search,\n  FilterList,\n  Refresh,\n  DarkMode,\n  LightMode,\n  Psychology,\n  Group,\n  CalendarToday,\n  Bar<PERSON>hart,\n  Pie<PERSON>hart,\n  Timeline,\n  Add,\n} from '@mui/icons-material';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate } from 'react-router-dom';\nimport ModernMetricCard from './ModernMetricCard';\nimport generateCompleteStudentData, { generateAnalyticsData } from '../../data/studentData';\nimport EnhancedButton from '../Common/EnhancedButton';\nimport EnhancedCard from '../Common/EnhancedCard';\n\n// Theme Toggle Component\nconst ThemeToggle = ({ darkMode, onToggle }) => {\n  const theme = useTheme();\n  \n  return (\n    <Tooltip title={darkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}>\n      <IconButton\n        onClick={onToggle}\n        sx={{\n          background: alpha(theme.palette.background.paper, 0.8),\n          backdropFilter: 'blur(10px)',\n          border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,\n          '&:hover': {\n            background: alpha(theme.palette.background.paper, 0.9),\n            transform: 'scale(1.05)',\n          },\n          transition: 'all 0.3s ease',\n        }}\n      >\n        <motion.div\n          initial={false}\n          animate={{ rotate: darkMode ? 180 : 0 }}\n          transition={{ duration: 0.5, ease: 'easeInOut' }}\n        >\n          {darkMode ? <LightMode /> : <DarkMode />}\n        </motion.div>\n      </IconButton>\n    </Tooltip>\n  );\n};\n\n// Modern Header Component\nconst ModernHeader = ({ darkMode, onThemeToggle, user }) => {\n  const theme = useTheme();\n  \n  return (\n    <AppBar\n      position=\"sticky\"\n      elevation={0}\n      sx={{\n        background: alpha(theme.palette.background.paper, 0.8),\n        backdropFilter: 'blur(20px)',\n        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,\n        color: theme.palette.text.primary,\n      }}\n    >\n      <Toolbar sx={{ justifyContent: 'space-between', py: 1 }}>\n        {/* Logo and Title */}\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Avatar\n              sx={{\n                bgcolor: theme.palette.primary.main,\n                width: 48,\n                height: 48,\n              }}\n            >\n              <School />\n            </Avatar>\n          </motion.div>\n          <Box>\n            <Typography\n              variant=\"h5\"\n              sx={{\n                fontWeight: 800,\n                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                backgroundClip: 'text',\n              }}\n            >\n              VidyaMitra\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Modern Educational Dashboard\n            </Typography>\n          </Box>\n        </Box>\n\n        {/* Actions */}\n        <Stack direction=\"row\" spacing={1} alignItems=\"center\">\n          <IconButton>\n            <Search />\n          </IconButton>\n          <IconButton>\n            <FilterList />\n          </IconButton>\n          <Badge badgeContent={3} color=\"error\">\n            <IconButton>\n              <Notifications />\n            </IconButton>\n          </Badge>\n          <ThemeToggle darkMode={darkMode} onToggle={onThemeToggle} />\n          <Avatar\n            sx={{\n              width: 40,\n              height: 40,\n              cursor: 'pointer',\n              border: `2px solid ${theme.palette.primary.main}`,\n            }}\n          >\n            {user?.name?.charAt(0) || 'U'}\n          </Avatar>\n        </Stack>\n      </Toolbar>\n    </AppBar>\n  );\n};\n\n// Quick Stats Component\nconst QuickStats = ({ loading, analyticsData }) => {\n  const navigate = useNavigate();\n\n  const statsData = analyticsData ? [\n    {\n      title: 'Total Students',\n      value: analyticsData.totalStudents,\n      subtitle: 'Active learners',\n      icon: Person,\n      trend: 'up',\n      trendValue: 12,\n      progress: 85,\n      progressLabel: 'Enrollment',\n      color: 'primary',\n      onClick: () => navigate('/dashboard/students'),\n    },\n    {\n      title: 'SWOT Analyses',\n      value: analyticsData.totalStudents,\n      subtitle: 'Generated this month',\n      icon: Psychology,\n      trend: 'up',\n      trendValue: 8,\n      progress: 72,\n      progressLabel: 'Completion',\n      color: 'secondary',\n      gradient: true,\n      onClick: () => navigate('/dashboard/swot'),\n    },\n    {\n      title: 'Average Performance',\n      value: `${analyticsData.averagePerformance}%`,\n      subtitle: 'Class average',\n      icon: TrendingUp,\n      trend: 'up',\n      trendValue: 5,\n      progress: analyticsData.averagePerformance,\n      progressLabel: 'Target: 85%',\n      color: 'success',\n      onClick: () => navigate('/dashboard/analytics'),\n    },\n    {\n      title: 'Attendance Rate',\n      value: `${analyticsData.averageAttendance}%`,\n      subtitle: 'Overall attendance',\n      icon: CalendarToday,\n      trend: analyticsData.averageAttendance >= 90 ? 'up' : 'down',\n      trendValue: 2,\n      progress: analyticsData.averageAttendance,\n      progressLabel: 'Target: 95%',\n      color: 'info',\n      onClick: () => navigate('/dashboard/reports'),\n    },\n  ] : [];\n\n  return (\n    <Grid container spacing={3}>\n      {statsData.map((stat, index) => (\n        <Grid item xs={12} sm={6} lg={3} key={stat.title}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1, duration: 0.6 }}\n          >\n            <ModernMetricCard\n              {...stat}\n              loading={loading}\n              onClick={() => console.log(`Clicked ${stat.title}`)}\n              tooltip={`View detailed ${stat.title.toLowerCase()} analytics`}\n            />\n          </motion.div>\n        </Grid>\n      ))}\n    </Grid>\n  );\n};\n\n// Recent Activity Component\nconst RecentActivity = ({ loading, studentsData }) => {\n  const navigate = useNavigate();\n\n  // Generate activities from real student data\n  const generateActivities = () => {\n    if (!studentsData || studentsData.length === 0) return [];\n\n    const activities = [];\n    const now = new Date();\n\n    // Recent SWOT analyses\n    studentsData.slice(0, 3).forEach((student, index) => {\n      activities.push({\n        id: `swot_${student.id}`,\n        type: 'swot_generated',\n        title: 'SWOT Analysis Generated',\n        description: `For student ${student.name} (${student.grade} ${student.section})`,\n        time: `${(index + 1) * 5} minutes ago`,\n        icon: Psychology,\n        color: 'primary',\n        onClick: () => navigate(`/dashboard/students/${student.id}/swot`),\n      });\n    });\n\n    // Performance alerts\n    const lowPerformers = studentsData.filter(s => s.academicLevel < 70);\n    if (lowPerformers.length > 0) {\n      activities.push({\n        id: 'performance_alert',\n        type: 'performance_alert',\n        title: 'Performance Alert',\n        description: `${lowPerformers.length} students need attention`,\n        time: '15 minutes ago',\n        icon: TrendingUp,\n        color: 'warning',\n        onClick: () => navigate('/dashboard/analytics'),\n      });\n    }\n\n    // New enrollments\n    activities.push({\n      id: 'new_student',\n      type: 'new_student',\n      title: 'New Student Enrolled',\n      description: `${studentsData[studentsData.length - 1]?.name} joined ${studentsData[studentsData.length - 1]?.grade} ${studentsData[studentsData.length - 1]?.section}`,\n      time: '1 hour ago',\n      icon: Person,\n      color: 'success',\n      onClick: () => navigate('/dashboard/students'),\n    });\n\n    return activities.slice(0, 4); // Show only 4 most recent\n  };\n\n  const activities = generateActivities();\n\n  return (\n    <EnhancedCard\n      loading={loading}\n      glassmorphism={true}\n      hoverable={true}\n      sx={{ height: '100%' }}\n    >\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n          Recent Activity\n        </Typography>\n        <EnhancedButton\n          size=\"small\"\n          variant=\"outlined\"\n          startIcon={<Refresh />}\n          tooltip=\"Refresh activities\"\n          onClick={() => window.location.reload()}\n        >\n          Refresh\n        </EnhancedButton>\n      </Box>\n        \n        <Stack spacing={2}>\n          {activities.map((activity, index) => (\n            <motion.div\n              key={activity.id}\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: index * 0.1 }}\n            >\n              <Box\n                sx={{\n                  display: 'flex',\n                  gap: 2,\n                  p: 2,\n                  borderRadius: 2,\n                  cursor: activity.onClick ? 'pointer' : 'default',\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    background: alpha('#000', 0.02),\n                    transform: 'translateX(4px)',\n                  },\n                }}\n                onClick={activity.onClick}\n              >\n                <Avatar\n                  sx={{\n                    bgcolor: `${activity.color}.main`,\n                    width: 40,\n                    height: 40,\n                  }}\n                >\n                  <activity.icon sx={{ fontSize: 20 }} />\n                </Avatar>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 600, mb: 0.5 }}>\n                    {activity.title}\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\" sx={{ display: 'block', mb: 0.5 }}>\n                    {activity.description}\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.disabled\">\n                    {activity.time}\n                  </Typography>\n                </Box>\n              </Box>\n            </motion.div>\n          ))}\n        </Stack>\n    </EnhancedCard>\n  );\n};\n\n// Quick Actions Component\nconst QuickActions = () => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n\n  const actions = [\n    {\n      label: 'Generate SWOT',\n      icon: Psychology,\n      color: 'primary',\n      onClick: () => navigate('/dashboard/swot')\n    },\n    {\n      label: 'Add Student',\n      icon: Person,\n      color: 'secondary',\n      onClick: () => navigate('/dashboard/students')\n    },\n    {\n      label: 'View Reports',\n      icon: BarChart,\n      color: 'success',\n      onClick: () => navigate('/dashboard/reports')\n    },\n    {\n      label: 'Analytics',\n      icon: Timeline,\n      color: 'info',\n      onClick: () => navigate('/dashboard/analytics')\n    },\n  ];\n\n  return (\n    <Box sx={{ position: 'fixed', bottom: 24, right: 24, zIndex: 1000 }}>\n      <Stack spacing={2}>\n        {actions.map((action, index) => (\n          <motion.div\n            key={action.label}\n            initial={{ opacity: 0, scale: 0 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: index * 0.1, type: 'spring' }}\n          >\n            <EnhancedButton\n              variant=\"contained\"\n              color={action.color}\n              onClick={action.onClick}\n              tooltip={action.label}\n              startIcon={<action.icon />}\n              sx={{\n                borderRadius: '50%',\n                minWidth: 56,\n                width: 56,\n                height: 56,\n                backdropFilter: 'blur(10px)',\n                boxShadow: '0px 8px 24px rgba(0, 0, 0, 0.15)',\n              }}\n            >\n            </EnhancedButton>\n          </motion.div>\n        ))}\n      </Stack>\n    </Box>\n  );\n};\n\n// Main Modern Dashboard Component\nconst ModernDashboard = () => {\n  const { t } = useTranslation(['dashboard', 'common']);\n  const theme = useTheme();\n  const [darkMode, setDarkMode] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [studentsData, setStudentsData] = useState([]);\n  const [analyticsData, setAnalyticsData] = useState(null);\n  const [user] = useState({\n    name: 'Dr. Priya Sharma',\n    role: 'Principal',\n    school: 'Delhi Public School',\n  });\n\n  // Load real student data\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      try {\n        // Simulate API call delay\n        await new Promise(resolve => setTimeout(resolve, 1500));\n\n        const students = generateCompleteStudentData();\n        const analytics = generateAnalyticsData(students);\n\n        setStudentsData(students);\n        setAnalyticsData(analytics);\n      } catch (error) {\n        console.error('Error loading student data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n\n  const handleThemeToggle = () => {\n    setDarkMode(!darkMode);\n    // In a real app, this would update the theme context\n  };\n\n  return (\n    <Box\n      sx={{\n        minHeight: '100vh',\n        background: `linear-gradient(135deg, ${theme.palette.background.default} 0%, ${theme.palette.background.surface || theme.palette.background.paper} 100%)`,\n      }}\n    >\n      {/* Modern Header */}\n      <ModernHeader\n        darkMode={darkMode}\n        onThemeToggle={handleThemeToggle}\n        user={user}\n      />\n\n      {/* Main Content */}\n      <Box sx={{ p: 3 }}>\n        {/* Welcome Section */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n        >\n          <Box sx={{ mb: 4 }}>\n            <Typography\n              variant=\"h4\"\n              sx={{\n                fontWeight: 700,\n                mb: 1,\n                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                backgroundClip: 'text',\n              }}\n            >\n              Welcome back, {user.name}! 👋\n            </Typography>\n            <Typography variant=\"body1\" color=\"text.secondary\" sx={{ fontSize: '1.1rem' }}>\n              Here's what's happening at {user.school} today\n            </Typography>\n          </Box>\n        </motion.div>\n\n        {/* Quick Stats */}\n        <Box sx={{ mb: 4 }}>\n          <QuickStats loading={loading} analyticsData={analyticsData} />\n        </Box>\n\n        {/* Secondary Content Grid */}\n        <Grid container spacing={3}>\n          {/* Recent Activity */}\n          <Grid item xs={12} lg={6}>\n            <motion.div\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.3, duration: 0.6 }}\n            >\n              <RecentActivity loading={loading} studentsData={studentsData} />\n            </motion.div>\n          </Grid>\n\n          {/* Performance Overview */}\n          <Grid item xs={12} lg={6}>\n            <motion.div\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.4, duration: 0.6 }}\n            >\n              <Card sx={{ height: '100%' }}>\n                <CardContent>\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 3 }}>\n                    Performance Overview\n                  </Typography>\n\n                  {loading ? (\n                    <Stack spacing={2}>\n                      <Skeleton variant=\"rectangular\" height={200} />\n                      <Skeleton variant=\"text\" />\n                      <Skeleton variant=\"text\" width=\"60%\" />\n                    </Stack>\n                  ) : (\n                    <Box>\n                      {/* Board Performance */}\n                      <Box sx={{ mb: 3 }}>\n                        <Typography variant=\"body2\" sx={{ mb: 2, fontWeight: 500 }}>\n                          Board Performance\n                        </Typography>\n                        <Stack spacing={2}>\n                          {[\n                            { board: 'CBSE', score: 92, students: 450 },\n                            { board: 'ICSE', score: 89, students: 320 },\n                            { board: 'State Board', score: 85, students: 477 },\n                          ].map((item, index) => (\n                            <motion.div\n                              key={item.board}\n                              initial={{ opacity: 0, x: -10 }}\n                              animate={{ opacity: 1, x: 0 }}\n                              transition={{ delay: 0.5 + index * 0.1 }}\n                            >\n                              <Box\n                                sx={{\n                                  display: 'flex',\n                                  justifyContent: 'space-between',\n                                  alignItems: 'center',\n                                  p: 2,\n                                  borderRadius: 2,\n                                  background: alpha(theme.palette.board?.[item.board] || theme.palette.primary.main, 0.1),\n                                  border: `1px solid ${alpha(theme.palette.board?.[item.board] || theme.palette.primary.main, 0.2)}`,\n                                }}\n                              >\n                                <Box>\n                                  <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                                    {item.board}\n                                  </Typography>\n                                  <Typography variant=\"caption\" color=\"text.secondary\">\n                                    {item.students} students\n                                  </Typography>\n                                </Box>\n                                <Chip\n                                  label={`${item.score}%`}\n                                  size=\"small\"\n                                  sx={{\n                                    bgcolor: theme.palette.board?.[item.board] || theme.palette.primary.main,\n                                    color: 'white',\n                                    fontWeight: 600,\n                                  }}\n                                />\n                              </Box>\n                            </motion.div>\n                          ))}\n                        </Stack>\n                      </Box>\n                    </Box>\n                  )}\n                </CardContent>\n              </Card>\n            </motion.div>\n          </Grid>\n        </Grid>\n\n        {/* Bottom Action Cards */}\n        <Box sx={{ mt: 4 }}>\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={4}>\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.6, duration: 0.6 }}\n              >\n                <Card\n                  sx={{\n                    p: 3,\n                    textAlign: 'center',\n                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,\n                    color: 'white',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    '&:hover': {\n                      transform: 'translateY(-4px)',\n                      boxShadow: '0px 20px 40px rgba(46, 91, 168, 0.3)',\n                    },\n                  }}\n                >\n                  <Psychology sx={{ fontSize: 48, mb: 2 }} />\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 1 }}>\n                    Generate SWOT Analysis\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Create comprehensive student assessments\n                  </Typography>\n                </Card>\n              </motion.div>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.7, duration: 0.6 }}\n              >\n                <Card\n                  sx={{\n                    p: 3,\n                    textAlign: 'center',\n                    background: `linear-gradient(135deg, ${theme.palette.secondary.main} 0%, ${theme.palette.secondary.dark} 100%)`,\n                    color: 'white',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    '&:hover': {\n                      transform: 'translateY(-4px)',\n                      boxShadow: '0px 20px 40px rgba(255, 153, 51, 0.3)',\n                    },\n                  }}\n                >\n                  <BarChart sx={{ fontSize: 48, mb: 2 }} />\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 1 }}>\n                    View Analytics\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Detailed performance insights\n                  </Typography>\n                </Card>\n              </motion.div>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.8, duration: 0.6 }}\n              >\n                <Card\n                  sx={{\n                    p: 3,\n                    textAlign: 'center',\n                    background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,\n                    color: 'white',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    '&:hover': {\n                      transform: 'translateY(-4px)',\n                      boxShadow: '0px 20px 40px rgba(0, 200, 83, 0.3)',\n                    },\n                  }}\n                >\n                  <Group sx={{ fontSize: 48, mb: 2 }} />\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 1 }}>\n                    Manage Students\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    Student profiles and records\n                  </Typography>\n                </Card>\n              </motion.div>\n            </Grid>\n          </Grid>\n        </Box>\n      </Box>\n\n      {/* Floating Quick Actions */}\n      <QuickActions />\n    </Box>\n  );\n};\n\nexport default ModernDashboard;\n", "/**\n * VidyaMitra Platform - Modern Login/Signup Page\n * \n * Contemporary design with glassmorphism, smooth animations, and modern UI patterns\n * Maintains Indian educational context while providing a visually impressive experience\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  TextField,\n  Button,\n  Typography,\n  IconButton,\n  InputAdornment,\n  Tabs,\n  Tab,\n  Chip,\n  Avatar,\n  Stack,\n  Divider,\n  useTheme,\n  alpha,\n  Fade,\n  Slide,\n  Zoom,\n} from '@mui/material';\nimport {\n  Visibility,\n  VisibilityOff,\n  School,\n  Person,\n  Email,\n  Lock,\n  Google,\n  Apple,\n  Facebook,\n  ArrowForward,\n  CheckCircle,\n  Star,\n  TrendingUp,\n} from '@mui/icons-material';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useTranslation } from 'react-i18next';\n\n// Animated Background Component\nconst AnimatedBackground = () => {\n  const theme = useTheme();\n  \n  return (\n    <Box\n      sx={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: `linear-gradient(135deg, ${theme.palette.primary.main}15 0%, ${theme.palette.secondary.main}10 100%)`,\n        overflow: 'hidden',\n        zIndex: -1,\n      }}\n    >\n      {/* Floating Elements */}\n      {[...Array(6)].map((_, i) => (\n        <motion.div\n          key={i}\n          initial={{ opacity: 0, scale: 0 }}\n          animate={{ \n            opacity: [0.1, 0.3, 0.1],\n            scale: [1, 1.2, 1],\n            x: [0, 100, 0],\n            y: [0, -50, 0],\n          }}\n          transition={{\n            duration: 8 + i * 2,\n            repeat: Infinity,\n            delay: i * 1.5,\n          }}\n          style={{\n            position: 'absolute',\n            top: `${20 + i * 15}%`,\n            left: `${10 + i * 15}%`,\n            width: 60 + i * 20,\n            height: 60 + i * 20,\n            borderRadius: '50%',\n            background: i % 2 === 0 \n              ? `linear-gradient(135deg, ${theme.palette.primary.main}20, ${theme.palette.primary.light}10)`\n              : `linear-gradient(135deg, ${theme.palette.secondary.main}20, ${theme.palette.secondary.light}10)`,\n            backdropFilter: 'blur(10px)',\n          }}\n        />\n      ))}\n    </Box>\n  );\n};\n\n// Feature Highlight Component\nconst FeatureHighlight = ({ icon: Icon, title, description, delay = 0 }) => {\n  const theme = useTheme();\n  \n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay, duration: 0.6 }}\n    >\n      <Box\n        sx={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2,\n          p: 2,\n          borderRadius: 2,\n          background: alpha(theme.palette.background.paper, 0.1),\n          backdropFilter: 'blur(10px)',\n          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n          transition: 'all 0.3s ease',\n          '&:hover': {\n            transform: 'translateX(8px)',\n            background: alpha(theme.palette.background.paper, 0.2),\n          },\n        }}\n      >\n        <Avatar\n          sx={{\n            bgcolor: theme.palette.primary.main,\n            width: 48,\n            height: 48,\n          }}\n        >\n          <Icon />\n        </Avatar>\n        <Box>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 0.5 }}>\n            {title}\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            {description}\n          </Typography>\n        </Box>\n      </Box>\n    </motion.div>\n  );\n};\n\n// Login Form Component\nconst LoginForm = ({ onSubmit, loading }) => {\n  const [showPassword, setShowPassword] = useState(false);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n  });\n\n  const handleChange = (field) => (event) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: event.target.value,\n    }));\n  };\n\n  const handleSubmit = (event) => {\n    event.preventDefault();\n    onSubmit(formData);\n  };\n\n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} sx={{ width: '100%' }}>\n      <Stack spacing={3}>\n        <TextField\n          fullWidth\n          label=\"Email Address\"\n          type=\"email\"\n          value={formData.email}\n          onChange={handleChange('email')}\n          InputProps={{\n            startAdornment: (\n              <InputAdornment position=\"start\">\n                <Email color=\"primary\" />\n              </InputAdornment>\n            ),\n          }}\n          sx={{\n            '& .MuiOutlinedInput-root': {\n              borderRadius: 2,\n              backdropFilter: 'blur(10px)',\n              background: alpha('#fff', 0.1),\n            },\n          }}\n        />\n        \n        <TextField\n          fullWidth\n          label=\"Password\"\n          type={showPassword ? 'text' : 'password'}\n          value={formData.password}\n          onChange={handleChange('password')}\n          InputProps={{\n            startAdornment: (\n              <InputAdornment position=\"start\">\n                <Lock color=\"primary\" />\n              </InputAdornment>\n            ),\n            endAdornment: (\n              <InputAdornment position=\"end\">\n                <IconButton\n                  onClick={() => setShowPassword(!showPassword)}\n                  edge=\"end\"\n                >\n                  {showPassword ? <VisibilityOff /> : <Visibility />}\n                </IconButton>\n              </InputAdornment>\n            ),\n          }}\n          sx={{\n            '& .MuiOutlinedInput-root': {\n              borderRadius: 2,\n              backdropFilter: 'blur(10px)',\n              background: alpha('#fff', 0.1),\n            },\n          }}\n        />\n\n        <Button\n          type=\"submit\"\n          fullWidth\n          variant=\"contained\"\n          size=\"large\"\n          disabled={loading}\n          endIcon={<ArrowForward />}\n          sx={{\n            py: 1.5,\n            borderRadius: 2,\n            background: 'linear-gradient(135deg, #2E5BA8 0%, #4A90E2 100%)',\n            boxShadow: '0px 8px 24px rgba(46, 91, 168, 0.3)',\n            '&:hover': {\n              background: 'linear-gradient(135deg, #1E4A97 0%, #2E5BA8 100%)',\n              boxShadow: '0px 12px 32px rgba(46, 91, 168, 0.4)',\n              transform: 'translateY(-2px)',\n            },\n          }}\n        >\n          {loading ? 'Signing In...' : 'Sign In'}\n        </Button>\n      </Stack>\n    </Box>\n  );\n};\n\n// Signup Form Component\nconst SignupForm = ({ onSubmit, loading }) => {\n  const [showPassword, setShowPassword] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    role: 'teacher',\n    board: 'CBSE',\n  });\n\n  const handleChange = (field) => (event) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: event.target.value,\n    }));\n  };\n\n  const handleSubmit = (event) => {\n    event.preventDefault();\n    onSubmit(formData);\n  };\n\n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} sx={{ width: '100%' }}>\n      <Stack spacing={3}>\n        <TextField\n          fullWidth\n          label=\"Full Name\"\n          value={formData.name}\n          onChange={handleChange('name')}\n          InputProps={{\n            startAdornment: (\n              <InputAdornment position=\"start\">\n                <Person color=\"primary\" />\n              </InputAdornment>\n            ),\n          }}\n          sx={{\n            '& .MuiOutlinedInput-root': {\n              borderRadius: 2,\n              backdropFilter: 'blur(10px)',\n              background: alpha('#fff', 0.1),\n            },\n          }}\n        />\n        \n        <TextField\n          fullWidth\n          label=\"Email Address\"\n          type=\"email\"\n          value={formData.email}\n          onChange={handleChange('email')}\n          InputProps={{\n            startAdornment: (\n              <InputAdornment position=\"start\">\n                <Email color=\"primary\" />\n              </InputAdornment>\n            ),\n          }}\n          sx={{\n            '& .MuiOutlinedInput-root': {\n              borderRadius: 2,\n              backdropFilter: 'blur(10px)',\n              background: alpha('#fff', 0.1),\n            },\n          }}\n        />\n        \n        <TextField\n          fullWidth\n          label=\"Password\"\n          type={showPassword ? 'text' : 'password'}\n          value={formData.password}\n          onChange={handleChange('password')}\n          InputProps={{\n            startAdornment: (\n              <InputAdornment position=\"start\">\n                <Lock color=\"primary\" />\n              </InputAdornment>\n            ),\n            endAdornment: (\n              <InputAdornment position=\"end\">\n                <IconButton\n                  onClick={() => setShowPassword(!showPassword)}\n                  edge=\"end\"\n                >\n                  {showPassword ? <VisibilityOff /> : <Visibility />}\n                </IconButton>\n              </InputAdornment>\n            ),\n          }}\n          sx={{\n            '& .MuiOutlinedInput-root': {\n              borderRadius: 2,\n              backdropFilter: 'blur(10px)',\n              background: alpha('#fff', 0.1),\n            },\n          }}\n        />\n\n        {/* Board Selection */}\n        <Box>\n          <Typography variant=\"body2\" sx={{ mb: 1, fontWeight: 500 }}>\n            Education Board\n          </Typography>\n          <Stack direction=\"row\" spacing={1} flexWrap=\"wrap\">\n            {['CBSE', 'ICSE', 'State Board', 'IB'].map((board) => (\n              <Chip\n                key={board}\n                label={board}\n                clickable\n                color={formData.board === board ? 'primary' : 'default'}\n                onClick={() => setFormData(prev => ({ ...prev, board }))}\n                sx={{\n                  borderRadius: 2,\n                  fontWeight: 500,\n                }}\n              />\n            ))}\n          </Stack>\n        </Box>\n\n        <Button\n          type=\"submit\"\n          fullWidth\n          variant=\"contained\"\n          size=\"large\"\n          disabled={loading}\n          endIcon={<ArrowForward />}\n          sx={{\n            py: 1.5,\n            borderRadius: 2,\n            background: 'linear-gradient(135deg, #FF9933 0%, #FFB366 100%)',\n            boxShadow: '0px 8px 24px rgba(255, 153, 51, 0.3)',\n            '&:hover': {\n              background: 'linear-gradient(135deg, #FF8F00 0%, #FF9933 100%)',\n              boxShadow: '0px 12px 32px rgba(255, 153, 51, 0.4)',\n              transform: 'translateY(-2px)',\n            },\n          }}\n        >\n          {loading ? 'Creating Account...' : 'Create Account'}\n        </Button>\n      </Stack>\n    </Box>\n  );\n};\n\n// Main Modern Login Page Component\nconst ModernLoginPage = ({ onLogin, onSignup }) => {\n  const { t } = useTranslation(['auth', 'common']);\n  const theme = useTheme();\n  const [activeTab, setActiveTab] = useState(0);\n  const [loading, setLoading] = useState(false);\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n\n  const handleLogin = async (formData) => {\n    setLoading(true);\n    try {\n      if (onLogin) {\n        await onLogin(formData);\n      } else {\n        // Fallback for demo\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        console.log('Login:', formData);\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSignup = async (formData) => {\n    setLoading(true);\n    try {\n      if (onSignup) {\n        await onSignup(formData);\n      } else {\n        // Fallback for demo\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        console.log('Signup:', formData);\n      }\n    } catch (error) {\n      console.error('Signup error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Box\n      sx={{\n        minHeight: '100vh',\n        display: 'flex',\n        position: 'relative',\n        background: `linear-gradient(135deg, ${theme.palette.background.default} 0%, ${theme.palette.background.surface} 100%)`,\n      }}\n    >\n      <AnimatedBackground />\n\n      {/* Left Side - Features */}\n      <Box\n        sx={{\n          flex: 1,\n          display: { xs: 'none', md: 'flex' },\n          flexDirection: 'column',\n          justifyContent: 'center',\n          p: 6,\n          position: 'relative',\n        }}\n      >\n        <motion.div\n          initial={{ opacity: 0, x: -50 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <Box sx={{ mb: 6 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>\n              <Avatar\n                sx={{\n                  bgcolor: theme.palette.primary.main,\n                  width: 64,\n                  height: 64,\n                }}\n              >\n                <School sx={{ fontSize: 32 }} />\n              </Avatar>\n              <Typography\n                variant=\"h3\"\n                sx={{\n                  fontWeight: 600, // Reduced from 800\n                  color: theme.palette.mode === 'dark' ? '#FFFFFF' : '#1E293B', // Solid color for better visibility\n                  textShadow: theme.palette.mode === 'dark'\n                    ? '0 2px 8px rgba(0,0,0,0.5)'\n                    : '0 2px 4px rgba(0,0,0,0.1)',\n                }}\n              >\n                VidyaMitra\n              </Typography>\n            </Box>\n            <Typography\n              variant=\"h5\"\n              sx={{\n                fontWeight: 600,\n                color: theme.palette.text.primary,\n                mb: 2,\n              }}\n            >\n              Empowering Indian Education with AI-Driven SWOT Analysis\n            </Typography>\n            <Typography\n              variant=\"body1\"\n              sx={{\n                color: theme.palette.text.secondary,\n                fontSize: '1.1rem',\n                lineHeight: 1.6,\n              }}\n            >\n              Transform student assessment and development with our comprehensive\n              platform designed specifically for Indian educational institutions.\n            </Typography>\n          </Box>\n\n          <Stack spacing={3}>\n            <FeatureHighlight\n              icon={TrendingUp}\n              title=\"AI-Powered Analysis\"\n              description=\"Advanced SWOT analysis using machine learning algorithms\"\n              delay={0.2}\n            />\n            <FeatureHighlight\n              icon={School}\n              title=\"Board-Specific Features\"\n              description=\"Tailored for CBSE, ICSE, and State Board curricula\"\n              delay={0.4}\n            />\n            <FeatureHighlight\n              icon={CheckCircle}\n              title=\"Comprehensive Tracking\"\n              description=\"Academic, behavioral, and extracurricular monitoring\"\n              delay={0.6}\n            />\n          </Stack>\n        </motion.div>\n      </Box>\n\n      {/* Right Side - Auth Form */}\n      <Box\n        sx={{\n          flex: { xs: 1, md: 0.6 },\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          p: 3,\n        }}\n      >\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          style={{ width: '100%', maxWidth: 480 }}\n        >\n          <Card\n            sx={{\n              p: 4,\n              borderRadius: 4,\n              background: alpha(theme.palette.background.paper, 0.9),\n              backdropFilter: 'blur(20px)',\n              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n              boxShadow: '0px 20px 40px rgba(46, 91, 168, 0.15)',\n            }}\n          >\n            <CardContent sx={{ p: 0 }}>\n              {/* Header */}\n              <Box sx={{ textAlign: 'center', mb: 4 }}>\n                <Typography\n                  variant=\"h4\"\n                  sx={{\n                    fontWeight: 500, // Reduced from 700\n                    mb: 1,\n                    color: theme.palette.text.primary, // Solid color for better visibility\n                  }}\n                >\n                  Welcome Back\n                </Typography>\n                <Typography variant=\"body1\" color=\"text.secondary\">\n                  Access your educational dashboard\n                </Typography>\n              </Box>\n\n              {/* Tabs */}\n              <Tabs\n                value={activeTab}\n                onChange={handleTabChange}\n                centered\n                sx={{\n                  mb: 4,\n                  '& .MuiTab-root': {\n                    textTransform: 'none',\n                    fontWeight: 600,\n                    fontSize: '1rem',\n                    minWidth: 120,\n                  },\n                  '& .MuiTabs-indicator': {\n                    borderRadius: 2,\n                    height: 3,\n                  },\n                }}\n              >\n                <Tab label=\"Sign In\" />\n                <Tab label=\"Sign Up\" />\n              </Tabs>\n\n              {/* Forms */}\n              <AnimatePresence mode=\"wait\">\n                {activeTab === 0 ? (\n                  <motion.div\n                    key=\"login\"\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: 20 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    <LoginForm onSubmit={handleLogin} loading={loading} />\n                  </motion.div>\n                ) : (\n                  <motion.div\n                    key=\"signup\"\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: -20 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    <SignupForm onSubmit={handleSignup} loading={loading} />\n                  </motion.div>\n                )}\n              </AnimatePresence>\n\n              {/* Social Login */}\n              <Box sx={{ mt: 4 }}>\n                <Divider sx={{ mb: 3 }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Or continue with\n                  </Typography>\n                </Divider>\n\n                <Stack direction=\"row\" spacing={2} justifyContent=\"center\">\n                  <IconButton\n                    sx={{\n                      border: `1px solid ${alpha(theme.palette.divider, 0.5)}`,\n                      borderRadius: 2,\n                      p: 1.5,\n                      '&:hover': {\n                        background: alpha(theme.palette.primary.main, 0.1),\n                        transform: 'translateY(-2px)',\n                      },\n                    }}\n                  >\n                    <Google />\n                  </IconButton>\n                  <IconButton\n                    sx={{\n                      border: `1px solid ${alpha(theme.palette.divider, 0.5)}`,\n                      borderRadius: 2,\n                      p: 1.5,\n                      '&:hover': {\n                        background: alpha(theme.palette.primary.main, 0.1),\n                        transform: 'translateY(-2px)',\n                      },\n                    }}\n                  >\n                    <Apple />\n                  </IconButton>\n                  <IconButton\n                    sx={{\n                      border: `1px solid ${alpha(theme.palette.divider, 0.5)}`,\n                      borderRadius: 2,\n                      p: 1.5,\n                      '&:hover': {\n                        background: alpha(theme.palette.primary.main, 0.1),\n                        transform: 'translateY(-2px)',\n                      },\n                    }}\n                  >\n                    <Facebook />\n                  </IconButton>\n                </Stack>\n              </Box>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </Box>\n    </Box>\n  );\n};\n\nexport default ModernLoginPage;\n", "/**\n * VidyaMitra Platform - Comprehensive Analytics Dashboard\n * \n * Advanced data visualizations showing student performance analytics,\n * attendance patterns, SWOT analysis distribution, and board-specific insights\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  Tab,\n  Tabs,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  alpha,\n  useTheme,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  School,\n  Assessment,\n  People,\n  Timeline,\n  BarChart,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n} from 'chart.js';\nimport { Line, Bar, Doughnut } from 'react-chartjs-2';\nimport generateCompleteStudentData, { generateAnalyticsData } from '../../data/studentData';\nimport ModernMetricCard from '../Dashboard/ModernMetricCard';\n\n// Register Chart.js components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement\n);\n\nconst AnalyticsDashboard = () => {\n  const theme = useTheme();\n  const [activeTab, setActiveTab] = useState(0);\n  const [timeRange, setTimeRange] = useState('year');\n  const [selectedBoard, setSelectedBoard] = useState('all');\n  const [studentsData, setStudentsData] = useState([]);\n  const [analyticsData, setAnalyticsData] = useState(null);\n\n  useEffect(() => {\n    const data = generateCompleteStudentData();\n    setStudentsData(data);\n    setAnalyticsData(generateAnalyticsData(data));\n  }, []);\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n\n  if (!analyticsData) {\n    return <Box>Loading...</Box>;\n  }\n\n  // Chart configurations\n  const performanceTrendConfig = {\n    data: {\n      labels: analyticsData.performanceTrends.map(d => d.month),\n      datasets: [\n        {\n          label: 'Overall Average',\n          data: analyticsData.performanceTrends.map(d => d.average),\n          borderColor: theme.palette.primary.main,\n          backgroundColor: alpha(theme.palette.primary.main, 0.1),\n          tension: 0.4,\n        },\n        {\n          label: 'CBSE',\n          data: analyticsData.performanceTrends.map(d => d.cbse),\n          borderColor: theme.palette.secondary.main,\n          backgroundColor: alpha(theme.palette.secondary.main, 0.1),\n          tension: 0.4,\n        },\n        {\n          label: 'ICSE',\n          data: analyticsData.performanceTrends.map(d => d.icse),\n          borderColor: theme.palette.success.main,\n          backgroundColor: alpha(theme.palette.success.main, 0.1),\n          tension: 0.4,\n        },\n        {\n          label: 'State Board',\n          data: analyticsData.performanceTrends.map(d => d.state),\n          borderColor: theme.palette.warning.main,\n          backgroundColor: alpha(theme.palette.warning.main, 0.1),\n          tension: 0.4,\n        },\n      ],\n    },\n    options: {\n      responsive: true,\n      plugins: {\n        legend: {\n          position: 'top',\n        },\n        title: {\n          display: true,\n          text: 'Academic Performance Trends',\n        },\n      },\n      scales: {\n        y: {\n          beginAtZero: false,\n          min: 60,\n          max: 100,\n        },\n      },\n    },\n  };\n\n  const subjectAnalysisConfig = {\n    data: {\n      labels: analyticsData.subjectAnalysis.map(s => s.subject),\n      datasets: [\n        {\n          label: 'Average Score',\n          data: analyticsData.subjectAnalysis.map(s => s.averageScore),\n          backgroundColor: [\n            alpha(theme.palette.primary.main, 0.8),\n            alpha(theme.palette.secondary.main, 0.8),\n            alpha(theme.palette.success.main, 0.8),\n            alpha(theme.palette.warning.main, 0.8),\n            alpha(theme.palette.error.main, 0.8),\n            alpha(theme.palette.info.main, 0.8),\n          ],\n          borderColor: [\n            theme.palette.primary.main,\n            theme.palette.secondary.main,\n            theme.palette.success.main,\n            theme.palette.warning.main,\n            theme.palette.error.main,\n            theme.palette.info.main,\n          ],\n          borderWidth: 2,\n        },\n      ],\n    },\n    options: {\n      responsive: true,\n      plugins: {\n        legend: {\n          display: false,\n        },\n        title: {\n          display: true,\n          text: 'Subject-wise Performance Analysis',\n        },\n      },\n      scales: {\n        y: {\n          beginAtZero: false,\n          min: 50,\n          max: 100,\n        },\n      },\n    },\n  };\n\n  const boardDistributionConfig = {\n    data: {\n      labels: ['CBSE', 'ICSE', 'State Board', 'IB'],\n      datasets: [\n        {\n          data: [\n            analyticsData.boardDistribution.cbse,\n            analyticsData.boardDistribution.icse,\n            analyticsData.boardDistribution.state,\n            analyticsData.boardDistribution.ib,\n          ],\n          backgroundColor: [\n            alpha(theme.palette.primary.main, 0.8),\n            alpha(theme.palette.secondary.main, 0.8),\n            alpha(theme.palette.success.main, 0.8),\n            alpha(theme.palette.warning.main, 0.8),\n          ],\n          borderColor: [\n            theme.palette.primary.main,\n            theme.palette.secondary.main,\n            theme.palette.success.main,\n            theme.palette.warning.main,\n          ],\n          borderWidth: 2,\n        },\n      ],\n    },\n    options: {\n      responsive: true,\n      plugins: {\n        legend: {\n          position: 'bottom',\n        },\n        title: {\n          display: true,\n          text: 'Student Distribution by Board',\n        },\n      },\n    },\n  };\n\n  const attendancePatternConfig = {\n    data: {\n      labels: analyticsData.attendancePatterns.map(d => d.month),\n      datasets: [\n        {\n          label: 'Attendance Percentage',\n          data: analyticsData.attendancePatterns.map(d => d.averageAttendance),\n          backgroundColor: alpha(theme.palette.success.main, 0.6),\n          borderColor: theme.palette.success.main,\n          borderWidth: 2,\n        },\n      ],\n    },\n    options: {\n      responsive: true,\n      plugins: {\n        legend: {\n          display: false,\n        },\n        title: {\n          display: true,\n          text: 'Monthly Attendance Patterns',\n        },\n      },\n      scales: {\n        y: {\n          beginAtZero: false,\n          min: 70,\n          max: 100,\n        },\n      },\n    },\n  };\n\n  const metricCards = [\n    {\n      title: 'Total Students',\n      value: analyticsData.totalStudents,\n      icon: People,\n      color: 'primary',\n      trend: '+5.2%',\n      description: 'Active students enrolled',\n    },\n    {\n      title: 'Average Performance',\n      value: `${analyticsData.averagePerformance}%`,\n      icon: TrendingUp,\n      color: 'success',\n      trend: '+2.1%',\n      description: 'Overall academic performance',\n    },\n    {\n      title: 'Average Attendance',\n      value: `${analyticsData.averageAttendance}%`,\n      icon: School,\n      color: 'info',\n      trend: '+1.8%',\n      description: 'Monthly attendance rate',\n    },\n    {\n      title: 'Top Performers',\n      value: analyticsData.topPerformers,\n      icon: Assessment,\n      color: 'warning',\n      trend: '+3.5%',\n      description: 'Students scoring 90%+',\n    },\n  ];\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ py: 3 }}>\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        {/* Header */}\n        <Box sx={{ mb: 4 }}>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              fontWeight: 600,\n              mb: 1,\n              color: theme.palette.text.primary,\n            }}\n          >\n            Analytics Dashboard\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Comprehensive insights into student performance and institutional metrics\n          </Typography>\n        </Box>\n\n        {/* Controls */}\n        <Box sx={{ mb: 4, display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n          <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n            <InputLabel>Time Range</InputLabel>\n            <Select\n              value={timeRange}\n              label=\"Time Range\"\n              onChange={(e) => setTimeRange(e.target.value)}\n            >\n              <MenuItem value=\"month\">This Month</MenuItem>\n              <MenuItem value=\"quarter\">This Quarter</MenuItem>\n              <MenuItem value=\"year\">This Year</MenuItem>\n              <MenuItem value=\"all\">All Time</MenuItem>\n            </Select>\n          </FormControl>\n\n          <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n            <InputLabel>Board</InputLabel>\n            <Select\n              value={selectedBoard}\n              label=\"Board\"\n              onChange={(e) => setSelectedBoard(e.target.value)}\n            >\n              <MenuItem value=\"all\">All Boards</MenuItem>\n              <MenuItem value=\"cbse\">CBSE</MenuItem>\n              <MenuItem value=\"icse\">ICSE</MenuItem>\n              <MenuItem value=\"state\">State Board</MenuItem>\n              <MenuItem value=\"ib\">IB</MenuItem>\n            </Select>\n          </FormControl>\n        </Box>\n\n        {/* Metric Cards */}\n        <Grid container spacing={3} sx={{ mb: 4 }}>\n          {metricCards.map((card, index) => (\n            <Grid item xs={12} sm={6} md={3} key={card.title}>\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n              >\n                <ModernMetricCard {...card} />\n              </motion.div>\n            </Grid>\n          ))}\n        </Grid>\n\n        {/* Charts Tabs */}\n        <Card\n          sx={{\n            background: alpha(theme.palette.background.paper, 0.9),\n            backdropFilter: 'blur(20px)',\n            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,\n          }}\n        >\n          <Tabs\n            value={activeTab}\n            onChange={handleTabChange}\n            sx={{\n              borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,\n              px: 2,\n            }}\n          >\n            <Tab label=\"Performance Trends\" icon={<Timeline />} />\n            <Tab label=\"Subject Analysis\" icon={<BarChart />} />\n            <Tab label=\"Board Distribution\" icon={<School />} />\n            <Tab label=\"Attendance Patterns\" icon={<TrendingUp />} />\n          </Tabs>\n\n          <CardContent sx={{ p: 3 }}>\n            {activeTab === 0 && (\n              <Box sx={{ height: 400 }}>\n                <Line {...performanceTrendConfig} />\n              </Box>\n            )}\n            {activeTab === 1 && (\n              <Box sx={{ height: 400 }}>\n                <Bar {...subjectAnalysisConfig} />\n              </Box>\n            )}\n            {activeTab === 2 && (\n              <Box sx={{ height: 400, display: 'flex', justifyContent: 'center' }}>\n                <Box sx={{ width: 400 }}>\n                  <Doughnut {...boardDistributionConfig} />\n                </Box>\n              </Box>\n            )}\n            {activeTab === 3 && (\n              <Box sx={{ height: 400 }}>\n                <Bar {...attendancePatternConfig} />\n              </Box>\n            )}\n          </CardContent>\n        </Card>\n      </motion.div>\n    </Container>\n  );\n};\n\nexport default AnalyticsDashboard;\n", "import React, { useState, useEffect, useMemo } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  TextField,\n  Button,\n  Avatar,\n  Chip,\n  IconButton,\n  Menu,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Grid,\n  InputAdornment,\n  Fab,\n  Fade,\n  Slide,\n  useTheme,\n  useMediaQuery,\n  alpha,\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  FilterList as FilterIcon,\n  Add as AddIcon,\n  MoreVert as MoreVertIcon,\n  Edit as EditIcon,\n  Visibility as ViewIcon,\n  Assessment as AssessmentIcon,\n  School as SchoolIcon,\n  Person as PersonIcon,\n  Close as CloseIcon,\n  TrendingUp as TrendingUpIcon,\n  TrendingDown as TrendingDownIcon,\n} from '@mui/icons-material';\nimport { useTranslation } from 'react-i18next';\n\n// Enhanced Student Card Component\nconst StudentCard = ({ student, onView, onEdit, onGenerateSWOT }) => {\n  const theme = useTheme();\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [isHovered, setIsHovered] = useState(false);\n\n  const handleMenuOpen = (event) => {\n    event.stopPropagation();\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  const getPerformanceColor = (score) => {\n    if (score >= 80) return theme.palette.success.main;\n    if (score >= 60) return theme.palette.warning.main;\n    return theme.palette.error.main;\n  };\n\n  const getPerformanceTrend = (trend) => {\n    if (trend > 0) return { icon: TrendingUpIcon, color: theme.palette.success.main };\n    if (trend < 0) return { icon: TrendingDownIcon, color: theme.palette.error.main };\n    return { icon: null, color: theme.palette.text.secondary };\n  };\n\n  const TrendIcon = getPerformanceTrend(student.performanceTrend).icon;\n\n  return (\n    <Fade in timeout={300}>\n      <Card\n        sx={{\n          height: '100%',\n          cursor: 'pointer',\n          transition: 'all 0.3s ease-in-out',\n          transform: isHovered ? 'translateY(-4px)' : 'translateY(0)',\n          boxShadow: isHovered ? theme.shadows[8] : theme.shadows[1],\n          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,\n          '&:hover': {\n            borderColor: theme.palette.primary.main,\n          },\n        }}\n        onMouseEnter={() => setIsHovered(true)}\n        onMouseLeave={() => setIsHovered(false)}\n        onClick={() => onView(student)}\n      >\n        <CardContent sx={{ p: 3 }}>\n          {/* Header */}\n          <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1 }}>\n              <Avatar\n                sx={{\n                  width: 56,\n                  height: 56,\n                  bgcolor: theme.palette.primary.main,\n                  fontSize: '1.25rem',\n                  fontWeight: 600,\n                }}\n              >\n                {student.name.split(' ').map(n => n[0]).join('').toUpperCase()}\n              </Avatar>\n              <Box sx={{ flex: 1, minWidth: 0 }}>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 0.5 }}>\n                  {student.name}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  {student.class} • Roll No: {student.rollNumber}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  ID: {student.admissionNumber}\n                </Typography>\n              </Box>\n            </Box>\n            <IconButton\n              size=\"small\"\n              onClick={handleMenuOpen}\n              sx={{ \n                opacity: isHovered ? 1 : 0.7,\n                transition: 'opacity 0.2s ease-in-out',\n              }}\n            >\n              <MoreVertIcon />\n            </IconButton>\n          </Box>\n\n          {/* Performance Metrics */}\n          <Box sx={{ mb: 2 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Overall Performance\n              </Typography>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                {TrendIcon && (\n                  <TrendIcon \n                    sx={{ \n                      fontSize: 16, \n                      color: getPerformanceTrend(student.performanceTrend).color \n                    }} \n                  />\n                )}\n                <Typography \n                  variant=\"body2\" \n                  sx={{ \n                    fontWeight: 600,\n                    color: getPerformanceColor(student.overallScore)\n                  }}\n                >\n                  {student.overallScore}%\n                </Typography>\n              </Box>\n            </Box>\n            <Box\n              sx={{\n                height: 6,\n                borderRadius: 3,\n                bgcolor: alpha(getPerformanceColor(student.overallScore), 0.2),\n                overflow: 'hidden',\n              }}\n            >\n              <Box\n                sx={{\n                  height: '100%',\n                  width: `${student.overallScore}%`,\n                  bgcolor: getPerformanceColor(student.overallScore),\n                  borderRadius: 3,\n                  transition: 'width 0.5s ease-in-out',\n                }}\n              />\n            </Box>\n          </Box>\n\n          {/* Status Chips */}\n          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>\n            <Chip\n              label={`${student.attendance}% Attendance`}\n              size=\"small\"\n              color={student.attendance >= 90 ? 'success' : student.attendance >= 75 ? 'warning' : 'error'}\n              variant=\"outlined\"\n            />\n            {student.hasActiveSWOT && (\n              <Chip\n                label=\"SWOT Active\"\n                size=\"small\"\n                color=\"primary\"\n                variant=\"filled\"\n              />\n            )}\n          </Box>\n\n          {/* Quick Actions */}\n          <Box sx={{ display: 'flex', gap: 1 }}>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              startIcon={<ViewIcon />}\n              onClick={(e) => {\n                e.stopPropagation();\n                onView(student);\n              }}\n              sx={{ flex: 1 }}\n            >\n              View\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"contained\"\n              startIcon={<AssessmentIcon />}\n              onClick={(e) => {\n                e.stopPropagation();\n                onGenerateSWOT(student);\n              }}\n              sx={{ flex: 1 }}\n            >\n              SWOT\n            </Button>\n          </Box>\n        </CardContent>\n\n        {/* Action Menu */}\n        <Menu\n          anchorEl={anchorEl}\n          open={Boolean(anchorEl)}\n          onClose={handleMenuClose}\n          onClick={(e) => e.stopPropagation()}\n        >\n          <MenuItem onClick={() => { onView(student); handleMenuClose(); }}>\n            <ViewIcon sx={{ mr: 1 }} />\n            View Details\n          </MenuItem>\n          <MenuItem onClick={() => { onEdit(student); handleMenuClose(); }}>\n            <EditIcon sx={{ mr: 1 }} />\n            Edit Student\n          </MenuItem>\n          <MenuItem onClick={() => { onGenerateSWOT(student); handleMenuClose(); }}>\n            <AssessmentIcon sx={{ mr: 1 }} />\n            Generate SWOT\n          </MenuItem>\n        </Menu>\n      </Card>\n    </Fade>\n  );\n};\n\n// Filter Dialog Component\nconst FilterDialog = ({ open, onClose, filters, onFiltersChange }) => {\n  const { t } = useTranslation(['common']);\n  \n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          Filter Students\n          <IconButton onClick={onClose} size=\"small\">\n            <CloseIcon />\n          </IconButton>\n        </Box>\n      </DialogTitle>\n      <DialogContent>\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12} sm={6}>\n            <TextField\n              fullWidth\n              label=\"Class\"\n              select\n              value={filters.class || ''}\n              onChange={(e) => onFiltersChange({ ...filters, class: e.target.value })}\n            >\n              <MenuItem value=\"\">All Classes</MenuItem>\n              <MenuItem value=\"10-A\">Class 10-A</MenuItem>\n              <MenuItem value=\"10-B\">Class 10-B</MenuItem>\n              <MenuItem value=\"11-Science\">Class 11-Science</MenuItem>\n              <MenuItem value=\"12-Commerce\">Class 12-Commerce</MenuItem>\n            </TextField>\n          </Grid>\n          <Grid item xs={12} sm={6}>\n            <TextField\n              fullWidth\n              label=\"Performance\"\n              select\n              value={filters.performance || ''}\n              onChange={(e) => onFiltersChange({ ...filters, performance: e.target.value })}\n            >\n              <MenuItem value=\"\">All Performance</MenuItem>\n              <MenuItem value=\"excellent\">Excellent (80%+)</MenuItem>\n              <MenuItem value=\"good\">Good (60-79%)</MenuItem>\n              <MenuItem value=\"needs-improvement\">Needs Improvement (&lt;60%)</MenuItem>\n            </TextField>\n          </Grid>\n          <Grid item xs={12} sm={6}>\n            <TextField\n              fullWidth\n              label=\"Attendance\"\n              select\n              value={filters.attendance || ''}\n              onChange={(e) => onFiltersChange({ ...filters, attendance: e.target.value })}\n            >\n              <MenuItem value=\"\">All Attendance</MenuItem>\n              <MenuItem value=\"excellent\">Excellent (90%+)</MenuItem>\n              <MenuItem value=\"good\">Good (75-89%)</MenuItem>\n              <MenuItem value=\"poor\">Poor (&lt;75%)</MenuItem>\n            </TextField>\n          </Grid>\n          <Grid item xs={12} sm={6}>\n            <TextField\n              fullWidth\n              label=\"SWOT Status\"\n              select\n              value={filters.swotStatus || ''}\n              onChange={(e) => onFiltersChange({ ...filters, swotStatus: e.target.value })}\n            >\n              <MenuItem value=\"\">All Students</MenuItem>\n              <MenuItem value=\"active\">Has Active SWOT</MenuItem>\n              <MenuItem value=\"pending\">SWOT Pending</MenuItem>\n            </TextField>\n          </Grid>\n        </Grid>\n      </DialogContent>\n      <DialogActions sx={{ p: 3 }}>\n        <Button onClick={() => onFiltersChange({})}>\n          Clear All\n        </Button>\n        <Button variant=\"contained\" onClick={onClose}>\n          Apply Filters\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\n// Main Student Management Component\nconst StudentManagement = () => {\n  const { t } = useTranslation(['common']);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  \n  const [students, setStudents] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filters, setFilters] = useState({});\n  const [filterDialogOpen, setFilterDialogOpen] = useState(false);\n\n  // Mock data\n  useEffect(() => {\n    const mockStudents = [\n      {\n        id: 1,\n        name: 'Aarav Sharma',\n        class: '10-A',\n        rollNumber: '001',\n        admissionNumber: 'VM2024001',\n        overallScore: 85,\n        attendance: 92,\n        performanceTrend: 5,\n        hasActiveSWOT: true,\n      },\n      {\n        id: 2,\n        name: 'Priya Patel',\n        class: '10-A',\n        rollNumber: '002',\n        admissionNumber: 'VM2024002',\n        overallScore: 78,\n        attendance: 88,\n        performanceTrend: -2,\n        hasActiveSWOT: false,\n      },\n      {\n        id: 3,\n        name: 'Arjun Kumar',\n        class: '10-B',\n        rollNumber: '003',\n        admissionNumber: 'VM2024003',\n        overallScore: 92,\n        attendance: 95,\n        performanceTrend: 8,\n        hasActiveSWOT: true,\n      },\n      {\n        id: 4,\n        name: 'Ananya Singh',\n        class: '11-Science',\n        rollNumber: '004',\n        admissionNumber: 'VM2024004',\n        overallScore: 67,\n        attendance: 82,\n        performanceTrend: 3,\n        hasActiveSWOT: false,\n      },\n    ];\n\n    setTimeout(() => {\n      setStudents(mockStudents);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  // Filter students based on search and filters\n  const filteredStudents = useMemo(() => {\n    return students.filter(student => {\n      const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           student.admissionNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           student.class.toLowerCase().includes(searchTerm.toLowerCase());\n      \n      const matchesClass = !filters.class || student.class === filters.class;\n      \n      const matchesPerformance = !filters.performance || \n        (filters.performance === 'excellent' && student.overallScore >= 80) ||\n        (filters.performance === 'good' && student.overallScore >= 60 && student.overallScore < 80) ||\n        (filters.performance === 'needs-improvement' && student.overallScore < 60);\n      \n      const matchesAttendance = !filters.attendance ||\n        (filters.attendance === 'excellent' && student.attendance >= 90) ||\n        (filters.attendance === 'good' && student.attendance >= 75 && student.attendance < 90) ||\n        (filters.attendance === 'poor' && student.attendance < 75);\n      \n      const matchesSWOT = !filters.swotStatus ||\n        (filters.swotStatus === 'active' && student.hasActiveSWOT) ||\n        (filters.swotStatus === 'pending' && !student.hasActiveSWOT);\n\n      return matchesSearch && matchesClass && matchesPerformance && matchesAttendance && matchesSWOT;\n    });\n  }, [students, searchTerm, filters]);\n\n  const handleViewStudent = (student) => {\n    console.log('View student:', student);\n  };\n\n  const handleEditStudent = (student) => {\n    console.log('Edit student:', student);\n  };\n\n  const handleGenerateSWOT = (student) => {\n    console.log('Generate SWOT for:', student);\n  };\n\n  const handleAddStudent = () => {\n    console.log('Add new student');\n  };\n\n  return (\n    <Box sx={{ p: { xs: 2, md: 3 } }}>\n      {/* Header */}\n      <Box sx={{ mb: 4 }}>\n        <Typography variant=\"h4\" component=\"h1\" sx={{ fontWeight: 700, mb: 1 }}>\n          Student Management\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\">\n          Manage student profiles, track performance, and generate SWOT analyses\n        </Typography>\n      </Box>\n\n      {/* Search and Filter Bar */}\n      <Box sx={{ mb: 3 }}>\n        <Grid container spacing={2} alignItems=\"center\">\n          <Grid item xs={12} md={8}>\n            <TextField\n              fullWidth\n              placeholder=\"Search students by name, ID, or class...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <SearchIcon color=\"action\" />\n                  </InputAdornment>\n                ),\n              }}\n              sx={{\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 2,\n                },\n              }}\n            />\n          </Grid>\n          <Grid item xs={12} md={4}>\n            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>\n              <Button\n                variant=\"outlined\"\n                startIcon={<FilterIcon />}\n                onClick={() => setFilterDialogOpen(true)}\n                sx={{ borderRadius: 2 }}\n              >\n                Filter\n              </Button>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={handleAddStudent}\n                sx={{ borderRadius: 2 }}\n              >\n                Add Student\n              </Button>\n            </Box>\n          </Grid>\n        </Grid>\n      </Box>\n\n      {/* Results Summary */}\n      <Box sx={{ mb: 3 }}>\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          Showing {filteredStudents.length} of {students.length} students\n        </Typography>\n      </Box>\n\n      {/* Students Grid */}\n      <Grid container spacing={3}>\n        {filteredStudents.map((student) => (\n          <Grid item xs={12} sm={6} lg={4} key={student.id}>\n            <StudentCard\n              student={student}\n              onView={handleViewStudent}\n              onEdit={handleEditStudent}\n              onGenerateSWOT={handleGenerateSWOT}\n            />\n          </Grid>\n        ))}\n      </Grid>\n\n      {/* Empty State */}\n      {filteredStudents.length === 0 && !loading && (\n        <Box sx={{ textAlign: 'center', py: 8 }}>\n          <PersonIcon sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />\n          <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n            No students found\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n            Try adjusting your search criteria or add a new student\n          </Typography>\n          <Button variant=\"contained\" startIcon={<AddIcon />} onClick={handleAddStudent}>\n            Add First Student\n          </Button>\n        </Box>\n      )}\n\n      {/* Floating Action Button for Mobile */}\n      {isMobile && (\n        <Fab\n          color=\"primary\"\n          aria-label=\"add student\"\n          onClick={handleAddStudent}\n          sx={{\n            position: 'fixed',\n            bottom: 24,\n            right: 24,\n            zIndex: theme.zIndex.fab,\n          }}\n        >\n          <AddIcon />\n        </Fab>\n      )}\n\n      {/* Filter Dialog */}\n      <FilterDialog\n        open={filterDialogOpen}\n        onClose={() => setFilterDialogOpen(false)}\n        filters={filters}\n        onFiltersChange={setFilters}\n      />\n    </Box>\n  );\n};\n\nexport default StudentManagement;\n", "import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  Avatar,\n  Chip,\n  IconButton,\n  Button,\n  LinearProgress,\n  Fade,\n  Skeleton,\n  useTheme,\n  useMediaQuery,\n  Paper,\n  Divider,\n} from '@mui/material';\nimport {\n  Psychology as StrengthIcon,\n  Warning as WeaknessIcon,\n  TrendingUp as OpportunityIcon,\n  Security as ThreatIcon,\n  Star as StarIcon,\n  EmojiEvents as TrophyIcon,\n  School as SchoolIcon,\n  Groups as GroupsIcon,\n} from '@mui/icons-material';\n\n// Cultural SWOT Card with Indian design patterns\nconst CulturalSWOTCard = ({ \n  type, \n  title, \n  items, \n  color, \n  icon: Icon, \n  loading = false,\n  culturalPattern = 'lotus' \n}) => {\n  const theme = useTheme();\n  \n  const getPatternBackground = (pattern) => {\n    switch (pattern) {\n      case 'lotus':\n        return `radial-gradient(circle at 20% 80%, ${color}15 0%, transparent 50%), \n                radial-gradient(circle at 80% 20%, ${color}10 0%, transparent 50%)`;\n      case 'mandala':\n        return `conic-gradient(from 0deg at 50% 50%, ${color}05, ${color}15, ${color}05)`;\n      case 'paisley':\n        return `linear-gradient(45deg, ${color}08 25%, transparent 25%), \n                linear-gradient(-45deg, ${color}08 25%, transparent 25%)`;\n      default:\n        return `linear-gradient(135deg, ${color}10 0%, ${color}05 100%)`;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Card sx={{ height: '100%', minHeight: 300 }}>\n        <CardContent>\n          <Skeleton variant=\"circular\" width={48} height={48} />\n          <Skeleton variant=\"text\" width=\"60%\" sx={{ mt: 2 }} />\n          {[...Array(4)].map((_, index) => (\n            <Skeleton key={index} variant=\"text\" width=\"90%\" sx={{ mt: 1 }} />\n          ))}\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Fade in timeout={300}>\n      <Card\n        sx={{\n          height: '100%',\n          minHeight: 300,\n          background: getPatternBackground(culturalPattern),\n          border: `2px solid ${color}20`,\n          transition: 'all 0.3s ease-in-out',\n          '&:hover': {\n            transform: 'translateY(-4px)',\n            boxShadow: theme.shadows[8],\n            border: `2px solid ${color}40`,\n          },\n        }}\n      >\n        <CardContent sx={{ p: 3 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n            <Avatar\n              sx={{\n                bgcolor: color,\n                width: 48,\n                height: 48,\n                mr: 2,\n              }}\n            >\n              <Icon />\n            </Avatar>\n            <Box>\n              <Typography variant=\"h6\" sx={{ fontWeight: 700, color: color }}>\n                {title}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                {items.length} identified areas\n              </Typography>\n            </Box>\n          </Box>\n\n          <Box>\n            {items.map((item, index) => (\n              <Fade in timeout={300 + index * 100} key={index}>\n                <Box\n                  sx={{\n                    p: 2,\n                    mb: 2,\n                    borderRadius: 2,\n                    bgcolor: 'background.paper',\n                    border: `1px solid ${color}20`,\n                    transition: 'all 0.2s ease-in-out',\n                    '&:hover': {\n                      bgcolor: `${color}05`,\n                      border: `1px solid ${color}40`,\n                    },\n                  }}\n                >\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                    <Box\n                      sx={{\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        bgcolor: color,\n                        mr: 1,\n                      }}\n                    />\n                    <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                      {item.title}\n                    </Typography>\n                    {item.priority && (\n                      <Chip\n                        label={item.priority}\n                        size=\"small\"\n                        sx={{ ml: 'auto', fontSize: '0.75rem' }}\n                        color={item.priority === 'High' ? 'error' : item.priority === 'Medium' ? 'warning' : 'default'}\n                      />\n                    )}\n                  </Box>\n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    {item.description}\n                  </Typography>\n                  {item.culturalContext && (\n                    <Typography variant=\"caption\" sx={{ \n                      display: 'block', \n                      mt: 0.5, \n                      fontStyle: 'italic',\n                      color: color \n                    }}>\n                      Cultural Context: {item.culturalContext}\n                    </Typography>\n                  )}\n                </Box>\n              </Fade>\n            ))}\n          </Box>\n        </CardContent>\n      </Card>\n    </Fade>\n  );\n};\n\n// Main Cultural SWOT Visualization Component\nconst CulturalSWOTVisualization = ({ studentId, boardType = 'CBSE' }) => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  \n  const [loading, setLoading] = useState(true);\n  const [swotData, setSWOTData] = useState({});\n  const [selectedStudent, setSelectedStudent] = useState(null);\n\n  useEffect(() => {\n    const loadSWOTData = async () => {\n      setLoading(true);\n      \n      // Simulate API call with Indian educational context\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      const mockSWOTData = {\n        strengths: [\n          {\n            title: 'Strong Mathematical Foundation',\n            description: 'Excellent performance in algebra and geometry',\n            priority: 'High',\n            culturalContext: 'Vedic mathematics influence'\n          },\n          {\n            title: 'Hindi Language Proficiency',\n            description: 'Native speaker with excellent writing skills',\n            priority: 'High',\n            culturalContext: 'Mother tongue advantage'\n          },\n          {\n            title: 'Cultural Values Integration',\n            description: 'Strong moral and ethical foundation',\n            priority: 'Medium',\n            culturalContext: 'Family and community values'\n          },\n          {\n            title: 'Collaborative Learning',\n            description: 'Works well in group projects and team activities',\n            priority: 'Medium',\n            culturalContext: 'Community-oriented upbringing'\n          },\n        ],\n        weaknesses: [\n          {\n            title: 'English Communication',\n            description: 'Needs improvement in spoken English confidence',\n            priority: 'High',\n            culturalContext: 'Regional language dominance'\n          },\n          {\n            title: 'Technology Adaptation',\n            description: 'Slower adoption of digital learning tools',\n            priority: 'Medium',\n            culturalContext: 'Traditional learning methods preference'\n          },\n          {\n            title: 'Time Management',\n            description: 'Struggles with deadline management',\n            priority: 'Medium',\n            culturalContext: 'Flexible time concept in culture'\n          },\n        ],\n        opportunities: [\n          {\n            title: 'Competitive Exam Preparation',\n            description: 'Strong foundation for JEE/NEET preparation',\n            priority: 'High',\n            culturalContext: 'Engineering/Medical career aspirations'\n          },\n          {\n            title: 'Multilingual Advantage',\n            description: 'Can leverage multiple language skills',\n            priority: 'High',\n            culturalContext: 'Diverse linguistic environment'\n          },\n          {\n            title: 'Cultural Leadership',\n            description: 'Can lead cultural and festival activities',\n            priority: 'Medium',\n            culturalContext: 'Rich cultural heritage knowledge'\n          },\n        ],\n        threats: [\n          {\n            title: 'Urban Competition',\n            description: 'Intense competition from metro city students',\n            priority: 'High',\n            culturalContext: 'Resource disparity between regions'\n          },\n          {\n            title: 'Digital Divide',\n            description: 'Limited access to advanced technology',\n            priority: 'Medium',\n            culturalContext: 'Infrastructure limitations'\n          },\n          {\n            title: 'Career Pressure',\n            description: 'Family expectations for traditional careers',\n            priority: 'Medium',\n            culturalContext: 'Societal career preferences'\n          },\n        ],\n      };\n      \n      setSWOTData(mockSWOTData);\n      setSelectedStudent({\n        name: 'Arjun Sharma',\n        class: 'Class X-A',\n        board: boardType,\n        rollNumber: 'CB2024001'\n      });\n      setLoading(false);\n    };\n\n    loadSWOTData();\n  }, [studentId, boardType]);\n\n  const swotConfig = [\n    {\n      type: 'strengths',\n      title: 'Strengths (शक्तियाँ)',\n      color: theme.palette.success.main,\n      icon: StrengthIcon,\n      pattern: 'lotus',\n      items: swotData.strengths || []\n    },\n    {\n      type: 'weaknesses',\n      title: 'Weaknesses (कमजोरियाँ)',\n      color: theme.palette.error.main,\n      icon: WeaknessIcon,\n      pattern: 'mandala',\n      items: swotData.weaknesses || []\n    },\n    {\n      type: 'opportunities',\n      title: 'Opportunities (अवसर)',\n      color: theme.palette.primary.main,\n      icon: OpportunityIcon,\n      pattern: 'paisley',\n      items: swotData.opportunities || []\n    },\n    {\n      type: 'threats',\n      title: 'Threats (चुनौतियाँ)',\n      color: theme.palette.warning.main,\n      icon: ThreatIcon,\n      pattern: 'lotus',\n      items: swotData.threats || []\n    },\n  ];\n\n  return (\n    <Box sx={{ p: { xs: 2, md: 3 } }}>\n      {/* Header Section */}\n      <Box sx={{ mb: 4 }}>\n        <Typography variant=\"h4\" component=\"h1\" sx={{ fontWeight: 700, mb: 1 }}>\n          Cultural SWOT Analysis\n        </Typography>\n        {selectedStudent && (\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>\n            <Avatar sx={{ bgcolor: 'primary.main' }}>\n              {selectedStudent.name.charAt(0)}\n            </Avatar>\n            <Box>\n              <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n                {selectedStudent.name}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                {selectedStudent.class} • {selectedStudent.board} Board • Roll: {selectedStudent.rollNumber}\n              </Typography>\n            </Box>\n          </Box>\n        )}\n        <Typography variant=\"body1\" color=\"text.secondary\">\n          Comprehensive analysis with Indian educational and cultural context\n        </Typography>\n      </Box>\n\n      {/* SWOT Grid */}\n      <Grid container spacing={3}>\n        {swotConfig.map((config) => (\n          <Grid item xs={12} md={6} key={config.type}>\n            <CulturalSWOTCard\n              type={config.type}\n              title={config.title}\n              items={config.items}\n              color={config.color}\n              icon={config.icon}\n              culturalPattern={config.pattern}\n              loading={loading}\n            />\n          </Grid>\n        ))}\n      </Grid>\n    </Box>\n  );\n};\n\nexport default CulturalSWOTVisualization;\n", "/**\n * VidyaMitra Platform - Individual Student SWOT Analysis View\n * \n * This component implements the individual student SWOT analysis view as specified \n * in the visualization mockups with comprehensive student profile and analysis.\n * \n * Based on: docs/visualization_mockups.md - Individual Student SWOT Analysis View\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  Button,\n  Chip,\n  Avatar,\n  LinearProgress,\n  IconButton,\n  Tooltip,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  Divider,\n  useTheme,\n  alpha,\n  Stack,\n  Badge,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow\n} from '@mui/material';\nimport {\n  ArrowBack,\n  Search,\n  Download,\n  Share,\n  Print,\n  Psychology,\n  School,\n  CalendarToday,\n  TrendingUp,\n  TrendingDown,\n  Star,\n  Warning,\n  CheckCircle,\n  Timeline\n} from '@mui/icons-material';\nimport { Radar } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  RadialLinearScale,\n  PointElement,\n  LineElement,\n  Filler,\n  Tooltip as ChartTooltip,\n  Legend,\n} from 'chart.js';\nimport { useTranslation } from 'react-i18next';\nimport { useParams, useNavigate } from 'react-router-dom';\n\n// Register Chart.js components\nChartJS.register(RadialLinearScale, PointElement, LineElement, Filler, ChartTooltip, Legend);\n\n// Academic Performance Radar Chart Component\nconst AcademicRadarChart = ({ performanceData, loading }) => {\n  if (loading) {\n    return (\n      <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n        <LinearProgress sx={{ width: '80%' }} />\n      </Box>\n    );\n  }\n\n  const chartData = {\n    labels: ['Math', 'English', 'Science', 'Geography', 'History', 'Art', 'PE'],\n    datasets: [\n      {\n        label: 'Student Performance',\n        data: [87, 92, 95, 75, 78, 96, 72],\n        backgroundColor: 'rgba(54, 162, 235, 0.2)',\n        borderColor: 'rgba(54, 162, 235, 1)',\n        borderWidth: 2,\n        pointBackgroundColor: 'rgba(54, 162, 235, 1)',\n        pointBorderColor: '#fff',\n        pointHoverBackgroundColor: '#fff',\n        pointHoverBorderColor: 'rgba(54, 162, 235, 1)',\n      },\n      {\n        label: 'Class Average',\n        data: [75, 78, 80, 72, 74, 82, 76],\n        backgroundColor: 'rgba(255, 99, 132, 0.2)',\n        borderColor: 'rgba(255, 99, 132, 1)',\n        borderWidth: 2,\n        pointBackgroundColor: 'rgba(255, 99, 132, 1)',\n        pointBorderColor: '#fff',\n        pointHoverBackgroundColor: '#fff',\n        pointHoverBorderColor: 'rgba(255, 99, 132, 1)',\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    scales: {\n      r: {\n        angleLines: {\n          display: true,\n        },\n        suggestedMin: 0,\n        suggestedMax: 100,\n        ticks: {\n          stepSize: 20,\n        },\n      },\n    },\n    plugins: {\n      legend: {\n        position: 'bottom',\n      },\n    },\n  };\n\n  return (\n    <Box sx={{ height: 300 }}>\n      <Radar data={chartData} options={chartOptions} />\n    </Box>\n  );\n};\n\n// SWOT Analysis Quadrant Component\nconst SWOTQuadrant = ({ title, items, color, icon: Icon }) => {\n  const theme = useTheme();\n\n  return (\n    <Card sx={{ height: '100%', border: `2px solid ${color}` }}>\n      <CardContent>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>\n          <Icon sx={{ color }} />\n          <Typography variant=\"h6\" sx={{ fontWeight: 600, color }}>\n            {title}\n          </Typography>\n        </Box>\n        <List dense>\n          {items.map((item, index) => (\n            <ListItem key={index} sx={{ px: 0 }}>\n              <ListItemText\n                primary={\n                  <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                    • {item}\n                  </Typography>\n                }\n              />\n            </ListItem>\n          ))}\n        </List>\n      </CardContent>\n    </Card>\n  );\n};\n\n// Attendance Calendar Component\nconst AttendanceCalendar = ({ attendanceData, loading }) => {\n  if (loading) {\n    return (\n      <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n        <LinearProgress sx={{ width: '80%' }} />\n      </Box>\n    );\n  }\n\n  // Mock calendar data\n  const calendarDays = Array.from({ length: 30 }, (_, i) => ({\n    day: i + 1,\n    status: Math.random() > 0.05 ? 'present' : Math.random() > 0.5 ? 'absent' : 'late'\n  }));\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'present': return '#4CAF50';\n      case 'absent': return '#F44336';\n      case 'late': return '#FF9800';\n      default: return '#E0E0E0';\n    }\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n        Attendance\n      </Typography>\n      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: 1, mb: 2 }}>\n        {calendarDays.map((day) => (\n          <Tooltip key={day.day} title={`Day ${day.day}: ${day.status}`}>\n            <Box\n              sx={{\n                width: 32,\n                height: 32,\n                borderRadius: 1,\n                backgroundColor: getStatusColor(day.status),\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '0.75rem',\n                color: 'white',\n                fontWeight: 500,\n                cursor: 'pointer'\n              }}\n            >\n              {day.day}\n            </Box>\n          </Tooltip>\n        ))}\n      </Box>\n      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n        <Typography variant=\"body2\">\n          <strong>Present:</strong> 43 days (95.6%)\n        </Typography>\n        <Typography variant=\"body2\">\n          <strong>Absent:</strong> 2 days\n        </Typography>\n        <Typography variant=\"body2\">\n          <strong>Tardy:</strong> 1 day\n        </Typography>\n      </Box>\n    </Box>\n  );\n};\n\n// Behavior Timeline Component\nconst BehaviorTimeline = ({ behaviorData, loading }) => {\n  if (loading) {\n    return (\n      <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n        <LinearProgress sx={{ width: '80%' }} />\n      </Box>\n    );\n  }\n\n  const incidents = [\n    { date: 5, type: 'positive', description: 'Helped new student' },\n    { date: 10, type: 'positive', description: 'Excellent project presentation' },\n    { date: 15, type: 'negative', description: 'Late to class' },\n    { date: 20, type: 'positive', description: 'Volunteered for cleanup' },\n  ];\n\n  return (\n    <Box>\n      <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n        Behavior\n      </Typography>\n      <Box sx={{ position: 'relative', height: 60, mb: 2 }}>\n        {/* Timeline line */}\n        <Box\n          sx={{\n            position: 'absolute',\n            top: '50%',\n            left: 0,\n            right: 0,\n            height: 2,\n            backgroundColor: '#E0E0E0',\n            transform: 'translateY(-50%)',\n          }}\n        />\n        {/* Timeline markers */}\n        {incidents.map((incident, index) => (\n          <Tooltip key={index} title={incident.description}>\n            <Box\n              sx={{\n                position: 'absolute',\n                left: `${(incident.date / 30) * 100}%`,\n                top: '50%',\n                transform: 'translate(-50%, -50%)',\n                width: 16,\n                height: 16,\n                borderRadius: '50%',\n                backgroundColor: incident.type === 'positive' ? '#4CAF50' : '#F44336',\n                cursor: 'pointer',\n                '&:hover': {\n                  transform: 'translate(-50%, -50%) scale(1.2)',\n                },\n                transition: 'transform 0.2s ease-in-out',\n              }}\n            />\n          </Tooltip>\n        ))}\n        {/* Date labels */}\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>\n          <Typography variant=\"caption\">1</Typography>\n          <Typography variant=\"caption\">10</Typography>\n          <Typography variant=\"caption\">20</Typography>\n          <Typography variant=\"caption\">30</Typography>\n        </Box>\n      </Box>\n      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n        <Typography variant=\"body2\">\n          <strong>Positive Incidents:</strong> 3\n        </Typography>\n        <Typography variant=\"body2\">\n          <strong>Negative Incidents:</strong> 1\n        </Typography>\n        <Typography variant=\"body2\">\n          <strong>Trend:</strong> <span style={{ color: '#4CAF50' }}>Improving</span>\n        </Typography>\n      </Box>\n    </Box>\n  );\n};\n\n// Extracurricular Activities Component\nconst ExtracurricularActivities = ({ activitiesData, loading }) => {\n  if (loading) {\n    return (\n      <Box sx={{ height: 150, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n        <LinearProgress sx={{ width: '80%' }} />\n      </Box>\n    );\n  }\n\n  const activities = [\n    { name: 'Chess Club', hours: 2, attendance: 100 },\n    { name: 'School Newspaper', hours: 3, attendance: 92 },\n  ];\n\n  return (\n    <Box>\n      <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n        Extracurricular Activities\n      </Typography>\n      <Stack spacing={2}>\n        {activities.map((activity, index) => (\n          <Card key={index} variant=\"outlined\">\n            <CardContent sx={{ py: 2 }}>\n              <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                {activity.name}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                {activity.hours} hrs/week, {activity.attendance}% attendance\n              </Typography>\n            </CardContent>\n          </Card>\n        ))}\n      </Stack>\n    </Box>\n  );\n};\n\n// Recommendations Component\nconst Recommendations = ({ recommendations, loading }) => {\n  if (loading) {\n    return (\n      <Box sx={{ height: 150, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n        <LinearProgress sx={{ width: '80%' }} />\n      </Box>\n    );\n  }\n\n  const recommendationsList = [\n    'Consider Math Olympiad to strengthen skills',\n    'Monitor History performance - offer additional resources',\n    'Encourage consistent PE participation',\n  ];\n\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n          Recommendations\n        </Typography>\n        <List>\n          {recommendationsList.map((recommendation, index) => (\n            <ListItem key={index} sx={{ px: 0 }}>\n              <ListItemText\n                primary={\n                  <Typography variant=\"body2\">\n                    • {recommendation}\n                  </Typography>\n                }\n              />\n            </ListItem>\n          ))}\n        </List>\n      </CardContent>\n    </Card>\n  );\n};\n\n// Main Individual Student SWOT Component\nconst IndividualStudentSWOT = () => {\n  const { t } = useTranslation(['swot', 'common']);\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const { studentId } = useParams();\n  const [loading, setLoading] = useState(true);\n\n  // Mock student data\n  const [studentData, setStudentData] = useState({\n    name: 'Jane Doe',\n    grade: 9,\n    id: 'STU12345',\n    quarter: 'Q1 2024-2025',\n    gpa: 3.7,\n    classRank: 8,\n    totalStudents: 28,\n    subjects: [\n      { name: 'Math', grade: 'B+', score: 87 },\n      { name: 'English', grade: 'A-', score: 92 },\n      { name: 'Science', grade: 'A', score: 95 },\n      { name: 'Geography', grade: 'C', score: 75 },\n      { name: 'History', grade: 'C+', score: 78 },\n      { name: 'Art', grade: 'A', score: 96 },\n      { name: 'PE', grade: 'C-', score: 72 },\n    ],\n    swotData: {\n      strengths: ['Math', 'Science', 'Art'],\n      weaknesses: ['Geography', 'History', 'PE'],\n      opportunities: ['Join Science Club', 'Math tutoring'],\n      threats: ['Attendance pattern in History class', 'Declining PE scores'],\n    },\n  });\n\n  useEffect(() => {\n    // Simulate data loading\n    const loadStudentData = async () => {\n      setLoading(true);\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      setLoading(false);\n    };\n\n    loadStudentData();\n  }, [studentId]);\n\n  const handleBackToClass = () => {\n    navigate('/dashboard/students');\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n            <IconButton onClick={handleBackToClass}>\n              <ArrowBack />\n            </IconButton>\n            <Typography variant=\"h4\" sx={{ fontWeight: 700 }}>\n              Student: {studentData.name}\n            </Typography>\n          </Box>\n          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>\n            <IconButton>\n              <Search />\n            </IconButton>\n          </Box>\n        </Box>\n\n        {/* Student Info Bar */}\n        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', mb: 2 }}>\n          <Chip label={`Grade: ${studentData.grade}`} color=\"primary\" />\n          <Chip label={`ID: ${studentData.id}`} variant=\"outlined\" />\n          <Chip label={studentData.quarter} variant=\"outlined\" />\n        </Box>\n      </Box>\n\n      {/* Main Content Grid */}\n      <Grid container spacing={3}>\n        {/* Left Column - Academic Performance */}\n        <Grid item xs={12} md={6}>\n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n                Academic Performance\n              </Typography>\n              <AcademicRadarChart loading={loading} />\n\n              {/* Subject Scores */}\n              <Box sx={{ mt: 3 }}>\n                {studentData.subjects.map((subject, index) => (\n                  <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                    <Typography variant=\"body2\">{subject.name}:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                      {subject.grade} ({subject.score}%)\n                    </Typography>\n                  </Box>\n                ))}\n                <Divider sx={{ my: 2 }} />\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>GPA:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{studentData.gpa}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>Class Rank:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>\n                    {studentData.classRank}/{studentData.totalStudents}\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Right Column - SWOT Analysis */}\n        <Grid item xs={12} md={6}>\n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n                SWOT Analysis\n              </Typography>\n              <Grid container spacing={2}>\n                <Grid item xs={6}>\n                  <SWOTQuadrant\n                    title=\"Strengths\"\n                    items={studentData.swotData.strengths}\n                    color=\"#4CAF50\"\n                    icon={CheckCircle}\n                  />\n                </Grid>\n                <Grid item xs={6}>\n                  <SWOTQuadrant\n                    title=\"Weaknesses\"\n                    items={studentData.swotData.weaknesses}\n                    color=\"#FF5722\"\n                    icon={Warning}\n                  />\n                </Grid>\n                <Grid item xs={6}>\n                  <SWOTQuadrant\n                    title=\"Opportunities\"\n                    items={studentData.swotData.opportunities}\n                    color=\"#2196F3\"\n                    icon={TrendingUp}\n                  />\n                </Grid>\n                <Grid item xs={6}>\n                  <SWOTQuadrant\n                    title=\"Threats\"\n                    items={studentData.swotData.threats}\n                    color=\"#FF9800\"\n                    icon={TrendingDown}\n                  />\n                </Grid>\n              </Grid>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Bottom Row - Attendance and Behavior */}\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%' }}>\n            <CardContent>\n              <AttendanceCalendar loading={loading} />\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%' }}>\n            <CardContent>\n              <BehaviorTimeline loading={loading} />\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Extracurricular Activities */}\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <ExtracurricularActivities loading={loading} />\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Recommendations */}\n        <Grid item xs={12} md={6}>\n          <Recommendations loading={loading} />\n        </Grid>\n      </Grid>\n\n      {/* Action Buttons */}\n      <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>\n        <Button\n          variant=\"outlined\"\n          startIcon={<Download />}\n          sx={{ textTransform: 'none' }}\n        >\n          Download Report\n        </Button>\n        <Button\n          variant=\"outlined\"\n          startIcon={<Share />}\n          sx={{ textTransform: 'none' }}\n        >\n          Share\n        </Button>\n        <Button\n          variant=\"outlined\"\n          startIcon={<Print />}\n          sx={{ textTransform: 'none' }}\n        >\n          Print\n        </Button>\n        <Button\n          variant=\"contained\"\n          startIcon={<Psychology />}\n          sx={{ textTransform: 'none' }}\n        >\n          Generate New Analysis\n        </Button>\n      </Box>\n    </Box>\n  );\n};\n\nexport default IndividualStudentSWOT;\n", "import { useEffect, useRef, useState } from 'react';\n\n/**\n * Custom hook for Intersection Observer API\n * Optimized for performance and mobile devices\n */\nexport const useIntersectionObserver = ({\n  threshold = 0.1,\n  root = null,\n  rootMargin = '0px',\n  once = true,\n  enabled = true,\n}) => {\n  const [isIntersecting, setIsIntersecting] = useState(false);\n  const [hasIntersected, setHasIntersected] = useState(false);\n  const targetRef = useRef(null);\n  const observerRef = useRef(null);\n\n  useEffect(() => {\n    if (!enabled) return;\n\n    const target = targetRef.current;\n    if (!target) return;\n\n    // Check if IntersectionObserver is supported\n    if (!window.IntersectionObserver) {\n      // Fallback for older browsers - assume visible\n      setIsIntersecting(true);\n      setHasIntersected(true);\n      return;\n    }\n\n    const handleIntersection = (entries) => {\n      const [entry] = entries;\n      const isCurrentlyIntersecting = entry.isIntersecting;\n      \n      setIsIntersecting(isCurrentlyIntersecting);\n      \n      if (isCurrentlyIntersecting && !hasIntersected) {\n        setHasIntersected(true);\n        \n        // If 'once' is true, disconnect after first intersection\n        if (once && observerRef.current) {\n          observerRef.current.disconnect();\n        }\n      }\n    };\n\n    // Create observer with optimized options\n    observerRef.current = new IntersectionObserver(handleIntersection, {\n      threshold: Array.isArray(threshold) ? threshold : [threshold],\n      root,\n      rootMargin,\n    });\n\n    observerRef.current.observe(target);\n\n    // Cleanup function\n    return () => {\n      if (observerRef.current) {\n        observerRef.current.disconnect();\n      }\n    };\n  }, [threshold, root, rootMargin, once, enabled, hasIntersected]);\n\n  // Return the ref and intersection state\n  return [targetRef, once ? hasIntersected : isIntersecting, hasIntersected];\n};\n\n/**\n * Hook for lazy loading images with intersection observer\n */\nexport const useLazyImage = (src, options = {}) => {\n  const {\n    placeholder = '',\n    threshold = 0.1,\n    rootMargin = '50px',\n  } = options;\n\n  const [imageSrc, setImageSrc] = useState(placeholder);\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [ref, isIntersecting] = useIntersectionObserver({\n    threshold,\n    rootMargin,\n    once: true,\n  });\n\n  useEffect(() => {\n    if (isIntersecting && src && !imageLoaded && !imageError) {\n      const img = new Image();\n      \n      img.onload = () => {\n        setImageSrc(src);\n        setImageLoaded(true);\n      };\n      \n      img.onerror = () => {\n        setImageError(true);\n      };\n      \n      img.src = src;\n    }\n  }, [isIntersecting, src, imageLoaded, imageError]);\n\n  return {\n    ref,\n    src: imageSrc,\n    isLoaded: imageLoaded,\n    isError: imageError,\n    isIntersecting,\n  };\n};\n\n/**\n * Hook for infinite scrolling with intersection observer\n */\nexport const useInfiniteScroll = (callback, options = {}) => {\n  const {\n    threshold = 1.0,\n    rootMargin = '0px',\n    enabled = true,\n  } = options;\n\n  const [isFetching, setIsFetching] = useState(false);\n  const [ref, isIntersecting] = useIntersectionObserver({\n    threshold,\n    rootMargin,\n    once: false,\n    enabled: enabled && !isFetching,\n  });\n\n  useEffect(() => {\n    if (isIntersecting && enabled && !isFetching) {\n      setIsFetching(true);\n      \n      Promise.resolve(callback())\n        .finally(() => {\n          setIsFetching(false);\n        });\n    }\n  }, [isIntersecting, enabled, isFetching, callback]);\n\n  return {\n    ref,\n    isFetching,\n    isIntersecting,\n  };\n};\n\n/**\n * Hook for tracking element visibility for analytics\n */\nexport const useVisibilityTracking = (callback, options = {}) => {\n  const {\n    threshold = 0.5,\n    rootMargin = '0px',\n    minVisibleTime = 1000, // Minimum time in ms to consider as \"viewed\"\n  } = options;\n\n  const [ref, isIntersecting] = useIntersectionObserver({\n    threshold,\n    rootMargin,\n    once: false,\n  });\n\n  const visibilityStartTime = useRef(null);\n  const hasBeenTracked = useRef(false);\n\n  useEffect(() => {\n    if (isIntersecting) {\n      // Element became visible\n      if (!visibilityStartTime.current) {\n        visibilityStartTime.current = Date.now();\n      }\n    } else {\n      // Element became hidden\n      if (visibilityStartTime.current && !hasBeenTracked.current) {\n        const visibleDuration = Date.now() - visibilityStartTime.current;\n        \n        if (visibleDuration >= minVisibleTime) {\n          callback({\n            duration: visibleDuration,\n            timestamp: Date.now(),\n          });\n          hasBeenTracked.current = true;\n        }\n        \n        visibilityStartTime.current = null;\n      }\n    }\n  }, [isIntersecting, callback, minVisibleTime]);\n\n  return {\n    ref,\n    isVisible: isIntersecting,\n    hasBeenTracked: hasBeenTracked.current,\n  };\n};\n\n/**\n * Hook for progressive content loading\n */\nexport const useProgressiveLoading = (items, options = {}) => {\n  const {\n    batchSize = 10,\n    threshold = 0.8,\n    rootMargin = '100px',\n  } = options;\n\n  const [visibleItems, setVisibleItems] = useState(\n    items.slice(0, Math.min(batchSize, items.length))\n  );\n  const [hasMore, setHasMore] = useState(items.length > batchSize);\n\n  const loadMore = () => {\n    const currentLength = visibleItems.length;\n    const nextBatch = items.slice(currentLength, currentLength + batchSize);\n    \n    setVisibleItems(prev => [...prev, ...nextBatch]);\n    setHasMore(currentLength + batchSize < items.length);\n  };\n\n  const { ref, isFetching } = useInfiniteScroll(loadMore, {\n    threshold,\n    rootMargin,\n    enabled: hasMore,\n  });\n\n  // Update visible items when items prop changes\n  useEffect(() => {\n    const newVisibleCount = Math.min(visibleItems.length, items.length);\n    setVisibleItems(items.slice(0, Math.max(newVisibleCount, batchSize)));\n    setHasMore(items.length > newVisibleCount);\n  }, [items, batchSize, visibleItems.length]);\n\n  return {\n    visibleItems,\n    hasMore,\n    isLoading: isFetching,\n    loadMoreRef: ref,\n  };\n};\n\nexport default useIntersectionObserver;\n", "import React, { useState } from 'react';\nimport {\n  Box,\n  Container,\n  Typo<PERSON>,\n  <PERSON>ton,\n  Grid,\n  Card,\n  CardContent,\n  AppBar,\n  Toolbar,\n  IconButton,\n  Drawer,\n  List,\n  ListItem,\n  ListItemText,\n  useTheme,\n  useMediaQuery,\n  Fade,\n  Slide,\n  Zoom,\n  Avatar,\n  Chip,\n  Paper,\n} from '@mui/material';\nimport {\n  Menu as MenuIcon,\n  School as SchoolIcon,\n  Assessment as AssessmentIcon,\n  TrendingUp as TrendingUpIcon,\n  People as PeopleIcon,\n  Star as StarIcon,\n  ArrowForward as ArrowForwardIcon,\n  Phone as PhoneIcon,\n  Email as EmailIcon,\n  LocationOn as LocationOnIcon,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useIntersectionObserver } from '../../hooks/useIntersectionObserver';\n\n// Navigation Header Component\nconst NavigationHeader = () => {\n  const navigate = useNavigate();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const [mobileOpen, setMobileOpen] = useState(false);\n\n  const handleDrawerToggle = () => {\n    setMobileOpen(!mobileOpen);\n  };\n\n  const navigationItems = [\n    { label: 'Home', path: '/' },\n    { label: 'About', path: '/about' },\n    { label: 'Features', path: '/features' },\n    { label: 'Contact', path: '/contact' },\n  ];\n\n  const drawer = (\n    <Box onClick={handleDrawerToggle} sx={{ textAlign: 'center' }}>\n      <Typography variant=\"h6\" sx={{ my: 2, fontWeight: 700 }}>\n        VidyaMitra\n      </Typography>\n      <List>\n        {navigationItems.map((item) => (\n          <ListItem key={item.label} disablePadding>\n            <Button\n              fullWidth\n              onClick={() => navigate(item.path)}\n              sx={{ textAlign: 'center', py: 1 }}\n            >\n              <ListItemText primary={item.label} />\n            </Button>\n          </ListItem>\n        ))}\n        <ListItem disablePadding>\n          <Button\n            fullWidth\n            variant=\"contained\"\n            onClick={() => navigate('/login')}\n            sx={{ m: 2 }}\n          >\n            Login\n          </Button>\n        </ListItem>\n      </List>\n    </Box>\n  );\n\n  return (\n    <>\n      <AppBar\n        position=\"fixed\"\n        sx={{\n          bgcolor: 'rgba(255, 255, 255, 0.95)',\n          backdropFilter: 'blur(10px)',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          color: 'text.primary',\n        }}\n      >\n        <Toolbar>\n          <Typography\n            variant=\"h6\"\n            component=\"div\"\n            sx={{\n              flexGrow: 1,\n              fontWeight: 700,\n              color: theme.palette.primary.main,\n              cursor: 'pointer',\n            }}\n            onClick={() => navigate('/')}\n          >\n            VidyaMitra\n          </Typography>\n          {isMobile ? (\n            <IconButton\n              color=\"inherit\"\n              aria-label=\"open drawer\"\n              edge=\"start\"\n              onClick={handleDrawerToggle}\n            >\n              <MenuIcon />\n            </IconButton>\n          ) : (\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              {navigationItems.map((item) => (\n                <Button\n                  key={item.label}\n                  onClick={() => navigate(item.path)}\n                  sx={{\n                    color: 'text.primary',\n                    fontWeight: 500,\n                    '&:hover': {\n                      bgcolor: 'rgba(46, 91, 168, 0.1)',\n                    },\n                  }}\n                >\n                  {item.label}\n                </Button>\n              ))}\n              <Button\n                variant=\"contained\"\n                onClick={() => navigate('/login')}\n                sx={{\n                  ml: 2,\n                  borderRadius: 2,\n                  px: 3,\n                }}\n              >\n                Login\n              </Button>\n            </Box>\n          )}\n        </Toolbar>\n      </AppBar>\n      <Drawer\n        variant=\"temporary\"\n        open={mobileOpen}\n        onClose={handleDrawerToggle}\n        ModalProps={{\n          keepMounted: true,\n        }}\n        sx={{\n          display: { xs: 'block', md: 'none' },\n          '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 240 },\n        }}\n      >\n        {drawer}\n      </Drawer>\n    </>\n  );\n};\n\n// Hero Section Component\nconst HeroSection = () => {\n  const navigate = useNavigate();\n  const theme = useTheme();\n  const [heroRef, isHeroVisible] = useIntersectionObserver({ threshold: 0.1 });\n\n  return (\n    <Box\n      ref={heroRef}\n      sx={{\n        minHeight: '100vh',\n        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n        display: 'flex',\n        alignItems: 'center',\n        position: 'relative',\n        overflow: 'hidden',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'url(\"data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"4\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")',\n        },\n      }}\n    >\n      <Container maxWidth=\"lg\" sx={{ position: 'relative', zIndex: 1 }}>\n        <Grid container spacing={4} alignItems=\"center\">\n          <Grid item xs={12} md={6}>\n            <Fade in={isHeroVisible} timeout={1000}>\n              <Box>\n                <Typography\n                  variant=\"h1\"\n                  sx={{\n                    color: 'white',\n                    fontWeight: 800,\n                    mb: 3,\n                    textShadow: '0 2px 4px rgba(0,0,0,0.3)',\n                    fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },\n                  }}\n                >\n                  VidyaMitra\n                </Typography>\n                <Typography\n                  variant=\"h4\"\n                  sx={{\n                    color: 'rgba(255,255,255,0.9)',\n                    mb: 3,\n                    fontWeight: 400,\n                    fontSize: { xs: '1.2rem', md: '1.5rem' },\n                  }}\n                >\n                  Student SWOT Analysis Platform\n                </Typography>\n                <Typography\n                  variant=\"h6\"\n                  sx={{\n                    color: 'rgba(255,255,255,0.8)',\n                    mb: 4,\n                    lineHeight: 1.6,\n                    fontSize: { xs: '1rem', md: '1.1rem' },\n                  }}\n                >\n                  Empowering Indian education through intelligent student analysis.\n                  Supporting CBSE, ICSE, and State boards with AI-powered insights.\n                </Typography>\n                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n                  <Button\n                    variant=\"contained\"\n                    size=\"large\"\n                    onClick={() => navigate('/login')}\n                    sx={{\n                      bgcolor: 'white',\n                      color: theme.palette.primary.main,\n                      px: 4,\n                      py: 1.5,\n                      fontSize: '1.1rem',\n                      fontWeight: 600,\n                      borderRadius: 3,\n                      boxShadow: '0 4px 12px rgba(0,0,0,0.2)',\n                      '&:hover': {\n                        bgcolor: 'rgba(255,255,255,0.9)',\n                        transform: 'translateY(-2px)',\n                        boxShadow: '0 6px 16px rgba(0,0,0,0.3)',\n                      },\n                    }}\n                    endIcon={<ArrowForwardIcon />}\n                  >\n                    Get Started\n                  </Button>\n                  <Button\n                    variant=\"outlined\"\n                    size=\"large\"\n                    onClick={() => navigate('/features')}\n                    sx={{\n                      color: 'white',\n                      borderColor: 'white',\n                      px: 4,\n                      py: 1.5,\n                      fontSize: '1.1rem',\n                      fontWeight: 600,\n                      borderRadius: 3,\n                      '&:hover': {\n                        bgcolor: 'rgba(255,255,255,0.1)',\n                        borderColor: 'white',\n                        transform: 'translateY(-2px)',\n                      },\n                    }}\n                  >\n                    Learn More\n                  </Button>\n                </Box>\n              </Box>\n            </Fade>\n          </Grid>\n          <Grid item xs={12} md={6}>\n            <Slide direction=\"left\" in={isHeroVisible} timeout={1200}>\n              <Box\n                sx={{\n                  display: 'flex',\n                  justifyContent: 'center',\n                  alignItems: 'center',\n                  height: { xs: 300, md: 400 },\n                }}\n              >\n                <Paper\n                  elevation={8}\n                  sx={{\n                    p: 4,\n                    borderRadius: 4,\n                    bgcolor: 'rgba(255,255,255,0.95)',\n                    backdropFilter: 'blur(10px)',\n                    maxWidth: 400,\n                    width: '100%',\n                  }}\n                >\n                  <Box sx={{ textAlign: 'center' }}>\n                    <Avatar\n                      sx={{\n                        width: 80,\n                        height: 80,\n                        bgcolor: theme.palette.secondary.main,\n                        mx: 'auto',\n                        mb: 2,\n                      }}\n                    >\n                      <SchoolIcon sx={{ fontSize: 40 }} />\n                    </Avatar>\n                    <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n                      Comprehensive Student Analysis\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                      AI-powered SWOT analysis tailored for Indian educational boards\n                    </Typography>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-around' }}>\n                      <Box sx={{ textAlign: 'center' }}>\n                        <Typography variant=\"h4\" color=\"primary\" sx={{ fontWeight: 700 }}>\n                          1000+\n                        </Typography>\n                        <Typography variant=\"caption\">Students</Typography>\n                      </Box>\n                      <Box sx={{ textAlign: 'center' }}>\n                        <Typography variant=\"h4\" color=\"secondary\" sx={{ fontWeight: 700 }}>\n                          50+\n                        </Typography>\n                        <Typography variant=\"caption\">Schools</Typography>\n                      </Box>\n                      <Box sx={{ textAlign: 'center' }}>\n                        <Typography variant=\"h4\" color=\"success.main\" sx={{ fontWeight: 700 }}>\n                          95%\n                        </Typography>\n                        <Typography variant=\"caption\">Satisfaction</Typography>\n                      </Box>\n                    </Box>\n                  </Box>\n                </Paper>\n              </Box>\n            </Slide>\n          </Grid>\n        </Grid>\n      </Container>\n    </Box>\n  );\n};\n\n// Features Section Component\nconst FeaturesSection = () => {\n  const theme = useTheme();\n  const [featuresRef, isFeaturesVisible] = useIntersectionObserver({ threshold: 0.1 });\n\n  const features = [\n    {\n      icon: AssessmentIcon,\n      title: 'AI-Powered SWOT Analysis',\n      description: 'Comprehensive analysis of student strengths, weaknesses, opportunities, and threats using advanced AI algorithms.',\n      color: theme.palette.primary.main,\n    },\n    {\n      icon: SchoolIcon,\n      title: 'Multi-Board Support',\n      description: 'Full support for CBSE, ICSE, and all major State boards with curriculum-specific insights.',\n      color: theme.palette.secondary.main,\n    },\n    {\n      icon: TrendingUpIcon,\n      title: 'Performance Tracking',\n      description: 'Real-time monitoring of student progress with detailed analytics and trend analysis.',\n      color: theme.palette.success.main,\n    },\n    {\n      icon: PeopleIcon,\n      title: 'Multi-Stakeholder Access',\n      description: 'Dedicated interfaces for teachers, parents, administrators, and students with role-based permissions.',\n      color: theme.palette.warning.main,\n    },\n  ];\n\n  return (\n    <Box ref={featuresRef} sx={{ py: 8, bgcolor: 'background.default' }}>\n      <Container maxWidth=\"lg\">\n        <Fade in={isFeaturesVisible} timeout={800}>\n          <Box sx={{ textAlign: 'center', mb: 6 }}>\n            <Typography variant=\"h3\" sx={{ fontWeight: 700, mb: 2, color: 'text.primary' }}>\n              Powerful Features for Indian Education\n            </Typography>\n            <Typography variant=\"h6\" color=\"text.secondary\" sx={{ maxWidth: 600, mx: 'auto' }}>\n              Designed specifically for the Indian education system with cultural sensitivity and local requirements in mind.\n            </Typography>\n          </Box>\n        </Fade>\n        <Grid container spacing={4}>\n          {features.map((feature, index) => (\n            <Grid item xs={12} sm={6} md={3} key={index}>\n              <Zoom in={isFeaturesVisible} timeout={800 + index * 200}>\n                <Card\n                  sx={{\n                    height: '100%',\n                    textAlign: 'center',\n                    p: 3,\n                    transition: 'all 0.3s ease-in-out',\n                    '&:hover': {\n                      transform: 'translateY(-8px)',\n                      boxShadow: theme.shadows[8],\n                    },\n                  }}\n                >\n                  <Avatar\n                    sx={{\n                      width: 64,\n                      height: 64,\n                      bgcolor: feature.color,\n                      mx: 'auto',\n                      mb: 2,\n                    }}\n                  >\n                    <feature.icon sx={{ fontSize: 32 }} />\n                  </Avatar>\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2 }}>\n                    {feature.title}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {feature.description}\n                  </Typography>\n                </Card>\n              </Zoom>\n            </Grid>\n          ))}\n        </Grid>\n      </Container>\n    </Box>\n  );\n};\n\n// Vision & Mission Section Component\nconst VisionMissionSection = () => {\n  const [visionRef, isVisionVisible] = useIntersectionObserver({ threshold: 0.1 });\n\n  return (\n    <Box ref={visionRef} sx={{ py: 8, bgcolor: 'primary.main', color: 'white' }}>\n      <Container maxWidth=\"lg\">\n        <Grid container spacing={6} alignItems=\"center\">\n          <Grid item xs={12} md={6}>\n            <Fade in={isVisionVisible} timeout={800}>\n              <Box>\n                <Typography variant=\"h3\" sx={{ fontWeight: 700, mb: 3 }}>\n                  Our Vision\n                </Typography>\n                <Typography variant=\"h6\" sx={{ mb: 4, lineHeight: 1.6, opacity: 0.9 }}>\n                  To revolutionize Indian education by providing intelligent, data-driven insights\n                  that help every student reach their full potential while respecting cultural values\n                  and educational traditions.\n                </Typography>\n                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n                  <Chip\n                    label=\"AI-Powered\"\n                    sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}\n                  />\n                  <Chip\n                    label=\"Culturally Sensitive\"\n                    sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}\n                  />\n                  <Chip\n                    label=\"Student-Centric\"\n                    sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}\n                  />\n                </Box>\n              </Box>\n            </Fade>\n          </Grid>\n          <Grid item xs={12} md={6}>\n            <Slide direction=\"left\" in={isVisionVisible} timeout={1000}>\n              <Box>\n                <Typography variant=\"h3\" sx={{ fontWeight: 700, mb: 3 }}>\n                  Our Mission\n                </Typography>\n                <Typography variant=\"h6\" sx={{ mb: 4, lineHeight: 1.6, opacity: 0.9 }}>\n                  Empowering teachers, parents, and administrators with comprehensive SWOT analysis\n                  tools that provide actionable insights for student development across all Indian\n                  educational boards.\n                </Typography>\n                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n                  <Chip\n                    label=\"Multi-Board Support\"\n                    sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}\n                  />\n                  <Chip\n                    label=\"Real-time Analytics\"\n                    sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}\n                  />\n                  <Chip\n                    label=\"Collaborative Platform\"\n                    sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}\n                  />\n                </Box>\n              </Box>\n            </Slide>\n          </Grid>\n        </Grid>\n      </Container>\n    </Box>\n  );\n};\n\n// Use Cases Section Component\nconst UseCasesSection = () => {\n  const theme = useTheme();\n  const [useCasesRef, isUseCasesVisible] = useIntersectionObserver({ threshold: 0.1 });\n\n  const useCases = [\n    {\n      title: 'For Teachers',\n      description: 'Generate comprehensive SWOT reports, track student progress, and identify areas for improvement with AI-powered insights.',\n      features: ['Automated SWOT Generation', 'Progress Tracking', 'Parent Communication', 'Class Analytics'],\n      color: theme.palette.primary.main,\n      icon: '👩‍🏫',\n    },\n    {\n      title: 'For Parents',\n      description: 'Stay informed about your child\\'s academic journey with detailed reports and actionable recommendations.',\n      features: ['Student Reports', 'Performance Trends', 'Improvement Suggestions', 'Meeting Scheduling'],\n      color: theme.palette.secondary.main,\n      icon: '👨‍👩‍👧‍👦',\n    },\n    {\n      title: 'For Administrators',\n      description: 'Manage school-wide analytics, monitor teacher effectiveness, and make data-driven decisions.',\n      features: ['School Analytics', 'Teacher Performance', 'Resource Planning', 'Compliance Reports'],\n      color: theme.palette.success.main,\n      icon: '🏫',\n    },\n  ];\n\n  return (\n    <Box ref={useCasesRef} sx={{ py: 8, bgcolor: 'background.paper' }}>\n      <Container maxWidth=\"lg\">\n        <Fade in={isUseCasesVisible} timeout={800}>\n          <Box sx={{ textAlign: 'center', mb: 6 }}>\n            <Typography variant=\"h3\" sx={{ fontWeight: 700, mb: 2, color: 'text.primary' }}>\n              Designed for Every Stakeholder\n            </Typography>\n            <Typography variant=\"h6\" color=\"text.secondary\" sx={{ maxWidth: 600, mx: 'auto' }}>\n              VidyaMitra serves the entire educational ecosystem with tailored solutions for each user type.\n            </Typography>\n          </Box>\n        </Fade>\n        <Grid container spacing={4}>\n          {useCases.map((useCase, index) => (\n            <Grid item xs={12} md={4} key={index}>\n              <Zoom in={isUseCasesVisible} timeout={800 + index * 200}>\n                <Card\n                  sx={{\n                    height: '100%',\n                    p: 4,\n                    transition: 'all 0.3s ease-in-out',\n                    border: `2px solid transparent`,\n                    '&:hover': {\n                      transform: 'translateY(-8px)',\n                      boxShadow: theme.shadows[12],\n                      borderColor: useCase.color,\n                    },\n                  }}\n                >\n                  <Box sx={{ textAlign: 'center', mb: 3 }}>\n                    <Typography variant=\"h2\" sx={{ mb: 1 }}>\n                      {useCase.icon}\n                    </Typography>\n                    <Typography variant=\"h5\" sx={{ fontWeight: 600, color: useCase.color }}>\n                      {useCase.title}\n                    </Typography>\n                  </Box>\n                  <Typography variant=\"body1\" sx={{ mb: 3, lineHeight: 1.6 }}>\n                    {useCase.description}\n                  </Typography>\n                  <Box>\n                    {useCase.features.map((feature, featureIndex) => (\n                      <Box key={featureIndex} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                        <StarIcon sx={{ fontSize: 16, color: useCase.color, mr: 1 }} />\n                        <Typography variant=\"body2\">{feature}</Typography>\n                      </Box>\n                    ))}\n                  </Box>\n                </Card>\n              </Zoom>\n            </Grid>\n          ))}\n        </Grid>\n      </Container>\n    </Box>\n  );\n};\n\n// Footer Component\nconst Footer = () => {\n  const navigate = useNavigate();\n\n  return (\n    <Box sx={{ bgcolor: 'grey.900', color: 'white', py: 6 }}>\n      <Container maxWidth=\"lg\">\n        <Grid container spacing={4}>\n          <Grid item xs={12} md={4}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, mb: 2 }}>\n              VidyaMitra\n            </Typography>\n            <Typography variant=\"body2\" sx={{ mb: 2, opacity: 0.8 }}>\n              Empowering Indian education through intelligent student analysis.\n              Supporting CBSE, ICSE, and State boards with AI-powered insights.\n            </Typography>\n            <Box sx={{ display: 'flex', gap: 1 }}>\n              <Chip\n                label=\"AI-Powered\"\n                size=\"small\"\n                sx={{ bgcolor: 'rgba(255,255,255,0.1)', color: 'white' }}\n              />\n              <Chip\n                label=\"Multi-Board\"\n                size=\"small\"\n                sx={{ bgcolor: 'rgba(255,255,255,0.1)', color: 'white' }}\n              />\n            </Box>\n          </Grid>\n          <Grid item xs={12} sm={6} md={2}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2 }}>\n              Platform\n            </Typography>\n            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n              <Button\n                color=\"inherit\"\n                sx={{ justifyContent: 'flex-start', p: 0 }}\n                onClick={() => navigate('/features')}\n              >\n                Features\n              </Button>\n              <Button\n                color=\"inherit\"\n                sx={{ justifyContent: 'flex-start', p: 0 }}\n                onClick={() => navigate('/about')}\n              >\n                About\n              </Button>\n              <Button\n                color=\"inherit\"\n                sx={{ justifyContent: 'flex-start', p: 0 }}\n                onClick={() => navigate('/login')}\n              >\n                Login\n              </Button>\n            </Box>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2 }}>\n              Support\n            </Typography>\n            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n              <Button\n                color=\"inherit\"\n                sx={{ justifyContent: 'flex-start', p: 0 }}\n                onClick={() => navigate('/contact')}\n              >\n                Contact Us\n              </Button>\n              <Button\n                color=\"inherit\"\n                sx={{ justifyContent: 'flex-start', p: 0 }}\n              >\n                Help Center\n              </Button>\n              <Button\n                color=\"inherit\"\n                sx={{ justifyContent: 'flex-start', p: 0 }}\n              >\n                Documentation\n              </Button>\n            </Box>\n          </Grid>\n          <Grid item xs={12} md={3}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2 }}>\n              Contact Info\n            </Typography>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>\n              <EmailIcon sx={{ fontSize: 16 }} />\n              <Typography variant=\"body2\"><EMAIL></Typography>\n            </Box>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>\n              <PhoneIcon sx={{ fontSize: 16 }} />\n              <Typography variant=\"body2\">+91 98765 43210</Typography>\n            </Box>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n              <LocationOnIcon sx={{ fontSize: 16 }} />\n              <Typography variant=\"body2\">Mumbai, India</Typography>\n            </Box>\n          </Grid>\n        </Grid>\n        <Box\n          sx={{\n            borderTop: '1px solid rgba(255,255,255,0.1)',\n            mt: 4,\n            pt: 3,\n            textAlign: 'center',\n          }}\n        >\n          <Typography variant=\"body2\" sx={{ opacity: 0.7 }}>\n            © 2024 VidyaMitra. All rights reserved. | Privacy Policy | Terms of Service\n          </Typography>\n        </Box>\n      </Container>\n    </Box>\n  );\n};\n\nexport default function LandingPage() {\n  return (\n    <Box>\n      <NavigationHeader />\n      <Box sx={{ pt: 8 }}> {/* Add padding top to account for fixed header */}\n        <HeroSection />\n        <FeaturesSection />\n        <VisionMissionSection />\n        <UseCasesSection />\n      </Box>\n      <Footer />\n    </Box>\n  );\n}\n", "import React from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  Avatar,\n  Chip,\n  Button,\n  Paper,\n  Fade,\n  useTheme,\n} from '@mui/material';\nimport {\n  School as SchoolIcon,\n  Psychology as PsychologyIcon,\n  Groups as GroupsIcon,\n  TrendingUp as TrendingUpIcon,\n  ArrowBack as ArrowBackIcon,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\n\nconst AboutPage = () => {\n  const { t } = useTranslation('common');\n  const navigate = useNavigate();\n  const theme = useTheme();\n\n  const values = [\n    {\n      icon: SchoolIcon,\n      title: 'Educational Excellence',\n      description: 'Committed to enhancing the quality of education through data-driven insights and personalized learning approaches.',\n      color: theme.palette.primary.main,\n    },\n    {\n      icon: PsychologyIcon,\n      title: 'AI-Powered Innovation',\n      description: 'Leveraging cutting-edge artificial intelligence to provide meaningful and actionable student analysis.',\n      color: theme.palette.secondary.main,\n    },\n    {\n      icon: GroupsIcon,\n      title: 'Collaborative Approach',\n      description: 'Bringing together teachers, parents, and administrators in a unified platform for student success.',\n      color: theme.palette.success.main,\n    },\n    {\n      icon: TrendingUpIcon,\n      title: 'Continuous Improvement',\n      description: 'Constantly evolving our platform based on user feedback and educational best practices.',\n      color: theme.palette.warning.main,\n    },\n  ];\n\n  const stats = [\n    { number: '1000+', label: 'Students Analyzed' },\n    { number: '50+', label: 'Partner Schools' },\n    { number: '95%', label: 'User Satisfaction' },\n    { number: '3', label: 'Educational Boards' },\n  ];\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box sx={{ bgcolor: 'primary.main', color: 'white', py: 8 }}>\n        <Container maxWidth=\"lg\">\n          <Button\n            startIcon={<ArrowBackIcon />}\n            onClick={() => navigate('/')}\n            sx={{ color: 'white', mb: 4 }}\n          >\n            Back to Home\n          </Button>\n          <Typography variant=\"h2\" sx={{ fontWeight: 700, mb: 3 }}>\n            About VidyaMitra\n          </Typography>\n          <Typography variant=\"h5\" sx={{ opacity: 0.9, maxWidth: 800 }}>\n            Revolutionizing Indian education through intelligent student analysis and \n            comprehensive SWOT insights tailored for CBSE, ICSE, and State boards.\n          </Typography>\n        </Container>\n      </Box>\n\n      {/* Mission & Vision */}\n      <Box sx={{ py: 8, bgcolor: 'background.default' }}>\n        <Container maxWidth=\"lg\">\n          <Grid container spacing={6}>\n            <Grid item xs={12} md={6}>\n              <Fade in timeout={800}>\n                <Paper elevation={2} sx={{ p: 4, height: '100%' }}>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600, mb: 3, color: 'primary.main' }}>\n                    Our Mission\n                  </Typography>\n                  <Typography variant=\"body1\" sx={{ lineHeight: 1.8, mb: 3 }}>\n                    To empower educators, parents, and administrators with comprehensive SWOT analysis \n                    tools that provide actionable insights for student development across all Indian \n                    educational boards.\n                  </Typography>\n                  <Typography variant=\"body1\" sx={{ lineHeight: 1.8 }}>\n                    We believe every student has unique strengths and potential. Our platform helps \n                    identify these strengths while addressing weaknesses through personalized \n                    recommendations and data-driven strategies.\n                  </Typography>\n                </Paper>\n              </Fade>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Fade in timeout={1000}>\n                <Paper elevation={2} sx={{ p: 4, height: '100%' }}>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 600, mb: 3, color: 'secondary.main' }}>\n                    Our Vision\n                  </Typography>\n                  <Typography variant=\"body1\" sx={{ lineHeight: 1.8, mb: 3 }}>\n                    To become the leading platform for student analysis in India, helping create \n                    a generation of well-rounded individuals who are prepared for the challenges \n                    of the 21st century.\n                  </Typography>\n                  <Typography variant=\"body1\" sx={{ lineHeight: 1.8 }}>\n                    We envision a future where every student receives personalized attention and \n                    guidance, leading to improved academic outcomes and holistic development.\n                  </Typography>\n                </Paper>\n              </Fade>\n            </Grid>\n          </Grid>\n        </Container>\n      </Box>\n\n      {/* Values */}\n      <Box sx={{ py: 8, bgcolor: 'background.paper' }}>\n        <Container maxWidth=\"lg\">\n          <Typography variant=\"h3\" sx={{ textAlign: 'center', fontWeight: 700, mb: 6 }}>\n            Our Core Values\n          </Typography>\n          <Grid container spacing={4}>\n            {values.map((value, index) => (\n              <Grid item xs={12} sm={6} md={3} key={index}>\n                <Fade in timeout={800 + index * 200}>\n                  <Card\n                    sx={{\n                      height: '100%',\n                      textAlign: 'center',\n                      p: 3,\n                      transition: 'all 0.3s ease-in-out',\n                      '&:hover': {\n                        transform: 'translateY(-8px)',\n                        boxShadow: theme.shadows[8],\n                      },\n                    }}\n                  >\n                    <Avatar\n                      sx={{\n                        width: 64,\n                        height: 64,\n                        bgcolor: value.color,\n                        mx: 'auto',\n                        mb: 2,\n                      }}\n                    >\n                      <value.icon sx={{ fontSize: 32 }} />\n                    </Avatar>\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2 }}>\n                      {value.title}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {value.description}\n                    </Typography>\n                  </Card>\n                </Fade>\n              </Grid>\n            ))}\n          </Grid>\n        </Container>\n      </Box>\n\n      {/* Statistics */}\n      <Box sx={{ py: 8, bgcolor: 'primary.main', color: 'white' }}>\n        <Container maxWidth=\"lg\">\n          <Typography variant=\"h3\" sx={{ textAlign: 'center', fontWeight: 700, mb: 6 }}>\n            Our Impact\n          </Typography>\n          <Grid container spacing={4}>\n            {stats.map((stat, index) => (\n              <Grid item xs={6} md={3} key={index}>\n                <Fade in timeout={800 + index * 200}>\n                  <Box sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"h2\" sx={{ fontWeight: 800, mb: 1 }}>\n                      {stat.number}\n                    </Typography>\n                    <Typography variant=\"h6\" sx={{ opacity: 0.9 }}>\n                      {stat.label}\n                    </Typography>\n                  </Box>\n                </Fade>\n              </Grid>\n            ))}\n          </Grid>\n        </Container>\n      </Box>\n\n      {/* Call to Action */}\n      <Box sx={{ py: 8, bgcolor: 'background.default', textAlign: 'center' }}>\n        <Container maxWidth=\"md\">\n          <Typography variant=\"h3\" sx={{ fontWeight: 700, mb: 3 }}>\n            Ready to Transform Education?\n          </Typography>\n          <Typography variant=\"h6\" color=\"text.secondary\" sx={{ mb: 4 }}>\n            Join thousands of educators who are already using VidyaMitra to enhance student outcomes.\n          </Typography>\n          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>\n            <Button\n              variant=\"contained\"\n              size=\"large\"\n              onClick={() => navigate('/login')}\n              sx={{ px: 4, py: 1.5 }}\n            >\n              Get Started Today\n            </Button>\n            <Button\n              variant=\"outlined\"\n              size=\"large\"\n              onClick={() => navigate('/contact')}\n              sx={{ px: 4, py: 1.5 }}\n            >\n              Contact Us\n            </Button>\n          </Box>\n        </Container>\n      </Box>\n    </Box>\n  );\n};\n\nexport default AboutPage;\n", "import React from 'react';\nimport {\n  Box,\n  Container,\n  Typo<PERSON>,\n  Grid,\n  Card,\n  CardContent,\n  Avatar,\n  Chip,\n  Button,\n  Paper,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Fade,\n  useTheme,\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n  School as SchoolIcon,\n  TrendingUp as TrendingUpIcon,\n  People as PeopleIcon,\n  Language as LanguageIcon,\n  Security as SecurityIcon,\n  CloudSync as CloudSyncIcon,\n  Analytics as AnalyticsIcon,\n  ArrowBack as ArrowBackIcon,\n  CheckCircle as CheckCircleIcon,\n  Psychology as PsychologyIcon,\n  Dashboard as DashboardIcon,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\n\nconst FeaturesPage = () => {\n  const { t } = useTranslation('common');\n  const navigate = useNavigate();\n  const theme = useTheme();\n\n  const coreFeatures = [\n    {\n      icon: AssessmentIcon,\n      title: 'AI-Powered SWOT Analysis',\n      description: 'Advanced artificial intelligence algorithms analyze student data to generate comprehensive SWOT reports.',\n      features: [\n        'Automated strength identification',\n        'Weakness pattern recognition',\n        'Opportunity mapping',\n        'Threat assessment',\n        'Personalized recommendations'\n      ],\n      color: theme.palette.primary.main,\n    },\n    {\n      icon: SchoolIcon,\n      title: 'Multi-Board Support',\n      description: 'Complete support for CBSE, ICSE, and all major State educational boards across India.',\n      features: [\n        'CBSE curriculum alignment',\n        'ICSE standards compliance',\n        'State board customization',\n        'Regional language support',\n        'Board-specific analytics'\n      ],\n      color: theme.palette.secondary.main,\n    },\n    {\n      icon: TrendingUpIcon,\n      title: 'Performance Tracking',\n      description: 'Real-time monitoring and analysis of student academic and behavioral performance.',\n      features: [\n        'Grade tracking',\n        'Attendance monitoring',\n        'Behavioral analysis',\n        'Progress visualization',\n        'Trend identification'\n      ],\n      color: theme.palette.success.main,\n    },\n    {\n      icon: PeopleIcon,\n      title: 'Multi-Stakeholder Platform',\n      description: 'Dedicated interfaces for teachers, parents, administrators, and students.',\n      features: [\n        'Teacher dashboard',\n        'Parent portal',\n        'Admin analytics',\n        'Student interface',\n        'Role-based permissions'\n      ],\n      color: theme.palette.warning.main,\n    },\n  ];\n\n  const additionalFeatures = [\n    {\n      icon: LanguageIcon,\n      title: 'Multi-Language Support',\n      description: 'Interface available in English, Hindi, and major regional languages.',\n    },\n    {\n      icon: SecurityIcon,\n      title: 'Data Security',\n      description: 'Enterprise-grade security with FERPA compliance and data encryption.',\n    },\n    {\n      icon: CloudSyncIcon,\n      title: 'Cloud Integration',\n      description: 'Seamless cloud synchronization with offline capabilities.',\n    },\n    {\n      icon: AnalyticsIcon,\n      title: 'Advanced Analytics',\n      description: 'Comprehensive reporting with exportable insights and visualizations.',\n    },\n    {\n      icon: PsychologyIcon,\n      title: 'Behavioral Insights',\n      description: 'AI-driven analysis of student behavior patterns and social interactions.',\n    },\n    {\n      icon: DashboardIcon,\n      title: 'Intuitive Dashboards',\n      description: 'User-friendly interfaces designed specifically for Indian educational context.',\n    },\n  ];\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box sx={{ bgcolor: 'primary.main', color: 'white', py: 8 }}>\n        <Container maxWidth=\"lg\">\n          <Button\n            startIcon={<ArrowBackIcon />}\n            onClick={() => navigate('/')}\n            sx={{ color: 'white', mb: 4 }}\n          >\n            Back to Home\n          </Button>\n          <Typography variant=\"h2\" sx={{ fontWeight: 700, mb: 3 }}>\n            Platform Features\n          </Typography>\n          <Typography variant=\"h5\" sx={{ opacity: 0.9, maxWidth: 800 }}>\n            Discover the comprehensive suite of features designed specifically for \n            Indian educational institutions and stakeholders.\n          </Typography>\n        </Container>\n      </Box>\n\n      {/* Core Features */}\n      <Box sx={{ py: 8, bgcolor: 'background.default' }}>\n        <Container maxWidth=\"lg\">\n          <Typography variant=\"h3\" sx={{ textAlign: 'center', fontWeight: 700, mb: 6 }}>\n            Core Features\n          </Typography>\n          <Grid container spacing={4}>\n            {coreFeatures.map((feature, index) => (\n              <Grid item xs={12} md={6} key={index}>\n                <Fade in timeout={800 + index * 200}>\n                  <Card\n                    sx={{\n                      height: '100%',\n                      p: 3,\n                      transition: 'all 0.3s ease-in-out',\n                      '&:hover': {\n                        transform: 'translateY(-4px)',\n                        boxShadow: theme.shadows[8],\n                      },\n                    }}\n                  >\n                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 3 }}>\n                      <Avatar\n                        sx={{\n                          width: 56,\n                          height: 56,\n                          bgcolor: feature.color,\n                          mr: 2,\n                        }}\n                      >\n                        <feature.icon sx={{ fontSize: 28 }} />\n                      </Avatar>\n                      <Box>\n                        <Typography variant=\"h5\" sx={{ fontWeight: 600, mb: 1 }}>\n                          {feature.title}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          {feature.description}\n                        </Typography>\n                      </Box>\n                    </Box>\n                    <List dense>\n                      {feature.features.map((item, itemIndex) => (\n                        <ListItem key={itemIndex} sx={{ px: 0 }}>\n                          <ListItemIcon sx={{ minWidth: 32 }}>\n                            <CheckCircleIcon sx={{ fontSize: 20, color: feature.color }} />\n                          </ListItemIcon>\n                          <ListItemText \n                            primary={item}\n                            primaryTypographyProps={{ variant: 'body2' }}\n                          />\n                        </ListItem>\n                      ))}\n                    </List>\n                  </Card>\n                </Fade>\n              </Grid>\n            ))}\n          </Grid>\n        </Container>\n      </Box>\n\n      {/* Additional Features */}\n      <Box sx={{ py: 8, bgcolor: 'background.paper' }}>\n        <Container maxWidth=\"lg\">\n          <Typography variant=\"h3\" sx={{ textAlign: 'center', fontWeight: 700, mb: 6 }}>\n            Additional Capabilities\n          </Typography>\n          <Grid container spacing={4}>\n            {additionalFeatures.map((feature, index) => (\n              <Grid item xs={12} sm={6} md={4} key={index}>\n                <Fade in timeout={800 + index * 150}>\n                  <Card\n                    sx={{\n                      height: '100%',\n                      textAlign: 'center',\n                      p: 3,\n                      transition: 'all 0.3s ease-in-out',\n                      '&:hover': {\n                        transform: 'translateY(-4px)',\n                        boxShadow: theme.shadows[6],\n                      },\n                    }}\n                  >\n                    <Avatar\n                      sx={{\n                        width: 64,\n                        height: 64,\n                        bgcolor: 'primary.main',\n                        mx: 'auto',\n                        mb: 2,\n                      }}\n                    >\n                      <feature.icon sx={{ fontSize: 32 }} />\n                    </Avatar>\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2 }}>\n                      {feature.title}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {feature.description}\n                    </Typography>\n                  </Card>\n                </Fade>\n              </Grid>\n            ))}\n          </Grid>\n        </Container>\n      </Box>\n\n      {/* Indian Education Focus */}\n      <Box sx={{ py: 8, bgcolor: 'secondary.main', color: 'white' }}>\n        <Container maxWidth=\"lg\">\n          <Grid container spacing={6} alignItems=\"center\">\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"h3\" sx={{ fontWeight: 700, mb: 3 }}>\n                Built for Indian Education\n              </Typography>\n              <Typography variant=\"h6\" sx={{ mb: 4, opacity: 0.9 }}>\n                Every feature is designed with the Indian educational context in mind, \n                ensuring cultural sensitivity and local relevance.\n              </Typography>\n              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n                <Chip\n                  label=\"CBSE Aligned\"\n                  sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}\n                />\n                <Chip\n                  label=\"ICSE Compatible\"\n                  sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}\n                />\n                <Chip\n                  label=\"State Board Ready\"\n                  sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}\n                />\n                <Chip\n                  label=\"Multi-Language\"\n                  sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}\n                />\n              </Box>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Paper elevation={4} sx={{ p: 4 }}>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2 }}>\n                  Supported Educational Boards\n                </Typography>\n                <List>\n                  <ListItem>\n                    <ListItemIcon>\n                      <CheckCircleIcon color=\"primary\" />\n                    </ListItemIcon>\n                    <ListItemText primary=\"Central Board of Secondary Education (CBSE)\" />\n                  </ListItem>\n                  <ListItem>\n                    <ListItemIcon>\n                      <CheckCircleIcon color=\"primary\" />\n                    </ListItemIcon>\n                    <ListItemText primary=\"Indian Certificate of Secondary Education (ICSE)\" />\n                  </ListItem>\n                  <ListItem>\n                    <ListItemIcon>\n                      <CheckCircleIcon color=\"primary\" />\n                    </ListItemIcon>\n                    <ListItemText primary=\"State Education Boards (All Major States)\" />\n                  </ListItem>\n                  <ListItem>\n                    <ListItemIcon>\n                      <CheckCircleIcon color=\"primary\" />\n                    </ListItemIcon>\n                    <ListItemText primary=\"International Baccalaureate (IB)\" />\n                  </ListItem>\n                </List>\n              </Paper>\n            </Grid>\n          </Grid>\n        </Container>\n      </Box>\n\n      {/* Call to Action */}\n      <Box sx={{ py: 8, bgcolor: 'background.default', textAlign: 'center' }}>\n        <Container maxWidth=\"md\">\n          <Typography variant=\"h3\" sx={{ fontWeight: 700, mb: 3 }}>\n            Experience the Power of VidyaMitra\n          </Typography>\n          <Typography variant=\"h6\" color=\"text.secondary\" sx={{ mb: 4 }}>\n            Start your journey towards data-driven education today.\n          </Typography>\n          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>\n            <Button\n              variant=\"contained\"\n              size=\"large\"\n              onClick={() => navigate('/login')}\n              sx={{ px: 4, py: 1.5 }}\n            >\n              Try VidyaMitra\n            </Button>\n            <Button\n              variant=\"outlined\"\n              size=\"large\"\n              onClick={() => navigate('/contact')}\n              sx={{ px: 4, py: 1.5 }}\n            >\n              Request Demo\n            </Button>\n          </Box>\n        </Container>\n      </Box>\n    </Box>\n  );\n};\n\nexport default FeaturesPage;\n", "import React, { useState } from 'react';\nimport {\n  Box,\n  Container,\n  Typo<PERSON>,\n  Grid,\n  Card,\n  CardContent,\n  TextField,\n  Button,\n  Paper,\n  Avatar,\n  Fade,\n  useTheme,\n  Alert,\n  Snackbar,\n} from '@mui/material';\nimport {\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  LocationOn as LocationOnIcon,\n  Schedule as ScheduleIcon,\n  ArrowBack as ArrowBackIcon,\n  Send as SendIcon,\n  Support as SupportIcon,\n  Business as BusinessIcon,\n  School as SchoolIcon,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\n\nconst ContactPage = () => {\n  const { t } = useTranslation('common');\n  const navigate = useNavigate();\n  const theme = useTheme();\n  \n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    organization: '',\n    subject: '',\n    message: '',\n  });\n  const [showSuccess, setShowSuccess] = useState(false);\n\n  const handleInputChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value,\n    });\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    // Simulate form submission\n    console.log('Form submitted:', formData);\n    setShowSuccess(true);\n    setFormData({\n      name: '',\n      email: '',\n      organization: '',\n      subject: '',\n      message: '',\n    });\n  };\n\n  const contactInfo = [\n    {\n      icon: EmailIcon,\n      title: 'Email Us',\n      primary: '<EMAIL>',\n      secondary: '<EMAIL>',\n      color: theme.palette.primary.main,\n    },\n    {\n      icon: PhoneIcon,\n      title: 'Call Us',\n      primary: '+91 98765 43210',\n      secondary: '+91 98765 43211',\n      color: theme.palette.secondary.main,\n    },\n    {\n      icon: LocationOnIcon,\n      title: 'Visit Us',\n      primary: 'Mumbai, Maharashtra',\n      secondary: 'India - 400001',\n      color: theme.palette.success.main,\n    },\n    {\n      icon: ScheduleIcon,\n      title: 'Business Hours',\n      primary: 'Mon - Fri: 9:00 AM - 6:00 PM',\n      secondary: 'Sat: 10:00 AM - 4:00 PM',\n      color: theme.palette.warning.main,\n    },\n  ];\n\n  const supportOptions = [\n    {\n      icon: SupportIcon,\n      title: 'Technical Support',\n      description: 'Get help with platform usage, troubleshooting, and technical issues.',\n      action: 'Get Support',\n    },\n    {\n      icon: BusinessIcon,\n      title: 'Sales Inquiry',\n      description: 'Learn about pricing, features, and how VidyaMitra can benefit your institution.',\n      action: 'Contact Sales',\n    },\n    {\n      icon: SchoolIcon,\n      title: 'Educational Partnership',\n      description: 'Explore partnership opportunities and institutional collaborations.',\n      action: 'Partner With Us',\n    },\n  ];\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box sx={{ bgcolor: 'primary.main', color: 'white', py: 8 }}>\n        <Container maxWidth=\"lg\">\n          <Button\n            startIcon={<ArrowBackIcon />}\n            onClick={() => navigate('/')}\n            sx={{ color: 'white', mb: 4 }}\n          >\n            Back to Home\n          </Button>\n          <Typography variant=\"h2\" sx={{ fontWeight: 700, mb: 3 }}>\n            Contact Us\n          </Typography>\n          <Typography variant=\"h5\" sx={{ opacity: 0.9, maxWidth: 800 }}>\n            We're here to help you transform education. Get in touch with our team \n            for support, sales inquiries, or partnership opportunities.\n          </Typography>\n        </Container>\n      </Box>\n\n      {/* Contact Information */}\n      <Box sx={{ py: 8, bgcolor: 'background.default' }}>\n        <Container maxWidth=\"lg\">\n          <Typography variant=\"h3\" sx={{ textAlign: 'center', fontWeight: 700, mb: 6 }}>\n            Get In Touch\n          </Typography>\n          <Grid container spacing={4}>\n            {contactInfo.map((info, index) => (\n              <Grid item xs={12} sm={6} md={3} key={index}>\n                <Fade in timeout={800 + index * 200}>\n                  <Card\n                    sx={{\n                      height: '100%',\n                      textAlign: 'center',\n                      p: 3,\n                      transition: 'all 0.3s ease-in-out',\n                      '&:hover': {\n                        transform: 'translateY(-4px)',\n                        boxShadow: theme.shadows[8],\n                      },\n                    }}\n                  >\n                    <Avatar\n                      sx={{\n                        width: 64,\n                        height: 64,\n                        bgcolor: info.color,\n                        mx: 'auto',\n                        mb: 2,\n                      }}\n                    >\n                      <info.icon sx={{ fontSize: 32 }} />\n                    </Avatar>\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2 }}>\n                      {info.title}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                      {info.primary}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {info.secondary}\n                    </Typography>\n                  </Card>\n                </Fade>\n              </Grid>\n            ))}\n          </Grid>\n        </Container>\n      </Box>\n\n      {/* Contact Form and Support Options */}\n      <Box sx={{ py: 8, bgcolor: 'background.paper' }}>\n        <Container maxWidth=\"lg\">\n          <Grid container spacing={6}>\n            {/* Contact Form */}\n            <Grid item xs={12} md={8}>\n              <Paper elevation={2} sx={{ p: 4 }}>\n                <Typography variant=\"h4\" sx={{ fontWeight: 600, mb: 3 }}>\n                  Send us a Message\n                </Typography>\n                <Box component=\"form\" onSubmit={handleSubmit}>\n                  <Grid container spacing={3}>\n                    <Grid item xs={12} sm={6}>\n                      <TextField\n                        fullWidth\n                        label=\"Full Name\"\n                        name=\"name\"\n                        value={formData.name}\n                        onChange={handleInputChange}\n                        required\n                      />\n                    </Grid>\n                    <Grid item xs={12} sm={6}>\n                      <TextField\n                        fullWidth\n                        label=\"Email Address\"\n                        name=\"email\"\n                        type=\"email\"\n                        value={formData.email}\n                        onChange={handleInputChange}\n                        required\n                      />\n                    </Grid>\n                    <Grid item xs={12}>\n                      <TextField\n                        fullWidth\n                        label=\"Organization/School\"\n                        name=\"organization\"\n                        value={formData.organization}\n                        onChange={handleInputChange}\n                      />\n                    </Grid>\n                    <Grid item xs={12}>\n                      <TextField\n                        fullWidth\n                        label=\"Subject\"\n                        name=\"subject\"\n                        value={formData.subject}\n                        onChange={handleInputChange}\n                        required\n                      />\n                    </Grid>\n                    <Grid item xs={12}>\n                      <TextField\n                        fullWidth\n                        label=\"Message\"\n                        name=\"message\"\n                        multiline\n                        rows={4}\n                        value={formData.message}\n                        onChange={handleInputChange}\n                        required\n                      />\n                    </Grid>\n                    <Grid item xs={12}>\n                      <Button\n                        type=\"submit\"\n                        variant=\"contained\"\n                        size=\"large\"\n                        endIcon={<SendIcon />}\n                        sx={{ px: 4, py: 1.5 }}\n                      >\n                        Send Message\n                      </Button>\n                    </Grid>\n                  </Grid>\n                </Box>\n              </Paper>\n            </Grid>\n\n            {/* Support Options */}\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"h5\" sx={{ fontWeight: 600, mb: 3 }}>\n                How Can We Help?\n              </Typography>\n              {supportOptions.map((option, index) => (\n                <Fade in timeout={800 + index * 200} key={index}>\n                  <Card sx={{ mb: 3, p: 3 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>\n                      <Avatar\n                        sx={{\n                          width: 48,\n                          height: 48,\n                          bgcolor: 'primary.main',\n                          mr: 2,\n                        }}\n                      >\n                        <option.icon sx={{ fontSize: 24 }} />\n                      </Avatar>\n                      <Box>\n                        <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 1 }}>\n                          {option.title}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                          {option.description}\n                        </Typography>\n                        <Button\n                          variant=\"outlined\"\n                          size=\"small\"\n                          sx={{ textTransform: 'none' }}\n                        >\n                          {option.action}\n                        </Button>\n                      </Box>\n                    </Box>\n                  </Card>\n                </Fade>\n              ))}\n            </Grid>\n          </Grid>\n        </Container>\n      </Box>\n\n      {/* FAQ Section */}\n      <Box sx={{ py: 8, bgcolor: 'background.default' }}>\n        <Container maxWidth=\"md\">\n          <Typography variant=\"h3\" sx={{ textAlign: 'center', fontWeight: 700, mb: 6 }}>\n            Frequently Asked Questions\n          </Typography>\n          <Grid container spacing={3}>\n            <Grid item xs={12}>\n              <Paper elevation={1} sx={{ p: 3 }}>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2 }}>\n                  How does VidyaMitra ensure data privacy?\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  We implement enterprise-grade security measures including data encryption, \n                  FERPA compliance, and strict access controls to protect student information.\n                </Typography>\n              </Paper>\n            </Grid>\n            <Grid item xs={12}>\n              <Paper elevation={1} sx={{ p: 3 }}>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2 }}>\n                  Which educational boards are supported?\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  VidyaMitra supports CBSE, ICSE, and all major State educational boards \n                  across India, with customizable features for each curriculum.\n                </Typography>\n              </Paper>\n            </Grid>\n            <Grid item xs={12}>\n              <Paper elevation={1} sx={{ p: 3 }}>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2 }}>\n                  Is training provided for teachers and staff?\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Yes, we provide comprehensive training sessions, documentation, and \n                  ongoing support to ensure successful platform adoption.\n                </Typography>\n              </Paper>\n            </Grid>\n          </Grid>\n        </Container>\n      </Box>\n\n      {/* Success Snackbar */}\n      <Snackbar\n        open={showSuccess}\n        autoHideDuration={6000}\n        onClose={() => setShowSuccess(false)}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert\n          onClose={() => setShowSuccess(false)}\n          severity=\"success\"\n          sx={{ width: '100%' }}\n        >\n          Thank you for your message! We'll get back to you within 24 hours.\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default ContactPage;\n", "/**\n * VidyaMitra Platform - Modern Footer Component\n * \n * Contemporary footer with glassmorphism design, contact information, and dynamic year\n * Maintains Indian educational context while providing comprehensive platform information\n */\n\nimport React from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Grid,\n  Link,\n  IconButton,\n  Divider,\n  Stack,\n  alpha,\n  useTheme,\n} from '@mui/material';\nimport {\n  School,\n  Email,\n  Phone,\n  LocationOn,\n  Facebook,\n  Twitter,\n  LinkedIn,\n  Instagram,\n  YouTube,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\n\nconst ModernFooter = () => {\n  const theme = useTheme();\n  const currentYear = new Date().getFullYear();\n\n  const footerSections = [\n    {\n      title: 'Platform',\n      links: [\n        { label: 'Dashboard', href: '/dashboard' },\n        { label: 'Student Management', href: '/dashboard/students' },\n        { label: 'SWOT Analysis', href: '/dashboard/swot' },\n        { label: 'Reports', href: '/dashboard/reports' },\n      ],\n    },\n    {\n      title: 'Educational Boards',\n      links: [\n        { label: 'CBSE Integration', href: '/features#cbse' },\n        { label: 'ICSE Support', href: '/features#icse' },\n        { label: 'State Boards', href: '/features#state' },\n        { label: 'International Boards', href: '/features#international' },\n      ],\n    },\n    {\n      title: 'Resources',\n      links: [\n        { label: 'Documentation', href: '/docs' },\n        { label: 'API Reference', href: '/api' },\n        { label: 'Support Center', href: '/support' },\n        { label: 'Training Materials', href: '/training' },\n      ],\n    },\n    {\n      title: 'Company',\n      links: [\n        { label: 'About Us', href: '/about' },\n        { label: 'Privacy Policy', href: '/privacy' },\n        { label: 'Terms of Service', href: '/terms' },\n        { label: 'Contact', href: '/contact' },\n      ],\n    },\n  ];\n\n  const socialLinks = [\n    { icon: Facebook, href: '#', label: 'Facebook' },\n    { icon: Twitter, href: '#', label: 'Twitter' },\n    { icon: LinkedIn, href: '#', label: 'LinkedIn' },\n    { icon: Instagram, href: '#', label: 'Instagram' },\n    { icon: YouTube, href: '#', label: 'YouTube' },\n  ];\n\n  return (\n    <Box\n      component=\"footer\"\n      sx={{\n        background: theme.palette.mode === 'dark'\n          ? `linear-gradient(135deg, ${alpha('#0F172A', 0.95)} 0%, ${alpha('#1E293B', 0.95)} 100%)`\n          : `linear-gradient(135deg, ${alpha('#F8FAFC', 0.95)} 0%, ${alpha('#E2E8F0', 0.95)} 100%)`,\n        backdropFilter: 'blur(20px)',\n        borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,\n        mt: 'auto',\n        py: 6,\n      }}\n    >\n      <Container maxWidth=\"lg\">\n        <Grid container spacing={4}>\n          {/* Brand Section */}\n          <Grid item xs={12} md={4}>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n            >\n              <Box sx={{ mb: 3 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>\n                  <School \n                    sx={{ \n                      fontSize: 32, \n                      color: theme.palette.primary.main \n                    }} \n                  />\n                  <Typography\n                    variant=\"h5\"\n                    sx={{\n                      fontWeight: 600,\n                      color: theme.palette.text.primary,\n                    }}\n                  >\n                    VidyaMitra\n                  </Typography>\n                </Box>\n                <Typography\n                  variant=\"body2\"\n                  sx={{\n                    color: theme.palette.text.secondary,\n                    lineHeight: 1.6,\n                    mb: 3,\n                  }}\n                >\n                  Empowering Indian education through intelligent student analysis.\n                  Supporting CBSE, ICSE, and State boards with AI-powered insights.\n                </Typography>\n\n                {/* Contact Information */}\n                <Stack spacing={1.5}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>\n                    <LocationOn sx={{\n                      fontSize: 18,\n                      color: theme.palette.primary.main,\n                      opacity: 0.8\n                    }} />\n                    <Typography\n                      variant=\"body2\"\n                      sx={{\n                        color: theme.palette.mode === 'dark'\n                          ? theme.palette.text.primary\n                          : theme.palette.text.secondary,\n                        fontWeight: 500,\n                        opacity: theme.palette.mode === 'dark' ? 0.9 : 0.8,\n                      }}\n                    >\n                      Hyderabad, India\n                    </Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>\n                    <Phone sx={{\n                      fontSize: 18,\n                      color: theme.palette.primary.main,\n                      opacity: 0.8\n                    }} />\n                    <Typography\n                      variant=\"body2\"\n                      sx={{\n                        color: theme.palette.mode === 'dark'\n                          ? theme.palette.text.primary\n                          : theme.palette.text.secondary,\n                        fontWeight: 500,\n                        opacity: theme.palette.mode === 'dark' ? 0.9 : 0.8,\n                      }}\n                    >\n                      +91 9392233989\n                    </Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>\n                    <Email sx={{\n                      fontSize: 18,\n                      color: theme.palette.primary.main,\n                      opacity: 0.8\n                    }} />\n                    <Typography\n                      variant=\"body2\"\n                      sx={{\n                        color: theme.palette.mode === 'dark'\n                          ? theme.palette.text.primary\n                          : theme.palette.text.secondary,\n                        fontWeight: 500,\n                        opacity: theme.palette.mode === 'dark' ? 0.9 : 0.8,\n                      }}\n                    >\n                      <EMAIL>\n                    </Typography>\n                  </Box>\n                </Stack>\n              </Box>\n            </motion.div>\n          </Grid>\n\n          {/* Links Sections */}\n          {footerSections.map((section, index) => (\n            <Grid item xs={6} md={2} key={section.title}>\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n              >\n                <Typography\n                  variant=\"h6\"\n                  sx={{\n                    fontWeight: 500,\n                    mb: 2,\n                    color: theme.palette.text.primary,\n                  }}\n                >\n                  {section.title}\n                </Typography>\n                <Stack spacing={1}>\n                  {section.links.map((link) => (\n                    <Link\n                      key={link.label}\n                      href={link.href}\n                      sx={{\n                        color: theme.palette.text.secondary,\n                        textDecoration: 'none',\n                        fontSize: '0.875rem',\n                        transition: 'all 0.3s ease',\n                        '&:hover': {\n                          color: theme.palette.primary.main,\n                          transform: 'translateX(4px)',\n                        },\n                      }}\n                    >\n                      {link.label}\n                    </Link>\n                  ))}\n                </Stack>\n              </motion.div>\n            </Grid>\n          ))}\n        </Grid>\n\n        <Divider sx={{ my: 4, opacity: 0.3 }} />\n\n        {/* Bottom Section */}\n        <Box\n          sx={{\n            display: 'flex',\n            flexDirection: { xs: 'column', md: 'row' },\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            gap: 2,\n          }}\n        >\n          <Typography\n            variant=\"body2\"\n            sx={{\n              color: theme.palette.mode === 'dark'\n                ? theme.palette.text.primary\n                : theme.palette.text.secondary,\n              textAlign: { xs: 'center', md: 'left' },\n              fontWeight: 500,\n              opacity: theme.palette.mode === 'dark' ? 0.9 : 0.8,\n            }}\n          >\n            © {currentYear} VidyaMitra. All rights reserved. Empowering Indian education with AI-driven insights.\n          </Typography>\n\n          {/* Social Links */}\n          <Stack direction=\"row\" spacing={1}>\n            {socialLinks.map((social) => (\n              <IconButton\n                key={social.label}\n                href={social.href}\n                aria-label={social.label}\n                sx={{\n                  color: theme.palette.text.secondary,\n                  border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,\n                  borderRadius: 2,\n                  p: 1,\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    color: theme.palette.primary.main,\n                    borderColor: theme.palette.primary.main,\n                    transform: 'translateY(-2px)',\n                    background: alpha(theme.palette.primary.main, 0.1),\n                  },\n                }}\n              >\n                <social.icon sx={{ fontSize: 18 }} />\n              </IconButton>\n            ))}\n          </Stack>\n        </Box>\n      </Container>\n    </Box>\n  );\n};\n\nexport default ModernFooter;\n", "import React, { useState, createContext, useContext, useEffect, Suspense } from 'react';\nimport { Browser<PERSON>outer, Routes, Route, Link, Outlet, Navigate, useNavigate } from 'react-router-dom';\nimport { CssBaseline, AppBar, Toolbar, Typography, Drawer, List, ListItem, ListItemIcon, ListItemText, Box, CircularProgress, Button, Select, MenuItem, FormControl, InputLabel, Avatar } from '@mui/material';\nimport { ThemeContextProvider } from './contexts/ThemeContext';\nimport { Dashboard as DashboardIcon, People as PeopleIcon, Assessment as AssessmentIcon, Settings as SettingsIcon, ExitToApp as ExitToAppIcon } from '@mui/icons-material';\nimport i18n from 'i18next';\nimport { initReactI18next, useTranslation } from 'react-i18next';\nimport HttpApi from 'i18next-http-backend';\n\n// --- 1. CONTEXTS (Authentication and Application Settings) ---\n\n// Authentication Context\nconst AuthContext = createContext();\n\nexport const AuthProvider = ({ children }) => {\n  const [isAuthenticated, setIsAuthenticated] = useState(localStorage.getItem('isAuthenticated') === 'true');\n  const [user, setUser] = useState(JSON.parse(localStorage.getItem('user')));\n  // In a real app, token would be stored, and user info fetched or stored upon login\n\n  const login = (userData) => {\n    // Simulate API call for login\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const mockUser = { username: userData.username, role: 'teacher', school_id: 'school123', name: 'Demo User' };\n        localStorage.setItem('isAuthenticated', 'true');\n        localStorage.setItem('user', JSON.stringify(mockUser));\n        setIsAuthenticated(true);\n        setUser(mockUser);\n        resolve(mockUser);\n      }, 500);\n    });\n  };\n\n  const logout = () => {\n    localStorage.removeItem('isAuthenticated');\n    localStorage.removeItem('user');\n    setIsAuthenticated(false);\n    setUser(null);\n  };\n\n  return (\n    <AuthContext.Provider value={{ isAuthenticated, user, login, logout }}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nconst useAuth = () => useContext(AuthContext);\n\n// Application Settings Context (for language, theme, etc.)\nconst AppSettingsContext = createContext();\n\nconst AppSettingsProvider = ({ children }) => {\n  const [language, setLanguage] = useState(localStorage.getItem('appLanguage') || 'en');\n\n  useEffect(() => {\n    i18n.changeLanguage(language);\n    localStorage.setItem('appLanguage', language);\n  }, [language]);\n\n  const changeLanguage = (lang) => {\n    setLanguage(lang);\n  };\n\n  return (\n    <AppSettingsContext.Provider value={{ language, changeLanguage }}>\n      {children}\n    </AppSettingsContext.Provider>\n  );\n};\n\nconst useAppSettings = () => useContext(AppSettingsContext);\n\n\n// --- 2. i18n INTERNATIONALIZATION SETUP ---\ni18n\n  .use(HttpApi) // Use HttpApi backend to load translations\n  .use(initReactI18next) // Passes i18n down to react-i18next\n  .init({\n    supportedLngs: ['en', 'hi'],\n    fallbackLng: 'en',\n    debug: process.env.NODE_ENV === 'development', // Enable debug in development\n    interpolation: {\n      escapeValue: false, // React already safes from xss\n    },\n    backend: {\n      loadPath: '/locales/{{lng}}/{{ns}}.json', // Path to translation files in public folder\n    },\n    ns: ['common', 'dashboard', 'login'], // Namespaces\n    defaultNS: 'common',\n  });\n\n\n// Modern components imports\nimport ModernDashboard from './components/Dashboard/ModernDashboard';\nimport ModernLoginPage from './components/Auth/ModernLoginPage';\nimport AnalyticsDashboard from './components/Analytics/AnalyticsDashboard';\nimport StudentManagement from './components/Students/StudentManagement';\nimport SWOTAnalysisVisualization from './components/SWOT/SWOTAnalysisVisualization';\nimport CulturalSWOTVisualization from './components/SWOT/CulturalSWOTVisualization';\nimport IndividualStudentSWOT from './components/SWOT/IndividualStudentSWOT';\nimport LandingPage from './components/Landing/LandingPage';\nimport AboutPage from './components/Pages/AboutPage';\nimport FeaturesPage from './components/Pages/FeaturesPage';\nimport ContactPage from './components/Pages/ContactPage';\nimport ModernFooter from './components/Common/ModernFooter';\n\n// Phase 3 - Lazy Loaded Components for Performance Optimization\nconst StudentRegistration = React.lazy(() => import('./components/Students/StudentRegistration'));\nconst StudentProfile = React.lazy(() => import('./components/Students/StudentProfile'));\nconst AttendanceManagement = React.lazy(() => import('./components/Attendance/AttendanceManagement'));\nconst GradeEntry = React.lazy(() => import('./components/Grades/GradeEntry'));\nconst TeacherDashboard = React.lazy(() => import('./components/Dashboard/TeacherDashboard'));\nconst SWOTWizard = React.lazy(() => import('./components/SWOT/SWOTWizard'));\nconst ReportGeneration = React.lazy(() => import('./components/Reports/ReportGeneration'));\n\n// --- 4. LAYOUT COMPONENTS ---\nconst drawerWidth = 240;\n\n// Enhanced Header Component\nconst Header = () => {\n  const { t } = useTranslation('common');\n  const { logout, user } = useAuth();\n  const { language, changeLanguage } = useAppSettings();\n  const navigate = useNavigate();\n\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n\n  const handleLanguageChange = (event) => {\n    changeLanguage(event.target.value);\n  };\n\n  return (\n    <AppBar position=\"fixed\" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>\n      <Toolbar sx={{ px: { xs: 2, md: 3 } }}>\n        <Typography variant=\"h6\" noWrap component=\"div\" sx={{ flexGrow: 1, fontWeight: 600 }}>\n          {t('platformTitle')}\n        </Typography>\n\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n            <InputLabel id=\"language-select-label\">{t('language')}</InputLabel>\n            <Select\n              labelId=\"language-select-label\"\n              id=\"language-select\"\n              value={language}\n              label={t('language')}\n              onChange={handleLanguageChange}\n              sx={{\n                '& .MuiOutlinedInput-notchedOutline': {\n                  borderColor: 'rgba(255, 255, 255, 0.3)',\n                },\n                '&:hover .MuiOutlinedInput-notchedOutline': {\n                  borderColor: 'rgba(255, 255, 255, 0.5)',\n                },\n              }}\n            >\n              <MenuItem value=\"en\">🇬🇧 English</MenuItem>\n              <MenuItem value=\"hi\">🇮🇳 हिन्दी</MenuItem>\n            </Select>\n          </FormControl>\n\n          {user && (\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n              <Avatar\n                sx={{\n                  width: 32,\n                  height: 32,\n                  bgcolor: 'secondary.main',\n                  fontSize: '0.875rem',\n                  fontWeight: 600,\n                }}\n              >\n                {(user.name || user.username).charAt(0).toUpperCase()}\n              </Avatar>\n              <Typography sx={{ display: { xs: 'none', sm: 'block' } }}>\n                {user.name || user.username}\n              </Typography>\n            </Box>\n          )}\n\n          <Button\n            color=\"inherit\"\n            onClick={handleLogout}\n            startIcon={<ExitToAppIcon />}\n            sx={{\n              borderRadius: 2,\n              '&:hover': {\n                bgcolor: 'rgba(255, 255, 255, 0.1)',\n              },\n            }}\n          >\n            {t('logout')}\n          </Button>\n        </Box>\n      </Toolbar>\n    </AppBar>\n  );\n};\n\n// Sidebar Component\nconst Sidebar = () => {\n  const { t } = useTranslation('common');\n  const menuItems = [\n    { text: t('dashboard'), icon: <DashboardIcon />, path: '/dashboard' },\n    { text: t('students'), icon: <PeopleIcon />, path: '/students' },\n    { text: t('reports'), icon: <AssessmentIcon />, path: '/reports' },\n    { text: t('settings'), icon: <SettingsIcon />, path: '/settings' },\n  ];\n\n  return (\n    <Drawer\n      variant=\"permanent\"\n      sx={{\n        width: drawerWidth,\n        flexShrink: 0,\n        [`& .MuiDrawer-paper`]: { width: drawerWidth, boxSizing: 'border-box', backgroundColor: '#2E5BA8', color: 'white' },\n      }}\n    >\n      <Toolbar /> {/* For spacing under the AppBar */}\n      <Box sx={{ overflow: 'auto' }}>\n        <List>\n          {menuItems.map((item) => (\n            <ListItem button component={Link} to={item.path} key={item.text} sx={{\n              '&:hover': {\n                backgroundColor: 'rgba(255, 255, 255, 0.1)',\n              },\n              '& .MuiListItemIcon-root': {\n                color: 'white',\n              }\n            }}>\n              <ListItemIcon>{item.icon}</ListItemIcon>\n              <ListItemText primary={item.text} />\n            </ListItem>\n          ))}\n        </List>\n      </Box>\n    </Drawer>\n  );\n};\n\n// Main Layout for Authenticated Routes\nconst MainLayout = () => {\n  return (\n    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>\n      <Box sx={{ display: 'flex', flex: 1 }}>\n        <CssBaseline />\n        <Header />\n        <Sidebar />\n        <Box\n          component=\"main\"\n          sx={{\n            flexGrow: 1,\n            bgcolor: 'background.default',\n            p: 3,\n            width: `calc(100% - ${drawerWidth}px)`,\n            display: 'flex',\n            flexDirection: 'column'\n          }}\n        >\n          <Toolbar /> {/* For spacing under the AppBar */}\n          <Box sx={{ flex: 1 }}>\n            <Suspense fallback={<LoadingSpinner />}>\n              <Outlet /> {/* Nested routes will render here */}\n            </Suspense>\n          </Box>\n        </Box>\n      </Box>\n      <ModernFooter />\n    </Box>\n  );\n};\n\n// --- 5. PAGE COMPONENTS (Placeholders) ---\n// These would typically be in separate files under src/pages/\n\n// Modern Login Page Wrapper\nconst LoginPageWrapper = () => {\n  const { login } = useAuth();\n  const navigate = useNavigate();\n\n  const handleLogin = async (formData) => {\n    try {\n      await login({ username: formData.email, password: formData.password });\n      navigate('/dashboard');\n    } catch (err) {\n      console.error('Login failed:', err);\n    }\n  };\n\n  const handleSignup = async (formData) => {\n    try {\n      // In a real app, this would call a signup API\n      console.log('Signup data:', formData);\n      // For demo, auto-login after signup\n      await login({ username: formData.email, password: formData.password });\n      navigate('/dashboard');\n    } catch (err) {\n      console.error('Signup failed:', err);\n    }\n  };\n\n  return <ModernLoginPage onLogin={handleLogin} onSignup={handleSignup} />;\n};\n\n// Modern Page Components\nconst DashboardPage = () => {\n  return <ModernDashboard />;\n};\n\nconst StudentsPage = () => {\n  return <StudentManagement />;\n};\n\n// Phase 3 - Optimized Page Components with Suspense\nconst StudentRegistrationPage = () => {\n  return (\n    <Suspense fallback={<LazyLoadWrapper />}>\n      <StudentRegistration />\n    </Suspense>\n  );\n};\n\nconst StudentProfilePage = () => {\n  return (\n    <Suspense fallback={<LazyLoadWrapper />}>\n      <StudentProfile />\n    </Suspense>\n  );\n};\n\nconst TeacherDashboardPage = () => {\n  return (\n    <Suspense fallback={<LazyLoadWrapper />}>\n      <TeacherDashboard />\n    </Suspense>\n  );\n};\n\nconst AttendanceManagementPage = () => {\n  return (\n    <Suspense fallback={<LazyLoadWrapper />}>\n      <AttendanceManagement />\n    </Suspense>\n  );\n};\n\nconst GradeEntryPage = () => {\n  return (\n    <Suspense fallback={<LazyLoadWrapper />}>\n      <GradeEntry />\n    </Suspense>\n  );\n};\n\nconst SWOTWizardPage = () => {\n  return (\n    <Suspense fallback={<LazyLoadWrapper />}>\n      <SWOTWizard />\n    </Suspense>\n  );\n};\n\nconst ReportGenerationPage = () => {\n  return (\n    <Suspense fallback={<LazyLoadWrapper />}>\n      <ReportGeneration />\n    </Suspense>\n  );\n};\n\nconst SWOTPage = () => {\n  return <CulturalSWOTVisualization />;\n};\n\n\n\nconst IndividualSWOTPage = () => {\n  return <IndividualStudentSWOT />;\n};\n\nconst AnalyticsPage = () => {\n  return <AnalyticsDashboard />;\n};\n\nconst ReportsPage = () => {\n  const { t } = useTranslation('common');\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h4\" sx={{ fontWeight: 700, mb: 2 }}>\n        {t('reports')}\n      </Typography>\n      <Typography variant=\"body1\" color=\"text.secondary\">\n        Reports functionality coming soon...\n      </Typography>\n    </Box>\n  );\n};\n\nconst SettingsPage = () => {\n  const { t } = useTranslation('common');\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h4\" sx={{ fontWeight: 700, mb: 2 }}>\n        {t('settings')}\n      </Typography>\n      <Typography variant=\"body1\" color=\"text.secondary\">\n        Settings functionality coming soon...\n      </Typography>\n    </Box>\n  );\n};\nconst NotFoundPage = () => {\n  const { t } = useTranslation('common');\n  return (\n    <Box sx={{ textAlign: 'center', mt: 5 }}>\n      <Typography variant=\"h3\">{t('notFoundTitle')}</Typography>\n      <Typography>{t('notFoundMessage')}</Typography>\n      <Button component={Link} to=\"/dashboard\" variant=\"contained\" sx={{ mt: 2 }}>\n        {t('goHome')}\n      </Button>\n    </Box>\n  );\n};\nconst LoadingSpinner = () => (\n  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>\n    <CircularProgress />\n  </Box>\n);\n\nconst LazyLoadWrapper = () => (\n  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '60vh' }}>\n    <CircularProgress size={40} />\n    <Typography sx={{ ml: 2 }}>Loading component...</Typography>\n  </Box>\n);\n\n\n// --- 6. PROTECTED ROUTE COMPONENT ---\nconst ProtectedRoute = ({ children }) => {\n  const { isAuthenticated } = useAuth();\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n  return children;\n};\n\n\n// --- 7. MAIN APP COMPONENT & ROUTER CONFIGURATION ---\nfunction App() {\n  return (\n    <ThemeContextProvider>\n      <AuthProvider>\n        <AppSettingsProvider>\n          <Suspense fallback={<LoadingSpinner />}>\n            <BrowserRouter>\n              <Routes>\n                {/* Public Routes */}\n                <Route path=\"/\" element={<LandingPage />} />\n                <Route path=\"/about\" element={<AboutPage />} />\n                <Route path=\"/features\" element={<FeaturesPage />} />\n                <Route path=\"/contact\" element={<ContactPage />} />\n                <Route path=\"/login\" element={<LoginPageWrapper />} />\n\n                {/* Protected Dashboard Routes */}\n                <Route\n                  path=\"/dashboard\"\n                  element={\n                    <ProtectedRoute>\n                      <MainLayout />\n                    </ProtectedRoute>\n                  }\n                >\n                  {/* Nested routes within MainLayout */}\n                  <Route index element={<DashboardPage />} />\n                  <Route path=\"students\" element={<StudentsPage />} />\n                  {/* Phase 3 routes - ✅ All Successfully Enabled */}\n                  <Route path=\"students/register\" element={<StudentRegistrationPage />} />\n                  <Route path=\"students/:studentId\" element={<StudentProfilePage />} />\n                  <Route path=\"students/:studentId/swot\" element={<IndividualSWOTPage />} />\n                  <Route path=\"teacher\" element={<TeacherDashboardPage />} />\n                  <Route path=\"attendance\" element={<AttendanceManagementPage />} />\n                  <Route path=\"grades\" element={<GradeEntryPage />} />\n                  <Route path=\"swot/wizard\" element={<SWOTWizardPage />} />\n                  <Route path=\"analytics\" element={<AnalyticsPage />} />\n                  <Route path=\"swot\" element={<SWOTPage />} />\n                  <Route path=\"reports\" element={<ReportsPage />} />\n                  <Route path=\"reports/generate\" element={<ReportGenerationPage />} />\n                  <Route path=\"settings\" element={<SettingsPage />} />\n                </Route>\n                <Route path=\"*\" element={<NotFoundPage />} />\n              </Routes>\n            </BrowserRouter>\n          </Suspense>\n        </AppSettingsProvider>\n      </AuthProvider>\n    </ThemeContextProvider>\n  );\n}\n\nexport default App;\n", "import React from 'react'\nimport ReactDOM from 'react-dom/client'\nimport App from './App.jsx'\nimport './index.css'\n\n// Error boundary for better error handling\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error) {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    console.error('Application Error:', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div style={{ \n          display: 'flex', \n          flexDirection: 'column', \n          alignItems: 'center', \n          justifyContent: 'center', \n          height: '100vh',\n          fontFamily: 'Arial, sans-serif'\n        }}>\n          <h1>Something went wrong.</h1>\n          <p>Please refresh the page or contact support if the problem persists.</p>\n          <button \n            onClick={() => window.location.reload()}\n            style={{\n              padding: '10px 20px',\n              backgroundColor: '#2E5BA8',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer'\n            }}\n          >\n            Refresh Page\n          </button>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nReactDOM.createRoot(document.getElementById('root')).render(\n  <React.StrictMode>\n    <ErrorBoundary>\n      <App />\n    </ErrorBoundary>\n  </React.StrictMode>,\n)\n"], "file": "assets/index-4Al5idu_.js"}