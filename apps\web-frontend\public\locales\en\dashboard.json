{"dashboardTitle": "Dashboard", "welcomeMessage": "Welcome to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quickStats": "Quick Statistics", "totalStudents": "Total Students", "activeClasses": "Active Classes", "pendingReports": "Pending Reports", "recentActivity": "Recent Activity", "upcomingEvents": "Upcoming Events", "notifications": "Notifications", "quickActions": "Quick Actions", "addStudent": "Add Student", "generateReport": "Generate Report", "viewAnalytics": "View Analytics", "manageClasses": "Manage Classes", "studentPerformance": "Student Performance", "attendanceOverview": "Attendance Overview", "behaviorInsights": "Behavior Insights", "academicProgress": "Academic Progress", "swotAnalyses": "SWOT Analyses", "completedAnalyses": "Completed Analyses", "pendingAnalyses": "Pending Analyses", "scheduledAnalyses": "Scheduled Analyses", "topPerformers": "Top Performers", "needsAttention": "Needs Attention", "improvementTrends": "Improvement Trends", "classOverview": "Class Overview", "subjectPerformance": "Subject Performance", "attendanceRate": "Attendance Rate", "behaviorScore": "Behavior Score", "extracurricularParticipation": "Extracurricular Participation", "parentEngagement": "Parent Engagement", "teacherFeedback": "Teacher <PERSON><PERSON><PERSON>", "systemHealth": "System Health", "dataBackup": "Data Backup", "lastBackup": "Last Backup", "systemStatus": "System Status", "activeUsers": "Active Users", "storageUsed": "Storage Used", "apiRequests": "API Requests", "errorRate": "Error Rate", "responseTime": "Response Time", "announcements": "Announcements", "schoolNews": "School News", "importantNotices": "Important Notices", "deadlines": "Deadlines", "examSchedule": "Exam Schedule", "holidayCalendar": "Holiday Calendar", "parentMeetings": "Parent Meetings", "staffMeetings": "Staff Meetings", "trainingPrograms": "Training Programs", "workshopsAndSeminars": "Workshops & Seminars", "competitiveExams": "Competitive Exams", "scholarshipOpportunities": "Scholarship Opportunities", "careerGuidance": "Career Guidance", "collegeAdmissions": "College Admissions", "streamSelection": "Stream Selection", "subjectSelection": "Subject Selection", "performanceTrends": "Performance Trends", "monthlyReport": "Monthly Report", "termReport": "Term Report", "annualReport": "Annual Report", "customReport": "Custom Report", "exportData": "Export Data", "printReport": "Print Report", "shareReport": "Share Report", "scheduleReport": "Schedule Report", "reportTemplates": "Report Templates", "dataVisualization": "Data Visualization", "charts": "Charts", "graphs": "Graphs", "tables": "Tables", "dashboards": "Dashboards", "filters": "Filters", "dateRange": "Date Range", "classFilter": "Class Filter", "subjectFilter": "Subject Filter", "studentFilter": "Student Filter", "performanceFilter": "Performance Filter", "refreshData": "Refresh Data", "autoRefresh": "Auto Refresh", "lastUpdated": "Last Updated", "dataSource": "Data Source", "noDataMessage": "No data available for the selected criteria.", "loadingMessage": "Loading dashboard data...", "errorMessage": "Error loading dashboard data. Please try again.", "retryButton": "Retry", "helpButton": "Help", "settingsButton": "Settings", "fullScreenMode": "Full Screen Mode", "exitFullScreen": "Exit Full Screen", "customizeLayout": "Customize Layout", "resetLayout": "Reset Layout", "saveLayout": "Save Layout", "boardSpecificMetrics": "Board-Specific Metrics", "competitiveExamPrep": "Competitive Exam Preparation", "culturalParticipation": "Cultural Participation", "languageScores": "Language Scores", "moralEducation": "Moral Education", "practicalScores": "Practical Scores", "projectWork": "Project Work", "skillAssessment": "Skill Assessment", "communityService": "Community Service", "indianAcademicCalendar": "Indian Academic Calendar", "upcomingFestivals": "Upcoming Festivals", "boardExamSchedule": "Board Exam Schedule", "culturalEvents": "Cultural Events", "nationalHolidays": "National Holidays", "termStructure": "Term Structure", "assessmentTypes": "Assessment Types", "gradingSystem": "Grading System", "subjectStructure": "Subject Structure", "compulsorySubjects": "Compulsory Subjects", "optionalSubjects": "Optional Subjects", "boardFeatures": "Board Features", "academicYear": "Academic Year", "termExams": "Term Examinations", "boardExams": "Board Examinations", "culturalSWOT": "Cultural SWOT Analysis", "strengthsHindi": "Strengths (शक्तियाँ)", "weaknessesHindi": "Weaknesses (कमजोरियाँ)", "opportunitiesHindi": "Opportunities (अवसर)", "threatsHindi": "Threats (चुनौतियाँ)", "culturalContext": "Cultural Context", "indianEducationalInsights": "Indian Educational Insights", "festivalCelebrations": "Festival Celebrations", "regionalLanguages": "Regional Languages", "valueBasedLearning": "Value-based Learning", "socialResponsibility": "Social Responsibility"}